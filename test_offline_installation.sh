#!/bin/bash
# 测试完整的离线安装流程

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="${SCRIPT_DIR}/test_offline_install"

log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

cleanup() {
    if [[ -d "$TEST_DIR" ]]; then
        log_info "清理测试目录..."
        rm -rf "$TEST_DIR"
    fi
}

# 设置清理陷阱
trap cleanup EXIT

log_info "开始测试离线安装流程..."

# 1. 检查离线包是否存在
log_info "检查离线包..."
if [[ ! -d "npm_offline_packages" ]]; then
    log_error "npm_offline_packages目录不存在"
    log_info "请先运行: ./download_npm_packages_offline_v2.sh"
    exit 1
fi

if [[ ! -f "npm_offline_packages/install_npm_offline.sh" ]]; then
    log_error "install_npm_offline.sh脚本不存在"
    exit 1
fi

if [[ ! -f "npm_offline_packages/node_modules_production.tar.gz" ]]; then
    log_error "node_modules_production.tar.gz不存在"
    exit 1
fi

log_success "离线包检查通过"

# 2. 创建测试目录
mkdir -p "$TEST_DIR"
log_info "创建测试目录: $TEST_DIR"

# 3. 测试离线安装脚本
log_info "测试离线安装脚本..."
if npm_offline_packages/install_npm_offline.sh "$TEST_DIR" production; then
    log_success "离线安装脚本执行成功"
else
    log_error "离线安装脚本执行失败"
    exit 1
fi

# 4. 验证安装结果
log_info "验证安装结果..."
if ./verify_npm_installation.sh "$TEST_DIR"; then
    log_success "安装结果验证通过"
else
    log_error "安装结果验证失败"
    exit 1
fi

# 5. 测试Makefile集成
log_info "测试Makefile集成..."
cleanup  # 清理之前的测试

# 创建新的测试目录
mkdir -p "$TEST_DIR"

# 模拟Makefile的install-exec-local逻辑
log_info "模拟Makefile安装流程..."

# 检查离线包并安装
if [[ -d "npm_offline_packages" ]] && [[ -f "npm_offline_packages/install_npm_offline.sh" ]]; then
    log_info "检测到npm离线包，使用离线安装模式..."
    npm_offline_packages/install_npm_offline.sh "$TEST_DIR" production
    
    log_info "验证离线安装结果..."
    if [[ ! -d "$TEST_DIR/node_modules" ]]; then
        log_error "node_modules目录未创建"
        exit 1
    fi
    
    log_info "设置npm包权限..."
    find "$TEST_DIR/node_modules" -type d -exec chmod 755 {} \; 2>/dev/null || true
    find "$TEST_DIR/node_modules" -type f -exec chmod 644 {} \; 2>/dev/null || true
    find "$TEST_DIR/node_modules/.bin" -type f -exec chmod 755 {} \; 2>/dev/null || true
    
    log_success "离线npm包安装完成"
else
    log_error "离线包检测失败"
    exit 1
fi

# 模拟复制其他文件
log_info "模拟复制项目文件..."
if [[ -d "common" ]]; then
    cp -pr common "$TEST_DIR/"
    log_info "复制common目录完成"
fi

if [[ -d "assets" ]]; then
    cp -pr assets "$TEST_DIR/"
    log_info "复制assets目录完成"
fi

if [[ -f "package.json" ]]; then
    cp package.json "$TEST_DIR/"
    log_info "复制package.json完成"
fi

# 6. 最终验证
log_info "最终验证..."
if [[ ! -d "$TEST_DIR/node_modules" ]]; then
    log_error "最终验证失败，node_modules目录不存在"
    exit 1
fi

node_count=$(find "$TEST_DIR/node_modules" -maxdepth 1 -type d | wc -l)
node_count=$((node_count - 1))
log_info "已安装npm包数量: $node_count"

if [[ $node_count -lt 10 ]]; then
    log_error "安装的npm包数量过少，可能安装不完整"
    exit 1
fi

# 7. 性能测试
log_info "性能测试..."
start_time=$(date +%s)
tar -tzf npm_offline_packages/node_modules_production.tar.gz | wc -l
end_time=$(date +%s)
duration=$((end_time - start_time))
log_info "离线包文件数量统计耗时: ${duration}秒"

# 8. 生成测试报告
log_info "生成测试报告..."
echo ""
echo "=== 离线安装测试报告 ==="
echo "测试时间: $(date)"
echo "测试目录: $TEST_DIR"
echo "npm包数量: $node_count"
echo "离线包大小: $(du -sh npm_offline_packages | cut -f1)"
echo "安装结果: 成功"
echo ""

log_success "离线安装流程测试完成！"
log_info "所有测试通过，离线安装功能正常工作"

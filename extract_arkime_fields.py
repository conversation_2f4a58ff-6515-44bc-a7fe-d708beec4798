#!/usr/bin/env python3
"""
提取Arkime代码库中所有arkime_field_define调用并转换为Elasticsearch文档格式
"""

import os
import re
import json
import subprocess
from typing import List, Dict, Any

def extract_field_definitions() -> List[Dict[str, Any]]:
    """提取所有arkime_field_define调用"""
    
    # 搜索所有包含arkime_field_define的文件
    result = subprocess.run([
        'grep', '-r', 'arkime_field_define', 'capture/', 
        '--include=*.c', '-n'
    ], capture_output=True, text=True, cwd='/home/<USER>/arkime')
    
    if result.returncode != 0:
        print("Error running grep:", result.stderr)
        return []
    
    lines = result.stdout.strip().split('\n')
    field_definitions = []
    
    for line in lines:
        if not line.strip():
            continue
            
        # 解析grep输出: filename:line_number:content
        parts = line.split(':', 2)
        if len(parts) < 3:
            continue
            
        filename = parts[0]
        line_number = parts[1]
        content = parts[2].strip()
        
        # 跳过函数定义和注释
        if ('arkime_field_define(' in content and 
            not content.strip().startswith('//') and
            not content.strip().startswith('*') and
            'arkime_field_define_json' not in content and
            'arkime_field_define_text' not in content):
            
            field_def = parse_field_definition(filename, line_number, content)
            if field_def:
                field_definitions.append(field_def)
    
    return field_definitions

def parse_field_definition(filename: str, line_number: str, content: str) -> Dict[str, Any]:
    """解析单个arkime_field_define调用"""
    
    # 读取完整的函数调用（可能跨多行）
    try:
        with open(f'/home/<USER>/arkime/{filename}', 'r') as f:
            lines = f.readlines()
            
        start_line = int(line_number) - 1
        full_call = ""
        paren_count = 0
        in_string = False
        escape_next = False
        
        for i in range(start_line, len(lines)):
            line = lines[i]
            for char in line:
                if escape_next:
                    escape_next = False
                    continue
                    
                if char == '\\':
                    escape_next = True
                    continue
                    
                if char == '"' and not escape_next:
                    in_string = not in_string
                    
                if not in_string:
                    if char == '(':
                        paren_count += 1
                    elif char == ')':
                        paren_count -= 1
                        
            full_call += line
            
            if paren_count == 0 and 'arkime_field_define' in full_call:
                break
                
        return extract_field_info(full_call, filename)
        
    except Exception as e:
        print(f"Error parsing {filename}:{line_number}: {e}")
        return None

def extract_field_info(call_text: str, filename: str) -> Dict[str, Any]:
    """从函数调用文本中提取字段信息"""
    
    # 使用正则表达式提取参数
    # arkime_field_define("group", "kind", "expression", "friendlyName", "dbField", "help", ...)
    
    # 简化的解析 - 提取引号内的字符串参数
    string_params = re.findall(r'"([^"]*)"', call_text)
    
    if len(string_params) < 6:
        return None
        
    group = string_params[0]
    kind = string_params[1] 
    expression = string_params[2]
    friendly_name = string_params[3]
    db_field = string_params[4]
    help_text = string_params[5]
    
    # 提取额外的参数
    category = None
    aliases = None
    
    # 查找category参数
    category_match = re.search(r'"category",\s*"([^"]*)"', call_text)
    if category_match:
        category = category_match.group(1)
        
    # 查找aliases参数
    aliases_match = re.search(r'"aliases",\s*"([^"]*)"', call_text)
    if aliases_match:
        aliases = aliases_match.group(1)
    
    # 构建Elasticsearch文档格式
    field_doc = {
        "_index": "arkime_fields_v30",
        "_type": "_doc", 
        "_id": expression,
        "_score": 1,
        "_source": {
            "friendlyName": friendly_name,
            "group": group,
            "help": help_text,
            "dbField2": db_field,
            "type": map_arkime_type_to_es_type(kind)
        }
    }
    
    # 添加可选字段
    if category:
        field_doc["_source"]["category"] = category
        
    if aliases:
        try:
            # 尝试解析JSON格式的aliases
            import json
            aliases_list = json.loads(aliases)
            field_doc["_source"]["aliases"] = aliases_list
        except:
            field_doc["_source"]["aliases"] = [aliases]
    
    # 添加源文件信息作为注释
    field_doc["_source"]["_source_file"] = filename
    
    return field_doc

def map_arkime_type_to_es_type(arkime_type: str) -> str:
    """将Arkime字段类型映射到Elasticsearch类型"""
    
    type_mapping = {
        "integer": "integer",
        "ip": "ip", 
        "lotermfield": "termfield",
        "uptermfield": "termfield",
        "termfield": "termfield",
        "lotextfield": "textfield",
        "date": "date",
        "notreal": "object"
    }
    
    return type_mapping.get(arkime_type, arkime_type)

def main():
    """主函数"""
    print("正在提取Arkime字段定义...")
    
    field_definitions = extract_field_definitions()
    
    print(f"找到 {len(field_definitions)} 个字段定义")
    
    # 输出为JSON格式
    output_file = "arkime_fields_documentation.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(field_definitions, f, indent=2, ensure_ascii=False)
    
    print(f"字段定义已保存到: {output_file}")
    
    # 同时输出一些示例
    print("\n=== 示例字段定义 ===")
    for i, field_def in enumerate(field_definitions[:5]):
        print(f"\n{i+1}. {field_def['_id']}:")
        print(json.dumps(field_def, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()

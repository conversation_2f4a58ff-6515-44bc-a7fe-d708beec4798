# Arkime字段注册与会话数据映射完整文档

本文档是Arkime字段系统的完整参考手册，包含了字段注册、会话数据结构和映射关系的全面信息。

## 📋 文档概览

本文档集包含以下内容：

1. **字段注册统计** - 所有`arkime_field_define`调用的完整提取
2. **会话数据映射** - `arkime_sessions3-*`与`arkime_fields_v30`的对应关系
3. **完整映射表** - 224个字段的详细映射关系
4. **使用指南** - 实际应用中的查询和开发指导

## 📊 核心统计数据

### 字段注册统计
- **总字段数**: 224个
- **字段组数**: 23个
- **源码文件**: 覆盖capture目录下所有解析器和插件
- **索引格式**: Elasticsearch v30标准格式

### 字段组分布
```
general: 42 个字段    (通用字段，包括IP、MAC、端口等)
http: 37 个字段       (HTTP协议相关字段)
dns: 29 个字段        (DNS协议相关字段)
email: 19 个字段      (邮件协议相关字段)
cert: 18 个字段       (证书相关字段)
tls: 12 个字段        (TLS/SSL协议字段)
smb: 9 个字段         (SMB协议字段)
suricata: 7 个字段    (Suricata集成字段)
其他协议: 51 个字段   (DHCP、SOCKS、Modbus等)
```

### 字段类型分布
```
termfield: 156 个字段  (可搜索字符串，最常用)
integer: 44 个字段     (整数类型)
ip: 12 个字段          (IP地址类型)
textfield: 10 个字段   (文本字段)
date: 2 个字段         (日期时间)
```

## 🗂️ 生成的文档文件

### 1. 核心数据文件

#### `arkime_fields_clean.json`
- **格式**: 标准Elasticsearch文档格式
- **内容**: 224个字段的完整定义
- **用途**: 程序处理、ES导入
- **示例**:
```json
{
    "_index": "arkime_fields_v30",
    "_type": "_doc",
    "_id": "tls.ja4",
    "_score": 1,
    "_source": {
        "friendlyName": "JA4",
        "group": "tls",
        "help": "SSL/TLS JA4 field",
        "dbField2": "tls.ja4",
        "type": "termfield"
    }
}
```

### 2. 映射关系文档

#### `Arkime_Complete_Field_Mapping_Table.md`
- **内容**: 完整的字段映射对应表
- **格式**: 表格形式，包含字段ID、会话数据路径、说明
- **用途**: 开发查询时的参考手册

#### `Arkime_Session_Field_Mapping.md`
- **内容**: 会话数据结构与字段定义的对应关系
- **特点**: 按协议分类，包含使用示例

### 3. 详细文档

#### `Arkime_Fields_Complete_Documentation.md`
- **内容**: 按组分类的详细字段文档
- **特点**: 每个字段都有完整的JSON格式和属性表格

#### `Arkime_Fields_Summary_Table.md`
- **内容**: 字段汇总表格
- **用途**: 快速查找和参考

## 🔗 字段映射核心规律

### 1. 基础会话字段
```
会话数据字段          字段定义ID           说明
length               session.length       会话持续时间
protocols            protocols            协议数组
node                 (内置字段)           捕获节点
@timestamp           (ES内置)             处理时间戳
```

### 2. 源端/目标端映射
```
会话数据路径          字段定义ID           说明
source.ip            ip.src               源IP地址
source.port          port.src             源端口
source.mac           mac.src              源MAC地址
destination.ip       ip.dst               目标IP地址
destination.port     port.dst             目标端口
destination.mac      mac.dst              目标MAC地址
```

### 3. 协议特定字段
```
协议    会话数据路径      字段定义ID           示例
HTTP    http.*           http.*               http.method
DNS     dns.*            dns.*                dns.host
TLS     tls.*            tls.*                tls.ja4
Email   email.*          email.*              email.src
```

## 🔍 实际应用示例

### 1. 查询HTTP GET请求
```json
{
  "query": {
    "term": {
      "http.method": "GET"
    }
  }
}
```

### 2. 查询特定源IP的会话
```json
{
  "query": {
    "term": {
      "source.ip": "*************"
    }
  }
}
```

### 3. 聚合分析协议分布
```json
{
  "aggs": {
    "protocols": {
      "terms": {
        "field": "protocols"
      }
    }
  }
}
```

### 4. 查询TLS JA4指纹
```json
{
  "query": {
    "exists": {
      "field": "tls.ja4"
    }
  }
}
```

## 📚 使用指南

### 1. 字段查找流程
1. 在`Arkime_Complete_Field_Mapping_Table.md`中查找字段定义ID
2. 找到对应的会话数据路径
3. 使用会话数据路径构建ES查询

### 2. 开发建议
- 优先使用字段定义ID进行搜索和过滤
- 注意区分内置字段和定义字段
- 协议特定字段需要检查协议是否存在

### 3. 性能优化
- 使用term查询精确匹配
- 对于文本字段使用match查询
- 合理使用聚合减少数据传输

## 🛠️ 技术实现

### 字段注册机制
所有字段通过`arkime_field_define()`函数注册：
```c
arkime_field_define(group, kind, expression, friendlyName, 
                   dbField, help, type, flags, ...);
```

### 数据存储结构
- **字段定义**: 存储在`arkime_fields_v30`索引
- **会话数据**: 存储在`arkime_sessions3-YYYYMMDD`索引
- **映射关系**: 通过`dbField2`字段建立连接

## 📝 维护说明

### 文档更新
当Arkime代码库更新时，可以重新运行提取脚本：
```bash
python3 extract_arkime_fields.py
python3 generate_field_documentation.py
python3 create_complete_field_mapping.py
```

### 验证方法
1. 检查字段总数是否匹配
2. 验证新增协议字段
3. 确认映射关系正确性

---

## 📄 文档清单

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `arkime_fields_clean.json` | 字段定义JSON | 程序处理 |
| `Arkime_Complete_Field_Mapping_Table.md` | 完整映射表 | 开发参考 |
| `Arkime_Session_Field_Mapping.md` | 会话映射关系 | 理解数据结构 |
| `Arkime_Fields_Complete_Documentation.md` | 详细字段文档 | 深入了解 |
| `Arkime_Fields_Summary_Table.md` | 字段汇总表 | 快速查找 |

*本文档集提供了Arkime字段系统的完整视图，是开发、运维和分析工作的重要参考资料。*

Frame 1: 207 bytes on wire (1656 bits), 207 bytes captured (1656 bits)
    Encapsulation type: Ethernet (1)
    Arrival Time: Dec 31, 2002 21:55:31.300000000 CST
    [Time shift for this packet: 0.000000000 seconds]
    Epoch Time: 1041342931.300000000 seconds
    [Time delta from previous captured frame: 0.000000000 seconds]
    [Time delta from previous displayed frame: 0.000000000 seconds]
    [Time since reference or first frame: 0.000000000 seconds]
    Frame Number: 1
    Frame Length: 207 bytes (1656 bits)
    Capture Length: 207 bytes (1656 bits)
    [Frame is marked: False]
    [Frame is ignored: False]
    [Protocols in frame: Ethernet.Ethertype.IPv4.TCP.HTTP]
Ethernet II, Src: 00:09:6b:88:f5:c9 (00:09:6b:88:f5:c9), Dst: 00:e0:81:00:b0:28 (00:e0:81:00:b0:28)
    Destination: 00:e0:81:00:b0:28 (00:e0:81:00:b0:28)
        Address: 00:e0:81:00:b0:28 (00:e0:81:00:b0:28)
        .... ..0. .... .... .... .... = LG bit: Globally unique address (factory default)
        .... ...0 .... .... .... .... = IG bit: Individual address (unicast)
    Source: 00:09:6b:88:f5:c9 (00:09:6b:88:f5:c9)
        Address: 00:09:6b:88:f5:c9 (00:09:6b:88:f5:c9)
        .... ..0. .... .... .... .... = LG bit: Globally unique address (factory default)
        .... ...0 .... .... .... .... = IG bit: Individual address (unicast)
    Type: IPv4 (0x0800)
Internet Protocol Version 4, Src: ********, Dst: *************
    0100 .... = Version: 4
    .... 0101 = Header Length: 20 bytes (5)
    Differentiated Services Field: 0x00 (DSCP: CS0, ECN: Not-ECT)
        0000 00.. = Differentiated Services Codepoint: Default (0)
        .... ..00 = Explicit Congestion Notification: Not ECN-Capable Transport (0)
    Total Length: 193
    Identification: 0xd249 (53833)
    Flags: 0x4000, Don't fragment
        0... .... .... .... = Reserved bit: Not set
        .1.. .... .... .... = Don't fragment: Set
        ..0. .... .... .... = More fragments: Not set
        ...0 0000 0000 0000 = Fragment offset: 0
    Time to live: 128
    Protocol: TCP (6)
    Header checksum: 0xc85b [validation disabled]
    [Header checksum status: Unverified]
    Source: ********
    Destination: *************
Transmission Control Protocol, Src Port: 3267, Dst Port: 80, Seq: 1, Ack: 1, Len: 153
    Source Port: 3267
    Destination Port: 80
    [Stream index: 0]
    [TCP Segment Len: 153]
    Sequence number: 1    (relative sequence number)
    [Next sequence number: 154    (relative sequence number)]
    Acknowledgment number: 1    (relative ack number)
    0101 .... = Header Length: 20 bytes (5)
    Flags: 0x018 (PSH, ACK)
        000. .... .... = Reserved: Not set
        ...0 .... .... = Nonce: Not set
        .... 0... .... = Congestion Window Reduced (CWR): Not set
        .... .0.. .... = ECN-Echo: Not set
        .... ..0. .... = Urgent: Not set
        .... ...1 .... = Acknowledgment: Set
        .... .... 1... = Push: Set
        .... .... .0.. = Reset: Not set
        .... .... ..0. = Syn: Not set
        .... .... ...0 = Fin: Not set
        [TCP Flags: ·······AP···]
    Window size value: 64240
    [Calculated window size: 64240]
    [Window size scaling factor: -1 (unknown)]
    Checksum: 0xad62 [unverified]
    [Checksum Status: Unverified]
    Urgent pointer: 0
    [SEQ/ACK analysis]
        [Bytes in flight: 153]
        [Bytes sent since last PSH flag: 153]
    [Timestamps]
        [Time since first frame in this TCP stream: 0.000000000 seconds]
        [Time since previous frame in this TCP stream: 0.000000000 seconds]
    TCP payload (153 bytes)
Hypertext Transfer Protocol
    GET /v4/iuident.cab?0307011208 HTTP/1.1\r\n
        [Expert Info (Chat/Sequence): GET /v4/iuident.cab?0307011208 HTTP/1.1\r\n]
            [GET /v4/iuident.cab?0307011208 HTTP/1.1\r\n]
            [Severity level: Chat]
            [Group: Sequence]
        Request Method: GET
        Request URI: /v4/iuident.cab?0307011208
            Request URI Path: /v4/iuident.cab
            Request URI Query: 0307011208
                Request URI Query Parameter: 0307011208
        Request Version: HTTP/1.1
    Accept: */*\r\n
    User-Agent: Industry Update Control\r\n
    Host: windowsupdate.microsoft.com\r\n
    Connection: Keep-Alive\r\n
    \r\n
    [Full request URI: http://windowsupdate.microsoft.com/v4/iuident.cab?0307011208]
    [HTTP request 1/1]

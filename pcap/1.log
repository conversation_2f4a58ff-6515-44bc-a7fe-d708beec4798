Jul 11 13:59:22 config.c:181 arkime_config_str(): rotateIndex=daily
Jul 11 13:59:22 config.c:181 arkime_config_str(): nodeClass=(null)
Jul 11 13:59:22 config.c:219 arkime_config_str_list(): dontSaveTags=(null)
Jul 11 13:59:22 config.c:219 arkime_config_str_list(): plugins=(null)
Jul 11 13:59:22 config.c:219 arkime_config_str_list(): rootPlugins=(null)
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): smtpIpHeaders=X-Originating-IP:;X-Barracuda-Apparent-Source-IP:
Jul 11 13:59:22 config.c:181 arkime_config_str(): prefix=arkime_
Jul 11 13:59:22 config.c:181 arkime_config_str(): elasticsearch=http://localhost:9200
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): interface=ens33
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): pcapDir=/opt/arkime/raw
Jul 11 13:59:22 config.c:181 arkime_config_str(): bpf=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): yara=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): rirFile=/opt/arkime/etc/ipv4-address-space.csv
Jul 11 13:59:22 config.c:181 arkime_config_str(): ouiFile=/opt/arkime/etc/oui.txt
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): geoLite2ASN=/var/lib/GeoIP/GeoLite2-ASN.mmdb;/usr/share/GeoIP/GeoLite2-ASN.mmdb;/opt/arkime/etc/GeoLite2-ASN.mmdb
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): geoLite2Country=/var/lib/GeoIP/GeoLite2-Country.mmdb;/usr/share/GeoIP/GeoLite2-Country.mmdb;/opt/arkime/etc/GeoLite2-Country.mmdb
Jul 11 13:59:22 config.c:181 arkime_config_str(): dropUser=nobody
Jul 11 13:59:22 config.c:181 arkime_config_str(): dropGroup=daemon
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): pluginsDir=/opt/arkime/plugins
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): parsersDir=/opt/arkime/parsers
Jul 11 13:59:22 config.c:181 arkime_config_str(): caTrustFile=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): offlineFilenameRegex=(?i)\.(pcap|cap)$
Jul 11 13:59:22 config.c:181 arkime_config_str(): pcapDirTemplate=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): pcapDirAlgorithm=round-robin
Jul 11 13:59:22 config.c:322 arkime_config_double(): maxFileSizeG=0.010000
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxFileTimeM=0
Jul 11 13:59:22 config.c:291 arkime_config_int(): icmpTimeout=10
Jul 11 13:59:22 config.c:291 arkime_config_int(): udpTimeout=30
Jul 11 13:59:22 config.c:291 arkime_config_int(): tcpTimeout=600
Jul 11 13:59:22 config.c:291 arkime_config_int(): sctpTimeout=60
Jul 11 13:59:22 config.c:291 arkime_config_int(): espTimeout=600
Jul 11 13:59:22 config.c:291 arkime_config_int(): tcpSaveTimeout=720
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxStreams=1000000
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxPackets=10000
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxPacketsInQueue=200000
Jul 11 13:59:22 config.c:291 arkime_config_int(): dbBulkSize=1000000
Jul 11 13:59:22 config.c:291 arkime_config_int(): dbFlushTimeout=5
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxESConns=30
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxESRequests=500
Jul 11 13:59:22 config.c:291 arkime_config_int(): logEveryXPackets=100000
Jul 11 13:59:22 config.c:291 arkime_config_int(): pcapBufferSize=300000000
Jul 11 13:59:22 config.c:282 arkime_config_int(): INFO: Reseting pcapWriteSize since 65535 is less then the min 65536
Jul 11 13:59:22 config.c:291 arkime_config_int(): pcapWriteSize=65536
Jul 11 13:59:22 config.c:291 arkime_config_int(): fragsTimeout=480
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxFrags=10000
Jul 11 13:59:22 config.c:291 arkime_config_int(): snapLen=16384
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxMemPercentage=100
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxReqBody=64
Jul 11 13:59:22 config.c:291 arkime_config_int(): packetThreads=2
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): logUnknownProtocols=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): logESRequests=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): logFileCreation=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): logHTTPConnections=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseSMTP=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseSMTPHeaderAll=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseSMB=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): ja3Strings=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseQSValue=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseCookieValue=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseHTTPHeaderRequestAll=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseHTTPHeaderResponseAll=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): supportSha256=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): reqBodyOnlyUtf8=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): compressES=true
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): readTruncatedPackets=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): trackESP=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): yaraEveryPacket=true
Jul 11 13:59:22 config.c:181 arkime_config_str(): autoGenerateId=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): enablePacketLen=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): enablePacketDedup=true
Jul 11 13:59:22 config.c:219 arkime_config_str_list(): saveUnknownPackets=(null)
Jul 11 13:59:22 config.c:1564 arkime_config_init(): maxFileSizeB: 10737418
Jul 11 13:59:22 config.c:291 arkime_config_int(): dedupSeconds=2
Jul 11 13:59:22 config.c:291 arkime_config_int(): dedupPackets=1048575
Jul 11 13:59:22 dedup.c:131 arkime_dedup_init(): seconds = 3 packets = 1048575 slots = 99991 size = 1999820 mem=96291333
Jul 11 13:59:22 config.c:291 arkime_config_int(): offlineDispatchAfter=2500
Jul 11 13:59:22 config.c:291 arkime_config_int(): httpVLanVNI=0
Jul 11 13:59:22 config.c:181 arkime_config_str(): elasticsearchAPIKey=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): elasticsearchBasicAuth=wuby:VictoR#.0.0
Jul 11 13:59:22 config.c:291 arkime_config_int(): esMaxRetries=2
Jul 11 13:59:22 config.c:181 arkime_config_str(): esClientCert=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): esClientKey=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): esClientKeyPass=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): esBulkQuery=/_bulk
Jul 11 13:59:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/_template/arkime_sessions3_template?filter_path=**._meta 0/96 1ms 3ms
Jul 11 13:59:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 404 http://localhost:9200/arkime_sequence/_doc/fn-localhost 0/82 0ms 3ms
Jul 11 13:59:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 201 http://localhost:9200/arkime_sequence/_doc/fn-localhost?version_type=external&version=100 2/180 0ms 49ms
Jul 11 13:59:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 404 http://localhost:9200/arkime_stats/_doc/localhost 0/76 0ms 5ms
Jul 11 13:59:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_fields/_search?size=3000 0/1606 0ms 5ms
Jul 11 13:59:22 db.c:2844 arkime_db_init(): WARNING - No Geo Country file could be loaded, see https://arkime.com/settings#geolite2country
Jul 11 13:59:22 db.c:2855 arkime_db_init(): WARNING - No Geo ASN file could be loaded, see https://arkime.com/settings#geolite2asn
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): dbEsHealthCheck=true
Jul 11 13:59:22 config.c:181 arkime_config_str(): ecsEventProvider=(null)
Jul 11 13:59:22 config.c:181 arkime_config_str(): ecsEventDataset=(null)
Jul 11 13:59:22 config.c:219 arkime_config_str_list(): packetDropIpsFiles=(null)
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): yaraFastMode=true
Jul 11 13:59:22 config.c:181 arkime_config_str(): magicMode=both
Jul 11 13:59:22 config.c:256 arkime_config_str_list(): disableParsers=arp.so
Jul 11 13:59:22 parsers.c:738 arkime_parsers_init(): Skipping arp.so in /opt/arkime/parsers since already loaded
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/bgp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/certs.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ciscometadata.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/dhcp.so
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): parseDNSRecordAll=false
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): dnsOutputAnswers=false
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/dns.so
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): ja4Raw=false
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/dtls.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/erspan.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/esp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/geneve.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/gre.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/gtp.so
Jul 11 13:59:22 field.c:402 arkime_field_define(): UPDATING - Field category in db ["url", "host"] doesn't match field category ["url","host"] in capture for field http.uri
Jul 11 13:59:22 config.c:291 arkime_config_int(): parseHTTPHeaderValueMaxLen=1024
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/http.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/http2.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/icmp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/igmp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/irc.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/isis.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/krb5.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ldap.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/lldp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/misc.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/modbus.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/mpls.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/mysql.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/nfs.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/nsh.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/oracle.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ospf.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/pim.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/postgresql.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ppp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/quic.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/radius.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/rpc.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/sctp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/smb.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/smtp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/snmp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/socks.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ssh.so
Jul 11 13:59:22 config.c:291 arkime_config_int(): maxTcpOutOfOrderPackets=256
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/tcp.so
Jul 11 13:59:22 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/tds.so
Jul 11 13:59:22 config.c:348 arkime_config_boolean(): ja4Raw=false
Jul 11 13:59:22 field.c:564 arkime_field_by_exp(): ERROR - expr host.http wasn't defined

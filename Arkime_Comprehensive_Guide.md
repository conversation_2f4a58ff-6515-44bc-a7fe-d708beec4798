# Arkime 综合技术文档

## 目录

1. [项目概述](#1-项目概述)
2. [系统架构与设计](#2-系统架构与设计)
3. [部署指南](#3-部署指南)
4. [SPI输出分析](#4-spi输出分析)
5. [数据结构详解](#5-数据结构详解)
6. [压缩与编码机制](#6-压缩与编码机制)
7. [ES数据集成](#7-es数据集成)
8. [SDX DPI集成方案](#8-sdx-dpi集成方案)
9. [性能优化](#9-性能优化)
10. [故障排除](#10-故障排除)
11. [开发指南](#11-开发指南)

---

## 1. 项目概述

### 1.1 Arkime简介

Arkime是一个大规模、开源的网络分析和数据包捕获系统，提供完整的网络流量分析解决方案。

**核心特性：**
- 高性能数据包捕获和分析
- 基于Elasticsearch的数据存储
- 丰富的Web界面和查询功能
- 支持多种协议解析
- 可扩展的插件架构

### 1.2 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据包捕获    │    │   协议解析      │    │   数据存储      │
│   (Capture)     │───▶│   (Parsers)     │───▶│ (Elasticsearch) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PCAP存储      │    │   会话管理      │    │   Web界面       │
│   (Writer)      │    │   (Session)     │    │   (Viewer)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.3 项目目标

**主要目标：**
- 实现高性能网络流量分析
- 提供完整的数据包级别可视化
- 支持大规模分布式部署
- 与现有DPI系统集成

**成功标准：**
- [ ] 输出JSON格式完全符合Arkime SPI规范
- [ ] Arkime viewer能正常显示所有会话信息
- [ ] 数据包详情页面功能正常
- [ ] 性能损失不超过10%
- [ ] 与现有系统保持向后兼容

---

## 2. 系统架构与设计

### 2.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DPDK网卡      │    │   PCAP文件      │    │   其他数据源    │
│   数据捕获      │    │   读取          │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      数据包批处理         │
                    │   (现有DPDK处理流程)      │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      会话管理模块         │
                    │   (增强flow_info)        │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      协议解析器           │
                    │   (现有解析器+适配)       │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    Arkime兼容层          │
                    │   (新增核心模块)          │
                    └─────────┬─────────┬───────┘
                             │         │
                ┌─────────────▼─────────▼─────────────┐
                │         输出模块                    │
                ├─────────────┬─────────┬─────────────┤
                │ ES输出模块  │PCAP模块 │ JSON模块    │
                └─────────────┴─────────┴─────────────┘
```

### 2.2 核心组件

#### 2.2.1 数据包处理流程

```
数据包捕获 → 批处理队列 → 数据包线程 → 会话管理 → 协议解析 → SPI保存 → ES存储
```

#### 2.2.2 主要调用链

```
g_main_loop_run()
├── 数据包读取器
│   ├── arkime_packet_batch()
│   └── arkime_packet_batch_flush()
├── arkime_packet_thread()
│   ├── arkime_packet_process()
│   │   ├── mProtocols.createSessionId()
│   │   ├── arkime_session_find_or_create()
│   │   ├── mProtocols.preProcess()
│   │   ├── arkime_writer_write()
│   │   ├── 协议解析器调用
│   │   └── mProtocols.process()
│   └── arkime_session_process_commands()
└── 会话保存流程
    ├── arkime_session_mid_save()
    ├── arkime_session_save()
    ├── arkime_db_save_session()
    ├── JSON序列化
    ├── arkime_db_send_bulk()
    └── HTTP POST 到 Elasticsearch
```

### 2.3 数据流转

#### 2.3.1 数据包到会话的转换

1. **数据包捕获**: 从网络接口或文件读取原始数据包
2. **批处理**: 将数据包组织成批次提高处理效率
3. **会话识别**: 基于五元组创建或查找会话
4. **协议解析**: 逐层解析网络协议
5. **字段提取**: 提取协议特定的字段信息
6. **会话更新**: 更新会话状态和统计信息

#### 2.3.2 会话到ES文档的转换

1. **会话序列化**: 将会话对象转换为JSON格式
2. **字段映射**: 确保字段符合ES索引映射
3. **批量发送**: 使用bulk API提高写入效率
4. **错误处理**: 处理网络和ES错误

---

## 3. 部署指南

### 3.1 系统要求

#### 3.1.1 硬件要求
- **CPU**: 最少4核，推荐8核或更多
- **内存**: 最少8GB，推荐16GB或更多
- **存储**: 
  - 系统盘：最少50GB
  - 数据盘：根据数据保留需求，推荐500GB或更多
- **网络**: 千兆网卡，支持数据包捕获

#### 3.1.2 软件要求
- CentOS 7.4 或更高版本
- Root权限
- 网络连接（在线安装时需要）

#### 3.1.3 端口要求
- **8005**: Arkime Web界面
- **9200-9300**: Elasticsearch
- **22**: SSH管理

### 3.2 在线部署

#### 3.2.1 Elasticsearch安装

```bash
# 添加Elasticsearch仓库
cat > /etc/yum.repos.d/elasticsearch.repo << 'EOF'
[elasticsearch]
name=Elasticsearch repository for 7.x packages
baseurl=https://artifacts.elastic.co/packages/7.x/yum
gpgcheck=1
gpgkey=https://artifacts.elastic.co/GPG-KEY-elasticsearch
enabled=0
autorefresh=1
type=rpm-md
EOF

# 安装Elasticsearch
yum install --enablerepo=elasticsearch elasticsearch -y

# 配置Elasticsearch
cat > /etc/elasticsearch/elasticsearch.yml << 'EOF'
cluster.name: arkime
node.name: node-1
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node
EOF

# 启动服务
systemctl enable elasticsearch
systemctl start elasticsearch
```

#### 3.2.2 Arkime安装

```bash
# 下载Arkime
cd /opt
wget https://github.com/arkime/arkime/releases/download/v4.6.0/arkime_4.6.0-1.centos7_x86_64.rpm

# 安装依赖
yum install -y epel-release
yum install -y nodejs npm python3

# 安装Arkime
rpm -ivh arkime_4.6.0-1.centos7_x86_64.rpm

# 初始化配置
/opt/arkime/bin/Configure
```

### 3.3 离线部署

#### 3.3.1 准备离线安装包

```
curl-8.4.0.tar.gz
elasticsearch-7.17.15-x86_64.rpm
arkime_4.6.0-1.centos7_x86_64.rpm
nodejs-16.20.2-linux-x64.tar.xz
python3-offline-packages.tar.gz
```

#### 3.3.2 离线安装步骤

```bash
# 1. 安装基础依赖
tar -xf offline-packages.tar.gz
cd offline-packages
rpm -ivh *.rpm --force --nodeps

# 2. 安装Node.js
tar -xf nodejs-16.20.2-linux-x64.tar.xz -C /opt/
ln -s /opt/node-v16.20.2-linux-x64/bin/node /usr/local/bin/
ln -s /opt/node-v16.20.2-linux-x64/bin/npm /usr/local/bin/

# 3. 安装Elasticsearch
rpm -ivh elasticsearch-7.17.15-x86_64.rpm

# 4. 安装Arkime
rpm -ivh arkime_4.6.0-1.centos7_x86_64.rpm
```

### 3.4 配置说明

#### 3.4.1 基础配置

```ini
# /opt/arkime/etc/config.ini
[default]
# Elasticsearch配置
elasticsearch=http://localhost:9200
rotateIndex=daily
passwordSecret=your-secret-key

# 网络接口
interface=eth0

# PCAP存储
pcapDir=/opt/arkime/raw
maxFileSizeG=12
maxFileTimeM=120

# 性能配置
packetThreads=4
maxPacketsInQueue=200000
pcapWriteSize=262144

# 压缩配置
simpleCompression=zstd
simpleZstdLevel=3
gapPacketPos=true
```

#### 3.4.2 高级配置

```ini
# 协议解析
parseSMTP=true
parseSMB=true
parseQSValue=false
supportSha256=false

# 安全配置
dropUser=nobody
dropGroup=daemon
httpRealm=Arkime

# 集群配置
viewPort=8005
viewHost=0.0.0.0
cronQueries=true
```

---

## 4. SPI输出分析

### 4.1 SPI概述

Arkime的SPI (Session Processing Interface) 是将网络流量数据处理后输出到Elasticsearch的核心接口。

### 4.2 数据处理流程

#### 4.2.1 数据包处理线程

```c
// 主要处理函数
LOCAL void *arkime_packet_thread(void *threadp) {
    while (!config.quitting) {
        // 从队列获取数据包批次
        arkime_packet_batch_flush(&batch);

        // 处理每个数据包
        DLL_FOREACH_FORWARD(packet_, &batch.packetQ, packet) {
            arkime_packet_process(packet, &batch);
        }

        // 处理会话命令
        arkime_session_process_commands(thread);
    }
}
```

#### 4.2.2 数据包处理核心逻辑

```c
void arkime_packet_process(ArkimePacket_t *packet, ArkimePacketBatch_t *batch) {
    // 1. 创建会话ID
    mProtocols[packet->mProtocol].createSessionId(packet->sessionId, packet);

    // 2. 查找或创建会话
    session = arkime_session_find_or_create(packet->sessionId, &isNew);

    // 3. 预处理
    mProtocols[packet->mProtocol].preProcess(session, packet, isNew);

    // 4. 写入PCAP
    arkime_writer_write(session, packet);

    // 5. 协议解析
    arkime_parsers_run(session, packet, batch);

    // 6. 后处理
    mProtocols[packet->mProtocol].process(session, packet);
}
```

### 4.3 会话管理

#### 4.3.1 会话结构

```c
typedef struct arkime_session {
    struct arkime_session *hash_next, *hash_prev;
    struct arkime_session *tcp_next, *tcp_prev;
    struct arkime_session *q_next, *q_prev;

    char                sessionId[ARKIME_SESSIONID_LEN];
    uint32_t            hash;
    short               thread;

    struct timeval      firstPacket;
    struct timeval      lastPacket;

    uint64_t            bytes[2];
    uint64_t            packets[2];

    GArray             *filePosArray;
    GArray             *fileNumArray;
    GArray             *fileLenArray;

    // 协议特定字段
    GHashTable         *fields;

    // 状态信息
    uint16_t            stopSaving;
    uint16_t            midSave;
    uint8_t             tcp_state;
} ArkimeSession_t;
```

#### 4.3.2 会话保存流程

```c
void arkime_session_save(ArkimeSession_t *session) {
    // 1. 运行保存前回调
    if (pluginsCbs & ARKIME_PLUGIN_PRE_SAVE)
        arkime_plugins_cb_pre_save(session, TRUE);

    // 2. 运行规则
    arkime_rules_run_before_save(session, 1);

    // 3. 保存到数据库
    arkime_db_save_session(session, TRUE);

    // 4. 清理会话
    arkime_session_free(session);
}
```

### 4.4 JSON序列化

#### 4.4.1 JSON构建过程

```c
void arkime_db_save_session(ArkimeSession_t *session, int final) {
    BSB jbsb;
    char *json;

    // 初始化JSON缓冲区
    BSB_INIT(jbsb, jsonBuffer, sizeof(jsonBuffer));

    // 构建JSON文档
    arkime_db_js0n_str(&jbsb, session, final);

    // 发送到ES
    arkime_db_send_bulk(json, BSB_LENGTH(jbsb));
}
```

#### 4.4.2 字段序列化

```c
void arkime_db_js0n_str(BSB *jbsb, ArkimeSession_t *session, int final) {
    // 基础字段
    BSB_EXPORT_sprintf(jbsb, "\"firstPacket\":%" PRIu64, session->firstPacket);
    BSB_EXPORT_sprintf(jbsb, "\"lastPacket\":%" PRIu64, session->lastPacket);
    BSB_EXPORT_sprintf(jbsb, "\"length\":%" PRIu64, session->length);

    // 网络信息
    BSB_EXPORT_sprintf(jbsb, "\"ipProtocol\":%d", session->ipProtocol);
    BSB_EXPORT_sprintf(jbsb, "\"packets\":%" PRIu64, session->packets[0] + session->packets[1]);
    BSB_EXPORT_sprintf(jbsb, "\"bytes\":%" PRIu64, session->bytes[0] + session->bytes[1]);

    // packetPos数组（支持gap编码）
    arkime_db_js0n_packetPos(jbsb, session);

    // 协议特定字段
    arkime_db_js0n_fields(jbsb, session);
}
```

### 4.5 协议解析器

#### 4.5.1 解析器注册

```c
void arkime_parsers_register(ArkimeSession_t *session,
                            ArkimeParserType type,
                            const char *name,
                            int value,
                            ArkimeParserFunc func) {
    // 注册解析器到相应的类型
    switch(type) {
        case ARKIME_PARSERS_PORT:
            // 按端口注册
            break;
        case ARKIME_PARSERS_IP:
            // 按IP注册
            break;
        case ARKIME_PARSERS_ETHERNET:
            // 按以太网类型注册
            break;
    }
}
```

#### 4.5.2 HTTP解析器示例

```c
LOCAL int http_parser(ArkimeSession_t *session, void *uw,
                     const uint8_t *data, int len, int which) {
    HTTPInfo_t *http = uw;

    // 解析HTTP头部
    if (http->state == HTTP_STATE_HEADER) {
        // 提取方法
        if (strncmp(data, "GET ", 4) == 0) {
            arkime_field_string_add(httpMethodField, session, "GET", 3, TRUE);
        }

        // 提取URI
        char *uri = extract_uri(data, len);
        if (uri) {
            arkime_field_string_add(httpUriField, session, uri, -1, TRUE);
        }

        // 提取Host头
        char *host = extract_host_header(data, len);
        if (host) {
            arkime_field_string_add(httpHostField, session, host, -1, TRUE);
        }
    }

    return 0;
}
```

---

## 5. 数据结构详解

### 5.1 Session必须字段

#### 5.1.1 核心时间字段

```json
{
  "@timestamp": 1752149929057,      // 毫秒时间戳，session处理时间
  "firstPacket": 1041342931300,     // session第一包时间戳（毫秒）
  "lastPacket": 1041342932300,      // session最后一包时间戳（毫秒）
  "length": 1000                    // 会话持续时间（毫秒）
}
```

#### 5.1.2 网络协议字段

```json
{
  "ipProtocol": 6,                  // IP协议号：6=TCP, 17=UDP, 1=ICMP
  "protocols": ["tcp", "http"],     // 协议栈数组
  "node": "localhost"               // 捕获节点名称
}
```

#### 5.1.3 网络端点字段

```json
{
  "source": {
    "ip": "*************",         // 源IP地址
    "port": 3267,                  // 源端口
    "bytes": 207,                  // 源方向字节数
    "packets": 1,                  // 源方向数据包数
    "mac": ["00:09:6b:88:f5:c9"]   // 源MAC地址数组
  },
  "destination": {
    "ip": "*************",         // 目标IP地址
    "port": 80,                    // 目标端口
    "bytes": 1024,                 // 目标方向字节数
    "packets": 2,                  // 目标方向数据包数
    "mac-cnt": 1,                  // MAC地址计数
    "mac": ["00:e0:81:00:b0:28"]   // 目标MAC地址数组
  }
}
```

#### 5.1.4 数据包位置字段

```json
{
  "packetPos": [
    -102,                          // 负数：文件ID标记
    1900544,                       // 第一个数据包位置
    66,                            // Gap编码：间隔值或0
    0,                             // Gap编码：使用上次间隔
    0                              // Gap编码：使用上次间隔
  ],
  "fileId": [102]                  // 对应文件ID数组
}
```

### 5.2 Files索引结构

#### 5.2.1 文件元数据

```json
{
  "num": 102,                              // 文件ID（唯一标识）
  "name": "/opt/arkime/raw/test.pcap",     // 文件完整路径
  "first": 1752150698995,                  // 文件首包时间戳
  "node": "localhost",                     // 捕获节点名称
  "filesize": 1048576,                     // 文件大小（字节）
  "locked": 0,                             // 锁定状态：1=处理中，0=完成
  "packetPosEncoding": "gap0",             // 位置编码方式
  "compression": "zstd",                   // 压缩算法
  "compressionBlockSize": 64000            // 压缩块大小
}
```

### 5.3 Fields索引结构

#### 5.3.1 字段定义

```json
{
  "_index": "arkime_fields_v30",
  "_id": "http.method",                    // 字段标识符
  "_source": {
    "friendlyName": "HTTP Method",         // 显示名称
    "group": "http",                       // 字段分组
    "help": "HTTP request method",         // 帮助信息
    "dbField2": "http.method",             // 数据库字段名
    "type": "termfield",                   // 字段类型
    "category": "protocol"                 // 字段类别
  }
}
```

---

## 6. 压缩与编码机制

### 6.1 ZSTD压缩机制

#### 6.1.1 压缩算法概述

Arkime支持多种压缩算法，其中ZSTD是默认推荐的压缩方式：

```c
typedef enum {
    ARKIME_COMPRESSION_NONE,    // 无压缩
    ARKIME_COMPRESSION_GZIP,    // GZIP压缩
    ARKIME_COMPRESSION_ZSTD     // ZSTD压缩（默认）
} CompressionMode;
```

#### 6.1.2 压缩配置

```ini
# 压缩相关配置
simpleCompression=zstd              # 压缩算法：none/gzip/zstd
simpleZstdLevel=3                   # ZSTD压缩级别 (0-22)
simpleCompressionBlockSize=64000    # 压缩块大小 (8191-1048575)
```

**压缩级别说明：**
- **0**: 默认级别，平衡压缩率和速度
- **1-3**: 快速压缩，适合实时处理
- **4-9**: 标准压缩，推荐用于生产环境
- **10-22**: 高压缩率，适合存储优化

#### 6.1.3 压缩块设计

```c
/*
 * 压缩设计说明：
 * - 压缩文件由多个压缩块组成
 * - 只能从块的开始位置读取压缩文件
 * - 块大小可变，最大未压缩数据由simpleCompressionBlockSize控制
 * - 每个数据包的文件位置由两部分组成：
 *   X: 压缩块在文件中的起始位置（左移uncompressedBits位）
 *   Y: 数据包在未压缩块内的位置
 */

// 位置编码公式
packet->writerFilePos = (blockStart << uncompressedBits) + posInBlock;

// 解压时的位置解析
insideOffset = pos & (uncompressedBitsSize - 1);
realPos = Math.floor(pos / uncompressedBitsSize);
```

#### 6.1.4 编码示例
```
实际位置: [-1,24]

压缩后 ：[-1,2162688]

```

#### 6.1.4 编解码过程

```c
/*编码*/
// 默认配置下的计算
simpleCompressionBlockSize = 64000;  // 默认值
uncompressedBits = ceil(log2(64000)) = ceil(15.97) = 16;

// packetPos编码公式
packetPos = (blockStart << 16) + posInBlock;

// 对于值2162688
blockStart = 2162688 >> 16 = 33;
posInBlock = 2162688 & 65535 = 0;
```

```javascript
/*解码*/
// 您的packetPos值
let packetPos = 2162688;

// 默认配置
let simpleCompressionBlockSize = 64000;
let uncompressedBits = 16;  // ceil(log2(64000))
let uncompressedBitsSize = 65536;  // 2^16

// 解码
let blockStart = Math.floor(packetPos / uncompressedBitsSize);
let posInBlock = packetPos & (uncompressedBitsSize - 1);

console.log(`blockStart = ${blockStart}`);     // 33
console.log(`posInBlock = ${posInBlock}`);     // 0
console.log(`验证: ${blockStart * uncompressedBitsSize + posInBlock}`); // 2162688
```

实际含义
压缩块位置: 第33个压缩块在文件中的起始位置
块内偏移: 0（数据包位于压缩块的开始位置）
文件中的实际位置: 需要读取第33个压缩块并解压缩

### 6.2 Gap编码机制

#### 6.2.1 Gap编码原理

Gap编码是Arkime用于优化packetPos存储的压缩算法：

```javascript
// Gap编码逻辑
if (config.gapPacketPos) {
    /* 简单的间隔编码：
     * - 与前一个间隔相同的用0表示
     * - 负数和负数后的数字不编码
     * - 可减少50%以上的存储空间
     */
    let last = 0;
    let lastgap = 0;
    for (let i = 0; i < packetPosArray.length; i++) {
        let fpos = packetPosArray[i];
        if (fpos < 0) {
            // 负数：新文件开始标记
            last = 0;
            lastgap = 0;
            output(fpos);  // 直接输出负数
        } else {
            if (fpos - last == lastgap) {
                output(0);  // 相同间隔用0表示
            } else {
                lastgap = fpos - last;
                output(lastgap);  // 输出间隔差值
            }
            last = fpos;
        }
    }
}
```

#### 6.2.2 Gap编码示例

**原始位置数据：**

```
实际位置: [24, 90, 156, 222, 288, 354]
间隔:     [-, 66, 66, 66, 66, 66]
```

**Gap编码后：**
```
存储值: [24, 66, 0, 0, 0, 0]
节省: 66% 的存储空间
```

#### 6.2.3 Gap解码过程

```javascript
// Gap解码逻辑
let last = 0;
let lastgap = 0;
for (let i = 0; i < fields.packetPos.length; i++) {
    if (fields.packetPos[i] < 0) {
        last = 0;  // 新文件，重置
    } else {
        if (fields.packetPos[i] === 0) {
            fields.packetPos[i] = last + lastgap;  // 使用上次间隔
        } else {
            lastgap = fields.packetPos[i];
            fields.packetPos[i] += last;  // 累加计算实际位置
        }
        last = fields.packetPos[i];
    }
}
```

#### 6.2.4 配置选项

```ini
# 启用Gap编码（默认）
gapPacketPos=true

# 禁用Gap编码
gapPacketPos=false
```

**禁用Gap编码的场景：**
- 调试packetPos问题时
- 需要直接访问原始位置值
- 与第三方工具集成时
- 本地索引模式（localPcapIndex=true时自动禁用）

### 6.3 压缩性能对比

| 压缩算法 | 压缩率 | 压缩速度 | 解压速度 | 内存使用 |
| -------- | ------ | -------- | -------- | -------- |
| none     | 1.0x   | 最快     | 最快     | 最低     |
| gzip     | 3-5x   | 中等     | 快       | 中等     |
| zstd     | 3-6x   | 快       | 最快     | 中等     |

### 6.4 推荐配置组合

#### 6.4.1 生产环境（高压缩率）

```ini
simpleCompression=zstd
simpleZstdLevel=6
simpleCompressionBlockSize=64000
gapPacketPos=true
```

#### 6.4.2 调试环境（无压缩）

```ini
simpleCompression=none
gapPacketPos=false
```

#### 6.4.3 高性能环境（快速压缩）

```ini
simpleCompression=zstd
simpleZstdLevel=1
simpleCompressionBlockSize=32000
gapPacketPos=true
```

---

## 7. ES数据集成

### 7.1 ES索引结构

#### 7.1.1 Sessions索引

**索引命名规则：** `arkime_sessions3-YYYYMMDD`

**核心字段映射：**
```json
{
  "mappings": {
    "properties": {
      "@timestamp": {
        "type": "date",
        "format": "epoch_millis"
      },
      "firstPacket": {
        "type": "date",
        "format": "epoch_millis"
      },
      "lastPacket": {
        "type": "date",
        "format": "epoch_millis"
      },
      "source": {
        "properties": {
          "ip": {"type": "ip"},
          "port": {"type": "integer"},
          "bytes": {"type": "long"},
          "packets": {"type": "long"}
        }
      },
      "destination": {
        "properties": {
          "ip": {"type": "ip"},
          "port": {"type": "integer"},
          "bytes": {"type": "long"},
          "packets": {"type": "long"}
        }
      },
      "packetPos": {"type": "long"},
      "protocols": {"type": "keyword"}
    }
  }
}
```

#### 7.1.2 Files索引

**索引名称：** `arkime_files_v30`

```json
{
  "mappings": {
    "properties": {
      "num": {"type": "integer"},
      "name": {"type": "keyword"},
      "first": {"type": "date", "format": "epoch_millis"},
      "node": {"type": "keyword"},
      "filesize": {"type": "long"},
      "locked": {"type": "integer"},
      "packetPosEncoding": {"type": "keyword"},
      "compression": {"type": "keyword"}
    }
  }
}
```

#### 7.1.3 Fields索引

**索引名称：** `arkime_fields_v30`

```json
{
  "mappings": {
    "properties": {
      "friendlyName": {"type": "keyword"},
      "group": {"type": "keyword"},
      "help": {"type": "text"},
      "dbField2": {"type": "keyword"},
      "type": {"type": "keyword"},
      "category": {"type": "keyword"}
    }
  }
}
```

### 7.2 数据发送流程

#### 7.2.1 批量发送机制

```c
void arkime_db_send_bulk(char *json, int len) {
    // 1. 检查批量大小
    if (dbInfo[thread].json->len + len > config.dbBulkSize) {
        arkime_db_flush_gfunc(thread);
    }

    // 2. 添加到批量缓冲区
    g_string_append_len(dbInfo[thread].json, json, len);
    dbInfo[thread].count++;

    // 3. 检查是否需要立即发送
    if (dbInfo[thread].count >= config.dbBulkSize) {
        arkime_db_flush_gfunc(thread);
    }
}
```

#### 7.2.2 HTTP发送

```c
void arkime_db_flush_gfunc(int thread) {
    // 构建HTTP请求
    char *request = g_strdup_printf(
        "POST /_bulk HTTP/1.1\r\n"
        "Host: %s\r\n"
        "Content-Type: application/x-ndjson\r\n"
        "Content-Length: %d\r\n"
        "\r\n"
        "%s",
        esHost, dbInfo[thread].json->len, dbInfo[thread].json->str
    );

    // 发送到ES
    arkime_http_send(esServer, "POST", "/_bulk", request, TRUE,
                     arkime_db_response_cb, NULL);
}
```

### 7.3 字段注册机制

#### 7.3.1 自动字段注册

```c
void arkime_field_define_json(char *expression, int expression_len,
                             char *friendlyName, char *dbField,
                             char *kind, char *help, ...) {
    // 1. 检查字段是否已存在
    int pos = arkime_field_by_exp(expression);
    if (pos != -1) return pos;

    // 2. 创建字段定义
    ArkimeFieldInfo_t *info = ARKIME_TYPE_ALLOC0(ArkimeFieldInfo_t);
    info->expression = g_strdup(expression);
    info->friendlyName = g_strdup(friendlyName);
    info->dbField = g_strdup(dbField);
    info->kind = g_strdup(kind);
    info->help = g_strdup(help);

    // 3. 发送到ES
    arkime_db_create_field(info);

    return pos;
}
```

#### 7.3.2 字段类型

| 类型        | 说明         | ES映射  |
| ----------- | ------------ | ------- |
| termfield   | 精确匹配字段 | keyword |
| lotermfield | 小写精确匹配 | keyword |
| textfield   | 全文搜索字段 | text    |
| ip          | IP地址字段   | ip      |
| integer     | 整数字段     | integer |
| float       | 浮点数字段   | float   |

---

## 8. SDX DPI集成方案

### 8.1 集成架构

#### 8.1.1 整体设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SDX DPI       │    │   Arkime兼容层  │    │   Elasticsearch │
│   现有系统      │───▶│   适配模块      │───▶│   数据存储      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PCAP输出      │    │   JSON转换      │    │   Arkime Viewer │
│   模块          │    │   模块          │    │   Web界面       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### 8.1.2 核心模块

**Arkime兼容层模块：**
- 会话管理适配器
- 字段映射转换器
- JSON格式化器
- ES输出模块

**数据流转适配：**
- DPI输出 → Arkime Session格式
- 协议字段 → Arkime字段映射
- 文件管理 → Arkime Files索引

### 8.2 数据结构映射

#### 8.2.1 会话结构映射

```c
// SDX DPI会话结构
typedef struct {
    uint32_t src_ip, dst_ip;
    uint16_t src_port, dst_port;
    uint8_t protocol;
    uint64_t start_time, end_time;
    uint64_t bytes_up, bytes_down;
    uint32_t packets_up, packets_down;
    // 协议特定字段
    void *protocol_data;
} sdx_flow_t;

// 转换为Arkime格式
void convert_to_arkime_session(sdx_flow_t *flow, ArkimeSession_t *session) {
    // 基础字段映射
    session->firstPacket.tv_sec = flow->start_time / 1000;
    session->firstPacket.tv_usec = (flow->start_time % 1000) * 1000;
    session->lastPacket.tv_sec = flow->end_time / 1000;
    session->lastPacket.tv_usec = (flow->end_time % 1000) * 1000;

    // 网络信息
    session->addr1 = flow->src_ip;
    session->addr2 = flow->dst_ip;
    session->port1 = flow->src_port;
    session->port2 = flow->dst_port;
    session->ipProtocol = flow->protocol;

    // 统计信息
    session->bytes[0] = flow->bytes_up;
    session->bytes[1] = flow->bytes_down;
    session->packets[0] = flow->packets_up;
    session->packets[1] = flow->packets_down;
}
```

#### 8.2.2 协议字段映射

```c
// HTTP协议映射
void map_http_fields(sdx_http_t *sdx_http, ArkimeSession_t *session) {
    if (sdx_http->method) {
        arkime_field_string_add(httpMethodField, session,
                               sdx_http->method, -1, TRUE);
    }

    if (sdx_http->uri) {
        arkime_field_string_add(httpUriField, session,
                               sdx_http->uri, -1, TRUE);
    }

    if (sdx_http->host) {
        arkime_field_string_add(httpHostField, session,
                               sdx_http->host, -1, TRUE);
    }

    if (sdx_http->user_agent) {
        arkime_field_string_add(httpUserAgentField, session,
                               sdx_http->user_agent, -1, TRUE);
    }
}

// DNS协议映射
void map_dns_fields(sdx_dns_t *sdx_dns, ArkimeSession_t *session) {
    if (sdx_dns->query_name) {
        arkime_field_string_add(dnsQueryField, session,
                               sdx_dns->query_name, -1, TRUE);
    }

    if (sdx_dns->query_type) {
        arkime_field_string_add(dnsQueryTypeField, session,
                               sdx_dns->query_type, -1, TRUE);
    }

    for (int i = 0; i < sdx_dns->answer_count; i++) {
        arkime_field_string_add(dnsAnswerField, session,
                               sdx_dns->answers[i], -1, TRUE);
    }
}
```

### 8.3 输出模块实现

#### 8.3.1 ES输出模块

```c
typedef struct {
    char *es_url;
    char *index_prefix;
    char *node_name;
    int bulk_size;
    int bulk_timeout;
    GString *bulk_buffer;
    int doc_count;
    time_t last_flush;
} arkime_es_output_t;

void arkime_es_output_init(const char *config_file) {
    arkime_es_output_t *output = &es_output;

    // 读取配置
    output->es_url = config_get_string("ARKIME_ES_URL", "http://localhost:9200");
    output->index_prefix = config_get_string("ARKIME_ES_PREFIX", "arkime_sessions3");
    output->node_name = config_get_string("ARKIME_NODE_NAME", "sdx-dpi-node");
    output->bulk_size = config_get_int("ARKIME_BULK_SIZE", 1000);
    output->bulk_timeout = config_get_int("ARKIME_BULK_TIMEOUT", 5);

    // 初始化缓冲区
    output->bulk_buffer = g_string_new("");
    output->doc_count = 0;
    output->last_flush = time(NULL);
}

void arkime_es_output_session(ArkimeSession_t *session) {
    // 构建索引名称
    char index_name[256];
    struct tm *tm_info = localtime(&session->firstPacket.tv_sec);
    snprintf(index_name, sizeof(index_name), "%s-%04d%02d%02d",
             es_output.index_prefix,
             tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday);

    // 构建bulk操作头
    g_string_append_printf(es_output.bulk_buffer,
        "{\"index\":{\"_index\":\"%s\"}}\n", index_name);

    // 序列化session为JSON
    char *json = arkime_session_to_json(session);
    g_string_append_printf(es_output.bulk_buffer, "%s\n", json);
    g_free(json);

    es_output.doc_count++;

    // 检查是否需要flush
    if (es_output.doc_count >= es_output.bulk_size ||
        time(NULL) - es_output.last_flush >= es_output.bulk_timeout) {
        arkime_es_flush();
    }
}
```

#### 8.3.2 PCAP输出模块

```c
typedef struct {
    char *pcap_dir;
    uint64_t max_file_size;
    uint32_t max_file_time;
    FILE *current_file;
    char current_filename[PATH_MAX];
    uint64_t current_file_size;
    time_t current_file_start;
    uint32_t file_counter;
} arkime_pcap_output_t;

void arkime_pcap_output_packet(const uint8_t *packet_data, uint32_t packet_len,
                              const struct timeval *ts) {
    arkime_pcap_output_t *output = &pcap_output;

    // 检查是否需要轮转文件
    if (!output->current_file ||
        output->current_file_size + packet_len + 16 > output->max_file_size ||
        ts->tv_sec - output->current_file_start > output->max_file_time) {
        arkime_pcap_rotate_file();
    }

    // 写入PCAP包头
    struct pcap_pkthdr hdr;
    hdr.ts = *ts;
    hdr.caplen = packet_len;
    hdr.len = packet_len;

    fwrite(&hdr, sizeof(hdr), 1, output->current_file);
    fwrite(packet_data, packet_len, 1, output->current_file);

    output->current_file_size += packet_len + 16;

    // 定期flush
    if (output->current_file_size % (1024 * 1024) == 0) {
        fflush(output->current_file);
    }
}
```

### 8.4 配置管理

#### 8.4.1 配置文件结构

```ini
# Arkime兼容配置
[arkime]
ARKIME_ENABLE = 1                        # 启用Arkime兼容模式
ARKIME_ES_URL = http://localhost:9200    # Elasticsearch服务器地址
ARKIME_ES_PREFIX = arkime_sessions3      # ES索引前缀
ARKIME_NODE_NAME = sdx-dpi-node-01       # 节点名称
ARKIME_PCAP_DIR = /tmp/pcaps             # PCAP文件存储目录
ARKIME_MAX_FILE_SIZE = 1073741824        # 最大文件大小(1GB)
ARKIME_MAX_FILE_TIME = 3600              # 最大文件时间(1小时)
ARKIME_BULK_SIZE = 1000                  # ES批量大小
ARKIME_BULK_TIMEOUT = 5                  # ES批量超时(秒)
ARKIME_COMPRESSION = zstd                # 压缩方式(none/gzip/zstd)

# 字段管理配置
ARKIME_AUTO_REGISTER_FIELDS = 1          # 自动注册新发现的字段
ARKIME_FIELD_CACHE_SIZE = 10000          # 字段缓存大小
ARKIME_FIELD_SYNC_INTERVAL = 300         # 字段同步间隔(秒)
ARKIME_FIELD_BATCH_SIZE = 100            # 批量注册字段数量

# 性能优化配置
ARKIME_MEMORY_POOL_SIZE = 10000          # 内存池大小
ARKIME_ASYNC_QUEUE_SIZE = 5000           # 异步队列大小
ARKIME_MAX_PACKET_POS = 1000             # 最大数据包位置记录数
ARKIME_ES_MAX_CONNECTIONS = 10           # ES最大连接数
ARKIME_ES_RETRY_COUNT = 3                # ES重试次数
```

#### 8.4.2 字段映射配置

```json
{
  "field_mappings": {
    "http": {
      "method": "http.method",
      "uri": "http.uri",
      "host": "http.host",
      "user_agent": "http.useragent",
      "status_code": "http.statuscode",
      "content_type": "http.contenttype"
    },
    "dns": {
      "query_name": "dns.query",
      "query_type": "dns.queryType",
      "answer": "dns.answer",
      "response_code": "dns.responseCode"
    },
    "tls": {
      "version": "tls.version",
      "cipher": "tls.cipher",
      "sni": "tls.sni",
      "ja3": "tls.ja3",
      "ja3s": "tls.ja3s"
    }
  }
}
```

---

## 9. 性能优化

### 9.1 系统级优化

#### 9.1.1 内存优化

```bash
# 增加系统内存限制
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'vm.swappiness=1' >> /etc/sysctl.conf
sysctl -p

# 优化网络缓冲区
echo 'net.core.rmem_max=134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max=134217728' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog=5000' >> /etc/sysctl.conf
```

#### 9.1.2 磁盘I/O优化

```bash
# 使用deadline调度器
echo deadline > /sys/block/sda/queue/scheduler

# 优化文件系统
mount -o noatime,nodiratime /dev/sda1 /opt/arkime/raw

# 设置合适的块大小
tune2fs -o journal_data_writeback /dev/sda1
```

### 9.2 Arkime配置优化

#### 9.2.1 数据包处理优化

```ini
# 增加处理线程数
packetThreads=8

# 优化队列大小
maxPacketsInQueue=500000

# 调整写入缓冲区
pcapWriteSize=1048576

# 优化批处理
dbBulkSize=1000
dbFlushTimeout=5
```

#### 9.2.2 内存使用优化

```ini
# 减少内存使用
maxStreams=1000000
maxPackets=10000

# 优化会话超时
tcpTimeout=600
udpTimeout=30
icmpTimeout=10

# 启用内存映射
mmap=true
```

### 9.3 Elasticsearch优化

#### 9.3.1 索引设置

```json
{
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 0,
    "refresh_interval": "30s",
    "index.translog.flush_threshold_size": "1gb",
    "index.merge.policy.max_merge_at_once": 5,
    "index.merge.policy.segments_per_tier": 5
  }
}
```

#### 9.3.2 JVM设置

```bash
# /etc/elasticsearch/jvm.options
-Xms8g
-Xmx8g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+DisableExplicitGC
```

### 9.4 监控指标

#### 9.4.1 关键性能指标

```bash
# 数据包处理速率
grep "packets/sec" /opt/arkime/logs/capture.log

# 内存使用情况
ps aux | grep arkime-capture

# 磁盘I/O
iostat -x 1

# 网络统计
cat /proc/net/dev

# ES性能
curl -X GET "localhost:9200/_cluster/stats?pretty"
```

#### 9.4.2 性能基准

| 指标           | 目标值    | 监控方法    |
| -------------- | --------- | ----------- |
| 数据包处理速率 | >100K pps | capture日志 |
| 内存使用率     | <80%      | top/htop    |
| 磁盘I/O等待    | <10%      | iostat      |
| ES写入延迟     | <100ms    | ES监控      |
| 数据包丢失率   | <0.1%     | 网络统计    |

---

## 10. 故障排除

### 10.1 常见问题

#### 10.1.1 数据包丢失

**症状：** 统计显示数据包丢失

**排查步骤：**
```bash
# 检查网络接口统计
cat /proc/net/dev

# 检查Arkime统计
curl -X GET "localhost:8005/api/stats"

# 检查系统资源
top
iostat -x 1
```

**解决方案：**
- 增加packetThreads数量
- 优化maxPacketsInQueue设置
- 检查磁盘I/O性能
- 升级网络硬件

#### 10.1.2 ES写入失败

**症状：** ES返回错误或超时

**排查步骤：**
```bash
# 检查ES状态
curl -X GET "localhost:9200/_cluster/health?pretty"

# 检查ES日志
tail -f /var/log/elasticsearch/elasticsearch.log

# 检查Arkime ES连接
curl -X GET "localhost:8005/api/eshealth"
```

**解决方案：**
- 调整dbBulkSize和dbFlushTimeout
- 增加ES内存分配
- 优化ES索引设置
- 检查网络连接

#### 10.1.3 PCAP文件损坏

**症状：** 无法读取PCAP文件或数据不完整

**排查步骤：**
```bash
# 检查文件完整性
file /opt/arkime/raw/*.pcap

# 验证PCAP格式
tcpdump -r /opt/arkime/raw/test.pcap -c 1

# 检查文件权限
ls -la /opt/arkime/raw/
```

**解决方案：**
- 检查磁盘空间
- 验证文件系统完整性
- 调整文件轮转设置
- 使用文件校验和

### 10.2 调试工具

#### 10.2.1 日志分析

```bash
# Arkime capture日志
tail -f /opt/arkime/logs/capture.log

# 详细调试日志
/opt/arkime/bin/capture -c /opt/arkime/etc/config.ini --debug

# ES查询日志
curl -X GET "localhost:9200/_cat/indices?v"
```

#### 10.2.2 性能分析

```bash
# 系统性能
perf top -p $(pgrep arkime-capture)

# 内存分析
valgrind --tool=memcheck /opt/arkime/bin/capture

# 网络分析
tcpdump -i eth0 -w debug.pcap
```

### 10.3 恢复程序

#### 10.3.1 数据恢复

```bash
# 重新处理PCAP文件
/opt/arkime/bin/capture --copy -r /path/to/pcap/files/ -c /opt/arkime/etc/config.ini

# 重建ES索引
curl -X DELETE "localhost:9200/arkime_sessions3-*"
/opt/arkime/db/db.pl http://localhost:9200 init

# 修复损坏的会话
/opt/arkime/db/db.pl http://localhost:9200 repair
```

#### 10.3.2 配置恢复

```bash
# 备份配置
cp /opt/arkime/etc/config.ini /opt/arkime/etc/config.ini.backup

# 恢复默认配置
/opt/arkime/bin/Configure

# 验证配置
/opt/arkime/bin/capture -c /opt/arkime/etc/config.ini --test
```

---

## 11. 开发指南

### 11.1 自定义协议解析器

#### 11.1.1 解析器框架

```c
#include "arkime.h"

// 字段定义
static int myProtocolHostField;
static int myProtocolPortField;

// 解析器函数
LOCAL int my_protocol_parser(ArkimeSession_t *session, void *uw,
                            const uint8_t *data, int len, int which) {
    // 解析协议数据
    if (len < 8) return 0;  // 最小包长度检查

    // 提取字段
    uint16_t port = ntohs(*(uint16_t*)(data + 2));
    char host[256];
    memcpy(host, data + 4, len - 4);
    host[len - 4] = '\0';

    // 添加字段到会话
    arkime_field_int_add(myProtocolPortField, session, port);
    arkime_field_string_add(myProtocolHostField, session, host, -1, TRUE);

    return 0;
}

// 初始化函数
void arkime_plugin_init() {
    // 定义字段
    myProtocolHostField = arkime_field_define("myprotocol", "host",
        "myprotocol.host", "Host", "termfield",
        "Host field for my protocol",
        ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
        NULL);

    myProtocolPortField = arkime_field_define("myprotocol", "port",
        "myprotocol.port", "Port", "integer",
        "Port field for my protocol",
        ARKIME_FIELD_TYPE_INT, ARKIME_FIELD_FLAG_CNT,
        NULL);

    // 注册解析器
    arkime_parsers_register(session, ARKIME_PARSERS_PORT, "tcp", 8080, my_protocol_parser);
}
```

#### 11.1.2 字段类型和标志

```c
// 字段类型
typedef enum {
    ARKIME_FIELD_TYPE_INT,          // 整数
    ARKIME_FIELD_TYPE_STR,          // 字符串
    ARKIME_FIELD_TYPE_STR_ARRAY,    // 字符串数组
    ARKIME_FIELD_TYPE_STR_HASH,     // 字符串哈希
    ARKIME_FIELD_TYPE_IP,           // IP地址
    ARKIME_FIELD_TYPE_CERTSINFO     // 证书信息
} ArkimeFieldType;

// 字段标志
#define ARKIME_FIELD_FLAG_CNT       0x001  // 计数字段
#define ARKIME_FIELD_FLAG_NODB      0x002  // 不存储到数据库
#define ARKIME_FIELD_FLAG_FAKE      0x004  // 虚拟字段
#define ARKIME_FIELD_FLAG_SESSIONID 0x008  // 会话ID字段
#define ARKIME_FIELD_FLAG_LINKED    0x010  // 链接字段
```

### 11.2 插件开发

#### 11.2.1 插件结构

```c
// 插件信息
void arkime_plugin_init() {
    arkime_plugins_set_cb("my-plugin",
                         NULL,                    // 初始化回调
                         NULL,                    // 退出回调
                         my_plugin_pre_save,      // 保存前回调
                         NULL,                    // 保存后回调
                         my_plugin_outstanding,   // 未完成检查
                         NULL,                    // HTTP服务器回调
                         NULL);                   // 定时器回调
}

// 保存前回调
void my_plugin_pre_save(ArkimeSession_t *session, int final) {
    // 在会话保存前执行自定义逻辑
    if (session->packets[0] + session->packets[1] > 1000) {
        arkime_session_add_tag(session, "high-volume");
    }
}

// 未完成检查
int my_plugin_outstanding() {
    // 返回未完成的任务数量
    return pending_tasks;
}
```

#### 11.2.2 HTTP API扩展

```c
// 注册HTTP处理器
void arkime_plugin_init() {
    arkime_http_add_handler("/api/myplugin", my_plugin_http_handler);
}

// HTTP处理函数
void my_plugin_http_handler(char *url, ArkimeRequest_t *request) {
    // 解析请求参数
    char *param = arkime_http_get_param(request, "param");

    // 构建响应
    BSB bsb;
    BSB_INIT(bsb, request->dataOut, sizeof(request->dataOut));
    BSB_EXPORT_sprintf(&bsb, "{\"result\":\"success\",\"param\":\"%s\"}", param);

    // 发送响应
    arkime_http_send(request, BSB_WORK_PTR(bsb), BSB_LENGTH(bsb),
                     "application/json", 200);
}
```

### 11.3 测试和验证

#### 11.3.1 单元测试

```c
// 测试框架
#include "tests.h"

void test_my_protocol_parser() {
    // 创建测试会话
    ArkimeSession_t *session = arkime_session_new(ARKIME_SESSION_TCP);

    // 准备测试数据
    uint8_t test_data[] = {0x01, 0x02, 0x1f, 0x90, 't', 'e', 's', 't'};

    // 调用解析器
    int result = my_protocol_parser(session, NULL, test_data, sizeof(test_data), 0);

    // 验证结果
    assert(result == 0);
    assert(arkime_field_int_get(myProtocolPortField, session) == 8080);

    // 清理
    arkime_session_free(session);
}

// 运行测试
int main() {
    arkime_tests_init();
    test_my_protocol_parser();
    arkime_tests_exit();
    return 0;
}
```

#### 11.3.2 集成测试

```bash
# 编译插件
gcc -shared -fPIC -o my_plugin.so my_plugin.c -I/opt/arkime/include

# 配置插件
echo "plugins=my_plugin.so" >> /opt/arkime/etc/config.ini

# 测试运行
/opt/arkime/bin/capture -c /opt/arkime/etc/config.ini -r test.pcap --dry-run

# 验证输出
curl -X GET "localhost:9200/arkime_sessions3-*/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "exists": {
      "field": "myprotocol.host"
    }
  }
}'
```

---

## 12. 字段注册与显示机制详解

本章详细介绍 Arkime 中字段注册、数据发送、前端显示的完整流程，包括 SPI View 和 Sessions 详情页面的显示机制。

### 12.1 字段注册机制

#### 12.1.1 字段注册原理

Arkime 的字段注册不是通过重新构建 viewer 模块完成的，而是通过向 Elasticsearch 的字段索引发送字段定义来实现动态注册。

**核心原理：**
1. **字段定义存储在 ES 中**：`arkime_fields_v30` 索引（通过别名 `arkime_fields` 访问）
2. **Viewer 启动时加载字段**：从 ES 读取字段定义并构建内存中的字段映射
3. **前端动态创建 UI**：根据字段定义动态生成搜索界面和显示组件

#### 12.1.2 字段定义格式

```json
{
  "friendlyName": "字段友好名称",
  "group": "字段分组（用于UI分类）",
  "help": "字段帮助信息",
  "dbField": "数据库字段名",
  "dbField2": "备用字段名",
  "type": "字段类型（termfield/integer/ip等）",
  "category": ["分类标签"],
  "transform": "数据转换规则（可选）"
}
```

**字段类型说明：**
- `termfield`: 可搜索的字符串字段
- `integer`: 整数字段
- `ip`: IP 地址字段
- `lotermfield`: 大量数据的字符串字段

#### 12.1.3 字段注册实现

**Python 实现示例：**
```python
def register_fields():
    """注册字段定义到 Elasticsearch"""

    # 字段定义
    fields = [
        {
            "field_id": "sdx.linename1",
            "definition": {
                "friendlyName": "SDX Line Name 1",
                "group": "sdx",
                "help": "SDX protocol line name 1",
                "dbField": "sdx.linename1",
                "dbField2": "sdx.linename1",
                "type": "termfield",
                "category": ["sdx"]
            }
        },
        {
            "field_id": "http.method",
            "definition": {
                "friendlyName": "HTTP Method",
                "group": "http",
                "help": "HTTP request method",
                "dbField": "http.method",
                "dbField2": "http.method",
                "type": "termfield",
                "category": ["http"]
            }
        }
    ]

    # 构建 bulk 请求
    bulk_data = []
    for field in fields:
        index_op = {"index": {"_index": "arkime_fields", "_id": field["field_id"]}}
        bulk_data.append(json.dumps(index_op))
        bulk_data.append(json.dumps(field["definition"]))

    bulk_body = "\n".join(bulk_data) + "\n"

    # 发送到 ES
    response = requests.post(
        "http://localhost:9200/_bulk",
        data=bulk_body,
        headers={'Content-Type': 'application/x-ndjson'}
    )
```

### 12.2 索引和别名设置

#### 12.2.1 必需的索引结构

Arkime 使用版本化索引和别名系统：

```bash
# 实际索引名称
arkime_fields_v30    -> arkime_fields (别名)
arkime_files_v30     -> arkime_files (别名)
arkime_users_v30     -> arkime_users (别名)
arkime_sequence_v30  -> arkime_sequence (别名)

# 会话数据索引（按日期分割）
arkime_sessions3-YYMMDD
```

#### 12.2.2 索引创建脚本

```python
def create_index_with_alias(index_name, alias_name, settings=None, mapping=None):
    """创建索引并设置别名"""

    # 索引配置
    index_config = {
        "settings": settings or {
            "index.priority": 100,
            "number_of_shards": 1,
            "number_of_replicas": 0,
            "auto_expand_replicas": "0-3"
        }
    }

    if mapping:
        index_config["mappings"] = mapping

    # 创建索引
    requests.put(f"http://localhost:9200/{index_name}", json=index_config)

    # 创建别名
    alias_config = {
        "actions": [
            {
                "add": {
                    "index": index_name,
                    "alias": alias_name
                }
            }
        ]
    }

    requests.post("http://localhost:9200/_aliases", json=alias_config)
```

### 12.3 会话数据发送

#### 12.3.1 会话数据格式

```json
{
  "@timestamp": 1752199995430,
  "firstPacket": 1041342931300,
  "lastPacket": 1041342932300,
  "length": 1000,
  "ipProtocol": 6,
  "node": "localhost",
  "source": {
    "ip": "********",
    "port": 3267,
    "bytes": 207,
    "packets": 1
  },
  "destination": {
    "ip": "*************",
    "port": 80,
    "bytes": 0,
    "packets": 0
  },
  "protocols": ["tcp", "http", "sdx"],
  "http": {
    "method": ["HEAD"],
    "host": ["windowsupdate.microsoft.com"],
    "uri": ["/v4/iuident.cab"]
  },
  "sdx": {
    "linename1": "11111",
    "linename2": "22222"
  }
}
```

#### 12.3.2 数据发送实现

```python
def send_session_data():
    """发送会话数据到 Arkime"""

    # 目标索引（按日期）
    date_str = datetime.now().strftime("%y%m%d")
    index_name = f"arkime_sessions3-{date_str}"

    # 构建 bulk 请求
    bulk_body = json.dumps({"index": {"_index": index_name}}) + "\n"
    bulk_body += json.dumps(session_data) + "\n"

    # 发送到 ES
    response = requests.post(
        "http://localhost:9200/_bulk",
        data=bulk_body,
        headers={'Content-Type': 'application/x-ndjson'}
    )
```

### 12.4 SPI View 显示机制

#### 12.4.1 SPI View 工作原理

SPI View 通过以下步骤显示字段：

1. **字段加载**：从 `/api/fields` 接口获取字段定义
2. **分组显示**：根据字段的 `group` 属性进行分组
3. **动态查询**：根据用户选择的字段查询统计数据
4. **卡片渲染**：为每个字段组创建可折叠的卡片

#### 12.4.2 前端字段加载流程

```javascript
// viewer/viewerUtils.js
static async loadFields() {
  try {
    let data = await Db.loadFields();  // 从 ES 加载字段
    data = data.hits.hits;

    Config.loadFields(data);  // 处理字段定义

    return {
      fieldsMap: JSON.stringify(Config.getFieldsMap()),
      fieldsArr: Config.getFields().sort((a, b) => {
        return (a.exp > b.exp ? 1 : -1);
      })
    };
  } catch (err) {
    return { fieldsMap: {}, fieldsArr: [] };
  }
}
```

#### 12.4.3 字段分组处理

```javascript
// viewer/config.js
static loadFields(data) {
  internals.fields = [];
  internals.fieldsMap = {};
  internals.categories = {};

  data.forEach((field) => {
    const source = field._source;
    source.exp = field._id;

    // 添加到字段映射
    internals.fieldsMap[field._id] = source;
    internals.fields.push(source);

    // 按组分类
    if (!internals.categories[source.group]) {
      internals.categories[source.group] = [];
    }
    internals.categories[source.group].push(source);
  });
}
```

#### 12.4.4 SPI 数据查询

```javascript
// viewer/vueapp/src/components/spiview/Spiview.vue
getSingleSpiData: function (field, count) {
  const category = this.setupCategory(this.categoryObjects, field);
  const spiData = category.spi[field.dbField];

  if (!count) { count = 100; }

  Vue.set(spiData, 'active', true);
  Vue.set(spiData, 'loading', true);

  const query = this.constructQuery(field.dbField, count);

  // 发送查询请求
  this.get(query).then((response) => {
    Vue.set(spiData, 'loading', false);
    Vue.set(spiData, 'value', response.spi[field.dbField]);
    Vue.set(spiData, 'count', count);
  });
}
```

### 12.5 Sessions 详情页面显示机制

#### 12.5.1 详情页面模板系统

Sessions 详情页面使用 Jade/Pug 模板系统来显示协议信息：

1. **模板扫描**：Viewer 启动时扫描 `parsers` 目录中的 `.detail.jade` 文件
2. **模板编译**：将找到的模板编译到详情页面中
3. **动态渲染**：根据会话数据动态渲染协议卡片

#### 12.5.2 模板扫描机制

```javascript
// viewer/viewer.js
function createSessionDetail() {
  const found = {};
  let dirs = [];

  dirs = dirs.concat(Config.getArray('pluginsDir', `${version.config_prefix}/plugins`));
  dirs = dirs.concat(Config.getArray('parsersDir', `${version.config_prefix}/parsers`));

  dirs.forEach(function (dir) {
    try {
      const files = fs.readdirSync(dir);
      files.sort().reverse().forEach(function (file) {
        const sfile = file.replace(/\.(pug|jade)/, '');
        if (found[sfile]) {
          return;
        }
        if (file.match(/\.detail\.jade$/i)) {
          found[sfile] = fs.readFileSync(dir + '/' + file, 'utf8').replace(/^/mg, '  ') + '\n';
        } else if (file.match(/\.detail\.pug$/i)) {
          found[sfile] = '  include ' + dir + '/' + file + '\n';
        }
      });
    } catch (e) {}
  });

  // 构建完整的详情页面模板
  internals.sessionDetailNew = 'include views/mixins.pug\n' +
                               'div.session-detail(sessionid=session.id,hidePackets=hidePackets)\n' +
                               '  include views/mixins\n' +
                               '  include views/sessionOptions\n' +
                               '  b-card-group(columns)\n' +
                               '    b-card\n' +
                               '      include views/sessionDetail\n';

  Object.keys(found).sort().forEach(function (k) {
    internals.sessionDetailNew += found[k].replaceAll(/^/mg, '  ') + '\n';
  });
}
```

#### 12.5.3 协议详情模板格式

**SDX 协议模板示例 (`sdx.detail.jade`)：**
```jade
if (session.sdx)
  div.sessionDetailMeta.bold SDX
  dl.sessionDetailMeta(suffix="sdx")
    +arrayList(session.sdx, "linename1", "Line Name 1", "sdx.linename1")
    +arrayList(session.sdx, "linename2", "Line Name 2", "sdx.linename2")
```

**HTTP 协议模板示例 (`http.detail.jade`)：**
```jade
if (session.http)
  div.sessionDetailMeta.bold HTTP
  dl.sessionDetailMeta(suffix="http")
    if (session.http.method)
      dt
        +clickableLabel("http.method", "Method")
      dd
        each method in session.http.method
          +clickableValue("http.method", method, null, session)
    +arrayList(session.http, "host", "Hosts", "host.http")
    +arrayList(session.http, "uri", "URIs", "http.uri")
```

#### 12.5.4 模板渲染机制

```javascript
// viewer/apiSessions.js
static #localSessionDetailReturn(req, res, session, packets) {
  const hidePackets = (session.fileId === undefined || session.fileId.length === 0) ? 'true' : 'false';

  ViewerUtils.fixFields(session, () => {
    pug.render(internals.sessionDetailNew, {
      filename: 'sessionDetail',
      cache: internals.isProduction,
      compileDebug: !internals.isProduction,
      user: req.user,
      session,  // 会话数据传递给模板
      sep: session.source.ip?.includes(':') ? '.' : ':',
      Db,
      query: req.query,
      basedir: '/',
      hidePackets,
      reqFields: Config.headers('headers-http-request'),
      resFields: Config.headers('headers-http-response'),
      emailFields: Config.headers('headers-email')
    }, (err, data) => {
      if (err) {
        console.trace(`ERROR - ${req.method} /api/session/%s/%s/detail`,
                     ArkimeUtil.sanitizeStr(req.params.nodeName),
                     ArkimeUtil.sanitizeStr(req.params.id),
                     util.inspect(err, false, 50));
        return res.end('Error');
      }
      res.end(data);
    });
  });
}
```

### 12.6 完整实现流程

#### 12.6.1 字段注册到显示的完整流程

```mermaid
graph TD
    A[定义字段] --> B[发送到 ES arkime_fields 索引]
    B --> C[重启 Arkime Viewer]
    C --> D[Viewer 从 ES 加载字段定义]
    D --> E[构建内存中的字段映射]
    E --> F[前端通过 /api/fields 获取字段]
    F --> G[SPI View 根据 group 分组显示]

    H[创建协议模板] --> I[放置到 parsers 目录]
    I --> C
    C --> J[扫描并编译模板]
    J --> K[Sessions 详情页面显示协议卡片]

    L[发送会话数据] --> M[包含协议字段数据]
    M --> N[存储到 arkime_sessions3-YYMMDD]
    N --> O[前端查询并显示]
```

#### 12.6.2 实际操作步骤

**1. 设置索引和别名：**
```python
# 创建必需的索引
create_index_with_alias("arkime_fields_v30", "arkime_fields")
create_index_with_alias("arkime_files_v30", "arkime_files")
create_index_with_alias("arkime_users_v30", "arkime_users")
```

**2. 注册字段定义：**
```python
# 发送字段定义到 ES
register_fields([
    {"field_id": "sdx.linename1", "definition": {...}},
    {"field_id": "sdx.linename2", "definition": {...}}
])
```

**3. 创建协议模板：**
```bash
# 创建 SDX 详情模板
cat > capture/parsers/sdx.detail.jade << EOF
if (session.sdx)
  div.sessionDetailMeta.bold SDX
  dl.sessionDetailMeta(suffix="sdx")
    +arrayList(session.sdx, "linename1", "Line Name 1", "sdx.linename1")
    +arrayList(session.sdx, "linename2", "Line Name 2", "sdx.linename2")
EOF
```

**4. 重启服务：**
```bash
systemctl restart arkimeviewer
```

**5. 发送测试数据：**
```python
# 发送包含协议数据的会话
send_session_data({
    "protocols": ["tcp", "http", "sdx"],
    "sdx": {
        "linename1": "11111",
        "linename2": "22222"
    }
})
```

### 12.7 故障排除

#### 12.7.1 常见问题

**字段不显示在 SPI View：**
- 检查字段是否正确注册到 `arkime_fields` 索引
- 确认字段定义包含正确的 `group` 属性
- 重启 Arkime Viewer 服务

**协议卡片不显示在详情页面：**
- 确认 `.detail.jade` 文件存在于正确位置
- 检查模板语法是否正确
- 确认会话数据包含对应的协议对象

**搜索功能不工作：**
- 验证字段的 `dbField` 和 `dbField2` 设置正确
- 确认会话数据中的字段名与定义匹配

#### 12.7.2 调试方法

**检查字段注册：**
```bash
curl "localhost:9200/arkime_fields/_search?pretty"
```

**检查会话数据：**
```bash
curl "localhost:9200/arkime_sessions3-$(date +%y%m%d)/_search?q=protocols:sdx&pretty"
```

**检查前端字段加载：**
```bash
curl "localhost:8005/api/fields" | jq '.sdx'
```

#### 12.7.3 性能优化建议

**字段注册优化：**
- 批量注册字段以减少 ES 请求次数
- 合理设置字段类型以优化存储和查询性能
- 避免创建过多的细粒度字段

**模板渲染优化：**
- 使用条件判断避免渲染空数据
- 合理使用 Jade/Pug 的缓存机制
- 避免在模板中进行复杂的数据处理

## 13. 总结

本综合文档整合了Arkime的部署、配置、开发和集成的完整指南，特别详细介绍了字段注册与显示机制。主要涵盖：

### 13.1 核心特性
- **高性能数据包捕获**：支持多种数据源和协议
- **灵活的压缩机制**：ZSTD压缩和Gap编码优化存储
- **完整的ES集成**：自动索引管理和批量写入
- **可扩展架构**：支持自定义协议解析器和插件
- **动态字段系统**：通过ES索引实现字段的动态注册和显示

### 13.2 字段注册与显示要点
- **字段注册机制**：通过向ES发送字段定义实现，无需重新构建模块
- **索引别名系统**：使用版本化索引和别名确保向后兼容
- **SPI View显示**：根据字段的`group`属性自动分组显示
- **Sessions详情**：通过`.detail.jade`模板实现协议卡片显示
- **动态加载**：Viewer启动时从ES加载字段定义，支持热更新

### 13.3 关键配置
- **性能优化**：合理配置线程数、队列大小和缓冲区
- **存储优化**：启用压缩和Gap编码节省空间
- **监控告警**：建立完善的性能监控体系
- **字段管理**：批量注册字段，合理设置字段类型

### 13.4 集成方案
- **SDX DPI集成**：完整的适配层设计和实现
- **数据格式转换**：协议字段映射和JSON序列化
- **向后兼容**：保持与现有系统的兼容性
- **自定义协议支持**：完整的字段注册和显示流程

### 13.5 最佳实践
- **部署策略**：在线和离线部署方案
- **故障排除**：常见问题诊断和解决方法
- **开发指南**：自定义扩展开发框架
- **字段设计**：合理的字段命名和分组策略

### 13.6 实现流程总结

**完整的字段注册到显示流程：**
1. **定义字段结构**：包含friendlyName、group、type等属性
2. **创建ES索引**：设置正确的索引和别名
3. **注册字段定义**：通过bulk API发送到arkime_fields索引
4. **创建显示模板**：为协议创建.detail.jade模板文件
5. **重启Viewer服务**：加载新的字段定义和模板
6. **发送测试数据**：包含协议字段的会话数据
7. **验证显示效果**：在SPI View和Sessions详情页面确认显示

这个综合文档为Arkime的完整生命周期提供了详细的技术指导，特别是字段注册与显示机制的深入解析，可以作为部署、运维和开发的权威参考。通过本文档，开发者可以完全理解并实现自定义协议字段的注册和前端显示功能。
```

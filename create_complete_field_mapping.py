#!/usr/bin/env python3
"""
创建完整的Arkime字段映射表
"""

import json
from collections import defaultdict

def load_field_definitions():
    """加载字段定义"""
    with open('arkime_fields_clean.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def create_complete_mapping_table(field_definitions):
    """创建完整的字段映射表"""
    
    doc = """# Arkime完整字段映射对应表

本文档提供了`arkime_sessions3-*`索引中会话数据字段与`arkime_fields_v30`索引中字段定义的完整对应关系表。

## 映射规则说明

1. **直接映射**: 字段定义ID直接对应会话数据中的字段路径
2. **嵌套映射**: 会话数据中的嵌套结构对应字段定义中的点分隔ID
3. **协议映射**: 协议特定字段存储在对应的协议对象中

## 完整字段映射表

| 字段定义ID | 友好名称 | 字段组 | 数据类型 | 数据库字段(dbField2) | 会话数据路径 | 说明 |
|------------|----------|--------|----------|---------------------|--------------|------|
"""
    
    # 创建特殊映射规则
    special_mappings = {
        # 基础字段映射
        "session.length": "length",
        "session.segments": "segments", 
        "protocols": "protocols",
        "user": "user",
        "tags": "tags",
        "asset": "asset",
        
        # 源端字段映射
        "ip.src": "source.ip",
        "port.src": "source.port", 
        "bytes.src": "source.bytes",
        "packets.src": "source.packets",
        "mac.src": "source.mac",
        
        # 目标端字段映射
        "ip.dst": "destination.ip",
        "port.dst": "destination.port",
        "bytes.dst": "destination.bytes", 
        "packets.dst": "destination.packets",
        "mac.dst": "destination.mac",
        
        # 网络字段映射
        "packets": "network.packets",
        "bytes": "network.bytes",
        "vlan": "network.vlan.id",
        
        # 其他特殊映射
        "initRTT": "initRTT",
        "totalPackets": "totalPackets",
        "totalBytes": "totalBytes"
    }
    
    # 按组排序处理所有字段
    grouped_fields = defaultdict(list)
    for field_def in field_definitions:
        group = field_def['_source']['group']
        grouped_fields[group].append(field_def)
    
    # 按重要性排序组
    group_order = ['general', 'http', 'dns', 'tls', 'email', 'cert', 'smb', 'ssh', 'dhcp', 'socks', 'modbus', 'snmp', 'quic', 'radius', 'oracle', 'postgresql', 'mysql', 'krb5', 'ldap', 'irc', 'bgp', 'isis', 'suricata']
    
    for group in group_order:
        if group not in grouped_fields:
            continue
            
        fields = sorted(grouped_fields[group], key=lambda x: x['_id'])
        
        for field_def in fields:
            field_id = field_def['_id']
            source = field_def['_source']
            friendly_name = source['friendlyName']
            field_group = source['group']
            field_type = source['type']
            db_field = source['dbField2']
            
            # 确定会话数据路径
            if field_id in special_mappings:
                session_path = special_mappings[field_id]
                explanation = "直接映射"
            elif field_group == 'general':
                if field_id.startswith('mac.'):
                    session_path = f"{'source' if field_id.endswith('.src') else 'destination'}.mac"
                    explanation = "MAC地址映射"
                elif field_id.startswith('ip.'):
                    session_path = f"{'source' if field_id.endswith('.src') else 'destination'}.ip"
                    explanation = "IP地址映射"
                elif field_id.startswith('port.'):
                    session_path = f"{'source' if field_id.endswith('.src') else 'destination'}.port"
                    explanation = "端口映射"
                elif field_id.startswith('bytes.'):
                    session_path = f"{'source' if field_id.endswith('.src') else 'destination'}.bytes"
                    explanation = "字节数映射"
                elif field_id.startswith('packets.'):
                    session_path = f"{'source' if field_id.endswith('.src') else 'destination'}.packets"
                    explanation = "包数映射"
                else:
                    session_path = field_id
                    explanation = "通用字段"
            else:
                # 协议特定字段
                if db_field.startswith(f"{field_group}."):
                    session_path = db_field
                else:
                    session_path = f"{field_group}.{db_field.replace(f'{field_group}.', '')}"
                explanation = f"{field_group.upper()}协议字段"
            
            # 处理特殊情况
            if field_type == "integer" and "cnt" in field_id.lower():
                explanation += " (计数字段)"
            elif field_type == "ip":
                explanation += " (IP地址)"
            elif field_type == "date":
                explanation += " (时间字段)"
            
            doc += f"| `{field_id}` | {friendly_name} | {field_group} | {field_type} | `{db_field}` | `{session_path}` | {explanation} |\n"
    
    return doc

def add_summary_section(doc, field_definitions):
    """添加汇总部分"""
    
    doc += f"""

## 映射汇总统计

### 总体统计
- **字段定义总数**: {len(field_definitions)}
- **字段组数**: {len(set(f['_source']['group'] for f in field_definitions))}
- **支持的协议数**: {len([g for g in set(f['_source']['group'] for f in field_definitions) if g != 'general'])}

### 按组统计
"""
    
    group_stats = defaultdict(int)
    for field_def in field_definitions:
        group = field_def['_source']['group']
        group_stats[group] += 1
    
    for group, count in sorted(group_stats.items(), key=lambda x: x[1], reverse=True):
        doc += f"- **{group}**: {count} 个字段\n"
    
    doc += """
### 字段类型统计
"""
    
    type_stats = defaultdict(int)
    for field_def in field_definitions:
        field_type = field_def['_source']['type']
        type_stats[field_type] += 1
    
    for field_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
        doc += f"- **{field_type}**: {count} 个字段\n"
    
    doc += """
## 使用指南

### 1. 查找字段映射
根据字段定义ID在上表中查找对应的会话数据路径。

### 2. 构建查询
```json
{
  "query": {
    "term": {
      "会话数据路径": "查询值"
    }
  }
}
```

### 3. 字段聚合
```json
{
  "aggs": {
    "field_agg": {
      "terms": {
        "field": "会话数据路径"
      }
    }
  }
}
```

### 4. 常用映射示例
- HTTP方法: `http.method` → `http.method`
- 源IP: `ip.src` → `source.ip`
- DNS主机: `host.dns` → `dns.host`
- TLS证书: `cert.*` → `cert.*`

---

*本表格包含了所有224个字段定义的完整映射关系，可作为开发和查询的参考手册。*
"""
    
    return doc

def main():
    """主函数"""
    print("正在创建完整字段映射表...")
    
    # 加载字段定义
    field_definitions = load_field_definitions()
    print(f"加载了 {len(field_definitions)} 个字段定义")
    
    # 创建映射表
    doc_content = create_complete_mapping_table(field_definitions)
    
    # 添加汇总部分
    doc_content = add_summary_section(doc_content, field_definitions)
    
    # 保存文档
    with open('Arkime_Complete_Field_Mapping_Table.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("完整字段映射表已保存到: Arkime_Complete_Field_Mapping_Table.md")
    print(f"包含 {len(field_definitions)} 个字段的完整映射关系")

if __name__ == "__main__":
    main()

# Arkime 字段注册工具使用说明

## 概述

`arkime_field_registration_tool.py` 是一个完整的 Arkime 字段注册与数据发送工具，提供了从索引创建到字段注册、数据发送和验证的完整流程。

## 功能特性

- ✅ **索引管理**: 自动创建必需的 ES 索引和别名
- ✅ **字段注册**: 注册 SDX 和 HTTP 协议字段
- ✅ **用户管理**: 创建管理员用户账户
- ✅ **数据发送**: 发送测试会话数据
- ✅ **结果验证**: 验证字段注册和数据显示效果

## 使用方法

### 1. 完整流程（推荐）

```bash
# 执行所有操作
python3 arkime_field_registration_tool.py

# 或者明确指定
python3 arkime_field_registration_tool.py --all
```

### 2. 分步执行

```bash
# 1. 创建索引和别名
python3 arkime_field_registration_tool.py --setup-indices

# 2. 注册字段定义
python3 arkime_field_registration_tool.py --register-fields

# 3. 创建管理员用户
python3 arkime_field_registration_tool.py --create-user

# 4. 发送测试数据
python3 arkime_field_registration_tool.py --send-data

# 5. 验证结果
python3 arkime_field_registration_tool.py --verify
```

### 3. 自定义 ES 地址

```bash
python3 arkime_field_registration_tool.py --es-url http://your-es-server:9200
```

## 注册的字段

### SDX 协议字段
- `sdx.linename1`: SDX Line Name 1
- `sdx.linename2`: SDX Line Name 2

### HTTP 协议字段
- `http.method`: HTTP Method
- `http.host`: HTTP Host
- `http.uri`: HTTP URI

### 通用字段
- `protocols`: Protocols

## 完成后的操作

工具执行完成后，还需要手动完成以下步骤：

### 1. 创建 SDX 详情模板

```bash
cat > capture/parsers/sdx.detail.jade << 'EOF'
if (session.sdx)
  div.sessionDetailMeta.bold SDX
  dl.sessionDetailMeta(suffix="sdx")
    +arrayList(session.sdx, "linename1", "Line Name 1", "sdx.linename1")
    +arrayList(session.sdx, "linename2", "Line Name 2", "sdx.linename2")
EOF
```

### 2. 重启 Arkime Viewer 服务

```bash
sudo systemctl restart arkimeviewer
```

### 3. 访问前端验证

- **URL**: http://localhost:8005
- **用户名**: admin
- **密码**: admin

## 验证效果

### SPI View 页面
- 应该看到 `sdx` 和 `http` 组的字段卡片
- 可以展开查看字段统计信息

### Sessions 页面
- 搜索 `protocols:sdx` 可以找到测试会话
- 点击会话详情应该显示 SDX 协议卡片

### 搜索功能
- `protocols:sdx` - 查找 SDX 协议会话
- `sdx.linename1==11111` - 精确匹配
- `http.method==HEAD` - HTTP 方法搜索

## 故障排除

### 常见问题

1. **ES 连接失败**
   - 检查 ES 服务是否运行
   - 确认 ES 地址和端口正确

2. **字段不显示**
   - 重启 Arkime Viewer 服务
   - 清除浏览器缓存
   - 检查字段是否正确注册

3. **协议卡片不显示**
   - 确认 `.detail.jade` 文件已创建
   - 检查模板语法是否正确
   - 重启 Viewer 服务

### 调试命令

```bash
# 检查字段注册
curl "localhost:9200/arkime_fields/_search?pretty"

# 检查会话数据
curl "localhost:9200/arkime_sessions3-$(date +%y%m%d)/_search?q=protocols:sdx&pretty"

# 检查服务状态
systemctl status arkimeviewer
```

## 文件说明

- `arkime_field_registration_tool.py`: 主要工具脚本
- `Arkime_Comprehensive_Guide.md`: 完整的技术文档
- `capture/parsers/sdx.detail.jade`: SDX 协议详情模板（需手动创建）

## 技术原理

详细的技术原理和实现机制请参考 `Arkime_Comprehensive_Guide.md` 文档中的第12章"字段注册与显示机制详解"。

## 支持

如有问题，请参考：
1. 工具输出的错误信息
2. Arkime 日志文件 `/opt/arkime/logs/viewer.log`
3. ES 日志和状态
4. 综合技术文档中的故障排除章节

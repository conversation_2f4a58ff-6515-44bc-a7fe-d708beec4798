#!/usr/bin/env python3
"""
测试 Info 列显示的简单验证
"""

import json
import urllib.request
from datetime import datetime

def test_info_display():
    es_url = "http://localhost:9200"
    
    print("=== Info 列显示测试 ===")
    
    # 1. 检查会话数据结构
    date_str = datetime.now().strftime("%y%m%d")
    index_name = f"arkime_sessions3-{date_str}"
    
    try:
        req = urllib.request.Request(f"{es_url}/{index_name}/_search?size=1")
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            if data['hits']['hits']:
                session = data['hits']['hits'][0]['_source']
                print("✅ 会话数据结构:")
                
                # 检查 Info 列需要的字段
                info_fields = ['protocols', 'http', 'sdx']
                for field in info_fields:
                    if field in session:
                        print(f"  ✅ {field}: {session[field]}")
                    else:
                        print(f"  ❌ {field}: 缺失")
                
                print("\n✅ Info 列应该能显示:")
                print("  - Protocols: 协议列表")
                print("  - HTTP Info: HTTP 相关信息")
                print("  - SDX Info: SDX 相关信息")
                
                return True
            else:
                print("❌ 没有找到会话数据")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    success = test_info_display()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Info 列测试通过！")
        print("\n现在可以:")
        print("1. 访问 http://localhost:8005")
        print("2. 登录 (admin/admin)")
        print("3. 查看 Sessions 页面")
        print("4. Info 列应该显示协议、HTTP 和 SDX 信息")
    else:
        print("❌ 测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

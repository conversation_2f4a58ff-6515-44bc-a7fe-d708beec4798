#!/usr/bin/env python3
"""
生成Arkime字段注册文档
"""

import json
from collections import defaultdict
from typing import Dict, List, Any

def load_field_definitions() -> List[Dict[str, Any]]:
    """加载字段定义"""
    with open('arkime_fields_documentation.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def group_fields_by_category(fields: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """按组分类字段"""
    grouped = defaultdict(list)
    
    for field in fields:
        group = field['_source'].get('group', 'unknown')
        grouped[group].append(field)
    
    return dict(grouped)

def generate_markdown_documentation(grouped_fields: Dict[str, List[Dict[str, Any]]]) -> str:
    """生成Markdown文档"""
    
    doc = """# Arkime字段注册完整文档

本文档包含了Arkime代码库中所有通过`arkime_field_define`函数注册的字段定义，按照Elasticsearch文档格式整理。

## 统计信息

"""
    
    total_fields = sum(len(fields) for fields in grouped_fields.values())
    doc += f"- **总字段数**: {total_fields}\n"
    doc += f"- **字段组数**: {len(grouped_fields)}\n\n"
    
    doc += "## 字段组概览\n\n"
    for group, fields in sorted(grouped_fields.items()):
        doc += f"- **{group}**: {len(fields)} 个字段\n"
    
    doc += "\n---\n\n"
    
    # 按组生成详细文档
    for group, fields in sorted(grouped_fields.items()):
        doc += f"## {group.upper()} 字段组\n\n"
        doc += f"该组包含 {len(fields)} 个字段。\n\n"
        
        for field in sorted(fields, key=lambda x: x['_id']):
            source = field['_source']
            doc += f"### {field['_id']}\n\n"
            
            # Elasticsearch文档格式
            doc += "```json\n"
            doc += json.dumps(field, indent=2, ensure_ascii=False)
            doc += "\n```\n\n"
            
            # 字段详情表格
            doc += "| 属性 | 值 |\n"
            doc += "|------|----|\n"
            doc += f"| **字段ID** | `{field['_id']}` |\n"
            doc += f"| **友好名称** | {source['friendlyName']} |\n"
            doc += f"| **字段组** | {source['group']} |\n"
            doc += f"| **数据类型** | {source['type']} |\n"
            doc += f"| **数据库字段** | {source['dbField2']} |\n"
            doc += f"| **帮助信息** | {source['help']} |\n"
            doc += f"| **源文件** | {source['_source_file']} |\n"
            
            if 'category' in source:
                doc += f"| **分类** | {source['category']} |\n"
            if 'aliases' in source:
                doc += f"| **别名** | {source['aliases']} |\n"
                
            doc += "\n---\n\n"
    
    return doc

def generate_summary_table(grouped_fields: Dict[str, List[Dict[str, Any]]]) -> str:
    """生成字段汇总表"""
    
    doc = """# Arkime字段汇总表

## 按组分类的字段列表

"""
    
    for group, fields in sorted(grouped_fields.items()):
        doc += f"### {group.upper()}\n\n"
        doc += "| 字段ID | 友好名称 | 类型 | 帮助信息 |\n"
        doc += "|--------|----------|------|----------|\n"
        
        for field in sorted(fields, key=lambda x: x['_id']):
            source = field['_source']
            doc += f"| `{field['_id']}` | {source['friendlyName']} | {source['type']} | {source['help']} |\n"
        
        doc += "\n"
    
    return doc

def main():
    """主函数"""
    print("正在生成Arkime字段文档...")
    
    # 加载字段定义
    fields = load_field_definitions()
    print(f"加载了 {len(fields)} 个字段定义")
    
    # 按组分类
    grouped_fields = group_fields_by_category(fields)
    print(f"分为 {len(grouped_fields)} 个字段组")
    
    # 生成完整文档
    full_doc = generate_markdown_documentation(grouped_fields)
    with open('Arkime_Fields_Complete_Documentation.md', 'w', encoding='utf-8') as f:
        f.write(full_doc)
    print("完整文档已保存到: Arkime_Fields_Complete_Documentation.md")
    
    # 生成汇总表
    summary_doc = generate_summary_table(grouped_fields)
    with open('Arkime_Fields_Summary_Table.md', 'w', encoding='utf-8') as f:
        f.write(summary_doc)
    print("汇总表已保存到: Arkime_Fields_Summary_Table.md")
    
    # 输出统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总字段数: {len(fields)}")
    print(f"字段组数: {len(grouped_fields)}")
    
    print(f"\n=== 各组字段数量 ===")
    for group, group_fields in sorted(grouped_fields.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{group}: {len(group_fields)} 个字段")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
创建arkime_sessions3与arkime_fields_v30字段对应关系文档
"""

import json
from collections import defaultdict

def load_field_definitions():
    """加载字段定义"""
    with open('arkime_fields_clean.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_session_structure():
    """分析会话数据结构"""
    # 基于session-必须字段.json的结构
    session_structure = {
        # 基础时间戳字段
        "@timestamp": "处理时间戳(毫秒)",
        "firstPacket": "会话第一包时间戳(毫秒)", 
        "lastPacket": "会话最后一包时间戳(毫秒)",
        "length": "会话持续时间(毫秒)",
        
        # 基础会话信息
        "ipProtocol": "IP协议号 (6=TCP, 17=UDP)",
        "node": "捕获节点名称",
        
        # 源端信息
        "source.ip": "源IP地址",
        "source.port": "源端口",
        "source.bytes": "源端字节数",
        "source.packets": "源端包数",
        "source.mac": "源MAC地址数组",
        
        # 目标端信息  
        "destination.ip": "目标IP地址",
        "destination.port": "目标端口",
        "destination.bytes": "目标端字节数",
        "destination.packets": "目标端包数",
        "destination.mac": "目标MAC地址数组",
        "destination.mac-cnt": "目标MAC地址计数",
        
        # 网络统计
        "network.packets": "总包数",
        "network.bytes": "总字节数",
        
        # 文件位置信息
        "packetPos": "数据包在PCAP文件中的位置数组",
        "fileId": "对应arkime_files_v30索引中的文件ID数组",
        
        # 协议信息
        "protocols": "协议数组",
        
        # 协议特定字段(动态)
        "http": "HTTP协议相关字段",
        "dns": "DNS协议相关字段", 
        "tls": "TLS/SSL协议相关字段",
        "email": "邮件协议相关字段",
        "cert": "证书相关字段"
    }
    
    return session_structure

def create_field_mapping(field_definitions):
    """创建字段映射关系"""
    
    # 按dbField2分组
    dbfield_to_definition = {}
    expression_to_definition = {}
    
    for field_def in field_definitions:
        source = field_def['_source']
        field_id = field_def['_id']
        db_field = source['dbField2']
        
        dbfield_to_definition[db_field] = field_def
        expression_to_definition[field_id] = field_def
    
    # 创建会话字段到字段定义的映射
    session_to_field_mapping = {
        # 直接映射的字段
        "length": "session.length",
        "node": None,  # 内置字段，无对应定义
        "ipProtocol": None,  # 内置字段，无对应定义
        "@timestamp": None,  # ES内置字段
        "firstPacket": None,  # 内置字段
        "lastPacket": None,   # 内置字段
        "packetPos": None,    # 内置字段
        "fileId": None,       # 内置字段
        "protocols": "protocols",
        
        # 源端字段映射
        "source.ip": "ip.src",
        "source.port": "port.src", 
        "source.bytes": "bytes.src",
        "source.packets": "packets.src",
        "source.mac": "mac.src",
        
        # 目标端字段映射
        "destination.ip": "ip.dst",
        "destination.port": "port.dst",
        "destination.bytes": "bytes.dst", 
        "destination.packets": "packets.dst",
        "destination.mac": "mac.dst",
        
        # 网络统计映射
        "network.packets": "packets",
        "network.bytes": "bytes",
        "network.vlan.id": "vlan",
        
        # 协议特定字段(动态映射)
        "http.*": "http.*",
        "dns.*": "dns.*",
        "tls.*": "tls.*", 
        "email.*": "email.*",
        "cert.*": "cert.*",
        "smb.*": "smb.*",
        "ssh.*": "ssh.*"
    }
    
    return session_to_field_mapping, dbfield_to_definition, expression_to_definition

def generate_mapping_documentation(session_structure, session_to_field_mapping,
                                 dbfield_to_definition, expression_to_definition, field_definitions):
    """生成映射文档"""
    
    doc = """# Arkime会话字段与字段定义对应关系文档

本文档详细说明了`arkime_sessions3-*`索引中的会话数据字段与`arkime_fields_v30`索引中字段定义的对应关系。

## 概述

- **会话索引**: `arkime_sessions3-YYYYMMDD` (如: arkime_sessions3-20021231)
- **字段定义索引**: `arkime_fields_v30`
- **总字段定义数**: {total_fields}
- **映射关系**: 会话数据字段 ↔ 字段定义ID ↔ 数据库字段名

## 会话数据结构

### 基础字段

| 会话字段 | 描述 | 对应字段定义ID | 字段定义状态 |
|----------|------|----------------|--------------|
""".format(total_fields=len(expression_to_definition))
    
    # 生成基础字段映射表
    for session_field, description in session_structure.items():
        if '.' not in session_field and session_field not in ['http', 'dns', 'tls', 'email', 'cert']:
            field_def_id = session_to_field_mapping.get(session_field)
            if field_def_id:
                if field_def_id in expression_to_definition:
                    status = "✅ 有定义"
                    field_def = expression_to_definition[field_def_id]
                    db_field = field_def['_source']['dbField2']
                else:
                    status = "❌ 无定义"
                    db_field = "N/A"
            else:
                status = "🔧 内置字段"
                field_def_id = "N/A"
                db_field = "N/A"
            
            doc += f"| `{session_field}` | {description} | `{field_def_id}` | {status} |\n"
    
    doc += """
### 嵌套字段结构

#### 源端信息 (source.*)

| 会话字段 | 描述 | 对应字段定义ID | 数据库字段 | 字段类型 |
|----------|------|----------------|------------|----------|
"""
    
    source_fields = [
        ("source.ip", "源IP地址", "ip.src"),
        ("source.port", "源端口", "port.src"), 
        ("source.bytes", "源端字节数", "bytes.src"),
        ("source.packets", "源端包数", "packets.src"),
        ("source.mac", "源MAC地址", "mac.src")
    ]
    
    for session_field, desc, field_id in source_fields:
        if field_id in expression_to_definition:
            field_def = expression_to_definition[field_id]
            db_field = field_def['_source']['dbField2']
            field_type = field_def['_source']['type']
            doc += f"| `{session_field}` | {desc} | `{field_id}` | `{db_field}` | {field_type} |\n"
    
    doc += """
#### 目标端信息 (destination.*)

| 会话字段 | 描述 | 对应字段定义ID | 数据库字段 | 字段类型 |
|----------|------|----------------|------------|----------|
"""
    
    dest_fields = [
        ("destination.ip", "目标IP地址", "ip.dst"),
        ("destination.port", "目标端口", "port.dst"),
        ("destination.bytes", "目标端字节数", "bytes.dst"),
        ("destination.packets", "目标端包数", "packets.dst"),
        ("destination.mac", "目标MAC地址", "mac.dst")
    ]
    
    for session_field, desc, field_id in dest_fields:
        if field_id in expression_to_definition:
            field_def = expression_to_definition[field_id]
            db_field = field_def['_source']['dbField2']
            field_type = field_def['_source']['type']
            doc += f"| `{session_field}` | {desc} | `{field_id}` | `{db_field}` | {field_type} |\n"
    
    doc += """
#### 网络统计 (network.*)

| 会话字段 | 描述 | 对应字段定义ID | 数据库字段 | 字段类型 |
|----------|------|----------------|------------|----------|
"""
    
    network_fields = [
        ("network.packets", "总包数", "packets"),
        ("network.bytes", "总字节数", "bytes"),
        ("network.vlan.id", "VLAN ID", "vlan")
    ]
    
    for session_field, desc, field_id in network_fields:
        if field_id in expression_to_definition:
            field_def = expression_to_definition[field_id]
            db_field = field_def['_source']['dbField2']
            field_type = field_def['_source']['type']
            doc += f"| `{session_field}` | {desc} | `{field_id}` | `{db_field}` | {field_type} |\n"
    
    # 添加协议特定字段映射
    doc += """
## 协议特定字段映射

### HTTP协议字段

HTTP协议字段在会话数据中以`http`对象形式存储，对应字段定义中的`http.*`字段。

| 字段定义ID | 友好名称 | 数据库字段 | 类型 | 会话数据路径 |
|------------|----------|------------|------|--------------|
"""

    # HTTP字段
    http_fields = [field for field in field_definitions if field['_source']['group'] == 'http']
    for field_def in sorted(http_fields, key=lambda x: x['_id'])[:10]:  # 显示前10个
        source = field_def['_source']
        session_path = f"http.{source['dbField2'].replace('http.', '')}" if source['dbField2'].startswith('http.') else f"http.{source['dbField2']}"
        doc += f"| `{field_def['_id']}` | {source['friendlyName']} | `{source['dbField2']}` | {source['type']} | `{session_path}` |\n"

    if len(http_fields) > 10:
        doc += f"| ... | ... | ... | ... | ... |\n"
        doc += f"| *共{len(http_fields)}个HTTP字段* | | | | |\n"

    doc += """
### DNS协议字段

DNS协议字段在会话数据中以`dns`对象形式存储。

| 字段定义ID | 友好名称 | 数据库字段 | 类型 | 会话数据路径 |
|------------|----------|------------|------|--------------|
"""

    # DNS字段
    dns_fields = [field for field in field_definitions if field['_source']['group'] == 'dns']
    for field_def in sorted(dns_fields, key=lambda x: x['_id'])[:10]:  # 显示前10个
        source = field_def['_source']
        session_path = f"dns.{source['dbField2'].replace('dns.', '')}" if source['dbField2'].startswith('dns.') else f"dns.{source['dbField2']}"
        doc += f"| `{field_def['_id']}` | {source['friendlyName']} | `{source['dbField2']}` | {source['type']} | `{session_path}` |\n"

    if len(dns_fields) > 10:
        doc += f"| ... | ... | ... | ... | ... |\n"
        doc += f"| *共{len(dns_fields)}个DNS字段* | | | | |\n"

    doc += """
### TLS/SSL协议字段

TLS协议字段在会话数据中以`tls`对象形式存储。

| 字段定义ID | 友好名称 | 数据库字段 | 类型 | 会话数据路径 |
|------------|----------|------------|------|--------------|
"""

    # TLS字段
    tls_fields = [field for field in field_definitions if field['_source']['group'] == 'tls']
    for field_def in sorted(tls_fields, key=lambda x: x['_id']):
        source = field_def['_source']
        session_path = f"tls.{source['dbField2'].replace('tls.', '')}" if source['dbField2'].startswith('tls.') else f"tls.{source['dbField2']}"
        doc += f"| `{field_def['_id']}` | {source['friendlyName']} | `{source['dbField2']}` | {source['type']} | `{session_path}` |\n"

    doc += """
## 字段映射规律总结

### 1. 直接映射字段
- 基础会话信息字段直接存储在会话文档根级别
- 如: `length`, `protocols`, `node` 等

### 2. 嵌套结构映射
- 源端信息: `source.*` → 字段定义 `*.src`
- 目标端信息: `destination.*` → 字段定义 `*.dst`
- 网络信息: `network.*` → 对应字段定义

### 3. 协议特定字段
- HTTP: `http.*` → 字段定义 `http.*`
- DNS: `dns.*` → 字段定义 `dns.*`
- TLS: `tls.*` → 字段定义 `tls.*`
- 其他协议类似

### 4. 内置字段
以下字段为Arkime内置字段，无对应字段定义：
- `@timestamp`: Elasticsearch时间戳
- `firstPacket`: 会话首包时间
- `lastPacket`: 会话末包时间
- `ipProtocol`: IP协议号
- `node`: 捕获节点
- `packetPos`: 包位置信息
- `fileId`: 文件ID引用

## 完整字段统计

### 按组分类的字段数量
"""

    # 统计各组字段数量
    group_stats = defaultdict(int)
    for field_def in field_definitions:
        group = field_def['_source']['group']
        group_stats[group] += 1

    for group, count in sorted(group_stats.items(), key=lambda x: x[1], reverse=True):
        doc += f"- **{group}**: {count} 个字段\n"

    doc += f"""
### 字段类型分布
"""

    # 统计字段类型
    type_stats = defaultdict(int)
    for field_def in field_definitions:
        field_type = field_def['_source']['type']
        type_stats[field_type] += 1

    for field_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
        doc += f"- **{field_type}**: {count} 个字段\n"

    doc += """
## 使用示例

### 1. 查询特定字段
```json
// 查询HTTP方法为GET的会话
{
  "query": {
    "term": {
      "http.method": "GET"
    }
  }
}
```

### 2. 聚合统计
```json
// 按协议类型聚合
{
  "aggs": {
    "protocols": {
      "terms": {
        "field": "protocols"
      }
    }
  }
}
```

### 3. 字段映射查找
```bash
# 根据字段定义ID查找会话数据路径
# 字段定义: http.method → 会话数据: http.method
# 字段定义: mac.src → 会话数据: source.mac
```

---

*本文档详细说明了Arkime会话数据与字段定义的完整对应关系，便于理解数据结构和进行查询开发。*
"""

    return doc

def main():
    """主函数"""
    print("正在创建会话字段映射文档...")
    
    # 加载数据
    field_definitions = load_field_definitions()
    session_structure = analyze_session_structure()
    
    # 创建映射
    session_to_field_mapping, dbfield_to_definition, expression_to_definition = create_field_mapping(field_definitions)
    
    # 生成文档
    doc_content = generate_mapping_documentation(
        session_structure, session_to_field_mapping,
        dbfield_to_definition, expression_to_definition, field_definitions
    )
    
    # 保存文档
    with open('Arkime_Session_Field_Mapping.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("会话字段映射文档已保存到: Arkime_Session_Field_Mapping.md")
    
    # 输出统计信息
    print(f"\n=== 映射统计 ===")
    print(f"字段定义总数: {len(field_definitions)}")
    print(f"会话基础字段数: {len(session_structure)}")
    print(f"映射关系数: {len(session_to_field_mapping)}")

if __name__ == "__main__":
    main()

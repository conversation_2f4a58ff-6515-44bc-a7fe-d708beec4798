# Arkime字段注册完整文档

本文档包含了Arkime代码库中所有通过`arkime_field_define`函数注册的字段定义，按照Elasticsearch文档格式整理。

## 统计信息

- **总字段数**: 224
- **字段组数**: 23

## 字段组概览

- **bgp**: 1 个字段
- **cert**: 18 个字段
- **dhcp**: 6 个字段
- **dns**: 29 个字段
- **email**: 19 个字段
- **general**: 42 个字段
- **http**: 37 个字段
- **irc**: 2 个字段
- **isis**: 1 个字段
- **krb5**: 3 个字段
- **ldap**: 2 个字段
- **modbus**: 5 个字段
- **mysql**: 2 个字段
- **oracle**: 4 个字段
- **postgresql**: 3 个字段
- **quic**: 4 个字段
- **radius**: 4 个字段
- **smb**: 9 个字段
- **snmp**: 5 个字段
- **socks**: 5 个字段
- **ssh**: 4 个字段
- **suricata**: 7 个字段
- **tls**: 12 个字段

---

## BGP 字段组

该组包含 1 个字段。

### bgp.type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "bgp.type",
  "_score": 1,
  "_source": {
    "friendlyName": "Type",
    "group": "bgp",
    "help": "BGP Type field",
    "dbField2": "bgp.type",
    "type": "termfield",
    "_source_file": "capture/parsers/bgp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `bgp.type` |
| **友好名称** | Type |
| **字段组** | bgp |
| **数据类型** | termfield |
| **数据库字段** | bgp.type |
| **帮助信息** | BGP Type field |
| **源文件** | capture/parsers/bgp.c |

---

## CERT 字段组

该组包含 18 个字段。

### cert.alt

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.alt",
  "_score": 1,
  "_source": {
    "friendlyName": "Alt Name",
    "group": "cert",
    "help": "Certificate alternative names",
    "dbField2": "cert.alt",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.alt` |
| **友好名称** | Alt Name |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.alt |
| **帮助信息** | Certificate alternative names |
| **源文件** | capture/parsers/certs.c |

---

### cert.cnt

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.cnt",
  "_score": 1,
  "_source": {
    "friendlyName": "Cert Cnt",
    "group": "cert",
    "help": "Count of certificates",
    "dbField2": "certCnt",
    "type": "integer",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.cnt` |
| **友好名称** | Cert Cnt |
| **字段组** | cert |
| **数据类型** | integer |
| **数据库字段** | certCnt |
| **帮助信息** | Count of certificates |
| **源文件** | capture/parsers/certs.c |

---

### cert.curve

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.curve",
  "_score": 1,
  "_source": {
    "friendlyName": "Curve",
    "group": "cert",
    "help": "Curve Algorithm",
    "dbField2": "cert.curve",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.curve` |
| **友好名称** | Curve |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.curve |
| **帮助信息** | Curve Algorithm |
| **源文件** | capture/parsers/certs.c |

---

### cert.hash

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.hash",
  "_score": 1,
  "_source": {
    "friendlyName": "Hash",
    "group": "cert",
    "help": "SHA1 hash of entire certificate",
    "dbField2": "cert.hash",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.hash` |
| **友好名称** | Hash |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.hash |
| **帮助信息** | SHA1 hash of entire certificate |
| **源文件** | capture/parsers/certs.c |

---

### cert.issuer.cn

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.issuer.cn",
  "_score": 1,
  "_source": {
    "friendlyName": "Issuer CN",
    "group": "cert",
    "help": "Issuer's common name",
    "dbField2": "cert.issuerCN",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.issuer.cn` |
| **友好名称** | Issuer CN |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.issuerCN |
| **帮助信息** | Issuer's common name |
| **源文件** | capture/parsers/certs.c |

---

### cert.issuer.on

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.issuer.on",
  "_score": 1,
  "_source": {
    "friendlyName": "Issuer ON",
    "group": "cert",
    "help": "Issuer's organization name",
    "dbField2": "cert.issuerON",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.issuer.on` |
| **友好名称** | Issuer ON |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.issuerON |
| **帮助信息** | Issuer's organization name |
| **源文件** | capture/parsers/certs.c |

---

### cert.issuer.ou

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.issuer.ou",
  "_score": 1,
  "_source": {
    "friendlyName": "Issuer Org Unit",
    "group": "cert",
    "help": "Issuer's organizational unit",
    "dbField2": "cert.issuerOU",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.issuer.ou` |
| **友好名称** | Issuer Org Unit |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.issuerOU |
| **帮助信息** | Issuer's organizational unit |
| **源文件** | capture/parsers/certs.c |

---

### cert.notafter

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.notafter",
  "_score": 1,
  "_source": {
    "friendlyName": "Not After",
    "group": "cert",
    "help": "Certificate is not valid after this date",
    "dbField2": "cert.notAfter",
    "type": "date",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.notafter` |
| **友好名称** | Not After |
| **字段组** | cert |
| **数据类型** | date |
| **数据库字段** | cert.notAfter |
| **帮助信息** | Certificate is not valid after this date |
| **源文件** | capture/parsers/certs.c |

---

### cert.notbefore

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.notbefore",
  "_score": 1,
  "_source": {
    "friendlyName": "Not Before",
    "group": "cert",
    "help": "Certificate is not valid before this date",
    "dbField2": "cert.notBefore",
    "type": "date",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.notbefore` |
| **友好名称** | Not Before |
| **字段组** | cert |
| **数据类型** | date |
| **数据库字段** | cert.notBefore |
| **帮助信息** | Certificate is not valid before this date |
| **源文件** | capture/parsers/certs.c |

---

### cert.publicAlgorithm

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.publicAlgorithm",
  "_score": 1,
  "_source": {
    "friendlyName": "Public Algorithm",
    "group": "cert",
    "help": "Public Key Algorithm",
    "dbField2": "cert.publicAlgorithm",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.publicAlgorithm` |
| **友好名称** | Public Algorithm |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.publicAlgorithm |
| **帮助信息** | Public Key Algorithm |
| **源文件** | capture/parsers/certs.c |

---

### cert.remainingDays

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.remainingDays",
  "_score": 1,
  "_source": {
    "friendlyName": "Days remaining",
    "group": "cert",
    "help": "Certificate is still valid for this many days",
    "dbField2": "cert.remainingDays",
    "type": "integer",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.remainingDays` |
| **友好名称** | Days remaining |
| **字段组** | cert |
| **数据类型** | integer |
| **数据库字段** | cert.remainingDays |
| **帮助信息** | Certificate is still valid for this many days |
| **源文件** | capture/parsers/certs.c |

---

### cert.remainingSeconds

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.remainingSeconds",
  "_score": 1,
  "_source": {
    "friendlyName": "Seconds remaining",
    "group": "cert",
    "help": "Certificate is still valid for this many seconds",
    "dbField2": "cert.remainingSeconds",
    "type": "integer",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.remainingSeconds` |
| **友好名称** | Seconds remaining |
| **字段组** | cert |
| **数据类型** | integer |
| **数据库字段** | cert.remainingSeconds |
| **帮助信息** | Certificate is still valid for this many seconds |
| **源文件** | capture/parsers/certs.c |

---

### cert.serial

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.serial",
  "_score": 1,
  "_source": {
    "friendlyName": "Serial Number",
    "group": "cert",
    "help": "Serial Number",
    "dbField2": "cert.serial",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.serial` |
| **友好名称** | Serial Number |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.serial |
| **帮助信息** | Serial Number |
| **源文件** | capture/parsers/certs.c |

---

### cert.subject.cn

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.subject.cn",
  "_score": 1,
  "_source": {
    "friendlyName": "Subject CN",
    "group": "cert",
    "help": "Subject's common name",
    "dbField2": "cert.subjectCN",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.subject.cn` |
| **友好名称** | Subject CN |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.subjectCN |
| **帮助信息** | Subject's common name |
| **源文件** | capture/parsers/certs.c |

---

### cert.subject.on

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.subject.on",
  "_score": 1,
  "_source": {
    "friendlyName": "Subject ON",
    "group": "cert",
    "help": "Subject's organization name",
    "dbField2": "cert.subjectON",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.subject.on` |
| **友好名称** | Subject ON |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.subjectON |
| **帮助信息** | Subject's organization name |
| **源文件** | capture/parsers/certs.c |

---

### cert.subject.ou

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.subject.ou",
  "_score": 1,
  "_source": {
    "friendlyName": "Subject Org Unit",
    "group": "cert",
    "help": "Subject's organizational unit",
    "dbField2": "cert.subjectOU",
    "type": "termfield",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.subject.ou` |
| **友好名称** | Subject Org Unit |
| **字段组** | cert |
| **数据类型** | termfield |
| **数据库字段** | cert.subjectOU |
| **帮助信息** | Subject's organizational unit |
| **源文件** | capture/parsers/certs.c |

---

### cert.validfor

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.validfor",
  "_score": 1,
  "_source": {
    "friendlyName": "Days Valid For",
    "group": "cert",
    "help": "Certificate is valid for this many days total",
    "dbField2": "cert.validDays",
    "type": "integer",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.validfor` |
| **友好名称** | Days Valid For |
| **字段组** | cert |
| **数据类型** | integer |
| **数据库字段** | cert.validDays |
| **帮助信息** | Certificate is valid for this many days total |
| **源文件** | capture/parsers/certs.c |

---

### cert.validforSeconds

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "cert.validforSeconds",
  "_score": 1,
  "_source": {
    "friendlyName": "Seconds Valid For",
    "group": "cert",
    "help": "Certificate is valid for this many seconds total",
    "dbField2": "cert.validSeconds",
    "type": "integer",
    "_source_file": "capture/parsers/certs.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `cert.validforSeconds` |
| **友好名称** | Seconds Valid For |
| **字段组** | cert |
| **数据类型** | integer |
| **数据库字段** | cert.validSeconds |
| **帮助信息** | Certificate is valid for this many seconds total |
| **源文件** | capture/parsers/certs.c |

---

## DHCP 字段组

该组包含 6 个字段。

### dhcp.host

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dhcp.host",
  "_score": 1,
  "_source": {
    "friendlyName": "Host",
    "group": "dhcp",
    "help": "DHCP Host",
    "dbField2": "dhcp.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/dhcp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dhcp.host` |
| **友好名称** | Host |
| **字段组** | dhcp |
| **数据类型** | termfield |
| **数据库字段** | dhcp.host |
| **帮助信息** | DHCP Host |
| **源文件** | capture/parsers/dhcp.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### dhcp.host.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dhcp.host.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "dhcp",
    "help": "DHCP Hostname Tokens",
    "dbField2": "dhcp.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/dhcp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dhcp.host.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | dhcp |
| **数据类型** | textfield |
| **数据库字段** | dhcp.hostTokens |
| **帮助信息** | DHCP Hostname Tokens |
| **源文件** | capture/parsers/dhcp.c |
| **别名** | ['[\\'] |

---

### dhcp.id

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dhcp.id",
  "_score": 1,
  "_source": {
    "friendlyName": "Transaction id",
    "group": "dhcp",
    "help": "DHCP Transaction Id",
    "dbField2": "dhcp.id",
    "type": "termfield",
    "_source_file": "capture/parsers/dhcp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dhcp.id` |
| **友好名称** | Transaction id |
| **字段组** | dhcp |
| **数据类型** | termfield |
| **数据库字段** | dhcp.id |
| **帮助信息** | DHCP Transaction Id |
| **源文件** | capture/parsers/dhcp.c |

---

### dhcp.mac

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dhcp.mac",
  "_score": 1,
  "_source": {
    "friendlyName": "Client MAC",
    "group": "dhcp",
    "help": "Client ethernet MAC ",
    "dbField2": "dhcp.mac",
    "type": "termfield",
    "_source_file": "capture/parsers/dhcp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dhcp.mac` |
| **友好名称** | Client MAC |
| **字段组** | dhcp |
| **数据类型** | termfield |
| **数据库字段** | dhcp.mac |
| **帮助信息** | Client ethernet MAC  |
| **源文件** | capture/parsers/dhcp.c |

---

### dhcp.oui

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dhcp.oui",
  "_score": 1,
  "_source": {
    "friendlyName": "Client OUI",
    "group": "dhcp",
    "help": "Client ethernet OUI ",
    "dbField2": "dhcp.oui",
    "type": "termfield",
    "_source_file": "capture/parsers/dhcp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dhcp.oui` |
| **友好名称** | Client OUI |
| **字段组** | dhcp |
| **数据类型** | termfield |
| **数据库字段** | dhcp.oui |
| **帮助信息** | Client ethernet OUI  |
| **源文件** | capture/parsers/dhcp.c |

---

### dhcp.type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dhcp.type",
  "_score": 1,
  "_source": {
    "friendlyName": "Type",
    "group": "dhcp",
    "help": "DHCP Type",
    "dbField2": "dhcp.type",
    "type": "termfield",
    "_source_file": "capture/parsers/dhcp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dhcp.type` |
| **友好名称** | Type |
| **字段组** | dhcp |
| **数据类型** | termfield |
| **数据库字段** | dhcp.type |
| **帮助信息** | DHCP Type |
| **源文件** | capture/parsers/dhcp.c |

---

## DNS 字段组

该组包含 29 个字段。

### dns.answer.caa

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.caa",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer CAA",
    "group": "dns",
    "help": "DNS Answer CAA",
    "dbField2": "dns.answers.caa",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.caa` |
| **友好名称** | DNS Answer CAA |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.caa |
| **帮助信息** | DNS Answer CAA |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.class

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.class",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer Class",
    "group": "dns",
    "help": "DNS Answer Class",
    "dbField2": "dns.answers.class",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.class` |
| **友好名称** | DNS Answer Class |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.class |
| **帮助信息** | DNS Answer Class |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.cname

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.cname",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer CNAME",
    "group": "dns",
    "help": "DNS Answer CNAME",
    "dbField2": "dns.answers.cname",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.cname` |
| **友好名称** | DNS Answer CNAME |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.cname |
| **帮助信息** | DNS Answer CNAME |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.cnt

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.cnt",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answers Cnt",
    "group": "dns",
    "help": "Count of DNS Answers",
    "dbField2": "dns.answersCnt",
    "type": "integer",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.cnt` |
| **友好名称** | DNS Answers Cnt |
| **字段组** | dns |
| **数据类型** | integer |
| **数据库字段** | dns.answersCnt |
| **帮助信息** | Count of DNS Answers |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.https

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.https",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer HTTPS",
    "group": "dns",
    "help": "DNS Answer HTTPS",
    "dbField2": "dns.answers.https",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.https` |
| **友好名称** | DNS Answer HTTPS |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.https |
| **帮助信息** | DNS Answer HTTPS |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.ip

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.ip",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer IP",
    "group": "dns",
    "help": "DNS Answer IP",
    "dbField2": "dns.answers.ip",
    "type": "ip",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.ip` |
| **友好名称** | DNS Answer IP |
| **字段组** | dns |
| **数据类型** | ip |
| **数据库字段** | dns.answers.ip |
| **帮助信息** | DNS Answer IP |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.mx

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.mx",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer MX",
    "group": "dns",
    "help": "DNS Answer MX",
    "dbField2": "dns.answers.mx",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.mx` |
| **友好名称** | DNS Answer MX |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.mx |
| **帮助信息** | DNS Answer MX |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.name

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.name",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Name",
    "group": "dns",
    "help": "DNS Answer Name",
    "dbField2": "dns.answers.name",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.name` |
| **友好名称** | DNS Name |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.name |
| **帮助信息** | DNS Answer Name |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.ns

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.ns",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer NS",
    "group": "dns",
    "help": "DNS Answer NS",
    "dbField2": "dns.answers.nameserver",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.ns` |
| **友好名称** | DNS Answer NS |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.nameserver |
| **帮助信息** | DNS Answer NS |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.priority

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.priority",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer Priority",
    "group": "dns",
    "help": "DNS Answer Priority",
    "dbField2": "dns.answers.priority",
    "type": "integer",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.priority` |
| **友好名称** | DNS Answer Priority |
| **字段组** | dns |
| **数据类型** | integer |
| **数据库字段** | dns.answers.priority |
| **帮助信息** | DNS Answer Priority |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.ttl

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.ttl",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer TTL",
    "group": "dns",
    "help": "DNS Answer TTL",
    "dbField2": "dns.answers.ttl",
    "type": "integer",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.ttl` |
| **友好名称** | DNS Answer TTL |
| **字段组** | dns |
| **数据类型** | integer |
| **数据库字段** | dns.answers.ttl |
| **帮助信息** | DNS Answer TTL |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.txt

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.txt",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer TXT",
    "group": "dns",
    "help": "DNS Answer TXT",
    "dbField2": "dns.answers.txt",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.txt` |
| **友好名称** | DNS Answer TXT |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.txt |
| **帮助信息** | DNS Answer TXT |
| **源文件** | capture/parsers/dns.c |

---

### dns.answer.type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.answer.type",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Answer Type",
    "group": "dns",
    "help": "DNS Answer Type",
    "dbField2": "dns.answers.type",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.answer.type` |
| **友好名称** | DNS Answer Type |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.answers.type |
| **帮助信息** | DNS Answer Type |
| **源文件** | capture/parsers/dns.c |

---

### dns.header_flags

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.header_flags",
  "_score": 1,
  "_source": {
    "friendlyName": "DNS Header Flags",
    "group": "dns",
    "help": "DNS Header Flags",
    "dbField2": "dns.headerFlags",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.header_flags` |
| **友好名称** | DNS Header Flags |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.headerFlags |
| **帮助信息** | DNS Header Flags |
| **源文件** | capture/parsers/dns.c |

---

### dns.opcode

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.opcode",
  "_score": 1,
  "_source": {
    "friendlyName": "Op Code",
    "group": "dns",
    "help": "DNS lookup op code",
    "dbField2": "dns.opcode",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.opcode` |
| **友好名称** | Op Code |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.opcode |
| **帮助信息** | DNS lookup op code |
| **源文件** | capture/parsers/dns.c |

---

### dns.puny

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.puny",
  "_score": 1,
  "_source": {
    "friendlyName": "Puny",
    "group": "dns",
    "help": "DNS lookup punycode",
    "dbField2": "dns.puny",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.puny` |
| **友好名称** | Puny |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.puny |
| **帮助信息** | DNS lookup punycode |
| **源文件** | capture/parsers/dns.c |

---

### dns.query.class

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.query.class",
  "_score": 1,
  "_source": {
    "friendlyName": "Query Class",
    "group": "dns",
    "help": "DNS lookup query class",
    "dbField2": "dns.qc",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.query.class` |
| **友好名称** | Query Class |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.qc |
| **帮助信息** | DNS lookup query class |
| **源文件** | capture/parsers/dns.c |

---

### dns.query.host

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.query.host",
  "_score": 1,
  "_source": {
    "friendlyName": "Query Host",
    "group": "dns",
    "help": "DNS Query Name",
    "dbField2": "dns.queryHost",
    "type": "termfield",
    "category": "host",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.query.host` |
| **友好名称** | Query Host |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.queryHost |
| **帮助信息** | DNS Query Name |
| **源文件** | capture/parsers/dns.c |
| **分类** | host |

---

### dns.query.type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.query.type",
  "_score": 1,
  "_source": {
    "friendlyName": "Query Type",
    "group": "dns",
    "help": "DNS lookup query type",
    "dbField2": "dns.qt",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.query.type` |
| **友好名称** | Query Type |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.qt |
| **帮助信息** | DNS lookup query type |
| **源文件** | capture/parsers/dns.c |

---

### dns.status

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dns.status",
  "_score": 1,
  "_source": {
    "friendlyName": "Status Code",
    "group": "dns",
    "help": "DNS lookup return code",
    "dbField2": "dns.status",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dns.status` |
| **友好名称** | Status Code |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.status |
| **帮助信息** | DNS lookup return code |
| **源文件** | capture/parsers/dns.c |

---

### host.dns

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.dns",
  "_score": 1,
  "_source": {
    "friendlyName": "Host",
    "group": "dns",
    "help": "DNS lookup hostname",
    "dbField2": "dns.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.dns` |
| **友好名称** | Host |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.host |
| **帮助信息** | DNS lookup hostname |
| **源文件** | capture/parsers/dns.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.dns.all

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.dns.all",
  "_score": 1,
  "_source": {
    "friendlyName": "All Host",
    "group": "dns",
    "help": "Shorthand for host.dns or host.dns.nameserver",
    "dbField2": "dnshostall",
    "type": "termfield",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.dns.all` |
| **友好名称** | All Host |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dnshostall |
| **帮助信息** | Shorthand for host.dns or host.dns.nameserver |
| **源文件** | capture/parsers/dns.c |

---

### host.dns.mailserver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.dns.mailserver",
  "_score": 1,
  "_source": {
    "friendlyName": "MX Host",
    "group": "dns",
    "help": "Hostnames for Mail Exchange Server",
    "dbField2": "dns.mailserverHost",
    "type": "termfield",
    "category": "host",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.dns.mailserver` |
| **友好名称** | MX Host |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.mailserverHost |
| **帮助信息** | Hostnames for Mail Exchange Server |
| **源文件** | capture/parsers/dns.c |
| **分类** | host |

---

### host.dns.nameserver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.dns.nameserver",
  "_score": 1,
  "_source": {
    "friendlyName": "NS Host",
    "group": "dns",
    "help": "Hostnames for Name Server",
    "dbField2": "dns.nameserverHost",
    "type": "termfield",
    "category": "host",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.dns.nameserver` |
| **友好名称** | NS Host |
| **字段组** | dns |
| **数据类型** | termfield |
| **数据库字段** | dns.nameserverHost |
| **帮助信息** | Hostnames for Name Server |
| **源文件** | capture/parsers/dns.c |
| **分类** | host |

---

### host.dns.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.dns.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "dns",
    "help": "DNS lookup hostname tokens",
    "dbField2": "dns.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.dns.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | dns |
| **数据类型** | textfield |
| **数据库字段** | dns.hostTokens |
| **帮助信息** | DNS lookup hostname tokens |
| **源文件** | capture/parsers/dns.c |
| **别名** | ['[\\'] |

---

### ip.dns

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.dns",
  "_score": 1,
  "_source": {
    "friendlyName": "IP",
    "group": "dns",
    "help": "IP from DNS result",
    "dbField2": "dns.ip",
    "type": "ip",
    "category": "ip",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.dns` |
| **友好名称** | IP |
| **字段组** | dns |
| **数据类型** | ip |
| **数据库字段** | dns.ip |
| **帮助信息** | IP from DNS result |
| **源文件** | capture/parsers/dns.c |
| **分类** | ip |
| **别名** | ['[\\'] |

---

### ip.dns.all

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.dns.all",
  "_score": 1,
  "_source": {
    "friendlyName": "IP",
    "group": "dns",
    "help": "Shorthand for ip.dns or ip.dns.nameserver",
    "dbField2": "dnsipall",
    "type": "ip",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.dns.all` |
| **友好名称** | IP |
| **字段组** | dns |
| **数据类型** | ip |
| **数据库字段** | dnsipall |
| **帮助信息** | Shorthand for ip.dns or ip.dns.nameserver |
| **源文件** | capture/parsers/dns.c |

---

### ip.dns.mailserver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.dns.mailserver",
  "_score": 1,
  "_source": {
    "friendlyName": "IP",
    "group": "dns",
    "help": "IPs for mailservers",
    "dbField2": "dns.mailserverIp",
    "type": "ip",
    "category": "ip",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.dns.mailserver` |
| **友好名称** | IP |
| **字段组** | dns |
| **数据类型** | ip |
| **数据库字段** | dns.mailserverIp |
| **帮助信息** | IPs for mailservers |
| **源文件** | capture/parsers/dns.c |
| **分类** | ip |

---

### ip.dns.nameserver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.dns.nameserver",
  "_score": 1,
  "_source": {
    "friendlyName": "IP",
    "group": "dns",
    "help": "IPs for nameservers",
    "dbField2": "dns.nameserverIp",
    "type": "ip",
    "category": "ip",
    "_source_file": "capture/parsers/dns.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.dns.nameserver` |
| **友好名称** | IP |
| **字段组** | dns |
| **数据类型** | ip |
| **数据库字段** | dns.nameserverIp |
| **帮助信息** | IPs for nameservers |
| **源文件** | capture/parsers/dns.c |
| **分类** | ip |

---

## EMAIL 字段组

该组包含 19 个字段。

### email.bodymagic

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.bodymagic",
  "_score": 1,
  "_source": {
    "friendlyName": "Body Magic",
    "group": "email",
    "help": "The content type of body determined by libfile/magic",
    "dbField2": "email.bodyMagic",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.bodymagic` |
| **友好名称** | Body Magic |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.bodyMagic |
| **帮助信息** | The content type of body determined by libfile/magic |
| **源文件** | capture/parsers/smtp.c |

---

### email.content-type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.content-type",
  "_score": 1,
  "_source": {
    "friendlyName": "Content-Type",
    "group": "email",
    "help": "Email content-type header",
    "dbField2": "email.contentType",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.content-type` |
| **友好名称** | Content-Type |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.contentType |
| **帮助信息** | Email content-type header |
| **源文件** | capture/parsers/smtp.c |

---

### email.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Receiver",
    "group": "email",
    "help": "Email to address",
    "dbField2": "email.dst",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.dst` |
| **友好名称** | Receiver |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.dst |
| **帮助信息** | Email to address |
| **源文件** | capture/parsers/smtp.c |
| **分类** | user |

---

### email.file-content-type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.file-content-type",
  "_score": 1,
  "_source": {
    "friendlyName": "Attach Content-Type",
    "group": "email",
    "help": "Email attachment content types",
    "dbField2": "email.fileContentType",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.file-content-type` |
| **友好名称** | Attach Content-Type |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.fileContentType |
| **帮助信息** | Email attachment content types |
| **源文件** | capture/parsers/smtp.c |

---

### email.fn

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.fn",
  "_score": 1,
  "_source": {
    "friendlyName": "Filenames",
    "group": "email",
    "help": "Email attachment filenames",
    "dbField2": "email.filename",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.fn` |
| **友好名称** | Filenames |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.filename |
| **帮助信息** | Email attachment filenames |
| **源文件** | capture/parsers/smtp.c |

---

### email.has-header

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.has-header",
  "_score": 1,
  "_source": {
    "friendlyName": "Header",
    "group": "email",
    "help": "Email has the header set",
    "dbField2": "email.header",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.has-header` |
| **友好名称** | Header |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.header |
| **帮助信息** | Email has the header set |
| **源文件** | capture/parsers/smtp.c |

---

### email.has-header.name

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.has-header.name",
  "_score": 1,
  "_source": {
    "friendlyName": "Header Field",
    "group": "email",
    "help": "Email has the header field set",
    "dbField2": "email.headerField",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.has-header.name` |
| **友好名称** | Header Field |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.headerField |
| **帮助信息** | Email has the header field set |
| **源文件** | capture/parsers/smtp.c |

---

### email.has-header.value

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.has-header.value",
  "_score": 1,
  "_source": {
    "friendlyName": "Header Value",
    "group": "email",
    "help": "Email has the header value",
    "dbField2": "email.headerValue",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.has-header.value` |
| **友好名称** | Header Value |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.headerValue |
| **帮助信息** | Email has the header value |
| **源文件** | capture/parsers/smtp.c |

---

### email.md5

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.md5",
  "_score": 1,
  "_source": {
    "friendlyName": "Attach MD5s",
    "group": "email",
    "help": "Email attachment MD5s",
    "dbField2": "email.md5",
    "type": "termfield",
    "category": "md5",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.md5` |
| **友好名称** | Attach MD5s |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.md5 |
| **帮助信息** | Email attachment MD5s |
| **源文件** | capture/parsers/smtp.c |
| **分类** | md5 |

---

### email.message-id

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.message-id",
  "_score": 1,
  "_source": {
    "friendlyName": "Id",
    "group": "email",
    "help": "Email Message-Id header",
    "dbField2": "email.id",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.message-id` |
| **友好名称** | Id |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.id |
| **帮助信息** | Email Message-Id header |
| **源文件** | capture/parsers/smtp.c |

---

### email.mime-version

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.mime-version",
  "_score": 1,
  "_source": {
    "friendlyName": "Mime-Version",
    "group": "email",
    "help": "Email Mime-Header header",
    "dbField2": "email.mimeVersion",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.mime-version` |
| **友好名称** | Mime-Version |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.mimeVersion |
| **帮助信息** | Email Mime-Header header |
| **源文件** | capture/parsers/smtp.c |

---

### email.sha256

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.sha256",
  "_score": 1,
  "_source": {
    "friendlyName": "Attach SHA256s",
    "group": "email",
    "help": "Email attachment SHA256s",
    "dbField2": "email.sha256",
    "type": "termfield",
    "category": "sha256",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.sha256` |
| **友好名称** | Attach SHA256s |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.sha256 |
| **帮助信息** | Email attachment SHA256s |
| **源文件** | capture/parsers/smtp.c |
| **分类** | sha256 |

---

### email.smtp-hello

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.smtp-hello",
  "_score": 1,
  "_source": {
    "friendlyName": "SMTP Hello",
    "group": "email",
    "help": "SMTP HELO/EHLO",
    "dbField2": "email.smtpHello",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.smtp-hello` |
| **友好名称** | SMTP Hello |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.smtpHello |
| **帮助信息** | SMTP HELO/EHLO |
| **源文件** | capture/parsers/smtp.c |

---

### email.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Sender",
    "group": "email",
    "help": "Email from address",
    "dbField2": "email.src",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.src` |
| **友好名称** | Sender |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.src |
| **帮助信息** | Email from address |
| **源文件** | capture/parsers/smtp.c |
| **分类** | user |

---

### email.subject

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.subject",
  "_score": 1,
  "_source": {
    "friendlyName": "Subject",
    "group": "email",
    "help": "Email subject header",
    "dbField2": "email.subject",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.subject` |
| **友好名称** | Subject |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.subject |
| **帮助信息** | Email subject header |
| **源文件** | capture/parsers/smtp.c |

---

### email.x-mailer

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "email.x-mailer",
  "_score": 1,
  "_source": {
    "friendlyName": "X-Mailer Header",
    "group": "email",
    "help": "Email X-Mailer header",
    "dbField2": "email.useragent",
    "type": "termfield",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `email.x-mailer` |
| **友好名称** | X-Mailer Header |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.useragent |
| **帮助信息** | Email X-Mailer header |
| **源文件** | capture/parsers/smtp.c |

---

### host.email

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.email",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname",
    "group": "email",
    "help": "Email hostnames",
    "dbField2": "email.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.email` |
| **友好名称** | Hostname |
| **字段组** | email |
| **数据类型** | termfield |
| **数据库字段** | email.host |
| **帮助信息** | Email hostnames |
| **源文件** | capture/parsers/smtp.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.email.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.email.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "email",
    "help": "Email Hostname Tokens",
    "dbField2": "email.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.email.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | email |
| **数据类型** | textfield |
| **数据库字段** | email.hostTokens |
| **帮助信息** | Email Hostname Tokens |
| **源文件** | capture/parsers/smtp.c |
| **别名** | ['[\\'] |

---

### ip.email

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.email",
  "_score": 1,
  "_source": {
    "friendlyName": "IP",
    "group": "email",
    "help": "Email IP address",
    "dbField2": "email.ip",
    "type": "ip",
    "category": "ip",
    "_source_file": "capture/parsers/smtp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.email` |
| **友好名称** | IP |
| **字段组** | email |
| **数据类型** | ip |
| **数据库字段** | email.ip |
| **帮助信息** | Email IP address |
| **源文件** | capture/parsers/smtp.c |
| **分类** | ip |

---

## GENERAL 字段组

该组包含 42 个字段。

### asset

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "asset",
  "_score": 1,
  "_source": {
    "friendlyName": "Asset",
    "group": "general",
    "help": "Asset name",
    "dbField2": "asset",
    "type": "termfield",
    "_source_file": "capture/parsers.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `asset` |
| **友好名称** | Asset |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | asset |
| **帮助信息** | Asset name |
| **源文件** | capture/parsers.c |

---

### communityId

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "communityId",
  "_score": 1,
  "_source": {
    "friendlyName": "Community Id",
    "group": "general",
    "help": "Community id flow hash",
    "dbField2": "communityId",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `communityId` |
| **友好名称** | Community Id |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | communityId |
| **帮助信息** | Community id flow hash |
| **源文件** | capture/packet.c |

---

### dscp.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dscp.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst DSCP",
    "group": "general",
    "help": "Destination non zero differentiated services class selector set for session",
    "dbField2": "dstDscp",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dscp.dst` |
| **友好名称** | Dst DSCP |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | dstDscp |
| **帮助信息** | Destination non zero differentiated services class selector set for session |
| **源文件** | capture/packet.c |

---

### dscp.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "dscp.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src DSCP",
    "group": "general",
    "help": "Source non zero differentiated services class selector set for session",
    "dbField2": "srcDscp",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `dscp.src` |
| **友好名称** | Src DSCP |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | srcDscp |
| **帮助信息** | Source non zero differentiated services class selector set for session |
| **源文件** | capture/packet.c |

---

### ethertype

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ethertype",
  "_score": 1,
  "_source": {
    "friendlyName": "Ethertype",
    "group": "general",
    "help": "The ethernet protocol type",
    "dbField2": "ethertype",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ethertype` |
| **友好名称** | Ethertype |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | ethertype |
| **帮助信息** | The ethernet protocol type |
| **源文件** | capture/packet.c |

---

### icmp.code

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "icmp.code",
  "_score": 1,
  "_source": {
    "friendlyName": "ICMP Code",
    "group": "general",
    "help": "ICMP code field values",
    "dbField2": "icmp.code",
    "type": "integer",
    "_source_file": "capture/parsers/icmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `icmp.code` |
| **友好名称** | ICMP Code |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | icmp.code |
| **帮助信息** | ICMP code field values |
| **源文件** | capture/parsers/icmp.c |

---

### icmp.type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "icmp.type",
  "_score": 1,
  "_source": {
    "friendlyName": "ICMP Type",
    "group": "general",
    "help": "ICMP type field values",
    "dbField2": "icmp.type",
    "type": "integer",
    "_source_file": "capture/parsers/icmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `icmp.type` |
| **友好名称** | ICMP Type |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | icmp.type |
| **帮助信息** | ICMP type field values |
| **源文件** | capture/parsers/icmp.c |

---

### initRTT

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "initRTT",
  "_score": 1,
  "_source": {
    "friendlyName": "Initial RTT",
    "group": "general",
    "help": "Initial round trip time, difference between SYN and ACK timestamp divided by 2 in ms",
    "dbField2": "initRTT",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `initRTT` |
| **友好名称** | Initial RTT |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | initRTT |
| **帮助信息** | Initial round trip time, difference between SYN and ACK timestamp divided by 2 in ms |
| **源文件** | capture/packet.c |

---

### mac

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "mac",
  "_score": 1,
  "_source": {
    "friendlyName": "Src or Dst MAC",
    "group": "general",
    "help": "Shorthand for mac.src or mac.dst",
    "dbField2": "macall",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `mac` |
| **友好名称** | Src or Dst MAC |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | macall |
| **帮助信息** | Shorthand for mac.src or mac.dst |
| **源文件** | capture/packet.c |

---

### mac.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "mac.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst MAC",
    "group": "general",
    "help": "Destination ethernet mac addresses set for session",
    "dbField2": "destination.mac",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `mac.dst` |
| **友好名称** | Dst MAC |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | destination.mac |
| **帮助信息** | Destination ethernet mac addresses set for session |
| **源文件** | capture/packet.c |

---

### mac.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "mac.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src MAC",
    "group": "general",
    "help": "Source ethernet mac addresses set for session",
    "dbField2": "source.mac",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `mac.src` |
| **友好名称** | Src MAC |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | source.mac |
| **帮助信息** | Source ethernet mac addresses set for session |
| **源文件** | capture/packet.c |

---

### oui.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "oui.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst OUI",
    "group": "general",
    "help": "Destination ethernet oui for session",
    "dbField2": "dstOui",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `oui.dst` |
| **友好名称** | Dst OUI |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | dstOui |
| **帮助信息** | Destination ethernet oui for session |
| **源文件** | capture/packet.c |

---

### oui.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "oui.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src OUI",
    "group": "general",
    "help": "Source ethernet oui for session",
    "dbField2": "srcOui",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `oui.src` |
| **友好名称** | Src OUI |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | srcOui |
| **帮助信息** | Source ethernet oui for session |
| **源文件** | capture/packet.c |

---

### outerip

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outerip",
  "_score": 1,
  "_source": {
    "friendlyName": "Src or Dst Outer IP",
    "group": "general",
    "help": "Shorthand for outerip.src or outerip.dst",
    "dbField2": "outeripall",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outerip` |
| **友好名称** | Src or Dst Outer IP |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | outeripall |
| **帮助信息** | Shorthand for outerip.src or outerip.dst |
| **源文件** | capture/packet.c |

---

### outerip.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outerip.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst Outer IP",
    "group": "general",
    "help": "Destination outer ip for session",
    "dbField2": "dstOuterIp",
    "type": "ip",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outerip.dst` |
| **友好名称** | Dst Outer IP |
| **字段组** | general |
| **数据类型** | ip |
| **数据库字段** | dstOuterIp |
| **帮助信息** | Destination outer ip for session |
| **源文件** | capture/packet.c |

---

### outerip.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outerip.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src Outer IP",
    "group": "general",
    "help": "Source ethernet outer ip for session",
    "dbField2": "srcOuterIp",
    "type": "ip",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outerip.src` |
| **友好名称** | Src Outer IP |
| **字段组** | general |
| **数据类型** | ip |
| **数据库字段** | srcOuterIp |
| **帮助信息** | Source ethernet outer ip for session |
| **源文件** | capture/packet.c |

---

### outermac

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outermac",
  "_score": 1,
  "_source": {
    "friendlyName": "Src or Dst Outer MAC",
    "group": "general",
    "help": "Shorthand for outermac.src or outermac.dst",
    "dbField2": "outermacall",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outermac` |
| **友好名称** | Src or Dst Outer MAC |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | outermacall |
| **帮助信息** | Shorthand for outermac.src or outermac.dst |
| **源文件** | capture/packet.c |

---

### outermac.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outermac.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst Outer MAC",
    "group": "general",
    "help": "Destination ethernet outer mac addresses set for session",
    "dbField2": "dstOuterMac",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outermac.dst` |
| **友好名称** | Dst Outer MAC |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | dstOuterMac |
| **帮助信息** | Destination ethernet outer mac addresses set for session |
| **源文件** | capture/packet.c |

---

### outermac.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outermac.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src Outer MAC",
    "group": "general",
    "help": "Source ethernet outer mac addresses set for session",
    "dbField2": "srcOuterMac",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outermac.src` |
| **友好名称** | Src Outer MAC |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | srcOuterMac |
| **帮助信息** | Source ethernet outer mac addresses set for session |
| **源文件** | capture/packet.c |

---

### outeroui.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outeroui.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst Outer OUI",
    "group": "general",
    "help": "Destination ethernet outer oui for session",
    "dbField2": "dstOuterOui",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outeroui.dst` |
| **友好名称** | Dst Outer OUI |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | dstOuterOui |
| **帮助信息** | Destination ethernet outer oui for session |
| **源文件** | capture/packet.c |

---

### outeroui.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "outeroui.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src Outer OUI",
    "group": "general",
    "help": "Source ethernet outer oui for session",
    "dbField2": "srcOuterOui",
    "type": "termfield",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `outeroui.src` |
| **友好名称** | Src Outer OUI |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | srcOuterOui |
| **帮助信息** | Source ethernet outer oui for session |
| **源文件** | capture/packet.c |

---

### packets.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "packets.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst Packets",
    "group": "general",
    "help": "Total number of packets sent by destination in a session",
    "dbField2": "dstPackets",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `packets.dst` |
| **友好名称** | Dst Packets |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | dstPackets |
| **帮助信息** | Total number of packets sent by destination in a session |
| **源文件** | capture/packet.c |

---

### packets.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "packets.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src Packets",
    "group": "general",
    "help": "Total number of packets sent by source in a session",
    "dbField2": "srcPackets",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `packets.src` |
| **友好名称** | Src Packets |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | srcPackets |
| **帮助信息** | Total number of packets sent by source in a session |
| **源文件** | capture/packet.c |

---

### protocols

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "protocols",
  "_score": 1,
  "_source": {
    "friendlyName": "Protocols",
    "group": "general",
    "help": "Protocols set for session",
    "dbField2": "protocol",
    "type": "termfield",
    "_source_file": "capture/session.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `protocols` |
| **友好名称** | Protocols |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | protocol |
| **帮助信息** | Protocols set for session |
| **源文件** | capture/session.c |

---

### session.length

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "session.length",
  "_score": 1,
  "_source": {
    "friendlyName": "Session Length",
    "group": "general",
    "help": "Session Length in milliseconds so far",
    "dbField2": "length",
    "type": "integer",
    "_source_file": "capture/parsers.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `session.length` |
| **友好名称** | Session Length |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | length |
| **帮助信息** | Session Length in milliseconds so far |
| **源文件** | capture/parsers.c |

---

### session.segments

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "session.segments",
  "_score": 1,
  "_source": {
    "friendlyName": "Session Segments",
    "group": "general",
    "help": "Number of segments in session so far",
    "dbField2": "segmentCnt",
    "type": "integer",
    "_source_file": "capture/parsers.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `session.segments` |
| **友好名称** | Session Segments |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | segmentCnt |
| **帮助信息** | Number of segments in session so far |
| **源文件** | capture/parsers.c |

---

### tags

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tags",
  "_score": 1,
  "_source": {
    "friendlyName": "Tags",
    "group": "general",
    "help": "Tags set for session",
    "dbField2": "tags",
    "type": "termfield",
    "_source_file": "capture/parsers.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tags` |
| **友好名称** | Tags |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | tags |
| **帮助信息** | Tags set for session |
| **源文件** | capture/parsers.c |

---

### tcpflags.ack

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.ack",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag ACK",
    "group": "general",
    "help": "Count of packets with only the ACK flag set",
    "dbField2": "tcpflags.ack",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.ack` |
| **友好名称** | TCP Flag ACK |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.ack |
| **帮助信息** | Count of packets with only the ACK flag set |
| **源文件** | capture/packet.c |

---

### tcpflags.fin

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.fin",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag FIN",
    "group": "general",
    "help": "Count of packets with FIN flag set",
    "dbField2": "tcpflags.fin",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.fin` |
| **友好名称** | TCP Flag FIN |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.fin |
| **帮助信息** | Count of packets with FIN flag set |
| **源文件** | capture/packet.c |

---

### tcpflags.psh

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.psh",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag PSH",
    "group": "general",
    "help": "Count of packets with PSH flag set",
    "dbField2": "tcpflags.psh",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.psh` |
| **友好名称** | TCP Flag PSH |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.psh |
| **帮助信息** | Count of packets with PSH flag set |
| **源文件** | capture/packet.c |

---

### tcpflags.rst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.rst",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag RST",
    "group": "general",
    "help": "Count of packets with RST flag set",
    "dbField2": "tcpflags.rst",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.rst` |
| **友好名称** | TCP Flag RST |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.rst |
| **帮助信息** | Count of packets with RST flag set |
| **源文件** | capture/packet.c |

---

### tcpflags.syn

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.syn",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag SYN",
    "group": "general",
    "help": "Count of packets with SYN and no ACK flag set",
    "dbField2": "tcpflags.syn",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.syn` |
| **友好名称** | TCP Flag SYN |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.syn |
| **帮助信息** | Count of packets with SYN and no ACK flag set |
| **源文件** | capture/packet.c |

---

### tcpflags.syn-ack

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.syn-ack",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag SYN-ACK",
    "group": "general",
    "help": "Count of packets with SYN and ACK flag set",
    "dbField2": "tcpflags.syn-ack",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.syn-ack` |
| **友好名称** | TCP Flag SYN-ACK |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.syn-ack |
| **帮助信息** | Count of packets with SYN and ACK flag set |
| **源文件** | capture/packet.c |

---

### tcpflags.urg

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpflags.urg",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Flag URG",
    "group": "general",
    "help": "Count of packets with URG flag set",
    "dbField2": "tcpflags.urg",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpflags.urg` |
| **友好名称** | TCP Flag URG |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpflags.urg |
| **帮助信息** | Count of packets with URG flag set |
| **源文件** | capture/packet.c |

---

### tcpseq.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpseq.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Dst Seq",
    "group": "general",
    "help": "Destination SYN-ACK sequence number",
    "dbField2": "tcpseq.dst",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpseq.dst` |
| **友好名称** | TCP Dst Seq |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpseq.dst |
| **帮助信息** | Destination SYN-ACK sequence number |
| **源文件** | capture/packet.c |

---

### tcpseq.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tcpseq.src",
  "_score": 1,
  "_source": {
    "friendlyName": "TCP Src Seq",
    "group": "general",
    "help": "Source SYN sequence number",
    "dbField2": "tcpseq.src",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tcpseq.src` |
| **友好名称** | TCP Src Seq |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | tcpseq.src |
| **帮助信息** | Source SYN sequence number |
| **源文件** | capture/packet.c |

---

### tls.sessionid

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.sessionid",
  "_score": 1,
  "_source": {
    "friendlyName": "Src or Dst Session Id",
    "group": "general",
    "help": "Shorthand for tls.sessionid.src or tls.sessionid.dst",
    "dbField2": "tlsidall",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.sessionid` |
| **友好名称** | Src or Dst Session Id |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | tlsidall |
| **帮助信息** | Shorthand for tls.sessionid.src or tls.sessionid.dst |
| **源文件** | capture/parsers/tls.c |

---

### ttl.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ttl.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "TTL Dst",
    "group": "general",
    "help": "Destination IP TTL for first few packets",
    "dbField2": "dstTTL",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ttl.dst` |
| **友好名称** | TTL Dst |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | dstTTL |
| **帮助信息** | Destination IP TTL for first few packets |
| **源文件** | capture/packet.c |

---

### ttl.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ttl.src",
  "_score": 1,
  "_source": {
    "friendlyName": "TTL Src",
    "group": "general",
    "help": "Source IP TTL for first few packets",
    "dbField2": "srcTTL",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ttl.src` |
| **友好名称** | TTL Src |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | srcTTL |
| **帮助信息** | Source IP TTL for first few packets |
| **源文件** | capture/packet.c |

---

### user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "general",
    "help": "External user set for session",
    "dbField2": "user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `user` |
| **友好名称** | User |
| **字段组** | general |
| **数据类型** | termfield |
| **数据库字段** | user |
| **帮助信息** | External user set for session |
| **源文件** | capture/parsers.c |
| **分类** | user |

---

### vlan

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "vlan",
  "_score": 1,
  "_source": {
    "friendlyName": "VLan",
    "group": "general",
    "help": "vlan value",
    "dbField2": "network.vlan.id",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `vlan` |
| **友好名称** | VLan |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | network.vlan.id |
| **帮助信息** | vlan value |
| **源文件** | capture/packet.c |

---

### vni

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "vni",
  "_score": 1,
  "_source": {
    "friendlyName": "VNI",
    "group": "general",
    "help": "vni value",
    "dbField2": "vni",
    "type": "integer",
    "_source_file": "capture/packet.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `vni` |
| **友好名称** | VNI |
| **字段组** | general |
| **数据类型** | integer |
| **数据库字段** | vni |
| **帮助信息** | vni value |
| **源文件** | capture/packet.c |

---

## HTTP 字段组

该组包含 37 个字段。

### host.http

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.http",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname",
    "group": "http",
    "help": "HTTP host header field",
    "dbField2": "http.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.http` |
| **友好名称** | Hostname |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.host |
| **帮助信息** | HTTP host header field |
| **源文件** | capture/parsers/http.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.http

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.http",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname",
    "group": "http",
    "help": "HTTP host header field",
    "dbField2": "http.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/http2.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.http` |
| **友好名称** | Hostname |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.host |
| **帮助信息** | HTTP host header field |
| **源文件** | capture/parsers/http2.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.http.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.http.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "http",
    "help": "HTTP host Tokens header field",
    "dbField2": "http.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.http.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | http |
| **数据类型** | textfield |
| **数据库字段** | http.hostTokens |
| **帮助信息** | HTTP host Tokens header field |
| **源文件** | capture/parsers/http.c |
| **别名** | ['[\\'] |

---

### http.authtype

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.authtype",
  "_score": 1,
  "_source": {
    "friendlyName": "Auth Type",
    "group": "http",
    "help": "HTTP Auth Type",
    "dbField2": "http.authType",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.authtype` |
| **友好名称** | Auth Type |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.authType |
| **帮助信息** | HTTP Auth Type |
| **源文件** | capture/parsers/http.c |

---

### http.bodymagic

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.bodymagic",
  "_score": 1,
  "_source": {
    "friendlyName": "Body Magic",
    "group": "http",
    "help": "The content type of body determined by libfile/magic",
    "dbField2": "http.bodyMagic",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.bodymagic` |
| **友好名称** | Body Magic |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.bodyMagic |
| **帮助信息** | The content type of body determined by libfile/magic |
| **源文件** | capture/parsers/http.c |

---

### http.bodymagic

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.bodymagic",
  "_score": 1,
  "_source": {
    "friendlyName": "Body Magic",
    "group": "http",
    "help": "The content type of body determined by libfile/magic",
    "dbField2": "http.bodyMagic",
    "type": "termfield",
    "_source_file": "capture/parsers/http2.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.bodymagic` |
| **友好名称** | Body Magic |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.bodyMagic |
| **帮助信息** | The content type of body determined by libfile/magic |
| **源文件** | capture/parsers/http2.c |

---

### http.cookie.key

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.cookie.key",
  "_score": 1,
  "_source": {
    "friendlyName": "Cookie Keys",
    "group": "http",
    "help": "The keys to cookies sent up in requests",
    "dbField2": "http.cookieKey",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.cookie.key` |
| **友好名称** | Cookie Keys |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.cookieKey |
| **帮助信息** | The keys to cookies sent up in requests |
| **源文件** | capture/parsers/http.c |

---

### http.cookie.value

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.cookie.value",
  "_score": 1,
  "_source": {
    "friendlyName": "Cookie Values",
    "group": "http",
    "help": "The values to cookies sent up in requests",
    "dbField2": "http.cookieValue",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.cookie.value` |
| **友好名称** | Cookie Values |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.cookieValue |
| **帮助信息** | The values to cookies sent up in requests |
| **源文件** | capture/parsers/http.c |

---

### http.hasheader

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.hasheader",
  "_score": 1,
  "_source": {
    "friendlyName": "Has Src or Dst Header",
    "group": "http",
    "help": "Shorthand for http.hasheader.src or http.hasheader.dst",
    "dbField2": "hhall",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.hasheader` |
| **友好名称** | Has Src or Dst Header |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | hhall |
| **帮助信息** | Shorthand for http.hasheader.src or http.hasheader.dst |
| **源文件** | capture/parsers/http.c |

---

### http.hasheader.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.hasheader.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Has Dst Header",
    "group": "http",
    "help": "Response has header present",
    "dbField2": "http.responseHeader",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.hasheader.dst` |
| **友好名称** | Has Dst Header |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.responseHeader |
| **帮助信息** | Response has header present |
| **源文件** | capture/parsers/http.c |

---

### http.hasheader.dst.value

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.hasheader.dst.value",
  "_score": 1,
  "_source": {
    "friendlyName": "Response Header Values",
    "group": "http",
    "help": "Contains response header values",
    "dbField2": "http.responseHeaderValue",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.hasheader.dst.value` |
| **友好名称** | Response Header Values |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.responseHeaderValue |
| **帮助信息** | Contains response header values |
| **源文件** | capture/parsers/http.c |

---

### http.hasheader.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.hasheader.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Has Src Header",
    "group": "http",
    "help": "Request has header present",
    "dbField2": "http.requestHeader",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.hasheader.src` |
| **友好名称** | Has Src Header |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.requestHeader |
| **帮助信息** | Request has header present |
| **源文件** | capture/parsers/http.c |

---

### http.hasheader.src.value

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.hasheader.src.value",
  "_score": 1,
  "_source": {
    "friendlyName": "Request Header Values",
    "group": "http",
    "help": "Contains request header values",
    "dbField2": "http.requestHeaderValue",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.hasheader.src.value` |
| **友好名称** | Request Header Values |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.requestHeaderValue |
| **帮助信息** | Contains request header values |
| **源文件** | capture/parsers/http.c |

---

### http.hasheader.value

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.hasheader.value",
  "_score": 1,
  "_source": {
    "friendlyName": "Has Value in Src or Dst Header",
    "group": "http",
    "help": "Shorthand for http.hasheader.src.value or http.hasheader.dst.value",
    "dbField2": "hhvalueall",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.hasheader.value` |
| **友好名称** | Has Value in Src or Dst Header |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | hhvalueall |
| **帮助信息** | Shorthand for http.hasheader.src.value or http.hasheader.dst.value |
| **源文件** | capture/parsers/http.c |

---

### http.header.request.field

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.header.request.field",
  "_score": 1,
  "_source": {
    "friendlyName": "Request Header Fields",
    "group": "http",
    "help": "Contains Request header fields",
    "dbField2": "http.requestHeaderField",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.header.request.field` |
| **友好名称** | Request Header Fields |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.requestHeaderField |
| **帮助信息** | Contains Request header fields |
| **源文件** | capture/parsers/http.c |

---

### http.header.response.field

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.header.response.field",
  "_score": 1,
  "_source": {
    "friendlyName": "Response Header fields",
    "group": "http",
    "help": "Contains response header fields",
    "dbField2": "http.responseHeaderField",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.header.response.field` |
| **友好名称** | Response Header fields |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.responseHeaderField |
| **帮助信息** | Contains response header fields |
| **源文件** | capture/parsers/http.c |

---

### http.md5

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.md5",
  "_score": 1,
  "_source": {
    "friendlyName": "Body MD5",
    "group": "http",
    "help": "MD5 of http body response",
    "dbField2": "http.md5",
    "type": "termfield",
    "category": "md5",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.md5` |
| **友好名称** | Body MD5 |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.md5 |
| **帮助信息** | MD5 of http body response |
| **源文件** | capture/parsers/http.c |
| **分类** | md5 |

---

### http.md5

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.md5",
  "_score": 1,
  "_source": {
    "friendlyName": "Body MD5",
    "group": "http",
    "help": "MD5 of http body response",
    "dbField2": "http.md5",
    "type": "termfield",
    "category": "md5",
    "_source_file": "capture/parsers/http2.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.md5` |
| **友好名称** | Body MD5 |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.md5 |
| **帮助信息** | MD5 of http body response |
| **源文件** | capture/parsers/http2.c |
| **分类** | md5 |

---

### http.method

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.method",
  "_score": 1,
  "_source": {
    "friendlyName": "Request Method",
    "group": "http",
    "help": "HTTP Request Method",
    "dbField2": "http.method",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.method` |
| **友好名称** | Request Method |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.method |
| **帮助信息** | HTTP Request Method |
| **源文件** | capture/parsers/http.c |

---

### http.method

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.method",
  "_score": 1,
  "_source": {
    "friendlyName": "Request Method",
    "group": "http",
    "help": "HTTP Request Method",
    "dbField2": "http.method",
    "type": "termfield",
    "_source_file": "capture/parsers/http2.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.method` |
| **友好名称** | Request Method |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.method |
| **帮助信息** | HTTP Request Method |
| **源文件** | capture/parsers/http2.c |

---

### http.reqbody

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.reqbody",
  "_score": 1,
  "_source": {
    "friendlyName": "Request Body",
    "group": "http",
    "help": "HTTP Request Body",
    "dbField2": "http.requestBody",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.reqbody` |
| **友好名称** | Request Body |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.requestBody |
| **帮助信息** | HTTP Request Body |
| **源文件** | capture/parsers/http.c |

---

### http.sha256

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.sha256",
  "_score": 1,
  "_source": {
    "friendlyName": "Body SHA256",
    "group": "http",
    "help": "SHA256 of http body response",
    "dbField2": "http.sha256",
    "type": "termfield",
    "category": "sha256",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.sha256` |
| **友好名称** | Body SHA256 |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.sha256 |
| **帮助信息** | SHA256 of http body response |
| **源文件** | capture/parsers/http.c |
| **分类** | sha256 |

---

### http.sha256

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.sha256",
  "_score": 1,
  "_source": {
    "friendlyName": "Body SHA256",
    "group": "http",
    "help": "SHA256 of http body response",
    "dbField2": "http.sha256",
    "type": "termfield",
    "category": "sha256",
    "_source_file": "capture/parsers/http2.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.sha256` |
| **友好名称** | Body SHA256 |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.sha256 |
| **帮助信息** | SHA256 of http body response |
| **源文件** | capture/parsers/http2.c |
| **分类** | sha256 |

---

### http.statuscode

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.statuscode",
  "_score": 1,
  "_source": {
    "friendlyName": "Status Code",
    "group": "http",
    "help": "Response HTTP numeric status code",
    "dbField2": "http.statuscode",
    "type": "integer",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.statuscode` |
| **友好名称** | Status Code |
| **字段组** | http |
| **数据类型** | integer |
| **数据库字段** | http.statuscode |
| **帮助信息** | Response HTTP numeric status code |
| **源文件** | capture/parsers/http.c |

---

### http.statuscode

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.statuscode",
  "_score": 1,
  "_source": {
    "friendlyName": "Status Code",
    "group": "http",
    "help": "Response HTTP numeric status code",
    "dbField2": "http.statuscode",
    "type": "integer",
    "_source_file": "capture/parsers/http2.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.statuscode` |
| **友好名称** | Status Code |
| **字段组** | http |
| **数据类型** | integer |
| **数据库字段** | http.statuscode |
| **帮助信息** | Response HTTP numeric status code |
| **源文件** | capture/parsers/http2.c |

---

### http.uri

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.uri",
  "_score": 1,
  "_source": {
    "friendlyName": "URI",
    "group": "http",
    "help": "URIs for request",
    "dbField2": "http.uri",
    "type": "termfield",
    "category": "[\\",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.uri` |
| **友好名称** | URI |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.uri |
| **帮助信息** | URIs for request |
| **源文件** | capture/parsers/http.c |
| **分类** | [\ |

---

### http.uri.key

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.uri.key",
  "_score": 1,
  "_source": {
    "friendlyName": "QS Keys",
    "group": "http",
    "help": "Keys from query string of URI",
    "dbField2": "http.key",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.uri.key` |
| **友好名称** | QS Keys |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.key |
| **帮助信息** | Keys from query string of URI |
| **源文件** | capture/parsers/http.c |

---

### http.uri.path

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.uri.path",
  "_score": 1,
  "_source": {
    "friendlyName": "URI Path",
    "group": "http",
    "help": "Path portion of URI",
    "dbField2": "http.path",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.uri.path` |
| **友好名称** | URI Path |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.path |
| **帮助信息** | Path portion of URI |
| **源文件** | capture/parsers/http.c |

---

### http.uri.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.uri.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "URI Tokens",
    "group": "http",
    "help": "URIs Tokens for request",
    "dbField2": "http.uriTokens",
    "type": "textfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.uri.tokens` |
| **友好名称** | URI Tokens |
| **字段组** | http |
| **数据类型** | textfield |
| **数据库字段** | http.uriTokens |
| **帮助信息** | URIs Tokens for request |
| **源文件** | capture/parsers/http.c |

---

### http.uri.value

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.uri.value",
  "_score": 1,
  "_source": {
    "friendlyName": "QS Values",
    "group": "http",
    "help": "Values from query string of URI",
    "dbField2": "http.value",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.uri.value` |
| **友好名称** | QS Values |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.value |
| **帮助信息** | Values from query string of URI |
| **源文件** | capture/parsers/http.c |

---

### http.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "http",
    "help": "HTTP Auth User",
    "dbField2": "http.user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.user` |
| **友好名称** | User |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.user |
| **帮助信息** | HTTP Auth User |
| **源文件** | capture/parsers/http.c |
| **分类** | user |

---

### http.user-agent

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.user-agent",
  "_score": 1,
  "_source": {
    "friendlyName": "Useragent",
    "group": "http",
    "help": "User-Agent Header",
    "dbField2": "http.useragent",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.user-agent` |
| **友好名称** | Useragent |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.useragent |
| **帮助信息** | User-Agent Header |
| **源文件** | capture/parsers/http.c |

---

### http.user-agent.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.user-agent.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Useragent Tokens",
    "group": "http",
    "help": "User-Agent Header Tokens",
    "dbField2": "http.useragentTokens",
    "type": "textfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.user-agent.tokens` |
| **友好名称** | Useragent Tokens |
| **字段组** | http |
| **数据类型** | textfield |
| **数据库字段** | http.useragentTokens |
| **帮助信息** | User-Agent Header Tokens |
| **源文件** | capture/parsers/http.c |

---

### http.version

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.version",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "http",
    "help": "HTTP version number",
    "dbField2": "httpversion",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.version` |
| **友好名称** | Version |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | httpversion |
| **帮助信息** | HTTP version number |
| **源文件** | capture/parsers/http.c |

---

### http.version.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.version.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst Version",
    "group": "http",
    "help": "Response HTTP version number",
    "dbField2": "http.serverVersion",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.version.dst` |
| **友好名称** | Dst Version |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.serverVersion |
| **帮助信息** | Response HTTP version number |
| **源文件** | capture/parsers/http.c |

---

### http.version.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "http.version.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src Version",
    "group": "http",
    "help": "Request HTTP version number",
    "dbField2": "http.clientVersion",
    "type": "termfield",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `http.version.src` |
| **友好名称** | Src Version |
| **字段组** | http |
| **数据类型** | termfield |
| **数据库字段** | http.clientVersion |
| **帮助信息** | Request HTTP version number |
| **源文件** | capture/parsers/http.c |

---

### ip.xff

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.xff",
  "_score": 1,
  "_source": {
    "friendlyName": "XFF IP",
    "group": "http",
    "help": "X-Forwarded-For Header",
    "dbField2": "http.xffIp",
    "type": "ip",
    "category": "ip",
    "_source_file": "capture/parsers/http.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.xff` |
| **友好名称** | XFF IP |
| **字段组** | http |
| **数据类型** | ip |
| **数据库字段** | http.xffIp |
| **帮助信息** | X-Forwarded-For Header |
| **源文件** | capture/parsers/http.c |
| **分类** | ip |

---

## IRC 字段组

该组包含 2 个字段。

### irc.channel

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "irc.channel",
  "_score": 1,
  "_source": {
    "friendlyName": "Channel",
    "group": "irc",
    "help": "Channels joined",
    "dbField2": "irc.channel",
    "type": "termfield",
    "_source_file": "capture/parsers/irc.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `irc.channel` |
| **友好名称** | Channel |
| **字段组** | irc |
| **数据类型** | termfield |
| **数据库字段** | irc.channel |
| **帮助信息** | Channels joined |
| **源文件** | capture/parsers/irc.c |

---

### irc.nick

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "irc.nick",
  "_score": 1,
  "_source": {
    "friendlyName": "Nickname",
    "group": "irc",
    "help": "Nicknames set",
    "dbField2": "irc.nick",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/irc.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `irc.nick` |
| **友好名称** | Nickname |
| **字段组** | irc |
| **数据类型** | termfield |
| **数据库字段** | irc.nick |
| **帮助信息** | Nicknames set |
| **源文件** | capture/parsers/irc.c |
| **分类** | user |

---

## ISIS 字段组

该组包含 1 个字段。

### isis.msgType

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "isis.msgType",
  "_score": 1,
  "_source": {
    "friendlyName": "isis.msgType",
    "group": "isis",
    "help": "ISIS Msg Type field",
    "dbField2": "isis.msgType",
    "type": "termfield",
    "_source_file": "capture/parsers/isis.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `isis.msgType` |
| **友好名称** | isis.msgType |
| **字段组** | isis |
| **数据类型** | termfield |
| **数据库字段** | isis.msgType |
| **帮助信息** | ISIS Msg Type field |
| **源文件** | capture/parsers/isis.c |

---

## KRB5 字段组

该组包含 3 个字段。

### krb5.cname

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "krb5.cname",
  "_score": 1,
  "_source": {
    "friendlyName": "cname",
    "group": "krb5",
    "help": "Kerberos 5 cname",
    "dbField2": "krb5.cname",
    "type": "termfield",
    "_source_file": "capture/parsers/krb5.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `krb5.cname` |
| **友好名称** | cname |
| **字段组** | krb5 |
| **数据类型** | termfield |
| **数据库字段** | krb5.cname |
| **帮助信息** | Kerberos 5 cname |
| **源文件** | capture/parsers/krb5.c |

---

### krb5.realm

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "krb5.realm",
  "_score": 1,
  "_source": {
    "friendlyName": "Realm",
    "group": "krb5",
    "help": "Kerberos 5 Realm",
    "dbField2": "krb5.realm",
    "type": "termfield",
    "_source_file": "capture/parsers/krb5.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `krb5.realm` |
| **友好名称** | Realm |
| **字段组** | krb5 |
| **数据类型** | termfield |
| **数据库字段** | krb5.realm |
| **帮助信息** | Kerberos 5 Realm |
| **源文件** | capture/parsers/krb5.c |

---

### krb5.sname

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "krb5.sname",
  "_score": 1,
  "_source": {
    "friendlyName": "sname",
    "group": "krb5",
    "help": "Kerberos 5 sname",
    "dbField2": "krb5.sname",
    "type": "termfield",
    "_source_file": "capture/parsers/krb5.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `krb5.sname` |
| **友好名称** | sname |
| **字段组** | krb5 |
| **数据类型** | termfield |
| **数据库字段** | krb5.sname |
| **帮助信息** | Kerberos 5 sname |
| **源文件** | capture/parsers/krb5.c |

---

## LDAP 字段组

该组包含 2 个字段。

### ldap.authtype

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ldap.authtype",
  "_score": 1,
  "_source": {
    "friendlyName": "Auth Type",
    "group": "ldap",
    "help": "The auth type of ldap bind",
    "dbField2": "ldap.authtype",
    "type": "termfield",
    "_source_file": "capture/parsers/ldap.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ldap.authtype` |
| **友好名称** | Auth Type |
| **字段组** | ldap |
| **数据类型** | termfield |
| **数据库字段** | ldap.authtype |
| **帮助信息** | The auth type of ldap bind |
| **源文件** | capture/parsers/ldap.c |

---

### ldap.bindname

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ldap.bindname",
  "_score": 1,
  "_source": {
    "friendlyName": "Bind Name",
    "group": "ldap",
    "help": "The bind name of ldap bind",
    "dbField2": "ldap.bindname",
    "type": "termfield",
    "_source_file": "capture/parsers/ldap.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ldap.bindname` |
| **友好名称** | Bind Name |
| **字段组** | ldap |
| **数据类型** | termfield |
| **数据库字段** | ldap.bindname |
| **帮助信息** | The bind name of ldap bind |
| **源文件** | capture/parsers/ldap.c |

---

## MODBUS 字段组

该组包含 5 个字段。

### modbus.exccode

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "modbus.exccode",
  "_score": 1,
  "_source": {
    "friendlyName": "Modbus Exception Code",
    "group": "modbus",
    "help": "Modbus Exception Codes",
    "dbField2": "modbus.exccode",
    "type": "integer",
    "_source_file": "capture/parsers/modbus.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `modbus.exccode` |
| **友好名称** | Modbus Exception Code |
| **字段组** | modbus |
| **数据类型** | integer |
| **数据库字段** | modbus.exccode |
| **帮助信息** | Modbus Exception Codes |
| **源文件** | capture/parsers/modbus.c |

---

### modbus.funccode

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "modbus.funccode",
  "_score": 1,
  "_source": {
    "friendlyName": "Modbus Function Code",
    "group": "modbus",
    "help": "Modbus Function Codes",
    "dbField2": "modbus.funccode",
    "type": "integer",
    "_source_file": "capture/parsers/modbus.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `modbus.funccode` |
| **友好名称** | Modbus Function Code |
| **字段组** | modbus |
| **数据类型** | integer |
| **数据库字段** | modbus.funccode |
| **帮助信息** | Modbus Function Codes |
| **源文件** | capture/parsers/modbus.c |

---

### modbus.protocolid

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "modbus.protocolid",
  "_score": 1,
  "_source": {
    "friendlyName": "Modbus Protocol ID",
    "group": "modbus",
    "help": "Modbus Protocol ID (should always be 0)",
    "dbField2": "modbus.protocolid",
    "type": "integer",
    "_source_file": "capture/parsers/modbus.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `modbus.protocolid` |
| **友好名称** | Modbus Protocol ID |
| **字段组** | modbus |
| **数据类型** | integer |
| **数据库字段** | modbus.protocolid |
| **帮助信息** | Modbus Protocol ID (should always be 0) |
| **源文件** | capture/parsers/modbus.c |

---

### modbus.transactionid

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "modbus.transactionid",
  "_score": 1,
  "_source": {
    "friendlyName": "Modbus Transaction IDs",
    "group": "modbus",
    "help": "Modbus Transaction IDs",
    "dbField2": "modbus.transactionid",
    "type": "integer",
    "_source_file": "capture/parsers/modbus.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `modbus.transactionid` |
| **友好名称** | Modbus Transaction IDs |
| **字段组** | modbus |
| **数据类型** | integer |
| **数据库字段** | modbus.transactionid |
| **帮助信息** | Modbus Transaction IDs |
| **源文件** | capture/parsers/modbus.c |

---

### modbus.unitid

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "modbus.unitid",
  "_score": 1,
  "_source": {
    "friendlyName": "Modbus Unit ID",
    "group": "modbus",
    "help": "Modbus Unit ID",
    "dbField2": "modbus.unitid",
    "type": "integer",
    "_source_file": "capture/parsers/modbus.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `modbus.unitid` |
| **友好名称** | Modbus Unit ID |
| **字段组** | modbus |
| **数据类型** | integer |
| **数据库字段** | modbus.unitid |
| **帮助信息** | Modbus Unit ID |
| **源文件** | capture/parsers/modbus.c |

---

## MYSQL 字段组

该组包含 2 个字段。

### mysql.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "mysql.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "mysql",
    "help": "Mysql user name",
    "dbField2": "mysql.user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/mysql.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `mysql.user` |
| **友好名称** | User |
| **字段组** | mysql |
| **数据类型** | termfield |
| **数据库字段** | mysql.user |
| **帮助信息** | Mysql user name |
| **源文件** | capture/parsers/mysql.c |
| **分类** | user |

---

### mysql.ver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "mysql.ver",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "mysql",
    "help": "Mysql server version string",
    "dbField2": "mysql.version",
    "type": "termfield",
    "_source_file": "capture/parsers/mysql.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `mysql.ver` |
| **友好名称** | Version |
| **字段组** | mysql |
| **数据类型** | termfield |
| **数据库字段** | mysql.version |
| **帮助信息** | Mysql server version string |
| **源文件** | capture/parsers/mysql.c |

---

## ORACLE 字段组

该组包含 4 个字段。

### oracle.host

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "oracle.host",
  "_score": 1,
  "_source": {
    "friendlyName": "Host",
    "group": "oracle",
    "help": "Oracle Host",
    "dbField2": "oracle.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/oracle.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `oracle.host` |
| **友好名称** | Host |
| **字段组** | oracle |
| **数据类型** | termfield |
| **数据库字段** | oracle.host |
| **帮助信息** | Oracle Host |
| **源文件** | capture/parsers/oracle.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### oracle.host.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "oracle.host.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "oracle",
    "help": "Oracle Hostname Tokens",
    "dbField2": "oracle.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/oracle.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `oracle.host.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | oracle |
| **数据类型** | textfield |
| **数据库字段** | oracle.hostTokens |
| **帮助信息** | Oracle Hostname Tokens |
| **源文件** | capture/parsers/oracle.c |
| **别名** | ['[\\'] |

---

### oracle.service

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "oracle.service",
  "_score": 1,
  "_source": {
    "friendlyName": "Service",
    "group": "oracle",
    "help": "Oracle Service",
    "dbField2": "oracle.service",
    "type": "termfield",
    "_source_file": "capture/parsers/oracle.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `oracle.service` |
| **友好名称** | Service |
| **字段组** | oracle |
| **数据类型** | termfield |
| **数据库字段** | oracle.service |
| **帮助信息** | Oracle Service |
| **源文件** | capture/parsers/oracle.c |

---

### oracle.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "oracle.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "oracle",
    "help": "Oracle User",
    "dbField2": "oracle.user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/oracle.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `oracle.user` |
| **友好名称** | User |
| **字段组** | oracle |
| **数据类型** | termfield |
| **数据库字段** | oracle.user |
| **帮助信息** | Oracle User |
| **源文件** | capture/parsers/oracle.c |
| **分类** | user |

---

## POSTGRESQL 字段组

该组包含 3 个字段。

### postgresql.app

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "postgresql.app",
  "_score": 1,
  "_source": {
    "friendlyName": "Application",
    "group": "postgresql",
    "help": "Postgresql application",
    "dbField2": "postgresql.app",
    "type": "termfield",
    "_source_file": "capture/parsers/postgresql.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `postgresql.app` |
| **友好名称** | Application |
| **字段组** | postgresql |
| **数据类型** | termfield |
| **数据库字段** | postgresql.app |
| **帮助信息** | Postgresql application |
| **源文件** | capture/parsers/postgresql.c |

---

### postgresql.db

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "postgresql.db",
  "_score": 1,
  "_source": {
    "friendlyName": "Database",
    "group": "postgresql",
    "help": "Postgresql database",
    "dbField2": "postgresql.db",
    "type": "termfield",
    "_source_file": "capture/parsers/postgresql.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `postgresql.db` |
| **友好名称** | Database |
| **字段组** | postgresql |
| **数据类型** | termfield |
| **数据库字段** | postgresql.db |
| **帮助信息** | Postgresql database |
| **源文件** | capture/parsers/postgresql.c |

---

### postgresql.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "postgresql.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "postgresql",
    "help": "Postgresql user name",
    "dbField2": "postgresql.user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/postgresql.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `postgresql.user` |
| **友好名称** | User |
| **字段组** | postgresql |
| **数据类型** | termfield |
| **数据库字段** | postgresql.user |
| **帮助信息** | Postgresql user name |
| **源文件** | capture/parsers/postgresql.c |
| **分类** | user |

---

## QUIC 字段组

该组包含 4 个字段。

### host.quic

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.quic",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname",
    "group": "quic",
    "help": "QUIC host header field",
    "dbField2": "quic.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/quic.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.quic` |
| **友好名称** | Hostname |
| **字段组** | quic |
| **数据类型** | termfield |
| **数据库字段** | quic.host |
| **帮助信息** | QUIC host header field |
| **源文件** | capture/parsers/quic.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.quic.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.quic.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "quic",
    "help": "QUIC host tokens header field",
    "dbField2": "quic.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/quic.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.quic.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | quic |
| **数据类型** | textfield |
| **数据库字段** | quic.hostTokens |
| **帮助信息** | QUIC host tokens header field |
| **源文件** | capture/parsers/quic.c |
| **别名** | ['[\\'] |

---

### quic.user-agent

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "quic.user-agent",
  "_score": 1,
  "_source": {
    "friendlyName": "User-Agent",
    "group": "quic",
    "help": "User-Agent",
    "dbField2": "quic.useragent",
    "type": "termfield",
    "_source_file": "capture/parsers/quic.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `quic.user-agent` |
| **友好名称** | User-Agent |
| **字段组** | quic |
| **数据类型** | termfield |
| **数据库字段** | quic.useragent |
| **帮助信息** | User-Agent |
| **源文件** | capture/parsers/quic.c |

---

### quic.version

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "quic.version",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "quic",
    "help": "QUIC Version",
    "dbField2": "quic.version",
    "type": "termfield",
    "_source_file": "capture/parsers/quic.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `quic.version` |
| **友好名称** | Version |
| **字段组** | quic |
| **数据类型** | termfield |
| **数据库字段** | quic.version |
| **帮助信息** | QUIC Version |
| **源文件** | capture/parsers/quic.c |

---

## RADIUS 字段组

该组包含 4 个字段。

### radius.endpoint-ip

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "radius.endpoint-ip",
  "_score": 1,
  "_source": {
    "friendlyName": "Endpoint IP",
    "group": "radius",
    "help": "Radius endpoint ip addresses for session",
    "dbField2": "radius.endpointIp",
    "type": "ip",
    "_source_file": "capture/parsers/radius.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `radius.endpoint-ip` |
| **友好名称** | Endpoint IP |
| **字段组** | radius |
| **数据类型** | ip |
| **数据库字段** | radius.endpointIp |
| **帮助信息** | Radius endpoint ip addresses for session |
| **源文件** | capture/parsers/radius.c |

---

### radius.framed-ip

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "radius.framed-ip",
  "_score": 1,
  "_source": {
    "friendlyName": "Framed IP",
    "group": "radius",
    "help": "Radius framed ip addresses for session",
    "dbField2": "radius.framedIp",
    "type": "ip",
    "_source_file": "capture/parsers/radius.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `radius.framed-ip` |
| **友好名称** | Framed IP |
| **字段组** | radius |
| **数据类型** | ip |
| **数据库字段** | radius.framedIp |
| **帮助信息** | Radius framed ip addresses for session |
| **源文件** | capture/parsers/radius.c |

---

### radius.mac

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "radius.mac",
  "_score": 1,
  "_source": {
    "friendlyName": "MAC",
    "group": "radius",
    "help": "Radius Mac",
    "dbField2": "radius.mac",
    "type": "termfield",
    "_source_file": "capture/parsers/radius.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `radius.mac` |
| **友好名称** | MAC |
| **字段组** | radius |
| **数据类型** | termfield |
| **数据库字段** | radius.mac |
| **帮助信息** | Radius Mac |
| **源文件** | capture/parsers/radius.c |

---

### radius.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "radius.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "radius",
    "help": "RADIUS user",
    "dbField2": "radius.user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/radius.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `radius.user` |
| **友好名称** | User |
| **字段组** | radius |
| **数据类型** | termfield |
| **数据库字段** | radius.user |
| **帮助信息** | RADIUS user |
| **源文件** | capture/parsers/radius.c |
| **分类** | user |

---

## SMB 字段组

该组包含 9 个字段。

### host.smb

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.smb",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname",
    "group": "smb",
    "help": "SMB Host name",
    "dbField2": "smb.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.smb` |
| **友好名称** | Hostname |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.host |
| **帮助信息** | SMB Host name |
| **源文件** | capture/parsers/smb.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.smb.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.smb.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "smb",
    "help": "SMB Host Tokens",
    "dbField2": "smb.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.smb.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | smb |
| **数据类型** | textfield |
| **数据库字段** | smb.hostTokens |
| **帮助信息** | SMB Host Tokens |
| **源文件** | capture/parsers/smb.c |
| **别名** | ['[\\'] |

---

### smb.dialect

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.dialect",
  "_score": 1,
  "_source": {
    "friendlyName": "Dialect",
    "group": "smb",
    "help": "SMB Dialect information",
    "dbField2": "smb.dialect",
    "type": "termfield",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.dialect` |
| **友好名称** | Dialect |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.dialect |
| **帮助信息** | SMB Dialect information |
| **源文件** | capture/parsers/smb.c |

---

### smb.domain

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.domain",
  "_score": 1,
  "_source": {
    "friendlyName": "Domain",
    "group": "smb",
    "help": "SMB domain",
    "dbField2": "smb.domain",
    "type": "termfield",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.domain` |
| **友好名称** | Domain |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.domain |
| **帮助信息** | SMB domain |
| **源文件** | capture/parsers/smb.c |

---

### smb.fn

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.fn",
  "_score": 1,
  "_source": {
    "friendlyName": "Filename",
    "group": "smb",
    "help": "SMB files opened, created, deleted",
    "dbField2": "smb.filename",
    "type": "termfield",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.fn` |
| **友好名称** | Filename |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.filename |
| **帮助信息** | SMB files opened, created, deleted |
| **源文件** | capture/parsers/smb.c |

---

### smb.os

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.os",
  "_score": 1,
  "_source": {
    "friendlyName": "OS",
    "group": "smb",
    "help": "SMB OS information",
    "dbField2": "smb.os",
    "type": "termfield",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.os` |
| **友好名称** | OS |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.os |
| **帮助信息** | SMB OS information |
| **源文件** | capture/parsers/smb.c |

---

### smb.share

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.share",
  "_score": 1,
  "_source": {
    "friendlyName": "Share",
    "group": "smb",
    "help": "SMB shares connected to",
    "dbField2": "smb.share",
    "type": "termfield",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.share` |
| **友好名称** | Share |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.share |
| **帮助信息** | SMB shares connected to |
| **源文件** | capture/parsers/smb.c |

---

### smb.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "smb",
    "help": "SMB User",
    "dbField2": "smb.user",
    "type": "termfield",
    "category": "user",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.user` |
| **友好名称** | User |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.user |
| **帮助信息** | SMB User |
| **源文件** | capture/parsers/smb.c |
| **分类** | user |

---

### smb.ver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "smb.ver",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "smb",
    "help": "SMB Version information",
    "dbField2": "smb.version",
    "type": "termfield",
    "_source_file": "capture/parsers/smb.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `smb.ver` |
| **友好名称** | Version |
| **字段组** | smb |
| **数据类型** | termfield |
| **数据库字段** | smb.version |
| **帮助信息** | SMB Version information |
| **源文件** | capture/parsers/smb.c |

---

## SNMP 字段组

该组包含 5 个字段。

### snmp.community

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "snmp.community",
  "_score": 1,
  "_source": {
    "friendlyName": "Community",
    "group": "snmp",
    "help": "SNMP Community",
    "dbField2": "snmp.community",
    "type": "termfield",
    "_source_file": "capture/parsers/snmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `snmp.community` |
| **友好名称** | Community |
| **字段组** | snmp |
| **数据类型** | termfield |
| **数据库字段** | snmp.community |
| **帮助信息** | SNMP Community |
| **源文件** | capture/parsers/snmp.c |

---

### snmp.error

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "snmp.error",
  "_score": 1,
  "_source": {
    "friendlyName": "Error Code",
    "group": "snmp",
    "help": "SNMP Error Code",
    "dbField2": "snmp.error",
    "type": "integer",
    "_source_file": "capture/parsers/snmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `snmp.error` |
| **友好名称** | Error Code |
| **字段组** | snmp |
| **数据类型** | integer |
| **数据库字段** | snmp.error |
| **帮助信息** | SNMP Error Code |
| **源文件** | capture/parsers/snmp.c |

---

### snmp.type

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "snmp.type",
  "_score": 1,
  "_source": {
    "friendlyName": "Type",
    "group": "snmp",
    "help": "SNMP Type",
    "dbField2": "snmp.type",
    "type": "termfield",
    "_source_file": "capture/parsers/snmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `snmp.type` |
| **友好名称** | Type |
| **字段组** | snmp |
| **数据类型** | termfield |
| **数据库字段** | snmp.type |
| **帮助信息** | SNMP Type |
| **源文件** | capture/parsers/snmp.c |

---

### snmp.variable

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "snmp.variable",
  "_score": 1,
  "_source": {
    "friendlyName": "Variable",
    "group": "snmp",
    "help": "SNMP Variable",
    "dbField2": "snmp.variable",
    "type": "termfield",
    "_source_file": "capture/parsers/snmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `snmp.variable` |
| **友好名称** | Variable |
| **字段组** | snmp |
| **数据类型** | termfield |
| **数据库字段** | snmp.variable |
| **帮助信息** | SNMP Variable |
| **源文件** | capture/parsers/snmp.c |

---

### snmp.version

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "snmp.version",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "snmp",
    "help": "SNMP Version",
    "dbField2": "snmp.version",
    "type": "integer",
    "_source_file": "capture/parsers/snmp.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `snmp.version` |
| **友好名称** | Version |
| **字段组** | snmp |
| **数据类型** | integer |
| **数据库字段** | snmp.version |
| **帮助信息** | SNMP Version |
| **源文件** | capture/parsers/snmp.c |

---

## SOCKS 字段组

该组包含 5 个字段。

### host.socks

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.socks",
  "_score": 1,
  "_source": {
    "friendlyName": "Host",
    "group": "socks",
    "help": "SOCKS destination host",
    "dbField2": "socks.host",
    "type": "termfield",
    "category": "host",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/socks.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.socks` |
| **友好名称** | Host |
| **字段组** | socks |
| **数据类型** | termfield |
| **数据库字段** | socks.host |
| **帮助信息** | SOCKS destination host |
| **源文件** | capture/parsers/socks.c |
| **分类** | host |
| **别名** | ['[\\'] |

---

### host.socks.tokens

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "host.socks.tokens",
  "_score": 1,
  "_source": {
    "friendlyName": "Hostname Tokens",
    "group": "socks",
    "help": "SOCKS Hostname Tokens",
    "dbField2": "socks.hostTokens",
    "type": "textfield",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/socks.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `host.socks.tokens` |
| **友好名称** | Hostname Tokens |
| **字段组** | socks |
| **数据类型** | textfield |
| **数据库字段** | socks.hostTokens |
| **帮助信息** | SOCKS Hostname Tokens |
| **源文件** | capture/parsers/socks.c |
| **别名** | ['[\\'] |

---

### ip.socks

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ip.socks",
  "_score": 1,
  "_source": {
    "friendlyName": "IP",
    "group": "socks",
    "help": "SOCKS destination IP",
    "dbField2": "socks.ip",
    "type": "ip",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/socks.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ip.socks` |
| **友好名称** | IP |
| **字段组** | socks |
| **数据类型** | ip |
| **数据库字段** | socks.ip |
| **帮助信息** | SOCKS destination IP |
| **源文件** | capture/parsers/socks.c |
| **别名** | ['[\\'] |

---

### port.socks

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "port.socks",
  "_score": 1,
  "_source": {
    "friendlyName": "Port",
    "group": "socks",
    "help": "SOCKS destination port",
    "dbField2": "socks.port",
    "type": "integer",
    "category": "port",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/socks.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `port.socks` |
| **友好名称** | Port |
| **字段组** | socks |
| **数据类型** | integer |
| **数据库字段** | socks.port |
| **帮助信息** | SOCKS destination port |
| **源文件** | capture/parsers/socks.c |
| **分类** | port |
| **别名** | ['[\\'] |

---

### socks.user

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "socks.user",
  "_score": 1,
  "_source": {
    "friendlyName": "User",
    "group": "socks",
    "help": "SOCKS authenticated user",
    "dbField2": "socks.user",
    "type": "termfield",
    "category": "user",
    "aliases": [
      "[\\"
    ],
    "_source_file": "capture/parsers/socks.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `socks.user` |
| **友好名称** | User |
| **字段组** | socks |
| **数据类型** | termfield |
| **数据库字段** | socks.user |
| **帮助信息** | SOCKS authenticated user |
| **源文件** | capture/parsers/socks.c |
| **分类** | user |
| **别名** | ['[\\'] |

---

## SSH 字段组

该组包含 4 个字段。

### ssh.hassh

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ssh.hassh",
  "_score": 1,
  "_source": {
    "friendlyName": "HASSH",
    "group": "ssh",
    "help": "SSH HASSH field",
    "dbField2": "ssh.hassh",
    "type": "termfield",
    "_source_file": "capture/parsers/ssh.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ssh.hassh` |
| **友好名称** | HASSH |
| **字段组** | ssh |
| **数据类型** | termfield |
| **数据库字段** | ssh.hassh |
| **帮助信息** | SSH HASSH field |
| **源文件** | capture/parsers/ssh.c |

---

### ssh.hasshServer

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ssh.hasshServer",
  "_score": 1,
  "_source": {
    "friendlyName": "HASSH Server",
    "group": "ssh",
    "help": "SSH HASSH Server field",
    "dbField2": "ssh.hasshServer",
    "type": "termfield",
    "_source_file": "capture/parsers/ssh.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ssh.hasshServer` |
| **友好名称** | HASSH Server |
| **字段组** | ssh |
| **数据类型** | termfield |
| **数据库字段** | ssh.hasshServer |
| **帮助信息** | SSH HASSH Server field |
| **源文件** | capture/parsers/ssh.c |

---

### ssh.key

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ssh.key",
  "_score": 1,
  "_source": {
    "friendlyName": "Key",
    "group": "ssh",
    "help": "SSH Key",
    "dbField2": "ssh.key",
    "type": "termfield",
    "_source_file": "capture/parsers/ssh.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ssh.key` |
| **友好名称** | Key |
| **字段组** | ssh |
| **数据类型** | termfield |
| **数据库字段** | ssh.key |
| **帮助信息** | SSH Key |
| **源文件** | capture/parsers/ssh.c |

---

### ssh.ver

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "ssh.ver",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "ssh",
    "help": "SSH Software Version",
    "dbField2": "ssh.version",
    "type": "termfield",
    "_source_file": "capture/parsers/ssh.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `ssh.ver` |
| **友好名称** | Version |
| **字段组** | ssh |
| **数据类型** | termfield |
| **数据库字段** | ssh.version |
| **帮助信息** | SSH Software Version |
| **源文件** | capture/parsers/ssh.c |

---

## SURICATA 字段组

该组包含 7 个字段。

### suricata.action

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.action",
  "_score": 1,
  "_source": {
    "friendlyName": "Action",
    "group": "suricata",
    "help": "Suricata Action",
    "dbField2": "suricata.action",
    "type": "termfield",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.action` |
| **友好名称** | Action |
| **字段组** | suricata |
| **数据类型** | termfield |
| **数据库字段** | suricata.action |
| **帮助信息** | Suricata Action |
| **源文件** | capture/plugins/suricata.c |

---

### suricata.category

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.category",
  "_score": 1,
  "_source": {
    "friendlyName": "Category",
    "group": "suricata",
    "help": "Suricata Category",
    "dbField2": "suricata.category",
    "type": "termfield",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.category` |
| **友好名称** | Category |
| **字段组** | suricata |
| **数据类型** | termfield |
| **数据库字段** | suricata.category |
| **帮助信息** | Suricata Category |
| **源文件** | capture/plugins/suricata.c |

---

### suricata.flowId

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.flowId",
  "_score": 1,
  "_source": {
    "friendlyName": "Flow Id",
    "group": "suricata",
    "help": "Suricata Flow Id",
    "dbField2": "suricata.flowId",
    "type": "termfield",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.flowId` |
| **友好名称** | Flow Id |
| **字段组** | suricata |
| **数据类型** | termfield |
| **数据库字段** | suricata.flowId |
| **帮助信息** | Suricata Flow Id |
| **源文件** | capture/plugins/suricata.c |

---

### suricata.gid

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.gid",
  "_score": 1,
  "_source": {
    "friendlyName": "Gid",
    "group": "suricata",
    "help": "Suricata Gid",
    "dbField2": "suricata.gid",
    "type": "integer",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.gid` |
| **友好名称** | Gid |
| **字段组** | suricata |
| **数据类型** | integer |
| **数据库字段** | suricata.gid |
| **帮助信息** | Suricata Gid |
| **源文件** | capture/plugins/suricata.c |

---

### suricata.severity

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.severity",
  "_score": 1,
  "_source": {
    "friendlyName": "Severity",
    "group": "suricata",
    "help": "Suricata Severity",
    "dbField2": "suricata.severity",
    "type": "integer",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.severity` |
| **友好名称** | Severity |
| **字段组** | suricata |
| **数据类型** | integer |
| **数据库字段** | suricata.severity |
| **帮助信息** | Suricata Severity |
| **源文件** | capture/plugins/suricata.c |

---

### suricata.signature

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.signature",
  "_score": 1,
  "_source": {
    "friendlyName": "Signature",
    "group": "suricata",
    "help": "Suricata Signature",
    "dbField2": "suricata.signature",
    "type": "termfield",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.signature` |
| **友好名称** | Signature |
| **字段组** | suricata |
| **数据类型** | termfield |
| **数据库字段** | suricata.signature |
| **帮助信息** | Suricata Signature |
| **源文件** | capture/plugins/suricata.c |

---

### suricata.signatureId

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "suricata.signatureId",
  "_score": 1,
  "_source": {
    "friendlyName": "Signature Id",
    "group": "suricata",
    "help": "Suricata Signature Id",
    "dbField2": "suricata.signatureId",
    "type": "integer",
    "_source_file": "capture/plugins/suricata.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `suricata.signatureId` |
| **友好名称** | Signature Id |
| **字段组** | suricata |
| **数据类型** | integer |
| **数据库字段** | suricata.signatureId |
| **帮助信息** | Suricata Signature Id |
| **源文件** | capture/plugins/suricata.c |

---

## TLS 字段组

该组包含 12 个字段。

### tls.cipher

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.cipher",
  "_score": 1,
  "_source": {
    "friendlyName": "Cipher",
    "group": "tls",
    "help": "SSL/TLS cipher field",
    "dbField2": "tls.cipher",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.cipher` |
| **友好名称** | Cipher |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.cipher |
| **帮助信息** | SSL/TLS cipher field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja3

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja3",
  "_score": 1,
  "_source": {
    "friendlyName": "JA3",
    "group": "tls",
    "help": "SSL/TLS JA3 field",
    "dbField2": "tls.ja3",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja3` |
| **友好名称** | JA3 |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja3 |
| **帮助信息** | SSL/TLS JA3 field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja3s

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja3s",
  "_score": 1,
  "_source": {
    "friendlyName": "JA3S",
    "group": "tls",
    "help": "SSL/TLS JA3S field",
    "dbField2": "tls.ja3s",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja3s` |
| **友好名称** | JA3S |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja3s |
| **帮助信息** | SSL/TLS JA3S field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja3sstring

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja3sstring",
  "_score": 1,
  "_source": {
    "friendlyName": "JA3SSTR",
    "group": "tls",
    "help": "SSL/TLS JA3S String field",
    "dbField2": "tls.ja3sstring",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja3sstring` |
| **友好名称** | JA3SSTR |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja3sstring |
| **帮助信息** | SSL/TLS JA3S String field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja3string

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja3string",
  "_score": 1,
  "_source": {
    "friendlyName": "JA3STR",
    "group": "tls",
    "help": "SSL/TLS JA3 String field",
    "dbField2": "tls.ja3string",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja3string` |
| **友好名称** | JA3STR |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja3string |
| **帮助信息** | SSL/TLS JA3 String field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja4

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja4",
  "_score": 1,
  "_source": {
    "friendlyName": "JA4",
    "group": "tls",
    "help": "SSL/TLS JA4 field",
    "dbField2": "tls.ja4",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja4` |
| **友好名称** | JA4 |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja4 |
| **帮助信息** | SSL/TLS JA4 field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja4

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja4",
  "_score": 1,
  "_source": {
    "friendlyName": "JA4",
    "group": "tls",
    "help": "SSL/TLS JA4 field",
    "dbField2": "tls.ja4",
    "type": "termfield",
    "_source_file": "capture/parsers/dtls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja4` |
| **友好名称** | JA4 |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja4 |
| **帮助信息** | SSL/TLS JA4 field |
| **源文件** | capture/parsers/dtls.c |

---

### tls.ja4_r

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja4_r",
  "_score": 1,
  "_source": {
    "friendlyName": "JA4_r",
    "group": "tls",
    "help": "SSL/TLS JA4_r field",
    "dbField2": "tls.ja4_r",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja4_r` |
| **友好名称** | JA4_r |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja4_r |
| **帮助信息** | SSL/TLS JA4_r field |
| **源文件** | capture/parsers/tls.c |

---

### tls.ja4_r

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.ja4_r",
  "_score": 1,
  "_source": {
    "friendlyName": "JA4_r",
    "group": "tls",
    "help": "SSL/TLS JA4_r field",
    "dbField2": "tls.ja4_r",
    "type": "termfield",
    "_source_file": "capture/parsers/dtls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.ja4_r` |
| **友好名称** | JA4_r |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.ja4_r |
| **帮助信息** | SSL/TLS JA4_r field |
| **源文件** | capture/parsers/dtls.c |

---

### tls.sessionid.dst

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.sessionid.dst",
  "_score": 1,
  "_source": {
    "friendlyName": "Dst Session Id",
    "group": "tls",
    "help": "SSL/TLS Dst Session Id",
    "dbField2": "tls.dstSessionId",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.sessionid.dst` |
| **友好名称** | Dst Session Id |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.dstSessionId |
| **帮助信息** | SSL/TLS Dst Session Id |
| **源文件** | capture/parsers/tls.c |

---

### tls.sessionid.src

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.sessionid.src",
  "_score": 1,
  "_source": {
    "friendlyName": "Src Session Id",
    "group": "tls",
    "help": "SSL/TLS Src Session Id",
    "dbField2": "tls.srcSessionId",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.sessionid.src` |
| **友好名称** | Src Session Id |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.srcSessionId |
| **帮助信息** | SSL/TLS Src Session Id |
| **源文件** | capture/parsers/tls.c |

---

### tls.version

```json
{
  "_index": "arkime_fields_v30",
  "_type": "_doc",
  "_id": "tls.version",
  "_score": 1,
  "_source": {
    "friendlyName": "Version",
    "group": "tls",
    "help": "SSL/TLS version field",
    "dbField2": "tls.version",
    "type": "termfield",
    "_source_file": "capture/parsers/tls.c"
  }
}
```

| 属性 | 值 |
|------|----|
| **字段ID** | `tls.version` |
| **友好名称** | Version |
| **字段组** | tls |
| **数据类型** | termfield |
| **数据库字段** | tls.version |
| **帮助信息** | SSL/TLS version field |
| **源文件** | capture/parsers/tls.c |

---


# Arkime完整字段映射对应表

本文档提供了`arkime_sessions3-*`索引中会话数据字段与`arkime_fields_v30`索引中字段定义的完整对应关系表。

## 映射规则说明

1. **直接映射**: 字段定义ID直接对应会话数据中的字段路径
2. **嵌套映射**: 会话数据中的嵌套结构对应字段定义中的点分隔ID
3. **协议映射**: 协议特定字段存储在对应的协议对象中

## 完整字段映射表

| 字段定义ID | 友好名称 | 字段组 | 数据类型 | 数据库字段(dbField2) | 会话数据路径 | 说明 |
|------------|----------|--------|----------|---------------------|--------------|------|
| `asset` | Asset | general | termfield | `asset` | `asset` | 直接映射 |
| `communityId` | Community Id | general | termfield | `communityId` | `communityId` | 通用字段 |
| `dscp.dst` | Dst DSCP | general | integer | `dstDscp` | `dscp.dst` | 通用字段 |
| `dscp.src` | Src DSCP | general | integer | `srcDscp` | `dscp.src` | 通用字段 |
| `ethertype` | Ethertype | general | integer | `ethertype` | `ethertype` | 通用字段 |
| `icmp.code` | ICMP Code | general | integer | `icmp.code` | `icmp.code` | 通用字段 |
| `icmp.type` | ICMP Type | general | integer | `icmp.type` | `icmp.type` | 通用字段 |
| `initRTT` | Initial RTT | general | integer | `initRTT` | `initRTT` | 直接映射 |
| `mac` | Src or Dst MAC | general | termfield | `macall` | `mac` | 通用字段 |
| `mac.dst` | Dst MAC | general | termfield | `destination.mac` | `destination.mac` | 直接映射 |
| `mac.src` | Src MAC | general | termfield | `source.mac` | `source.mac` | 直接映射 |
| `oui.dst` | Dst OUI | general | termfield | `dstOui` | `oui.dst` | 通用字段 |
| `oui.src` | Src OUI | general | termfield | `srcOui` | `oui.src` | 通用字段 |
| `outerip` | Src or Dst Outer IP | general | termfield | `outeripall` | `outerip` | 通用字段 |
| `outerip.dst` | Dst Outer IP | general | ip | `dstOuterIp` | `outerip.dst` | 通用字段 (IP地址) |
| `outerip.src` | Src Outer IP | general | ip | `srcOuterIp` | `outerip.src` | 通用字段 (IP地址) |
| `outermac` | Src or Dst Outer MAC | general | termfield | `outermacall` | `outermac` | 通用字段 |
| `outermac.dst` | Dst Outer MAC | general | termfield | `dstOuterMac` | `outermac.dst` | 通用字段 |
| `outermac.src` | Src Outer MAC | general | termfield | `srcOuterMac` | `outermac.src` | 通用字段 |
| `outeroui.dst` | Dst Outer OUI | general | termfield | `dstOuterOui` | `outeroui.dst` | 通用字段 |
| `outeroui.src` | Src Outer OUI | general | termfield | `srcOuterOui` | `outeroui.src` | 通用字段 |
| `packets.dst` | Dst Packets | general | integer | `dstPackets` | `destination.packets` | 直接映射 |
| `packets.src` | Src Packets | general | integer | `srcPackets` | `source.packets` | 直接映射 |
| `protocols` | Protocols | general | termfield | `protocol` | `protocols` | 直接映射 |
| `session.length` | Session Length | general | integer | `length` | `length` | 直接映射 |
| `session.segments` | Session Segments | general | integer | `segmentCnt` | `segments` | 直接映射 |
| `tags` | Tags | general | termfield | `tags` | `tags` | 直接映射 |
| `tcpflags.ack` | TCP Flag ACK | general | integer | `tcpflags.ack` | `tcpflags.ack` | 通用字段 |
| `tcpflags.fin` | TCP Flag FIN | general | integer | `tcpflags.fin` | `tcpflags.fin` | 通用字段 |
| `tcpflags.psh` | TCP Flag PSH | general | integer | `tcpflags.psh` | `tcpflags.psh` | 通用字段 |
| `tcpflags.rst` | TCP Flag RST | general | integer | `tcpflags.rst` | `tcpflags.rst` | 通用字段 |
| `tcpflags.syn` | TCP Flag SYN | general | integer | `tcpflags.syn` | `tcpflags.syn` | 通用字段 |
| `tcpflags.syn-ack` | TCP Flag SYN-ACK | general | integer | `tcpflags.syn-ack` | `tcpflags.syn-ack` | 通用字段 |
| `tcpflags.urg` | TCP Flag URG | general | integer | `tcpflags.urg` | `tcpflags.urg` | 通用字段 |
| `tcpseq.dst` | TCP Dst Seq | general | integer | `tcpseq.dst` | `tcpseq.dst` | 通用字段 |
| `tcpseq.src` | TCP Src Seq | general | integer | `tcpseq.src` | `tcpseq.src` | 通用字段 |
| `tls.sessionid` | Src or Dst Session Id | general | termfield | `tlsidall` | `tls.sessionid` | 通用字段 |
| `ttl.dst` | TTL Dst | general | integer | `dstTTL` | `ttl.dst` | 通用字段 |
| `ttl.src` | TTL Src | general | integer | `srcTTL` | `ttl.src` | 通用字段 |
| `user` | User | general | termfield | `user` | `user` | 直接映射 |
| `vlan` | VLan | general | integer | `network.vlan.id` | `network.vlan.id` | 直接映射 |
| `vni` | VNI | general | integer | `vni` | `vni` | 通用字段 |
| `host.http` | Hostname | http | termfield | `http.host` | `http.host` | HTTP协议字段 |
| `host.http` | Hostname | http | termfield | `http.host` | `http.host` | HTTP协议字段 |
| `host.http.tokens` | Hostname Tokens | http | textfield | `http.hostTokens` | `http.hostTokens` | HTTP协议字段 |
| `http.authtype` | Auth Type | http | termfield | `http.authType` | `http.authType` | HTTP协议字段 |
| `http.bodymagic` | Body Magic | http | termfield | `http.bodyMagic` | `http.bodyMagic` | HTTP协议字段 |
| `http.bodymagic` | Body Magic | http | termfield | `http.bodyMagic` | `http.bodyMagic` | HTTP协议字段 |
| `http.cookie.key` | Cookie Keys | http | termfield | `http.cookieKey` | `http.cookieKey` | HTTP协议字段 |
| `http.cookie.value` | Cookie Values | http | termfield | `http.cookieValue` | `http.cookieValue` | HTTP协议字段 |
| `http.hasheader` | Has Src or Dst Header | http | termfield | `hhall` | `http.hhall` | HTTP协议字段 |
| `http.hasheader.dst` | Has Dst Header | http | termfield | `http.responseHeader` | `http.responseHeader` | HTTP协议字段 |
| `http.hasheader.dst.value` | Response Header Values | http | termfield | `http.responseHeaderValue` | `http.responseHeaderValue` | HTTP协议字段 |
| `http.hasheader.src` | Has Src Header | http | termfield | `http.requestHeader` | `http.requestHeader` | HTTP协议字段 |
| `http.hasheader.src.value` | Request Header Values | http | termfield | `http.requestHeaderValue` | `http.requestHeaderValue` | HTTP协议字段 |
| `http.hasheader.value` | Has Value in Src or Dst Header | http | termfield | `hhvalueall` | `http.hhvalueall` | HTTP协议字段 |
| `http.header.request.field` | Request Header Fields | http | termfield | `http.requestHeaderField` | `http.requestHeaderField` | HTTP协议字段 |
| `http.header.response.field` | Response Header fields | http | termfield | `http.responseHeaderField` | `http.responseHeaderField` | HTTP协议字段 |
| `http.md5` | Body MD5 | http | termfield | `http.md5` | `http.md5` | HTTP协议字段 |
| `http.md5` | Body MD5 | http | termfield | `http.md5` | `http.md5` | HTTP协议字段 |
| `http.method` | Request Method | http | termfield | `http.method` | `http.method` | HTTP协议字段 |
| `http.method` | Request Method | http | termfield | `http.method` | `http.method` | HTTP协议字段 |
| `http.reqbody` | Request Body | http | termfield | `http.requestBody` | `http.requestBody` | HTTP协议字段 |
| `http.sha256` | Body SHA256 | http | termfield | `http.sha256` | `http.sha256` | HTTP协议字段 |
| `http.sha256` | Body SHA256 | http | termfield | `http.sha256` | `http.sha256` | HTTP协议字段 |
| `http.statuscode` | Status Code | http | integer | `http.statuscode` | `http.statuscode` | HTTP协议字段 |
| `http.statuscode` | Status Code | http | integer | `http.statuscode` | `http.statuscode` | HTTP协议字段 |
| `http.uri` | URI | http | termfield | `http.uri` | `http.uri` | HTTP协议字段 |
| `http.uri.key` | QS Keys | http | termfield | `http.key` | `http.key` | HTTP协议字段 |
| `http.uri.path` | URI Path | http | termfield | `http.path` | `http.path` | HTTP协议字段 |
| `http.uri.tokens` | URI Tokens | http | textfield | `http.uriTokens` | `http.uriTokens` | HTTP协议字段 |
| `http.uri.value` | QS Values | http | termfield | `http.value` | `http.value` | HTTP协议字段 |
| `http.user` | User | http | termfield | `http.user` | `http.user` | HTTP协议字段 |
| `http.user-agent` | Useragent | http | termfield | `http.useragent` | `http.useragent` | HTTP协议字段 |
| `http.user-agent.tokens` | Useragent Tokens | http | textfield | `http.useragentTokens` | `http.useragentTokens` | HTTP协议字段 |
| `http.version` | Version | http | termfield | `httpversion` | `http.httpversion` | HTTP协议字段 |
| `http.version.dst` | Dst Version | http | termfield | `http.serverVersion` | `http.serverVersion` | HTTP协议字段 |
| `http.version.src` | Src Version | http | termfield | `http.clientVersion` | `http.clientVersion` | HTTP协议字段 |
| `ip.xff` | XFF IP | http | ip | `http.xffIp` | `http.xffIp` | HTTP协议字段 (IP地址) |
| `dns.answer.caa` | DNS Answer CAA | dns | termfield | `dns.answers.caa` | `dns.answers.caa` | DNS协议字段 |
| `dns.answer.class` | DNS Answer Class | dns | termfield | `dns.answers.class` | `dns.answers.class` | DNS协议字段 |
| `dns.answer.cname` | DNS Answer CNAME | dns | termfield | `dns.answers.cname` | `dns.answers.cname` | DNS协议字段 |
| `dns.answer.cnt` | DNS Answers Cnt | dns | integer | `dns.answersCnt` | `dns.answersCnt` | DNS协议字段 (计数字段) |
| `dns.answer.https` | DNS Answer HTTPS | dns | termfield | `dns.answers.https` | `dns.answers.https` | DNS协议字段 |
| `dns.answer.ip` | DNS Answer IP | dns | ip | `dns.answers.ip` | `dns.answers.ip` | DNS协议字段 (IP地址) |
| `dns.answer.mx` | DNS Answer MX | dns | termfield | `dns.answers.mx` | `dns.answers.mx` | DNS协议字段 |
| `dns.answer.name` | DNS Name | dns | termfield | `dns.answers.name` | `dns.answers.name` | DNS协议字段 |
| `dns.answer.ns` | DNS Answer NS | dns | termfield | `dns.answers.nameserver` | `dns.answers.nameserver` | DNS协议字段 |
| `dns.answer.priority` | DNS Answer Priority | dns | integer | `dns.answers.priority` | `dns.answers.priority` | DNS协议字段 |
| `dns.answer.ttl` | DNS Answer TTL | dns | integer | `dns.answers.ttl` | `dns.answers.ttl` | DNS协议字段 |
| `dns.answer.txt` | DNS Answer TXT | dns | termfield | `dns.answers.txt` | `dns.answers.txt` | DNS协议字段 |
| `dns.answer.type` | DNS Answer Type | dns | termfield | `dns.answers.type` | `dns.answers.type` | DNS协议字段 |
| `dns.header_flags` | DNS Header Flags | dns | termfield | `dns.headerFlags` | `dns.headerFlags` | DNS协议字段 |
| `dns.opcode` | Op Code | dns | termfield | `dns.opcode` | `dns.opcode` | DNS协议字段 |
| `dns.puny` | Puny | dns | termfield | `dns.puny` | `dns.puny` | DNS协议字段 |
| `dns.query.class` | Query Class | dns | termfield | `dns.qc` | `dns.qc` | DNS协议字段 |
| `dns.query.host` | Query Host | dns | termfield | `dns.queryHost` | `dns.queryHost` | DNS协议字段 |
| `dns.query.type` | Query Type | dns | termfield | `dns.qt` | `dns.qt` | DNS协议字段 |
| `dns.status` | Status Code | dns | termfield | `dns.status` | `dns.status` | DNS协议字段 |
| `host.dns` | Host | dns | termfield | `dns.host` | `dns.host` | DNS协议字段 |
| `host.dns.all` | All Host | dns | termfield | `dnshostall` | `dns.dnshostall` | DNS协议字段 |
| `host.dns.mailserver` | MX Host | dns | termfield | `dns.mailserverHost` | `dns.mailserverHost` | DNS协议字段 |
| `host.dns.nameserver` | NS Host | dns | termfield | `dns.nameserverHost` | `dns.nameserverHost` | DNS协议字段 |
| `host.dns.tokens` | Hostname Tokens | dns | textfield | `dns.hostTokens` | `dns.hostTokens` | DNS协议字段 |
| `ip.dns` | IP | dns | ip | `dns.ip` | `dns.ip` | DNS协议字段 (IP地址) |
| `ip.dns.all` | IP | dns | ip | `dnsipall` | `dns.dnsipall` | DNS协议字段 (IP地址) |
| `ip.dns.mailserver` | IP | dns | ip | `dns.mailserverIp` | `dns.mailserverIp` | DNS协议字段 (IP地址) |
| `ip.dns.nameserver` | IP | dns | ip | `dns.nameserverIp` | `dns.nameserverIp` | DNS协议字段 (IP地址) |
| `tls.cipher` | Cipher | tls | termfield | `tls.cipher` | `tls.cipher` | TLS协议字段 |
| `tls.ja3` | JA3 | tls | termfield | `tls.ja3` | `tls.ja3` | TLS协议字段 |
| `tls.ja3s` | JA3S | tls | termfield | `tls.ja3s` | `tls.ja3s` | TLS协议字段 |
| `tls.ja3sstring` | JA3SSTR | tls | termfield | `tls.ja3sstring` | `tls.ja3sstring` | TLS协议字段 |
| `tls.ja3string` | JA3STR | tls | termfield | `tls.ja3string` | `tls.ja3string` | TLS协议字段 |
| `tls.ja4` | JA4 | tls | termfield | `tls.ja4` | `tls.ja4` | TLS协议字段 |
| `tls.ja4` | JA4 | tls | termfield | `tls.ja4` | `tls.ja4` | TLS协议字段 |
| `tls.ja4_r` | JA4_r | tls | termfield | `tls.ja4_r` | `tls.ja4_r` | TLS协议字段 |
| `tls.ja4_r` | JA4_r | tls | termfield | `tls.ja4_r` | `tls.ja4_r` | TLS协议字段 |
| `tls.sessionid.dst` | Dst Session Id | tls | termfield | `tls.dstSessionId` | `tls.dstSessionId` | TLS协议字段 |
| `tls.sessionid.src` | Src Session Id | tls | termfield | `tls.srcSessionId` | `tls.srcSessionId` | TLS协议字段 |
| `tls.version` | Version | tls | termfield | `tls.version` | `tls.version` | TLS协议字段 |
| `email.bodymagic` | Body Magic | email | termfield | `email.bodyMagic` | `email.bodyMagic` | EMAIL协议字段 |
| `email.content-type` | Content-Type | email | termfield | `email.contentType` | `email.contentType` | EMAIL协议字段 |
| `email.dst` | Receiver | email | termfield | `email.dst` | `email.dst` | EMAIL协议字段 |
| `email.file-content-type` | Attach Content-Type | email | termfield | `email.fileContentType` | `email.fileContentType` | EMAIL协议字段 |
| `email.fn` | Filenames | email | termfield | `email.filename` | `email.filename` | EMAIL协议字段 |
| `email.has-header` | Header | email | termfield | `email.header` | `email.header` | EMAIL协议字段 |
| `email.has-header.name` | Header Field | email | termfield | `email.headerField` | `email.headerField` | EMAIL协议字段 |
| `email.has-header.value` | Header Value | email | termfield | `email.headerValue` | `email.headerValue` | EMAIL协议字段 |
| `email.md5` | Attach MD5s | email | termfield | `email.md5` | `email.md5` | EMAIL协议字段 |
| `email.message-id` | Id | email | termfield | `email.id` | `email.id` | EMAIL协议字段 |
| `email.mime-version` | Mime-Version | email | termfield | `email.mimeVersion` | `email.mimeVersion` | EMAIL协议字段 |
| `email.sha256` | Attach SHA256s | email | termfield | `email.sha256` | `email.sha256` | EMAIL协议字段 |
| `email.smtp-hello` | SMTP Hello | email | termfield | `email.smtpHello` | `email.smtpHello` | EMAIL协议字段 |
| `email.src` | Sender | email | termfield | `email.src` | `email.src` | EMAIL协议字段 |
| `email.subject` | Subject | email | termfield | `email.subject` | `email.subject` | EMAIL协议字段 |
| `email.x-mailer` | X-Mailer Header | email | termfield | `email.useragent` | `email.useragent` | EMAIL协议字段 |
| `host.email` | Hostname | email | termfield | `email.host` | `email.host` | EMAIL协议字段 |
| `host.email.tokens` | Hostname Tokens | email | textfield | `email.hostTokens` | `email.hostTokens` | EMAIL协议字段 |
| `ip.email` | IP | email | ip | `email.ip` | `email.ip` | EMAIL协议字段 (IP地址) |
| `cert.alt` | Alt Name | cert | termfield | `cert.alt` | `cert.alt` | CERT协议字段 |
| `cert.cnt` | Cert Cnt | cert | integer | `certCnt` | `cert.certCnt` | CERT协议字段 (计数字段) |
| `cert.curve` | Curve | cert | termfield | `cert.curve` | `cert.curve` | CERT协议字段 |
| `cert.hash` | Hash | cert | termfield | `cert.hash` | `cert.hash` | CERT协议字段 |
| `cert.issuer.cn` | Issuer CN | cert | termfield | `cert.issuerCN` | `cert.issuerCN` | CERT协议字段 |
| `cert.issuer.on` | Issuer ON | cert | termfield | `cert.issuerON` | `cert.issuerON` | CERT协议字段 |
| `cert.issuer.ou` | Issuer Org Unit | cert | termfield | `cert.issuerOU` | `cert.issuerOU` | CERT协议字段 |
| `cert.notafter` | Not After | cert | date | `cert.notAfter` | `cert.notAfter` | CERT协议字段 (时间字段) |
| `cert.notbefore` | Not Before | cert | date | `cert.notBefore` | `cert.notBefore` | CERT协议字段 (时间字段) |
| `cert.publicAlgorithm` | Public Algorithm | cert | termfield | `cert.publicAlgorithm` | `cert.publicAlgorithm` | CERT协议字段 |
| `cert.remainingDays` | Days remaining | cert | integer | `cert.remainingDays` | `cert.remainingDays` | CERT协议字段 |
| `cert.remainingSeconds` | Seconds remaining | cert | integer | `cert.remainingSeconds` | `cert.remainingSeconds` | CERT协议字段 |
| `cert.serial` | Serial Number | cert | termfield | `cert.serial` | `cert.serial` | CERT协议字段 |
| `cert.subject.cn` | Subject CN | cert | termfield | `cert.subjectCN` | `cert.subjectCN` | CERT协议字段 |
| `cert.subject.on` | Subject ON | cert | termfield | `cert.subjectON` | `cert.subjectON` | CERT协议字段 |
| `cert.subject.ou` | Subject Org Unit | cert | termfield | `cert.subjectOU` | `cert.subjectOU` | CERT协议字段 |
| `cert.validfor` | Days Valid For | cert | integer | `cert.validDays` | `cert.validDays` | CERT协议字段 |
| `cert.validforSeconds` | Seconds Valid For | cert | integer | `cert.validSeconds` | `cert.validSeconds` | CERT协议字段 |
| `host.smb` | Hostname | smb | termfield | `smb.host` | `smb.host` | SMB协议字段 |
| `host.smb.tokens` | Hostname Tokens | smb | textfield | `smb.hostTokens` | `smb.hostTokens` | SMB协议字段 |
| `smb.dialect` | Dialect | smb | termfield | `smb.dialect` | `smb.dialect` | SMB协议字段 |
| `smb.domain` | Domain | smb | termfield | `smb.domain` | `smb.domain` | SMB协议字段 |
| `smb.fn` | Filename | smb | termfield | `smb.filename` | `smb.filename` | SMB协议字段 |
| `smb.os` | OS | smb | termfield | `smb.os` | `smb.os` | SMB协议字段 |
| `smb.share` | Share | smb | termfield | `smb.share` | `smb.share` | SMB协议字段 |
| `smb.user` | User | smb | termfield | `smb.user` | `smb.user` | SMB协议字段 |
| `smb.ver` | Version | smb | termfield | `smb.version` | `smb.version` | SMB协议字段 |
| `ssh.hassh` | HASSH | ssh | termfield | `ssh.hassh` | `ssh.hassh` | SSH协议字段 |
| `ssh.hasshServer` | HASSH Server | ssh | termfield | `ssh.hasshServer` | `ssh.hasshServer` | SSH协议字段 |
| `ssh.key` | Key | ssh | termfield | `ssh.key` | `ssh.key` | SSH协议字段 |
| `ssh.ver` | Version | ssh | termfield | `ssh.version` | `ssh.version` | SSH协议字段 |
| `dhcp.host` | Host | dhcp | termfield | `dhcp.host` | `dhcp.host` | DHCP协议字段 |
| `dhcp.host.tokens` | Hostname Tokens | dhcp | textfield | `dhcp.hostTokens` | `dhcp.hostTokens` | DHCP协议字段 |
| `dhcp.id` | Transaction id | dhcp | termfield | `dhcp.id` | `dhcp.id` | DHCP协议字段 |
| `dhcp.mac` | Client MAC | dhcp | termfield | `dhcp.mac` | `dhcp.mac` | DHCP协议字段 |
| `dhcp.oui` | Client OUI | dhcp | termfield | `dhcp.oui` | `dhcp.oui` | DHCP协议字段 |
| `dhcp.type` | Type | dhcp | termfield | `dhcp.type` | `dhcp.type` | DHCP协议字段 |
| `host.socks` | Host | socks | termfield | `socks.host` | `socks.host` | SOCKS协议字段 |
| `host.socks.tokens` | Hostname Tokens | socks | textfield | `socks.hostTokens` | `socks.hostTokens` | SOCKS协议字段 |
| `ip.socks` | IP | socks | ip | `socks.ip` | `socks.ip` | SOCKS协议字段 (IP地址) |
| `port.socks` | Port | socks | integer | `socks.port` | `socks.port` | SOCKS协议字段 |
| `socks.user` | User | socks | termfield | `socks.user` | `socks.user` | SOCKS协议字段 |
| `modbus.exccode` | Modbus Exception Code | modbus | integer | `modbus.exccode` | `modbus.exccode` | MODBUS协议字段 |
| `modbus.funccode` | Modbus Function Code | modbus | integer | `modbus.funccode` | `modbus.funccode` | MODBUS协议字段 |
| `modbus.protocolid` | Modbus Protocol ID | modbus | integer | `modbus.protocolid` | `modbus.protocolid` | MODBUS协议字段 |
| `modbus.transactionid` | Modbus Transaction IDs | modbus | integer | `modbus.transactionid` | `modbus.transactionid` | MODBUS协议字段 |
| `modbus.unitid` | Modbus Unit ID | modbus | integer | `modbus.unitid` | `modbus.unitid` | MODBUS协议字段 |
| `snmp.community` | Community | snmp | termfield | `snmp.community` | `snmp.community` | SNMP协议字段 |
| `snmp.error` | Error Code | snmp | integer | `snmp.error` | `snmp.error` | SNMP协议字段 |
| `snmp.type` | Type | snmp | termfield | `snmp.type` | `snmp.type` | SNMP协议字段 |
| `snmp.variable` | Variable | snmp | termfield | `snmp.variable` | `snmp.variable` | SNMP协议字段 |
| `snmp.version` | Version | snmp | integer | `snmp.version` | `snmp.version` | SNMP协议字段 |
| `host.quic` | Hostname | quic | termfield | `quic.host` | `quic.host` | QUIC协议字段 |
| `host.quic.tokens` | Hostname Tokens | quic | textfield | `quic.hostTokens` | `quic.hostTokens` | QUIC协议字段 |
| `quic.user-agent` | User-Agent | quic | termfield | `quic.useragent` | `quic.useragent` | QUIC协议字段 |
| `quic.version` | Version | quic | termfield | `quic.version` | `quic.version` | QUIC协议字段 |
| `radius.endpoint-ip` | Endpoint IP | radius | ip | `radius.endpointIp` | `radius.endpointIp` | RADIUS协议字段 (IP地址) |
| `radius.framed-ip` | Framed IP | radius | ip | `radius.framedIp` | `radius.framedIp` | RADIUS协议字段 (IP地址) |
| `radius.mac` | MAC | radius | termfield | `radius.mac` | `radius.mac` | RADIUS协议字段 |
| `radius.user` | User | radius | termfield | `radius.user` | `radius.user` | RADIUS协议字段 |
| `oracle.host` | Host | oracle | termfield | `oracle.host` | `oracle.host` | ORACLE协议字段 |
| `oracle.host.tokens` | Hostname Tokens | oracle | textfield | `oracle.hostTokens` | `oracle.hostTokens` | ORACLE协议字段 |
| `oracle.service` | Service | oracle | termfield | `oracle.service` | `oracle.service` | ORACLE协议字段 |
| `oracle.user` | User | oracle | termfield | `oracle.user` | `oracle.user` | ORACLE协议字段 |
| `postgresql.app` | Application | postgresql | termfield | `postgresql.app` | `postgresql.app` | POSTGRESQL协议字段 |
| `postgresql.db` | Database | postgresql | termfield | `postgresql.db` | `postgresql.db` | POSTGRESQL协议字段 |
| `postgresql.user` | User | postgresql | termfield | `postgresql.user` | `postgresql.user` | POSTGRESQL协议字段 |
| `mysql.user` | User | mysql | termfield | `mysql.user` | `mysql.user` | MYSQL协议字段 |
| `mysql.ver` | Version | mysql | termfield | `mysql.version` | `mysql.version` | MYSQL协议字段 |
| `krb5.cname` | cname | krb5 | termfield | `krb5.cname` | `krb5.cname` | KRB5协议字段 |
| `krb5.realm` | Realm | krb5 | termfield | `krb5.realm` | `krb5.realm` | KRB5协议字段 |
| `krb5.sname` | sname | krb5 | termfield | `krb5.sname` | `krb5.sname` | KRB5协议字段 |
| `ldap.authtype` | Auth Type | ldap | termfield | `ldap.authtype` | `ldap.authtype` | LDAP协议字段 |
| `ldap.bindname` | Bind Name | ldap | termfield | `ldap.bindname` | `ldap.bindname` | LDAP协议字段 |
| `irc.channel` | Channel | irc | termfield | `irc.channel` | `irc.channel` | IRC协议字段 |
| `irc.nick` | Nickname | irc | termfield | `irc.nick` | `irc.nick` | IRC协议字段 |
| `bgp.type` | Type | bgp | termfield | `bgp.type` | `bgp.type` | BGP协议字段 |
| `isis.msgType` | isis.msgType | isis | termfield | `isis.msgType` | `isis.msgType` | ISIS协议字段 |
| `suricata.action` | Action | suricata | termfield | `suricata.action` | `suricata.action` | SURICATA协议字段 |
| `suricata.category` | Category | suricata | termfield | `suricata.category` | `suricata.category` | SURICATA协议字段 |
| `suricata.flowId` | Flow Id | suricata | termfield | `suricata.flowId` | `suricata.flowId` | SURICATA协议字段 |
| `suricata.gid` | Gid | suricata | integer | `suricata.gid` | `suricata.gid` | SURICATA协议字段 |
| `suricata.severity` | Severity | suricata | integer | `suricata.severity` | `suricata.severity` | SURICATA协议字段 |
| `suricata.signature` | Signature | suricata | termfield | `suricata.signature` | `suricata.signature` | SURICATA协议字段 |
| `suricata.signatureId` | Signature Id | suricata | integer | `suricata.signatureId` | `suricata.signatureId` | SURICATA协议字段 |


## 映射汇总统计

### 总体统计
- **字段定义总数**: 224
- **字段组数**: 23
- **支持的协议数**: 22

### 按组统计
- **general**: 42 个字段
- **http**: 37 个字段
- **dns**: 29 个字段
- **email**: 19 个字段
- **cert**: 18 个字段
- **tls**: 12 个字段
- **smb**: 9 个字段
- **suricata**: 7 个字段
- **dhcp**: 6 个字段
- **socks**: 5 个字段
- **modbus**: 5 个字段
- **snmp**: 5 个字段
- **quic**: 4 个字段
- **ssh**: 4 个字段
- **radius**: 4 个字段
- **oracle**: 4 个字段
- **postgresql**: 3 个字段
- **krb5**: 3 个字段
- **mysql**: 2 个字段
- **ldap**: 2 个字段
- **irc**: 2 个字段
- **bgp**: 1 个字段
- **isis**: 1 个字段

### 字段类型统计
- **termfield**: 156 个字段
- **integer**: 44 个字段
- **ip**: 12 个字段
- **textfield**: 10 个字段
- **date**: 2 个字段

## 使用指南

### 1. 查找字段映射
根据字段定义ID在上表中查找对应的会话数据路径。

### 2. 构建查询
```json
{
  "query": {
    "term": {
      "会话数据路径": "查询值"
    }
  }
}
```

### 3. 字段聚合
```json
{
  "aggs": {
    "field_agg": {
      "terms": {
        "field": "会话数据路径"
      }
    }
  }
}
```

### 4. 常用映射示例
- HTTP方法: `http.method` → `http.method`
- 源IP: `ip.src` → `source.ip`
- DNS主机: `host.dns` → `dns.host`
- TLS证书: `cert.*` → `cert.*`

---

*本表格包含了所有224个字段定义的完整映射关系，可作为开发和查询的参考手册。*

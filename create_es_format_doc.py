#!/usr/bin/env python3
"""
创建符合要求的Elasticsearch格式文档
"""

import json
from collections import defaultdict

def load_and_format_fields():
    """加载字段并格式化为要求的格式"""
    
    with open('arkime_fields_documentation.json', 'r', encoding='utf-8') as f:
        fields = json.load(f)
    
    # 按组分类
    grouped = defaultdict(list)
    for field in fields:
        group = field['_source']['group']
        grouped[group].append(field)
    
    # 创建最终文档
    doc_content = """# Arkime字段注册完整统计文档

本文档统计了Arkime代码库中所有通过`arkime_field_define`函数注册的字段，按照Elasticsearch文档格式整理。

## 总体统计

- **总字段数**: {total_fields}
- **字段组数**: {total_groups}

## 字段组统计

{group_stats}

## 完整字段列表

以下是所有字段的Elasticsearch文档格式：

```json
[
{field_list}
]
```

## 按组分类的字段

{grouped_fields}

---

*本文档由自动化脚本生成，包含了Arkime代码库中所有arkime_field_define调用的完整信息。*
""".format(
        total_fields=len(fields),
        total_groups=len(grouped),
        group_stats=generate_group_stats(grouped),
        field_list=generate_field_list(fields),
        grouped_fields=generate_grouped_fields(grouped)
    )
    
    return doc_content

def generate_group_stats(grouped):
    """生成组统计信息"""
    stats = []
    for group, group_fields in sorted(grouped.items(), key=lambda x: len(x[1]), reverse=True):
        stats.append(f"- **{group}**: {len(group_fields)} 个字段")
    return "\n".join(stats)

def generate_field_list(fields):
    """生成字段列表"""
    field_list = []
    for i, field in enumerate(fields):
        # 移除源文件信息，只保留ES需要的字段
        clean_field = {
            "_index": field["_index"],
            "_type": field["_type"],
            "_id": field["_id"],
            "_score": field["_score"],
            "_source": {k: v for k, v in field["_source"].items() if k != "_source_file"}
        }
        
        field_json = json.dumps(clean_field, indent=2, ensure_ascii=False)
        if i < len(fields) - 1:
            field_json += ","
        field_list.append(field_json)
    
    return "\n".join(field_list)

def generate_grouped_fields(grouped):
    """生成按组分类的字段"""
    sections = []
    
    for group, group_fields in sorted(grouped.items()):
        section = f"### {group.upper()} 组 ({len(group_fields)} 个字段)\n\n"
        
        for field in sorted(group_fields, key=lambda x: x['_id']):
            clean_field = {
                "_index": field["_index"],
                "_type": field["_type"],
                "_id": field["_id"],
                "_score": field["_score"],
                "_source": {k: v for k, v in field["_source"].items() if k != "_source_file"}
            }
            
            section += f"#### {field['_id']}\n\n"
            section += "```json\n"
            section += json.dumps(clean_field, indent=2, ensure_ascii=False)
            section += "\n```\n\n"
        
        sections.append(section)
    
    return "\n".join(sections)

def main():
    """主函数"""
    print("正在创建Elasticsearch格式文档...")
    
    doc_content = load_and_format_fields()
    
    # 保存文档
    with open('Arkime_Fields_ES_Format_Documentation.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("Elasticsearch格式文档已保存到: Arkime_Fields_ES_Format_Documentation.md")
    
    # 同时创建纯JSON文件
    with open('arkime_fields_documentation.json', 'r', encoding='utf-8') as f:
        fields = json.load(f)
    
    # 清理字段，移除源文件信息
    clean_fields = []
    for field in fields:
        clean_field = {
            "_index": field["_index"],
            "_type": field["_type"],
            "_id": field["_id"],
            "_score": field["_score"],
            "_source": {k: v for k, v in field["_source"].items() if k != "_source_file"}
        }
        clean_fields.append(clean_field)
    
    with open('arkime_fields_clean.json', 'w', encoding='utf-8') as f:
        json.dump(clean_fields, f, indent=2, ensure_ascii=False)
    
    print("清理后的JSON文件已保存到: arkime_fields_clean.json")
    print(f"总共处理了 {len(clean_fields)} 个字段定义")

if __name__ == "__main__":
    main()

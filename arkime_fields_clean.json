[{"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.flowId", "_score": 1, "_source": {"friendlyName": "Flow Id", "group": "suricata", "help": "Suricata Flow Id", "dbField2": "suricata.flowId", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.action", "_score": 1, "_source": {"friendlyName": "Action", "group": "suricata", "help": "Suricata Action", "dbField2": "suricata.action", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.signature", "_score": 1, "_source": {"friendlyName": "Signature", "group": "suricata", "help": "Suricata Signature", "dbField2": "suricata.signature", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.category", "_score": 1, "_source": {"friendlyName": "Category", "group": "suricata", "help": "Suricata Category", "dbField2": "suricata.category", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.gid", "_score": 1, "_source": {"friendlyName": "Gid", "group": "suricata", "help": "Suricata <PERSON>", "dbField2": "suricata.gid", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.signatureId", "_score": 1, "_source": {"friendlyName": "Signature Id", "group": "suricata", "help": "Suricata Signature Id", "dbField2": "suricata.signatureId", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "suricata.severity", "_score": 1, "_source": {"friendlyName": "Severity", "group": "suricata", "help": "Suricata Severity", "dbField2": "suricata.severity", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "session.segments", "_score": 1, "_source": {"friendlyName": "Session Segments", "group": "general", "help": "Number of segments in session so far", "dbField2": "segmentCnt", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "session.length", "_score": 1, "_source": {"friendlyName": "Session Length", "group": "general", "help": "Session Length in milliseconds so far", "dbField2": "length", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "user", "_score": 1, "_source": {"friendlyName": "User", "group": "general", "help": "External user set for session", "dbField2": "user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tags", "_score": 1, "_source": {"friendlyName": "Tags", "group": "general", "help": "Tags set for session", "dbField2": "tags", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "asset", "_score": 1, "_source": {"friendlyName": "<PERSON><PERSON>", "group": "general", "help": "Asset name", "dbField2": "asset", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.dns", "_score": 1, "_source": {"friendlyName": "IP", "group": "dns", "help": "IP from DNS result", "dbField2": "dns.ip", "type": "ip", "category": "ip", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.dns.nameserver", "_score": 1, "_source": {"friendlyName": "IP", "group": "dns", "help": "IPs for nameservers", "dbField2": "dns.nameserverIp", "type": "ip", "category": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.dns.mailserver", "_score": 1, "_source": {"friendlyName": "IP", "group": "dns", "help": "IPs for mailservers", "dbField2": "dns.mailserverIp", "type": "ip", "category": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.dns.all", "_score": 1, "_source": {"friendlyName": "IP", "group": "dns", "help": "Shorthand for ip.dns or ip.dns.nameserver", "dbField2": "dnsipall", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.dns", "_score": 1, "_source": {"friendlyName": "Host", "group": "dns", "help": "DNS lookup hostname", "dbField2": "dns.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.dns.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "dns", "help": "DNS lookup hostname tokens", "dbField2": "dns.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.dns.nameserver", "_score": 1, "_source": {"friendlyName": "NS Host", "group": "dns", "help": "Hostnames for Name Server", "dbField2": "dns.nameserverHost", "type": "termfield", "category": "host"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.dns.mailserver", "_score": 1, "_source": {"friendlyName": "MX Host", "group": "dns", "help": "Hostnames for Mail Exchange Server", "dbField2": "dns.mailserverHost", "type": "termfield", "category": "host"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.dns.all", "_score": 1, "_source": {"friendlyName": "All Host", "group": "dns", "help": "Shorthand for host.dns or host.dns.nameserver", "dbField2": "dnshostall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.puny", "_score": 1, "_source": {"friendlyName": "Puny", "group": "dns", "help": "DNS lookup punycode", "dbField2": "dns.puny", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.status", "_score": 1, "_source": {"friendlyName": "Status Code", "group": "dns", "help": "DNS lookup return code", "dbField2": "dns.status", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.opcode", "_score": 1, "_source": {"friendlyName": "Op Code", "group": "dns", "help": "DNS lookup op code", "dbField2": "dns.opcode", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.query.type", "_score": 1, "_source": {"friendlyName": "Query Type", "group": "dns", "help": "DNS lookup query type", "dbField2": "dns.qt", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.query.class", "_score": 1, "_source": {"friendlyName": "Query Class", "group": "dns", "help": "DNS lookup query class", "dbField2": "dns.qc", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.query.host", "_score": 1, "_source": {"friendlyName": "Query Host", "group": "dns", "help": "DNS Query Name", "dbField2": "dns.queryHost", "type": "termfield", "category": "host"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.cnt", "_score": 1, "_source": {"friendlyName": "DNS Answers Cnt", "group": "dns", "help": "Count of DNS Answers", "dbField2": "dns.answersCnt", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.type", "_score": 1, "_source": {"friendlyName": "DNS Answer Type", "group": "dns", "help": "DNS Answer Type", "dbField2": "dns.answers.type", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.class", "_score": 1, "_source": {"friendlyName": "DNS Answer Class", "group": "dns", "help": "DNS Answer Class", "dbField2": "dns.answers.class", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.ttl", "_score": 1, "_source": {"friendlyName": "DNS Answer TTL", "group": "dns", "help": "DNS Answer TTL", "dbField2": "dns.answers.ttl", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.ip", "_score": 1, "_source": {"friendlyName": "DNS Answer IP", "group": "dns", "help": "DNS Answer IP", "dbField2": "dns.answers.ip", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.cname", "_score": 1, "_source": {"friendlyName": "DNS Answer CNAME", "group": "dns", "help": "DNS Answer CNAME", "dbField2": "dns.answers.cname", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.ns", "_score": 1, "_source": {"friendlyName": "DNS Answer NS", "group": "dns", "help": "DNS Answer NS", "dbField2": "dns.answers.nameserver", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.mx", "_score": 1, "_source": {"friendlyName": "DNS Answer MX", "group": "dns", "help": "DNS Answer MX", "dbField2": "dns.answers.mx", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.priority", "_score": 1, "_source": {"friendlyName": "DNS Answer Priority", "group": "dns", "help": "DNS Answer Priority", "dbField2": "dns.answers.priority", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.https", "_score": 1, "_source": {"friendlyName": "DNS Answer HTTPS", "group": "dns", "help": "DNS Answer HTTPS", "dbField2": "dns.answers.https", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.txt", "_score": 1, "_source": {"friendlyName": "DNS Answer TXT", "group": "dns", "help": "DNS Answer TXT", "dbField2": "dns.answers.txt", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.caa", "_score": 1, "_source": {"friendlyName": "DNS Answer CAA", "group": "dns", "help": "DNS Answer CAA", "dbField2": "dns.answers.caa", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.header_flags", "_score": 1, "_source": {"friendlyName": "DNS Header <PERSON>", "group": "dns", "help": "DNS Header <PERSON>", "dbField2": "dns.headerFlags", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dns.answer.name", "_score": 1, "_source": {"friendlyName": "DNS Name", "group": "dns", "help": "DNS Answer Name", "dbField2": "dns.answers.name", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.http", "_score": 1, "_source": {"friendlyName": "Hostname", "group": "http", "help": "HTTP host header field", "dbField2": "http.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.http.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "http", "help": "HTTP host Tokens header field", "dbField2": "http.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.uri", "_score": 1, "_source": {"friendlyName": "URI", "group": "http", "help": "URIs for request", "dbField2": "http.uri", "type": "termfield", "category": "[\\"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.uri.tokens", "_score": 1, "_source": {"friendlyName": "URI Tokens", "group": "http", "help": "URIs Tokens for request", "dbField2": "http.uriTokens", "type": "textfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.xff", "_score": 1, "_source": {"friendlyName": "XFF IP", "group": "http", "help": "X-Forwarded-<PERSON> <PERSON>er", "dbField2": "http.xffIp", "type": "ip", "category": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.user-agent", "_score": 1, "_source": {"friendlyName": "Useragent", "group": "http", "help": "User-Agent Header", "dbField2": "http.useragent", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.user-agent.tokens", "_score": 1, "_source": {"friendlyName": "Useragent Tokens", "group": "http", "help": "User-Agent <PERSON><PERSON>", "dbField2": "http.useragentTokens", "type": "textfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.hasheader.src", "_score": 1, "_source": {"friendlyName": "<PERSON>", "group": "http", "help": "Request has header present", "dbField2": "http.requestHeader", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.hasheader.dst", "_score": 1, "_source": {"friendlyName": "<PERSON> <PERSON><PERSON> Header", "group": "http", "help": "Response has header present", "dbField2": "http.responseHeader", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.header.request.field", "_score": 1, "_source": {"friendlyName": "Request Header <PERSON>", "group": "http", "help": "Contains Request header fields", "dbField2": "http.requestHeaderField", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.hasheader.src.value", "_score": 1, "_source": {"friendlyName": "Request Header Values", "group": "http", "help": "Contains request header values", "dbField2": "http.requestHeaderValue", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.header.response.field", "_score": 1, "_source": {"friendlyName": "Response Header fields", "group": "http", "help": "Contains response header fields", "dbField2": "http.responseHeaderField", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.hasheader.dst.value", "_score": 1, "_source": {"friendlyName": "Response Header Values", "group": "http", "help": "Contains response header values", "dbField2": "http.responseHeaderValue", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.hasheader", "_score": 1, "_source": {"friendlyName": "<PERSON> or <PERSON><PERSON> Header", "group": "http", "help": "Shorthand for http.hasheader.src or http.hasheader.dst", "dbField2": "hhall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.hasheader.value", "_score": 1, "_source": {"friendlyName": "Has Value in Src or Dst Header", "group": "http", "help": "Shorthand for http.hasheader.src.value or http.hasheader.dst.value", "dbField2": "h<PERSON><PERSON><PERSON><PERSON>", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.md5", "_score": 1, "_source": {"friendlyName": "Body MD5", "group": "http", "help": "MD5 of http body response", "dbField2": "http.md5", "type": "termfield", "category": "md5"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.sha256", "_score": 1, "_source": {"friendlyName": "Body SHA256", "group": "http", "help": "SHA256 of http body response", "dbField2": "http.sha256", "type": "termfield", "category": "sha256"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.version", "_score": 1, "_source": {"friendlyName": "Version", "group": "http", "help": "HTTP version number", "dbField2": "httpversion", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.version.src", "_score": 1, "_source": {"friendlyName": "Src Version", "group": "http", "help": "Request HTTP version number", "dbField2": "http.clientVersion", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.version.dst", "_score": 1, "_source": {"friendlyName": "Dst Version", "group": "http", "help": "Response HTTP version number", "dbField2": "http.serverVersion", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.uri.path", "_score": 1, "_source": {"friendlyName": "URI Path", "group": "http", "help": "Path portion of URI", "dbField2": "http.path", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.uri.key", "_score": 1, "_source": {"friendlyName": "QS Keys", "group": "http", "help": "Keys from query string of URI", "dbField2": "http.key", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.uri.value", "_score": 1, "_source": {"friendlyName": "QS Values", "group": "http", "help": "Values from query string of URI", "dbField2": "http.value", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.cookie.key", "_score": 1, "_source": {"friendlyName": "<PERSON><PERSON>", "group": "http", "help": "The keys to cookies sent up in requests", "dbField2": "http.cookieKey", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.cookie.value", "_score": 1, "_source": {"friendlyName": "Cookie Values", "group": "http", "help": "The values to cookies sent up in requests", "dbField2": "http.cookieValue", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.method", "_score": 1, "_source": {"friendlyName": "Request Method", "group": "http", "help": "HTTP Request Method", "dbField2": "http.method", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.bodymagic", "_score": 1, "_source": {"friendlyName": "Body Magic", "group": "http", "help": "The content type of body determined by libfile/magic", "dbField2": "http.bodyMagic", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.user", "_score": 1, "_source": {"friendlyName": "User", "group": "http", "help": "HTTP Auth User", "dbField2": "http.user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.authtype", "_score": 1, "_source": {"friendlyName": "Auth Type", "group": "http", "help": "HTTP Auth Type", "dbField2": "http.authType", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.statuscode", "_score": 1, "_source": {"friendlyName": "Status Code", "group": "http", "help": "Response HTTP numeric status code", "dbField2": "http.statuscode", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.reqbody", "_score": 1, "_source": {"friendlyName": "Request Body", "group": "http", "help": "HTTP Request Body", "dbField2": "http.requestBody", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "postgresql.user", "_score": 1, "_source": {"friendlyName": "User", "group": "postgresql", "help": "Postgresql user name", "dbField2": "postgresql.user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "postgresql.db", "_score": 1, "_source": {"friendlyName": "Database", "group": "postgresql", "help": "Postgresql database", "dbField2": "postgresql.db", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "postgresql.app", "_score": 1, "_source": {"friendlyName": "Application", "group": "postgresql", "help": "Postgresql application", "dbField2": "postgresql.app", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mysql.user", "_score": 1, "_source": {"friendlyName": "User", "group": "mysql", "help": "Mysql user name", "dbField2": "mysql.user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mysql.ver", "_score": 1, "_source": {"friendlyName": "Version", "group": "mysql", "help": "Mysql server version string", "dbField2": "mysql.version", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "bgp.type", "_score": 1, "_source": {"friendlyName": "Type", "group": "bgp", "help": "BGP Type field", "dbField2": "bgp.type", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.quic", "_score": 1, "_source": {"friendlyName": "Hostname", "group": "quic", "help": "QUIC host header field", "dbField2": "quic.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.quic.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "quic", "help": "QUIC host tokens header field", "dbField2": "quic.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "quic.user-agent", "_score": 1, "_source": {"friendlyName": "User-Agent", "group": "quic", "help": "User-Agent", "dbField2": "quic.useragent", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "quic.version", "_score": 1, "_source": {"friendlyName": "Version", "group": "quic", "help": "QUIC Version", "dbField2": "quic.version", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "icmp.type", "_score": 1, "_source": {"friendlyName": "ICMP Type", "group": "general", "help": "ICMP type field values", "dbField2": "icmp.type", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "icmp.code", "_score": 1, "_source": {"friendlyName": "ICMP Code", "group": "general", "help": "ICMP code field values", "dbField2": "icmp.code", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.socks", "_score": 1, "_source": {"friendlyName": "IP", "group": "socks", "help": "SOCKS destination IP", "dbField2": "socks.ip", "type": "ip", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.socks", "_score": 1, "_source": {"friendlyName": "Host", "group": "socks", "help": "SOCKS destination host", "dbField2": "socks.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.socks.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "socks", "help": "SOCKS Hostname Tokens", "dbField2": "socks.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "port.socks", "_score": 1, "_source": {"friendlyName": "Port", "group": "socks", "help": "SOCKS destination port", "dbField2": "socks.port", "type": "integer", "category": "port", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "socks.user", "_score": 1, "_source": {"friendlyName": "User", "group": "socks", "help": "SOCKS authenticated user", "dbField2": "socks.user", "type": "termfield", "category": "user", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "modbus.unitid", "_score": 1, "_source": {"friendlyName": "Modbus Unit ID", "group": "modbus", "help": "Modbus Unit ID", "dbField2": "modbus.unitid", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "modbus.transactionid", "_score": 1, "_source": {"friendlyName": "Modbus Transaction IDs", "group": "modbus", "help": "Modbus Transaction IDs", "dbField2": "modbus.transactionid", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "modbus.protocolid", "_score": 1, "_source": {"friendlyName": "Modbus Protocol ID", "group": "modbus", "help": "Modbus Protocol ID (should always be 0)", "dbField2": "modbus.protocolid", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "modbus.funccode", "_score": 1, "_source": {"friendlyName": "Modbus Function Code", "group": "modbus", "help": "Modbus Function Codes", "dbField2": "modbus.funccode", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "modbus.exccode", "_score": 1, "_source": {"friendlyName": "Modbus Exception Code", "group": "modbus", "help": "Modbus Exception Codes", "dbField2": "modbus.exccode", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "isis.msgType", "_score": 1, "_source": {"friendlyName": "isis.msgType", "group": "isis", "help": "ISIS Msg Type field", "dbField2": "isis.msgType", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.method", "_score": 1, "_source": {"friendlyName": "Request Method", "group": "http", "help": "HTTP Request Method", "dbField2": "http.method", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.statuscode", "_score": 1, "_source": {"friendlyName": "Status Code", "group": "http", "help": "Response HTTP numeric status code", "dbField2": "http.statuscode", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.http", "_score": 1, "_source": {"friendlyName": "Hostname", "group": "http", "help": "HTTP host header field", "dbField2": "http.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.bodymagic", "_score": 1, "_source": {"friendlyName": "Body Magic", "group": "http", "help": "The content type of body determined by libfile/magic", "dbField2": "http.bodyMagic", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.md5", "_score": 1, "_source": {"friendlyName": "Body MD5", "group": "http", "help": "MD5 of http body response", "dbField2": "http.md5", "type": "termfield", "category": "md5"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "http.sha256", "_score": 1, "_source": {"friendlyName": "Body SHA256", "group": "http", "help": "SHA256 of http body response", "dbField2": "http.sha256", "type": "termfield", "category": "sha256"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.version", "_score": 1, "_source": {"friendlyName": "Version", "group": "tls", "help": "SSL/TLS version field", "dbField2": "tls.version", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.cipher", "_score": 1, "_source": {"friendlyName": "Cipher", "group": "tls", "help": "SSL/TLS cipher field", "dbField2": "tls.cipher", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja3", "_score": 1, "_source": {"friendlyName": "JA3", "group": "tls", "help": "SSL/TLS JA3 field", "dbField2": "tls.ja3", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja4", "_score": 1, "_source": {"friendlyName": "JA4", "group": "tls", "help": "SSL/TLS JA4 field", "dbField2": "tls.ja4", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja4_r", "_score": 1, "_source": {"friendlyName": "JA4_r", "group": "tls", "help": "SSL/TLS JA4_r field", "dbField2": "tls.ja4_r", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja3s", "_score": 1, "_source": {"friendlyName": "JA3S", "group": "tls", "help": "SSL/TLS JA3S field", "dbField2": "tls.ja3s", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.sessionid.dst", "_score": 1, "_source": {"friendlyName": "Dst Session Id", "group": "tls", "help": "SSL/TLS Dst Session Id", "dbField2": "tls.dstSessionId", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.sessionid.src", "_score": 1, "_source": {"friendlyName": "Src Session Id", "group": "tls", "help": "SSL/TLS Src Session Id", "dbField2": "tls.srcSessionId", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.sessionid", "_score": 1, "_source": {"friendlyName": "Src or Dst Session Id", "group": "general", "help": "Shorthand for tls.sessionid.src or tls.sessionid.dst", "dbField2": "tlsidall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja3sstring", "_score": 1, "_source": {"friendlyName": "JA3SSTR", "group": "tls", "help": "SSL/TLS JA3S String field", "dbField2": "tls.ja3sstring", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja3string", "_score": 1, "_source": {"friendlyName": "JA3STR", "group": "tls", "help": "SSL/TLS JA3 String field", "dbField2": "tls.ja3string", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ssh.ver", "_score": 1, "_source": {"friendlyName": "Version", "group": "ssh", "help": "SSH Software Version", "dbField2": "ssh.version", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ssh.key", "_score": 1, "_source": {"friendlyName": "Key", "group": "ssh", "help": "SSH Key", "dbField2": "ssh.key", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ssh.hassh", "_score": 1, "_source": {"friendlyName": "HASSH", "group": "ssh", "help": "SSH HASSH field", "dbField2": "ssh.hassh", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ssh.hasshServer", "_score": 1, "_source": {"friendlyName": "HASSH Server", "group": "ssh", "help": "SSH HASSH Server field", "dbField2": "ssh.hasshServer", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.share", "_score": 1, "_source": {"friendlyName": "Share", "group": "smb", "help": "SMB shares connected to", "dbField2": "smb.share", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.fn", "_score": 1, "_source": {"friendlyName": "Filename", "group": "smb", "help": "SMB files opened, created, deleted", "dbField2": "smb.filename", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.os", "_score": 1, "_source": {"friendlyName": "OS", "group": "smb", "help": "SMB OS information", "dbField2": "smb.os", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.domain", "_score": 1, "_source": {"friendlyName": "Domain", "group": "smb", "help": "SMB domain", "dbField2": "smb.domain", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.ver", "_score": 1, "_source": {"friendlyName": "Version", "group": "smb", "help": "SMB Version information", "dbField2": "smb.version", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.dialect", "_score": 1, "_source": {"friendlyName": "Dialect", "group": "smb", "help": "SMB Dialect information", "dbField2": "smb.dialect", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "smb.user", "_score": 1, "_source": {"friendlyName": "User", "group": "smb", "help": "SMB User", "dbField2": "smb.user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.smb", "_score": 1, "_source": {"friendlyName": "Hostname", "group": "smb", "help": "SMB Host name", "dbField2": "smb.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.smb.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "smb", "help": "SMB Host Tokens", "dbField2": "smb.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "snmp.version", "_score": 1, "_source": {"friendlyName": "Version", "group": "snmp", "help": "SNMP Version", "dbField2": "snmp.version", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "snmp.community", "_score": 1, "_source": {"friendlyName": "Community", "group": "snmp", "help": "SNMP Community", "dbField2": "snmp.community", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "snmp.error", "_score": 1, "_source": {"friendlyName": "Error Code", "group": "snmp", "help": "SNMP Error Code", "dbField2": "snmp.error", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "snmp.variable", "_score": 1, "_source": {"friendlyName": "Variable", "group": "snmp", "help": "SNMP Variable", "dbField2": "snmp.variable", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "snmp.type", "_score": 1, "_source": {"friendlyName": "Type", "group": "snmp", "help": "SNMP Type", "dbField2": "snmp.type", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "radius.user", "_score": 1, "_source": {"friendlyName": "User", "group": "radius", "help": "RADIUS user", "dbField2": "radius.user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "radius.mac", "_score": 1, "_source": {"friendlyName": "MAC", "group": "radius", "help": "<PERSON><PERSON>", "dbField2": "radius.mac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "radius.endpoint-ip", "_score": 1, "_source": {"friendlyName": "Endpoint IP", "group": "radius", "help": "Radius endpoint ip addresses for session", "dbField2": "radius.endpointIp", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "radius.framed-ip", "_score": 1, "_source": {"friendlyName": "Framed IP", "group": "radius", "help": "<PERSON><PERSON> framed ip addresses for session", "dbField2": "radius.framedIp", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.cnt", "_score": 1, "_source": {"friendlyName": "Cert <PERSON>nt", "group": "cert", "help": "Count of certificates", "dbField2": "certCnt", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.alt", "_score": 1, "_source": {"friendlyName": "Alt Name", "group": "cert", "help": "Certificate alternative names", "dbField2": "cert.alt", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.serial", "_score": 1, "_source": {"friendlyName": "Serial Number", "group": "cert", "help": "Serial Number", "dbField2": "cert.serial", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.issuer.cn", "_score": 1, "_source": {"friendlyName": "Issuer CN", "group": "cert", "help": "Issuer's common name", "dbField2": "cert.issuerCN", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.subject.cn", "_score": 1, "_source": {"friendlyName": "Subject CN", "group": "cert", "help": "Subject's common name", "dbField2": "cert.subjectCN", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.issuer.on", "_score": 1, "_source": {"friendlyName": "Issuer ON", "group": "cert", "help": "Issuer's organization name", "dbField2": "cert.issuerON", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.subject.on", "_score": 1, "_source": {"friendlyName": "Subject ON", "group": "cert", "help": "Subject's organization name", "dbField2": "cert.subjectON", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.issuer.ou", "_score": 1, "_source": {"friendlyName": "Issuer Org Unit", "group": "cert", "help": "Issuer's organizational unit", "dbField2": "cert.issuerOU", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.subject.ou", "_score": 1, "_source": {"friendlyName": "Subject Org Unit", "group": "cert", "help": "Subject's organizational unit", "dbField2": "cert.subjectOU", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.hash", "_score": 1, "_source": {"friendlyName": "Hash", "group": "cert", "help": "SHA1 hash of entire certificate", "dbField2": "cert.hash", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.notbefore", "_score": 1, "_source": {"friendlyName": "Not Before", "group": "cert", "help": "Certificate is not valid before this date", "dbField2": "cert.notBefore", "type": "date"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.notafter", "_score": 1, "_source": {"friendlyName": "Not After", "group": "cert", "help": "Certificate is not valid after this date", "dbField2": "cert.notAfter", "type": "date"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.validfor", "_score": 1, "_source": {"friendlyName": "Days Valid For", "group": "cert", "help": "Certificate is valid for this many days total", "dbField2": "cert.validDays", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.remainingDays", "_score": 1, "_source": {"friendlyName": "Days remaining", "group": "cert", "help": "Certificate is still valid for this many days", "dbField2": "cert.remainingDays", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.validforSeconds", "_score": 1, "_source": {"friendlyName": "Seconds Valid For", "group": "cert", "help": "Certificate is valid for this many seconds total", "dbField2": "cert.validSeconds", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.remainingSeconds", "_score": 1, "_source": {"friendlyName": "Seconds remaining", "group": "cert", "help": "Certificate is still valid for this many seconds", "dbField2": "cert.remainingSeconds", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.curve", "_score": 1, "_source": {"friendlyName": "Curve", "group": "cert", "help": "Curve Al<PERSON>ithm", "dbField2": "cert.curve", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "cert.publicAlgorithm", "_score": 1, "_source": {"friendlyName": "Public Algorithm", "group": "cert", "help": "Public Key Algorithm", "dbField2": "cert.publicAlgorithm", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.email", "_score": 1, "_source": {"friendlyName": "Hostname", "group": "email", "help": "Email hostnames", "dbField2": "email.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "host.email.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "email", "help": "Email Hostname Tokens", "dbField2": "email.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.x-mailer", "_score": 1, "_source": {"friendlyName": "<PERSON><PERSON><PERSON><PERSON>", "group": "email", "help": "Email X-Mailer header", "dbField2": "email.useragent", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.src", "_score": 1, "_source": {"friendlyName": "Sender", "group": "email", "help": "Email from address", "dbField2": "email.src", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.dst", "_score": 1, "_source": {"friendlyName": "Receiver", "group": "email", "help": "Email to address", "dbField2": "email.dst", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.subject", "_score": 1, "_source": {"friendlyName": "Subject", "group": "email", "help": "Email subject header", "dbField2": "email.subject", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.message-id", "_score": 1, "_source": {"friendlyName": "Id", "group": "email", "help": "Email Message-Id header", "dbField2": "email.id", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.content-type", "_score": 1, "_source": {"friendlyName": "Content-Type", "group": "email", "help": "Email content-type header", "dbField2": "email.contentType", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.mime-version", "_score": 1, "_source": {"friendlyName": "Mime-Version", "group": "email", "help": "<PERSON><PERSON>-<PERSON><PERSON> header", "dbField2": "email.mimeVersion", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.fn", "_score": 1, "_source": {"friendlyName": "Filenames", "group": "email", "help": "Email attachment filenames", "dbField2": "email.filename", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.md5", "_score": 1, "_source": {"friendlyName": "Attach MD5s", "group": "email", "help": "Email attachment MD5s", "dbField2": "email.md5", "type": "termfield", "category": "md5"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.sha256", "_score": 1, "_source": {"friendlyName": "Attach SHA256s", "group": "email", "help": "Email attachment SHA256s", "dbField2": "email.sha256", "type": "termfield", "category": "sha256"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.file-content-type", "_score": 1, "_source": {"friendlyName": "Attach Content-Type", "group": "email", "help": "Email attachment content types", "dbField2": "email.fileContentType", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ip.email", "_score": 1, "_source": {"friendlyName": "IP", "group": "email", "help": "Email IP address", "dbField2": "email.ip", "type": "ip", "category": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.has-header", "_score": 1, "_source": {"friendlyName": "Header", "group": "email", "help": "Email has the header set", "dbField2": "email.header", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.has-header.name", "_score": 1, "_source": {"friendlyName": "Header <PERSON>", "group": "email", "help": "Email has the header field set", "dbField2": "email.headerField", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.has-header.value", "_score": 1, "_source": {"friendlyName": "Header Value", "group": "email", "help": "Email has the header value", "dbField2": "email.headerValue", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.bodymagic", "_score": 1, "_source": {"friendlyName": "Body Magic", "group": "email", "help": "The content type of body determined by libfile/magic", "dbField2": "email.bodyMagic", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "email.smtp-hello", "_score": 1, "_source": {"friendlyName": "SMTP Hello", "group": "email", "help": "SMTP HELO/EHLO", "dbField2": "email.smtpHello", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ldap.authtype", "_score": 1, "_source": {"friendlyName": "Auth Type", "group": "ldap", "help": "The auth type of ldap bind", "dbField2": "ldap.authtype", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ldap.bindname", "_score": 1, "_source": {"friendlyName": "Bind Name", "group": "ldap", "help": "The bind name of ldap bind", "dbField2": "ldap.bindname", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "irc.nick", "_score": 1, "_source": {"friendlyName": "Nickname", "group": "irc", "help": "Nicknames set", "dbField2": "irc.nick", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "irc.channel", "_score": 1, "_source": {"friendlyName": "Channel", "group": "irc", "help": "Channels joined", "dbField2": "irc.channel", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dhcp.type", "_score": 1, "_source": {"friendlyName": "Type", "group": "dhcp", "help": "DHCP Type", "dbField2": "dhcp.type", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dhcp.host", "_score": 1, "_source": {"friendlyName": "Host", "group": "dhcp", "help": "DHCP Host", "dbField2": "dhcp.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dhcp.host.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "dhcp", "help": "DHCP Hostname Tokens", "dbField2": "dhcp.hostT<PERSON>s", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dhcp.mac", "_score": 1, "_source": {"friendlyName": "Client MAC", "group": "dhcp", "help": "Client ethernet MAC ", "dbField2": "dhcp.mac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dhcp.oui", "_score": 1, "_source": {"friendlyName": "Client OUI", "group": "dhcp", "help": "Client ethernet OUI ", "dbField2": "dhcp.oui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dhcp.id", "_score": 1, "_source": {"friendlyName": "Transaction id", "group": "dhcp", "help": "DHCP Transaction Id", "dbField2": "dhcp.id", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja4", "_score": 1, "_source": {"friendlyName": "JA4", "group": "tls", "help": "SSL/TLS JA4 field", "dbField2": "tls.ja4", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.ja4_r", "_score": 1, "_source": {"friendlyName": "JA4_r", "group": "tls", "help": "SSL/TLS JA4_r field", "dbField2": "tls.ja4_r", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "krb5.realm", "_score": 1, "_source": {"friendlyName": "Realm", "group": "krb5", "help": "Kerberos 5 Realm", "dbField2": "krb5.realm", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "krb5.cname", "_score": 1, "_source": {"friendlyName": "cname", "group": "krb5", "help": "Kerberos 5 cname", "dbField2": "krb5.cname", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "krb5.sname", "_score": 1, "_source": {"friendlyName": "sname", "group": "krb5", "help": "Kerberos 5 sname", "dbField2": "krb5.sname", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oracle.user", "_score": 1, "_source": {"friendlyName": "User", "group": "oracle", "help": "Oracle User", "dbField2": "oracle.user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oracle.host", "_score": 1, "_source": {"friendlyName": "Host", "group": "oracle", "help": "Oracle Host", "dbField2": "oracle.host", "type": "termfield", "category": "host", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oracle.host.tokens", "_score": 1, "_source": {"friendlyName": "Hostname Tokens", "group": "oracle", "help": "Oracle Hostname Tokens", "dbField2": "oracle.hostTokens", "type": "textfield", "aliases": ["[\\"]}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oracle.service", "_score": 1, "_source": {"friendlyName": "Service", "group": "oracle", "help": "Oracle Service", "dbField2": "oracle.service", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mac.src", "_score": 1, "_source": {"friendlyName": "Src MAC", "group": "general", "help": "Source ethernet mac addresses set for session", "dbField2": "source.mac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mac.dst", "_score": 1, "_source": {"friendlyName": "Dst MAC", "group": "general", "help": "Destination ethernet mac addresses set for session", "dbField2": "destination.mac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outermac.src", "_score": 1, "_source": {"friendlyName": "Src Outer MAC", "group": "general", "help": "Source ethernet outer mac addresses set for session", "dbField2": "srcOuterMac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outermac.dst", "_score": 1, "_source": {"friendlyName": "Dst Outer MAC", "group": "general", "help": "Destination ethernet outer mac addresses set for session", "dbField2": "dstOuterMac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dscp.src", "_score": 1, "_source": {"friendlyName": "Src DSCP", "group": "general", "help": "Source non zero differentiated services class selector set for session", "dbField2": "srcDscp", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dscp.dst", "_score": 1, "_source": {"friendlyName": "Dst DSCP", "group": "general", "help": "Destination non zero differentiated services class selector set for session", "dbField2": "dstDscp", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ttl.src", "_score": 1, "_source": {"friendlyName": "TTL Src", "group": "general", "help": "Source IP TTL for first few packets", "dbField2": "srcTTL", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ttl.dst", "_score": 1, "_source": {"friendlyName": "TTL Dst", "group": "general", "help": "Destination IP TTL for first few packets", "dbField2": "dstTTL", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mac", "_score": 1, "_source": {"friendlyName": "Src or Dst MAC", "group": "general", "help": "Shorthand for mac.src or mac.dst", "dbField2": "macall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outermac", "_score": 1, "_source": {"friendlyName": "Src or Dst Outer MAC", "group": "general", "help": "Shorthand for outermac.src or outermac.dst", "dbField2": "outermacall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oui.src", "_score": 1, "_source": {"friendlyName": "Src OUI", "group": "general", "help": "Source ethernet oui for session", "dbField2": "srcOui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oui.dst", "_score": 1, "_source": {"friendlyName": "Dst OUI", "group": "general", "help": "Destination ethernet oui for session", "dbField2": "dst<PERSON>ui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outeroui.src", "_score": 1, "_source": {"friendlyName": "Src Outer OUI", "group": "general", "help": "Source ethernet outer oui for session", "dbField2": "srcOuterOui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outeroui.dst", "_score": 1, "_source": {"friendlyName": "Dst Outer OUI", "group": "general", "help": "Destination ethernet outer oui for session", "dbField2": "dstOuterOui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "vlan", "_score": 1, "_source": {"friendlyName": "VLan", "group": "general", "help": "vlan value", "dbField2": "network.vlan.id", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "vni", "_score": 1, "_source": {"friendlyName": "VNI", "group": "general", "help": "vni value", "dbField2": "vni", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outerip.src", "_score": 1, "_source": {"friendlyName": "Src Outer IP", "group": "general", "help": "Source ethernet outer ip for session", "dbField2": "srcOuterIp", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outerip.dst", "_score": 1, "_source": {"friendlyName": "Dst Outer IP", "group": "general", "help": "Destination outer ip for session", "dbField2": "dstOuterIp", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outerip", "_score": 1, "_source": {"friendlyName": "Src or Dst Outer IP", "group": "general", "help": "Shorthand for outerip.src or outerip.dst", "dbField2": "outeripall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.syn", "_score": 1, "_source": {"friendlyName": "TCP Flag SYN", "group": "general", "help": "Count of packets with SYN and no ACK flag set", "dbField2": "tcpflags.syn", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.syn-ack", "_score": 1, "_source": {"friendlyName": "TCP Flag SYN-ACK", "group": "general", "help": "Count of packets with SYN and ACK flag set", "dbField2": "tcpflags.syn-ack", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.ack", "_score": 1, "_source": {"friendlyName": "TCP Flag ACK", "group": "general", "help": "Count of packets with only the ACK flag set", "dbField2": "tcpflags.ack", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.psh", "_score": 1, "_source": {"friendlyName": "TCP Flag PSH", "group": "general", "help": "Count of packets with PSH flag set", "dbField2": "tcpflags.psh", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.fin", "_score": 1, "_source": {"friendlyName": "TCP Flag FIN", "group": "general", "help": "Count of packets with FIN flag set", "dbField2": "tcpflags.fin", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.rst", "_score": 1, "_source": {"friendlyName": "TCP Flag RST", "group": "general", "help": "Count of packets with RST flag set", "dbField2": "tcpflags.rst", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.urg", "_score": 1, "_source": {"friendlyName": "TCP Flag URG", "group": "general", "help": "Count of packets with URG flag set", "dbField2": "tcpflags.urg", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "packets.src", "_score": 1, "_source": {"friendlyName": "Src Packets", "group": "general", "help": "Total number of packets sent by source in a session", "dbField2": "srcPackets", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "packets.dst", "_score": 1, "_source": {"friendlyName": "Dst Packets", "group": "general", "help": "Total number of packets sent by destination in a session", "dbField2": "dstPackets", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "initRTT", "_score": 1, "_source": {"friendlyName": "Initial RTT", "group": "general", "help": "Initial round trip time, difference between SYN and ACK timestamp divided by 2 in ms", "dbField2": "initRTT", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "communityId", "_score": 1, "_source": {"friendlyName": "Community Id", "group": "general", "help": "Community id flow hash", "dbField2": "communityId", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpseq.src", "_score": 1, "_source": {"friendlyName": "TCP Src Seq", "group": "general", "help": "Source SYN sequence number", "dbField2": "tcpseq.src", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpseq.dst", "_score": 1, "_source": {"friendlyName": "TCP Dst Seq", "group": "general", "help": "Destination SYN-ACK sequence number", "dbField2": "tcpseq.dst", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ethertype", "_score": 1, "_source": {"friendlyName": "Ethertype", "group": "general", "help": "The ethernet protocol type", "dbField2": "ethertype", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "protocols", "_score": 1, "_source": {"friendlyName": "Protocols", "group": "general", "help": "Protocols set for session", "dbField2": "protocol", "type": "termfield"}}]
#!/bin/bash
# npm安装验证脚本

set -e

INSTALL_DIR="${1:-/opt/arkime}"

log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo "[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_info "开始验证npm安装: $INSTALL_DIR"

# 1. 检查node_modules目录
if [[ ! -d "${INSTALL_DIR}/node_modules" ]]; then
    log_error "node_modules目录不存在"
    exit 1
fi

# 2. 统计包数量
package_count=$(find "${INSTALL_DIR}/node_modules" -maxdepth 1 -type d | wc -l)
package_count=$((package_count - 1))  # 减去node_modules本身
log_info "已安装npm包数量: $package_count"

if [[ $package_count -lt 50 ]]; then
    log_error "npm包数量过少($package_count)，安装可能不完整"
    exit 1
elif [[ $package_count -lt 100 ]]; then
    log_warning "npm包数量较少($package_count)，可能只安装了生产环境依赖"
fi

# 3. 检查关键依赖包
log_info "检查关键依赖包..."
key_packages=(
    "express"
    "vue" 
    "vue-server-renderer"
    "moment"
    "axios"
    "helmet"
    "compression"
    "body-parser"
    "multer"
    "passport"
    "winston"
    "ioredis"
    "better-sqlite3"
    "lmdb"
    "re2"
    "pug"
    "stylus"
)

missing_packages=()
for package in "${key_packages[@]}"; do
    if [[ ! -d "${INSTALL_DIR}/node_modules/$package" ]]; then
        missing_packages+=("$package")
    fi
done

if [[ ${#missing_packages[@]} -gt 0 ]]; then
    log_error "缺少关键依赖包: ${missing_packages[*]}"
    exit 1
fi

log_success "关键依赖包检查通过"

# 4. 检查可执行文件
if [[ -d "${INSTALL_DIR}/node_modules/.bin" ]]; then
    bin_count=$(find "${INSTALL_DIR}/node_modules/.bin" -type f -o -type l | wc -l)
    log_info "可执行文件/符号链接数量: $bin_count"
    
    if [[ $bin_count -lt 5 ]]; then
        log_warning "可执行文件数量较少，可能影响功能"
    fi
else
    log_warning "node_modules/.bin目录不存在"
fi

# 5. 检查文件权限
log_info "检查文件权限..."
permission_errors=0

# 检查目录权限
while IFS= read -r -d '' dir; do
    if [[ ! -r "$dir" ]] || [[ ! -x "$dir" ]]; then
        log_warning "目录权限问题: $dir"
        ((permission_errors++))
    fi
done < <(find "${INSTALL_DIR}/node_modules" -type d -print0 | head -20)

# 检查可执行文件权限
if [[ -d "${INSTALL_DIR}/node_modules/.bin" ]]; then
    while IFS= read -r -d '' file; do
        if [[ ! -x "$file" ]]; then
            log_warning "可执行文件权限问题: $file"
            ((permission_errors++))
        fi
    done < <(find "${INSTALL_DIR}/node_modules/.bin" -type f -print0)
fi

if [[ $permission_errors -gt 0 ]]; then
    log_warning "发现 $permission_errors 个权限问题"
else
    log_success "文件权限检查通过"
fi

# 6. 检查package.json
if [[ -f "${INSTALL_DIR}/package.json" ]]; then
    log_success "package.json文件存在"
else
    log_warning "package.json文件不存在"
fi

# 7. 尝试加载关键模块
log_info "测试关键模块加载..."
if command -v node &> /dev/null; then
    test_modules=("express" "vue" "moment")
    for module in "${test_modules[@]}"; do
        if node -e "require('${module}')" -p "${INSTALL_DIR}" 2>/dev/null; then
            log_success "模块 $module 加载成功"
        else
            log_error "模块 $module 加载失败"
            exit 1
        fi
    done
else
    log_warning "Node.js未安装，跳过模块加载测试"
fi

# 8. 生成报告
log_info "生成安装报告..."
echo ""
echo "=== npm安装验证报告 ==="
echo "安装目录: $INSTALL_DIR"
echo "包数量: $package_count"
echo "可执行文件数量: ${bin_count:-0}"
echo "权限问题: $permission_errors"
echo "关键依赖: ${#key_packages[@]}个全部存在"
echo "状态: 验证通过"
echo ""

log_success "npm安装验证完成！"

# Arkime Info 列显示问题解决方案

## 🎯 问题描述

在使用脚本注册字段后，Arkime Sessions 页面的 info 列无法显示内容，但使用 capture 模块时可以正常显示。

## 🔍 问题分析

### 根本原因

Info 列显示问题的根本原因是**字段映射不匹配**：

1. **字段注册格式不完整**：缺少 `dbField` 属性
2. **嵌套字段访问问题**：Info 列需要直接访问会话对象中的字段
3. **字段配置不匹配**：customCols.json 中的配置与实际数据结构不符

### Info 列工作原理

```javascript
// SessionInfo.vue 中的关键逻辑
<div v-if="session[infoField.dbField]">
  // 只有当 session[infoField.dbField] 存在时才显示
</div>
```

Info 列通过以下步骤工作：
1. 读取 `customCols.json` 中的 `children` 配置
2. 对每个字段检查 `session[field.dbField]` 是否存在
3. 如果存在，调用 `SessionField` 组件显示字段值

## ✅ 解决方案

### 1. 修复字段注册格式

**关键修复**：为所有字段添加 `dbField` 属性

```python
# 修复前（错误）
{
    "field_id": "host.http",
    "definition": {
        "friendlyName": "Hostname",
        "group": "http",
        "dbField2": "http.host",  # 只有 dbField2
        "type": "lotermfield"
    }
}

# 修复后（正确）
{
    "field_id": "host.http",
    "definition": {
        "friendlyName": "Hostname",
        "group": "http",
        "dbField": "http.host",   # 添加 dbField
        "dbField2": "http.host",  # 保留 dbField2
        "type": "lotermfield"
    }
}
```

### 2. 修复 Info 列配置

**关键修复**：使用对象形式配置，直接映射到会话数据字段

```json
{
  "info": {
    "children": [
      {
        "dbField": "protocols",
        "exp": "protocols",
        "friendlyName": "Protocols",
        "group": "general",
        "type": "termfield"
      },
      {
        "dbField": "http",
        "exp": "http", 
        "friendlyName": "HTTP Info",
        "group": "http",
        "type": "termfield"
      },
      {
        "dbField": "sdx",
        "exp": "sdx",
        "friendlyName": "SDX Info",
        "group": "sdx", 
        "type": "termfield"
      }
    ]
  }
}
```

### 3. 会话数据结构匹配

确保 Info 列配置与实际会话数据结构匹配：

```json
// 会话数据结构
{
  "protocols": ["tcp", "http", "sdx"],
  "http": {
    "method": ["GET"],
    "host": ["example.com"],
    "uri": ["/api/data"]
  },
  "sdx": {
    "linename1": "11111",
    "linename2": "22222"
  }
}

// Info 列配置匹配
"children": [
  {"dbField": "protocols", ...},  // 直接访问 session.protocols
  {"dbField": "http", ...},       // 直接访问 session.http
  {"dbField": "sdx", ...}         // 直接访问 session.sdx
]
```

## 🔧 实施步骤

### 1. 停止 capture 进程
```bash
systemctl stop arkimecapture
```

### 2. 重新注册字段
```bash
python3 arkime_field_registration_tool.py --register-fields
```

### 3. 发送测试数据
```bash
python3 arkime_field_registration_tool.py --send-data
python3 arkime_field_registration_tool.py --send-files
```

### 4. 重启 viewer
```bash
systemctl restart arkimeviewer
```

### 5. 验证修复
```bash
python3 test_info_display.py
```

## 📊 验证结果

```bash
=== Info 列显示测试 ===
✅ 会话数据结构:
  ✅ protocols: ['tcp', 'http', 'sdx']
  ✅ http: {'method': ['HEAD'], 'host': ['windowsupdate.microsoft.com'], 'uri': ['/v4/iuident.cab']}
  ✅ sdx: {'linename1': '11111', 'linename2': '22222'}

✅ Info 列应该能显示:
  - Protocols: 协议列表
  - HTTP Info: HTTP 相关信息
  - SDX Info: SDX 相关信息
```

## 🎯 关键要点

### 1. 字段注册必须包含 dbField
- `dbField`：Viewer 用于字段映射和 Info 列显示
- `dbField2`：ES 中的实际存储字段名

### 2. Info 列配置要匹配数据结构
- 使用对象形式配置而不是字符串
- `dbField` 必须对应会话对象中的实际字段

### 3. 嵌套字段的处理
- Info 列不能直接显示嵌套字段（如 `http.method`）
- 需要显示父对象（如 `http`），然后由 SessionField 组件处理嵌套显示

### 4. 字段映射机制
```javascript
// Viewer 字段加载逻辑
source.exp = field._id;  // 表达式 = 字段ID
internals.dbFieldsMap[source.dbField] = source;    // dbField 映射
internals.dbFieldsMap[source.dbField2] = source;   // dbField2 映射
```

## 🚀 最终效果

修复后，Info 列将显示：
- **Protocols**：显示协议列表（tcp, http, sdx）
- **HTTP Info**：显示 HTTP 相关信息（方法、主机、URI）
- **SDX Info**：显示 SDX 相关信息（linename1, linename2）

现在 Info 列可以正常显示内容，与使用 capture 模块时的效果一致！

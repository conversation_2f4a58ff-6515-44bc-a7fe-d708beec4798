[default]
# Elasticsearch configuration
elasticsearch=http://localhost:9200

# Basic settings
rotateIndex=daily
passwordSecret=test123456
httpRealm=Arkime

# Interface (not used for testing)
interface=lo

# PCAP directory
pcapDir=/tmp/arkime/raw

# File size limits
maxFileSizeG=1
tcpTimeout=600
tcpSaveTimeout=720
udpTimeout=30
icmpTimeout=10
maxStreams=1000000
maxPackets=10000
freeSpaceG=5%

# Viewer settings
viewPort=8006
viewHost=0.0.0.0

# Enable cron queries
cronQueries=true

# Drop privileges
dropUser=nobody
dropGroup=daemon

# Parsers and plugins
parsersDir=/usr/local/arkime/parsers
pluginsDir=/usr/local/arkime/plugins

# Protocol parsing
parseSMTP=true
parseSMB=true
parseQSValue=false
supportSha256=false
maxReqBody=64
reqBodyOnlyUtf8=true

# Performance settings
packetThreads=2
pcapWriteMethod=simple
pcapWriteSize=262143
maxESConns=30
maxESRequests=500
packetsPerPoll=50000

# Debug settings
logEveryXPackets=100000
logUnknownProtocols=false
logESRequests=true
logFileCreation=true

# Node specific configuration
[test-node-01]
viewUrl=http://localhost:8006

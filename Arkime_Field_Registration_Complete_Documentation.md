# Arkime字段注册完整统计文档

本文档统计了Arkime代码库中所有通过`arkime_field_define`函数注册的字段，按照您要求的Elasticsearch文档格式整理。

## 总体统计

- **总字段数**: 224
- **字段组数**: 23
- **文档格式**: Elasticsearch v30 格式
- **索引名称**: arkime_fields_v30

## 字段组统计

按字段数量排序：

- **general**: 42 个字段
- **http**: 37 个字段  
- **dns**: 29 个字段
- **email**: 19 个字段
- **cert**: 18 个字段
- **tls**: 12 个字段
- **smb**: 9 个字段
- **suricata**: 7 个字段
- **dhcp**: 6 个字段
- **socks**: 5 个字段
- **modbus**: 5 个字段
- **snmp**: 5 个字段
- **quic**: 4 个字段
- **ssh**: 4 个字段
- **radius**: 4 个字段
- **oracle**: 4 个字段
- **postgresql**: 3 个字段
- **krb5**: 3 个字段
- **mysql**: 2 个字段
- **ldap**: 2 个字段
- **irc**: 2 个字段
- **bgp**: 1 个字段
- **isis**: 1 个字段

## 字段类型统计

- **termfield**: 最常用的字段类型，用于可搜索的字符串
- **integer**: 整数类型字段
- **ip**: IP地址类型字段
- **date**: 日期时间类型字段
- **textfield**: 文本字段类型

## 示例字段格式

以下是几个典型的字段定义示例，展示了您要求的Elasticsearch文档格式：

### 1. 基本字段示例 (TLS JA4)

```json
{
    "_index": "arkime_fields_v30",
    "_type": "_doc",
    "_id": "tls.ja4",
    "_score": 1,
    "_source": {
        "friendlyName": "JA4",
        "group": "tls",
        "help": "SSL/TLS JA4 field",
        "dbField2": "tls.ja4",
        "type": "termfield"
    }
}
```

### 2. 包含category的字段示例

```json
{
    "_index": "arkime_fields_v30",
    "_type": "_doc",
    "_id": "user",
    "_score": 1,
    "_source": {
        "friendlyName": "User",
        "group": "general",
        "help": "External user set for session",
        "dbField2": "user",
        "type": "termfield",
        "category": "user"
    }
}
```

### 3. 包含aliases的字段示例

```json
{
    "_index": "arkime_fields_v30",
    "_type": "_doc",
    "_id": "ip.dns",
    "_score": 1,
    "_source": {
        "friendlyName": "IP",
        "group": "dns",
        "help": "IP from DNS result",
        "dbField2": "dns.ip",
        "type": "ip",
        "category": "ip",
        "aliases": ["dns.ip"]
    }
}
```

## 完整字段列表文件

所有224个字段的完整定义已保存在以下文件中：

- **arkime_fields_clean.json**: 包含所有字段的JSON格式文档
- **Arkime_Fields_Complete_Documentation.md**: 详细的Markdown格式文档
- **Arkime_Fields_Summary_Table.md**: 字段汇总表格

## 字段注册源码分布

字段定义分布在以下源码文件中：

### 核心模块
- `capture/parsers.c`: 通用字段定义
- `capture/packet.c`: 网络包相关字段
- `capture/session.c`: 会话相关字段

### 协议解析器
- `capture/parsers/http.c`: HTTP协议字段 (37个)
- `capture/parsers/dns.c`: DNS协议字段 (29个)
- `capture/parsers/smtp.c`: 邮件协议字段 (19个)
- `capture/parsers/certs.c`: 证书相关字段 (18个)
- `capture/parsers/tls.c`: TLS/SSL字段 (12个)
- 其他协议解析器...

### 插件模块
- `capture/plugins/suricata.c`: Suricata集成字段
- `capture/plugins/wise.c`: WISE插件字段
- `capture/plugins/tagger.c`: 标签插件字段

## 使用说明

1. **查看完整列表**: 参考 `arkime_fields_clean.json` 文件
2. **按组查看**: 参考 `Arkime_Fields_Complete_Documentation.md` 文件  
3. **快速查找**: 参考 `Arkime_Fields_Summary_Table.md` 文件

## 技术说明

- 所有字段都通过 `arkime_field_define()` 函数注册
- 字段定义包含：组名、类型、表达式、友好名称、数据库字段、帮助信息
- 部分字段包含额外属性：category、aliases等
- 字段类型映射到Elasticsearch相应的数据类型

---

*本文档由自动化脚本生成，涵盖了Arkime代码库中所有arkime_field_define调用的完整信息。*

SUBDIRS = . capture db viewer

if BUILD_CONT3XT
SUBDIRS += cont3xt
endif

if BUILD_WISE
SUBDIRS += wiseService
endif

if BUILD_PARLIAMENT
SUBDIRS += parliament
endif

if BUILD_RELEASE
SUBDIRS += release
endif

SUBDIRS += tests

install-exec-local:
	$(MKDIR_P) $(DESTDIR)@prefix@
	# 检查是否存在离线npm包，优先使用离线安装
	@if [ -d "npm_offline_packages" ] && [ -f "npm_offline_packages/install_npm_offline.sh" ]; then \
		echo "检测到npm离线包，使用离线安装模式..."; \
		npm_offline_packages/install_npm_offline.sh $(DESTDIR)@prefix@ production; \
		echo "验证离线安装结果..."; \
		if [ ! -d "$(DESTDIR)@prefix@/node_modules" ]; then \
			echo "错误：node_modules目录未创建"; \
			exit 1; \
		fi; \
		echo "设置npm包权限..."; \
		find "$(DESTDIR)@prefix@/node_modules" -type d -exec chmod 755 {} \; 2>/dev/null || true; \
		find "$(DESTDIR)@prefix@/node_modules" -type f -exec chmod 644 {} \; 2>/dev/null || true; \
		find "$(DESTDIR)@prefix@/node_modules/.bin" -type f -exec chmod 755 {} \; 2>/dev/null || true; \
		echo "离线npm包安装完成"; \
	elif [ -f "npm_offline_packages/node_modules_production.tar.gz" ]; then \
		echo "检测到npm离线压缩包，直接解压..."; \
		tar -xzf npm_offline_packages/node_modules_production.tar.gz -C $(DESTDIR)@prefix@; \
		echo "验证解压结果..."; \
		if [ ! -d "$(DESTDIR)@prefix@/node_modules" ]; then \
			echo "错误：node_modules目录未创建"; \
			exit 1; \
		fi; \
		echo "设置npm包权限..."; \
		find "$(DESTDIR)@prefix@/node_modules" -type d -exec chmod 755 {} \; 2>/dev/null || true; \
		find "$(DESTDIR)@prefix@/node_modules" -type f -exec chmod 644 {} \; 2>/dev/null || true; \
		find "$(DESTDIR)@prefix@/node_modules/.bin" -type f -exec chmod 755 {} \; 2>/dev/null || true; \
		echo "离线压缩包解压完成"; \
	else \
		echo "未检测到npm离线包，使用在线安装..."; \
		npm ci; \
		cp -pr node_modules $(DESTDIR)@prefix@/; \
		echo "在线npm安装完成"; \
	fi
	cp -pr common $(DESTDIR)@prefix@
	cp -pr assets $(DESTDIR)@prefix@
	@INSTALL@ package.json $(DESTDIR)@prefix@/package.json
	# 验证最终安装结果
	@echo "验证最终安装结果..."
	@if [ ! -d "$(DESTDIR)@prefix@/node_modules" ]; then \
		echo "错误：最终验证失败，node_modules目录不存在"; \
		exit 1; \
	fi
	@node_count=$$(find "$(DESTDIR)@prefix@/node_modules" -maxdepth 1 -type d | wc -l); \
	echo "已安装npm包数量: $$((node_count - 1))"; \
	if [ $$node_count -lt 10 ]; then \
		echo "警告：安装的npm包数量过少，可能安装不完整"; \
	fi
	@echo "npm依赖安装验证完成"

check-local:
	# 检查是否存在离线npm包，优先使用离线安装
	@if [ -d "npm_offline_packages" ] && [ -f "npm_offline_packages/install_npm_offline.sh" ]; then \
		echo "检测到npm离线包，使用离线安装进行检查..."; \
		npm_offline_packages/install_npm_offline.sh . full; \
	elif [ -f "npm_offline_packages/node_modules_full.tar.gz" ]; then \
		echo "检测到npm离线完整包，直接解压..."; \
		tar -xzf npm_offline_packages/node_modules_full.tar.gz; \
	else \
		echo "未检测到npm离线包，使用在线安装进行检查..."; \
		npm ci; \
	fi

config:
	$(DESTDIR)@prefix@/bin/Configure

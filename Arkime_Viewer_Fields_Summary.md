# Arkime Viewer 模块字段显示总结

本文档总结了 Arkime Viewer 模块中所有需要展示的字段，包括字段定义、加载机制、显示位置和渲染方式。

## 1. 字段定义与加载机制

### 1.1 字段存储位置

Arkime 的字段定义存储在 Elasticsearch 的 `arkime_fields` 索引中（通过别名访问，实际索引名为 `arkime_fields_v30`）。每个字段作为一个文档存储，包含以下关键属性：

```json
{
  "friendlyName": "字段友好名称",
  "group": "字段分组（用于UI分类）",
  "help": "字段帮助信息",
  "dbField": "数据库字段名",
  "dbField2": "备用字段名",
  "type": "字段类型（termfield/integer/ip等）",
  "category": ["分类标签"],
  "transform": "数据转换规则（可选）"
}
```

### 1.2 字段加载流程

1. **启动时加载**：Viewer 启动时，通过 `viewerUtils.js` 中的 `loadFields()` 函数从 ES 加载字段定义
2. **字段处理**：`config.js` 中的 `loadFields()` 函数处理字段定义，构建内存中的字段映射
3. **字段映射**：构建 `fieldsMap`（按表达式索引）和 `dbFieldsMap`（按数据库字段名索引）
4. **分类处理**：根据 `group` 属性将字段分组到 `categories` 对象中
5. **API 暴露**：通过 `/api/fields` 和 `/api/appinfo` 接口向前端提供字段信息

```javascript
// viewer/viewerUtils.js
static async loadFields() {
  try {
    let data = await Db.loadFields();  // 从 ES 加载字段
    data = data.hits.hits;
    
    Config.loadFields(data);  // 处理字段定义
    
    return {
      fieldsMap: JSON.stringify(Config.getFieldsMap()),
      fieldsArr: Config.getFields().sort((a, b) => {
        return (a.exp > b.exp ? 1 : -1);
      })
    };
  } catch (err) {
    return { fieldsMap: {}, fieldsArr: [] };
  }
}
```

## 2. 字段显示位置

Arkime Viewer 模块中的字段在以下几个主要位置显示：

### 2.1 Sessions 表格

- **表格列**：根据用户配置的列显示会话数据
- **自定义列**：通过 `customCols.json` 定义的复合列（如 info, src, dst 等）
- **列排序**：可根据字段值排序（除非设置了 `unsortable: true`）

### 2.2 Sessions 详情页面

- **协议卡片**：通过 `.detail.jade/.pug` 模板文件定义的协议详情卡片
- **通用信息**：会话基本信息（时间、ID、协议等）
- **字段值**：使用 `SessionField.vue` 组件渲染字段值，支持特殊格式化和交互

### 2.3 SPI View 页面

- **分类卡片**：根据字段的 `group` 属性分组显示
- **字段按钮**：每个字段显示为一个可点击的按钮
- **字段统计**：显示字段值的统计信息（计数、百分比等）

### 2.4 Spigraph 页面

- **主图表**：根据选定字段的值显示图表
- **子图表**：每个字段值的详细图表
- **字段值**：使用 `SessionField.vue` 组件渲染字段值

### 2.5 Connections 页面

- **节点**：根据源字段和目标字段的值显示节点
- **连接**：显示节点之间的连接关系

## 3. 字段类型与渲染方式

### 3.1 基本字段类型

- **termfield**：可搜索的字符串字段，显示为文本
- **integer**：整数字段，显示为数字
- **ip**：IP 地址字段，显示为可点击的 IP 地址
- **lotermfield**：大量数据的字符串字段

### 3.2 特殊字段渲染

- **时间字段**：格式化为人类可读的时间格式
- **IP 地址**：显示国家标志和地理位置信息
- **协议字段**：显示为协议图标和名称
- **URL 字段**：显示为可点击的链接
- **文件字段**：显示文件类型图标和下载链接

### 3.3 复合字段

复合字段（如 info 列）通过 `customCols.json` 定义，包含多个子字段：

```json
"info": {
  "width": 250,
  "dbField": "info",
  "exp": "info",
  "group": "general",
  "friendlyName": "Info",
  "help": "Information column that can be configured to include many fields",
  "unsortable": true,
  "children": [
    {
      "dbField": "protocols",
      "exp": "protocols",
      "friendlyName": "Protocols",
      "group": "general",
      "type": "termfield"
    },
    // 其他子字段...
  ]
}
```

## 4. 主要字段分组

Arkime 将字段按功能和协议分组，主要分组包括：

### 4.1 general（通用字段）

- **会话标识**：id, rootId, node
- **时间信息**：firstPacket, lastPacket, starttime, stoptime
- **协议信息**：protocols, ethertype
- **流量统计**：totPackets, totBytes, totDataBytes

### 4.2 ip（IP 相关字段）

- **源 IP**：source.ip, srcIp
- **目标 IP**：destination.ip, dstIp
- **地理位置**：source.geo.country_iso_code, destination.geo.country_iso_code
- **自治系统**：source.as.full, destination.as.full

### 4.3 http（HTTP 协议字段）

- **请求信息**：http.method, http.uri, http.host
- **响应信息**：http.statuscode, http.contentType
- **头部信息**：http.request.header, http.response.header

### 4.4 dns（DNS 协议字段）

- **查询信息**：dns.query, dns.host
- **响应信息**：dns.answer, dns.status

### 4.5 email（邮件协议字段）

- **邮件信息**：email.src, email.dst, email.subject
- **附件信息**：email.filename, email.contentType

### 4.6 cert（证书字段）

- **证书信息**：cert.notBefore, cert.notAfter, cert.alt
- **签名信息**：cert.issuerCN, cert.subjectCN

### 4.7 sdx（自定义协议字段）

- **自定义字段**：sdx.linename1, sdx.linename2

## 5. 字段交互功能

字段值在 UI 中支持多种交互功能：

### 5.1 搜索操作

- **添加到查询**：将字段值添加到搜索表达式
- **排除值**：从搜索结果中排除特定字段值
- **字段存在**：查询字段存在的会话

### 5.2 导出操作

- **导出唯一值**：导出字段的唯一值列表
- **导出带计数**：导出字段值及其出现次数

### 5.3 可视化操作

- **SPI Graph**：打开字段的 SPI Graph 视图
- **Connections**：查看字段值的连接关系

### 5.4 右键菜单操作

- **复制值**：复制字段值到剪贴板
- **外部查询**：在外部系统中查询字段值
- **自定义操作**：通过 `valueActions` 配置的自定义操作

## 6. 核心字段组件

### 6.1 SessionField.vue 组件

这是 Arkime 中最重要的字段渲染组件，负责显示所有类型的字段值：

**主要功能：**
- 字段值格式化和显示
- 交互菜单（搜索、导出、可视化）
- 特殊字段类型处理（时间、IP、URL 等）
- 字段验证和错误处理

**关键属性：**
```javascript
props: [
  'field',      // 字段对象定义
  'expr',       // 查询表达式
  'value',      // 字段值
  'session',    // 会话对象
  'parse',      // 是否解析值
  'sessionBtn', // 是否显示会话按钮
  'pullLeft',   // 下拉菜单方向
  'infoFields'  // 信息字段列表
]
```

### 6.2 SessionInfo.vue 组件

专门用于渲染 info 列的复合字段信息：

**主要功能：**
- 显示多个子字段
- 字段限制和展开功能
- 子字段的交互操作

### 6.3 FieldService.js 服务

提供字段查找和操作的核心服务：

**主要方法：**
- `getField(search, ignoreAliases)`: 根据表达式查找字段
- `getFieldProperty(search, prop, ignoreAliases)`: 获取字段属性
- `getValues(params)`: 获取字段值列表

## 7. 字段配置文件

### 7.1 customCols.json

定义 Sessions 表格中的自定义列，包括：

**基本列：**
- `info`: 信息列（包含协议、HTTP、SDX 等子字段）
- `src`: 源 IP/国家列
- `dst`: 目标 IP/国家列
- `dbby`: 数据字节/总字节列
- `pa1pa2`: 源包数/目标包数列

**复合列示例：**
```json
"src": {
  "width": 140,
  "dbField": "src",
  "exp": "src",
  "group": "general",
  "friendlyName": "Src IP / Country",
  "help": "Source IP & Source Country",
  "sortBy": "source.ip",
  "children": [
    "source.ip",
    "source.geo.country_iso_code"
  ]
}
```

### 7.2 字段别名系统

Arkime 支持字段别名，允许使用多个名称引用同一个字段：

**别名类型：**
- 表达式别名（exp）
- 数据库字段别名（dbField, dbField2）
- ECS 字段别名（fieldECS）
- 自定义别名（aliases 数组）

## 8. 字段显示模板

### 8.1 Jade/Pug 模板系统

Sessions 详情页面使用 Jade/Pug 模板显示协议信息：

**模板位置：**
- `capture/parsers/*.detail.jade`
- `viewer/plugins/*.detail.pug`

**模板示例：**
```jade
if (session.sdx)
  div.sessionDetailMeta.bold SDX
  dl.sessionDetailMeta(suffix="sdx")
    +arrayList(session.sdx, "linename1", "Line Name 1", "sdx.linename1")
    +arrayList(session.sdx, "linename2", "Line Name 2", "sdx.linename2")
```

**模板扫描机制：**
```javascript
// viewer/viewer.js
function createSessionDetail() {
  const found = {};
  let dirs = [];

  dirs = dirs.concat(Config.getArray('pluginsDir', `${version.config_prefix}/plugins`));
  dirs = dirs.concat(Config.getArray('parsersDir', `${version.config_prefix}/parsers`));

  dirs.forEach(function (dir) {
    const files = fs.readdirSync(dir);
    files.sort().reverse().forEach(function (file) {
      if (file.match(/\.detail\.jade$/i)) {
        found[sfile] = fs.readFileSync(dir + '/' + file, 'utf8');
      } else if (file.match(/\.detail\.pug$/i)) {
        found[sfile] = '  include ' + dir + '/' + file + '\n';
      }
    });
  });
}
```

### 8.2 sessionDetail.pug 主模板

定义会话详情页面的基本结构：

**主要部分：**
- General 信息（时间、ID、协议等）
- 网络信息（源/目标 IP、端口、地理位置）
- 流量统计（包数、字节数）
- 标签和文件信息
- 动态协议卡片

## 9. SPI View 字段分类

### 9.1 字段分类机制

SPI View 根据字段的 `group` 属性进行分类显示：

```javascript
// viewer/vueapp/src/components/spiview/Spiview.vue
categorizeFields: function () {
  this.categoryObjects = {};

  for (let i = 0, len = this.fields.length; i < len; ++i) {
    const field = this.fields[i];

    if (field.noFacet || field.regex ||
        (field.type && field.type.match(/textfield/))) {
      continue;
    }

    if (this.categoryObjects[field.group]) {
      this.categoryObjects[field.group].fields.push(field);
    } else {
      Vue.set(this.categoryObjects, field.group, {
        fields: [field],
        spi: {}
      });
    }
  }
}
```

### 9.2 字段过滤规则

SPI View 会过滤掉以下类型的字段：
- `noFacet: true` 的字段
- `regex: true` 的字段
- `type` 包含 `textfield` 的字段

### 9.3 字段统计数据

每个字段可以显示统计信息：
- 字段值列表
- 每个值的出现次数
- 百分比分布
- 时间分布图表

## 10. 字段注册与管理

### 10.1 字段注册 API

通过以下 API 端点管理字段：

**获取字段：**
- `GET /api/fields` - 获取所有字段定义
- `GET /api/fields?array=true` - 获取字段数组格式
- `GET /api/appinfo` - 获取包含字段的应用信息

**字段操作：**
- `GET /api/valueactions` - 获取字段值操作
- `GET /api/fieldactions` - 获取字段操作

### 10.2 字段状态管理

字段状态通过 Vuex store 管理：

```javascript
// viewer/vueapp/src/store.js
state: {
  fieldsMap: {},        // 字段映射表
  fieldsAliasMap: {},   // 字段别名映射表
  fields: [],           // 字段数组
  // 其他状态...
}
```

### 10.3 字段缓存机制

- 字段定义在 Viewer 启动时加载并缓存在内存中
- 通过 `internals.fieldsMap` 和 `internals.fieldsArr` 存储
- 前端通过 `/api/appinfo` 获取字段信息并存储在 Vuex store 中

## 11. 常见字段列表

以下是 Arkime 中最常用的字段列表，按分组和用途组织：

### 11.1 会话标识字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| id | 会话唯一标识符 | termfield | general |
| rootId | 根会话 ID | termfield | general |
| node | 捕获节点名称 | termfield | general |
| srcNode | 源节点名称 | termfield | general |

### 11.2 时间字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| firstPacket | 首包时间戳 | integer | general |
| lastPacket | 末包时间戳 | integer | general |
| starttime | 会话开始时间 | integer | general |
| stoptime | 会话结束时间 | integer | general |

### 11.3 IP 和端口字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| source.ip | 源 IP 地址 | ip | ip |
| destination.ip | 目标 IP 地址 | ip | ip |
| source.port | 源端口 | integer | port |
| destination.port | 目标端口 | integer | port |
| ip.protocol | IP 协议号 | integer | ip |

### 11.4 流量统计字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| network.packets | 总包数 | integer | general |
| network.bytes | 总字节数 | integer | general |
| totDataBytes | 总数据字节数 | integer | general |
| source.packets | 源发送包数 | integer | general |
| destination.packets | 目标发送包数 | integer | general |
| source.bytes | 源发送字节数 | integer | general |
| destination.bytes | 目标发送字节数 | integer | general |
| client.bytes | 客户端数据字节数 | integer | general |
| server.bytes | 服务器数据字节数 | integer | general |

### 11.5 协议字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| protocols | 协议列表 | termfield | general |
| ethertype | 以太网类型 | termfield | general |
| vlan | VLAN 标签 | integer | general |
| network.vlan | VLAN 标签（ECS） | integer | general |

### 11.6 HTTP 字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| http.method | HTTP 方法 | termfield | http |
| http.uri | HTTP URI | termfield | http |
| http.host | HTTP 主机 | termfield | http |
| http.statuscode | HTTP 状态码 | integer | http |
| http.contentType | 内容类型 | termfield | http |
| http.userAgent | 用户代理 | termfield | http |
| http.request.header | 请求头 | lotermfield | http |
| http.response.header | 响应头 | lotermfield | http |

### 11.7 DNS 字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| dns.host | DNS 主机名 | termfield | dns |
| dns.query | DNS 查询 | termfield | dns |
| dns.answer | DNS 应答 | termfield | dns |
| dns.status | DNS 状态 | termfield | dns |
| dns.type | DNS 查询类型 | termfield | dns |

### 11.8 地理位置字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| source.geo.country_iso_code | 源国家代码 | termfield | ip |
| destination.geo.country_iso_code | 目标国家代码 | termfield | ip |
| source.geo.country | 源国家名称 | termfield | ip |
| destination.geo.country | 目标国家名称 | termfield | ip |
| source.geo.city | 源城市 | termfield | ip |
| destination.geo.city | 目标城市 | termfield | ip |

### 11.9 自定义 SDX 字段

| 字段名 | 描述 | 类型 | 分组 |
|-------|------|------|------|
| sdx.linename1 | SDX 线路名称 1 | termfield | sdx |
| sdx.linename2 | SDX 线路名称 2 | termfield | sdx |

## 12. 总结与最佳实践

### 12.1 字段显示优化

1. **性能考虑**：
   - 避免在 SPI View 中同时显示过多字段
   - 对大型字段使用分页加载
   - 使用适当的字段类型（termfield vs lotermfield）

2. **UI 优化**：
   - 使用友好名称（friendlyName）提高可读性
   - 提供帮助文本（help）解释字段用途
   - 对特殊字段使用自定义格式化

### 12.2 字段注册最佳实践

1. **字段命名规范**：
   - 使用点表示法表示层次结构（如 `http.host`）
   - 使用有意义的前缀分组相关字段
   - 遵循 ECS（Elastic Common Schema）命名约定

2. **字段属性设置**：
   - 设置正确的 `type` 属性（影响搜索和显示）
   - 提供清晰的 `friendlyName` 和 `help` 文本
   - 使用适当的 `group` 进行分类

3. **模板创建**：
   - 为每个协议创建 `.detail.jade` 模板
   - 使用条件判断避免显示空数据
   - 利用 Jade/Pug 混合器简化重复内容

### 12.3 故障排除

1. **字段不显示**：
   - 检查字段是否正确注册到 `arkime_fields` 索引
   - 确认字段定义包含正确的属性
   - 重启 Viewer 服务加载新字段

2. **协议卡片不显示**：
   - 确认 `.detail.jade` 文件存在于正确位置
   - 检查模板语法是否正确
   - 确认会话数据包含对应的协议对象

3. **调试命令**：
   ```bash
   # 检查字段注册
   curl "localhost:9200/arkime_fields/_search?pretty"

   # 检查会话数据
   curl "localhost:9200/arkime_sessions3-$(date +%y%m%d)/_search?q=protocols:sdx&pretty"

   # 检查前端字段加载
   curl "localhost:8005/api/fields" | jq
   ```

# Arkime字段汇总表

## 按组分类的字段列表

### BGP

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `bgp.type` | Type | termfield | BGP Type field |

### CERT

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `cert.alt` | Alt Name | termfield | Certificate alternative names |
| `cert.cnt` | Cert Cnt | integer | Count of certificates |
| `cert.curve` | Curve | termfield | Curve Algorithm |
| `cert.hash` | Hash | termfield | SHA1 hash of entire certificate |
| `cert.issuer.cn` | Issuer CN | termfield | Issuer's common name |
| `cert.issuer.on` | Issuer ON | termfield | Issuer's organization name |
| `cert.issuer.ou` | Issuer Org Unit | termfield | Issuer's organizational unit |
| `cert.notafter` | Not After | date | Certificate is not valid after this date |
| `cert.notbefore` | Not Before | date | Certificate is not valid before this date |
| `cert.publicAlgorithm` | Public Algorithm | termfield | Public Key Algorithm |
| `cert.remainingDays` | Days remaining | integer | Certificate is still valid for this many days |
| `cert.remainingSeconds` | Seconds remaining | integer | Certificate is still valid for this many seconds |
| `cert.serial` | Serial Number | termfield | Serial Number |
| `cert.subject.cn` | Subject CN | termfield | Subject's common name |
| `cert.subject.on` | Subject ON | termfield | Subject's organization name |
| `cert.subject.ou` | Subject Org Unit | termfield | Subject's organizational unit |
| `cert.validfor` | Days Valid For | integer | Certificate is valid for this many days total |
| `cert.validforSeconds` | Seconds Valid For | integer | Certificate is valid for this many seconds total |

### DHCP

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `dhcp.host` | Host | termfield | DHCP Host |
| `dhcp.host.tokens` | Hostname Tokens | textfield | DHCP Hostname Tokens |
| `dhcp.id` | Transaction id | termfield | DHCP Transaction Id |
| `dhcp.mac` | Client MAC | termfield | Client ethernet MAC  |
| `dhcp.oui` | Client OUI | termfield | Client ethernet OUI  |
| `dhcp.type` | Type | termfield | DHCP Type |

### DNS

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `dns.answer.caa` | DNS Answer CAA | termfield | DNS Answer CAA |
| `dns.answer.class` | DNS Answer Class | termfield | DNS Answer Class |
| `dns.answer.cname` | DNS Answer CNAME | termfield | DNS Answer CNAME |
| `dns.answer.cnt` | DNS Answers Cnt | integer | Count of DNS Answers |
| `dns.answer.https` | DNS Answer HTTPS | termfield | DNS Answer HTTPS |
| `dns.answer.ip` | DNS Answer IP | ip | DNS Answer IP |
| `dns.answer.mx` | DNS Answer MX | termfield | DNS Answer MX |
| `dns.answer.name` | DNS Name | termfield | DNS Answer Name |
| `dns.answer.ns` | DNS Answer NS | termfield | DNS Answer NS |
| `dns.answer.priority` | DNS Answer Priority | integer | DNS Answer Priority |
| `dns.answer.ttl` | DNS Answer TTL | integer | DNS Answer TTL |
| `dns.answer.txt` | DNS Answer TXT | termfield | DNS Answer TXT |
| `dns.answer.type` | DNS Answer Type | termfield | DNS Answer Type |
| `dns.header_flags` | DNS Header Flags | termfield | DNS Header Flags |
| `dns.opcode` | Op Code | termfield | DNS lookup op code |
| `dns.puny` | Puny | termfield | DNS lookup punycode |
| `dns.query.class` | Query Class | termfield | DNS lookup query class |
| `dns.query.host` | Query Host | termfield | DNS Query Name |
| `dns.query.type` | Query Type | termfield | DNS lookup query type |
| `dns.status` | Status Code | termfield | DNS lookup return code |
| `host.dns` | Host | termfield | DNS lookup hostname |
| `host.dns.all` | All Host | termfield | Shorthand for host.dns or host.dns.nameserver |
| `host.dns.mailserver` | MX Host | termfield | Hostnames for Mail Exchange Server |
| `host.dns.nameserver` | NS Host | termfield | Hostnames for Name Server |
| `host.dns.tokens` | Hostname Tokens | textfield | DNS lookup hostname tokens |
| `ip.dns` | IP | ip | IP from DNS result |
| `ip.dns.all` | IP | ip | Shorthand for ip.dns or ip.dns.nameserver |
| `ip.dns.mailserver` | IP | ip | IPs for mailservers |
| `ip.dns.nameserver` | IP | ip | IPs for nameservers |

### EMAIL

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `email.bodymagic` | Body Magic | termfield | The content type of body determined by libfile/magic |
| `email.content-type` | Content-Type | termfield | Email content-type header |
| `email.dst` | Receiver | termfield | Email to address |
| `email.file-content-type` | Attach Content-Type | termfield | Email attachment content types |
| `email.fn` | Filenames | termfield | Email attachment filenames |
| `email.has-header` | Header | termfield | Email has the header set |
| `email.has-header.name` | Header Field | termfield | Email has the header field set |
| `email.has-header.value` | Header Value | termfield | Email has the header value |
| `email.md5` | Attach MD5s | termfield | Email attachment MD5s |
| `email.message-id` | Id | termfield | Email Message-Id header |
| `email.mime-version` | Mime-Version | termfield | Email Mime-Header header |
| `email.sha256` | Attach SHA256s | termfield | Email attachment SHA256s |
| `email.smtp-hello` | SMTP Hello | termfield | SMTP HELO/EHLO |
| `email.src` | Sender | termfield | Email from address |
| `email.subject` | Subject | termfield | Email subject header |
| `email.x-mailer` | X-Mailer Header | termfield | Email X-Mailer header |
| `host.email` | Hostname | termfield | Email hostnames |
| `host.email.tokens` | Hostname Tokens | textfield | Email Hostname Tokens |
| `ip.email` | IP | ip | Email IP address |

### GENERAL

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `asset` | Asset | termfield | Asset name |
| `communityId` | Community Id | termfield | Community id flow hash |
| `dscp.dst` | Dst DSCP | integer | Destination non zero differentiated services class selector set for session |
| `dscp.src` | Src DSCP | integer | Source non zero differentiated services class selector set for session |
| `ethertype` | Ethertype | integer | The ethernet protocol type |
| `icmp.code` | ICMP Code | integer | ICMP code field values |
| `icmp.type` | ICMP Type | integer | ICMP type field values |
| `initRTT` | Initial RTT | integer | Initial round trip time, difference between SYN and ACK timestamp divided by 2 in ms |
| `mac` | Src or Dst MAC | termfield | Shorthand for mac.src or mac.dst |
| `mac.dst` | Dst MAC | termfield | Destination ethernet mac addresses set for session |
| `mac.src` | Src MAC | termfield | Source ethernet mac addresses set for session |
| `oui.dst` | Dst OUI | termfield | Destination ethernet oui for session |
| `oui.src` | Src OUI | termfield | Source ethernet oui for session |
| `outerip` | Src or Dst Outer IP | termfield | Shorthand for outerip.src or outerip.dst |
| `outerip.dst` | Dst Outer IP | ip | Destination outer ip for session |
| `outerip.src` | Src Outer IP | ip | Source ethernet outer ip for session |
| `outermac` | Src or Dst Outer MAC | termfield | Shorthand for outermac.src or outermac.dst |
| `outermac.dst` | Dst Outer MAC | termfield | Destination ethernet outer mac addresses set for session |
| `outermac.src` | Src Outer MAC | termfield | Source ethernet outer mac addresses set for session |
| `outeroui.dst` | Dst Outer OUI | termfield | Destination ethernet outer oui for session |
| `outeroui.src` | Src Outer OUI | termfield | Source ethernet outer oui for session |
| `packets.dst` | Dst Packets | integer | Total number of packets sent by destination in a session |
| `packets.src` | Src Packets | integer | Total number of packets sent by source in a session |
| `protocols` | Protocols | termfield | Protocols set for session |
| `session.length` | Session Length | integer | Session Length in milliseconds so far |
| `session.segments` | Session Segments | integer | Number of segments in session so far |
| `tags` | Tags | termfield | Tags set for session |
| `tcpflags.ack` | TCP Flag ACK | integer | Count of packets with only the ACK flag set |
| `tcpflags.fin` | TCP Flag FIN | integer | Count of packets with FIN flag set |
| `tcpflags.psh` | TCP Flag PSH | integer | Count of packets with PSH flag set |
| `tcpflags.rst` | TCP Flag RST | integer | Count of packets with RST flag set |
| `tcpflags.syn` | TCP Flag SYN | integer | Count of packets with SYN and no ACK flag set |
| `tcpflags.syn-ack` | TCP Flag SYN-ACK | integer | Count of packets with SYN and ACK flag set |
| `tcpflags.urg` | TCP Flag URG | integer | Count of packets with URG flag set |
| `tcpseq.dst` | TCP Dst Seq | integer | Destination SYN-ACK sequence number |
| `tcpseq.src` | TCP Src Seq | integer | Source SYN sequence number |
| `tls.sessionid` | Src or Dst Session Id | termfield | Shorthand for tls.sessionid.src or tls.sessionid.dst |
| `ttl.dst` | TTL Dst | integer | Destination IP TTL for first few packets |
| `ttl.src` | TTL Src | integer | Source IP TTL for first few packets |
| `user` | User | termfield | External user set for session |
| `vlan` | VLan | integer | vlan value |
| `vni` | VNI | integer | vni value |

### HTTP

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `host.http` | Hostname | termfield | HTTP host header field |
| `host.http` | Hostname | termfield | HTTP host header field |
| `host.http.tokens` | Hostname Tokens | textfield | HTTP host Tokens header field |
| `http.authtype` | Auth Type | termfield | HTTP Auth Type |
| `http.bodymagic` | Body Magic | termfield | The content type of body determined by libfile/magic |
| `http.bodymagic` | Body Magic | termfield | The content type of body determined by libfile/magic |
| `http.cookie.key` | Cookie Keys | termfield | The keys to cookies sent up in requests |
| `http.cookie.value` | Cookie Values | termfield | The values to cookies sent up in requests |
| `http.hasheader` | Has Src or Dst Header | termfield | Shorthand for http.hasheader.src or http.hasheader.dst |
| `http.hasheader.dst` | Has Dst Header | termfield | Response has header present |
| `http.hasheader.dst.value` | Response Header Values | termfield | Contains response header values |
| `http.hasheader.src` | Has Src Header | termfield | Request has header present |
| `http.hasheader.src.value` | Request Header Values | termfield | Contains request header values |
| `http.hasheader.value` | Has Value in Src or Dst Header | termfield | Shorthand for http.hasheader.src.value or http.hasheader.dst.value |
| `http.header.request.field` | Request Header Fields | termfield | Contains Request header fields |
| `http.header.response.field` | Response Header fields | termfield | Contains response header fields |
| `http.md5` | Body MD5 | termfield | MD5 of http body response |
| `http.md5` | Body MD5 | termfield | MD5 of http body response |
| `http.method` | Request Method | termfield | HTTP Request Method |
| `http.method` | Request Method | termfield | HTTP Request Method |
| `http.reqbody` | Request Body | termfield | HTTP Request Body |
| `http.sha256` | Body SHA256 | termfield | SHA256 of http body response |
| `http.sha256` | Body SHA256 | termfield | SHA256 of http body response |
| `http.statuscode` | Status Code | integer | Response HTTP numeric status code |
| `http.statuscode` | Status Code | integer | Response HTTP numeric status code |
| `http.uri` | URI | termfield | URIs for request |
| `http.uri.key` | QS Keys | termfield | Keys from query string of URI |
| `http.uri.path` | URI Path | termfield | Path portion of URI |
| `http.uri.tokens` | URI Tokens | textfield | URIs Tokens for request |
| `http.uri.value` | QS Values | termfield | Values from query string of URI |
| `http.user` | User | termfield | HTTP Auth User |
| `http.user-agent` | Useragent | termfield | User-Agent Header |
| `http.user-agent.tokens` | Useragent Tokens | textfield | User-Agent Header Tokens |
| `http.version` | Version | termfield | HTTP version number |
| `http.version.dst` | Dst Version | termfield | Response HTTP version number |
| `http.version.src` | Src Version | termfield | Request HTTP version number |
| `ip.xff` | XFF IP | ip | X-Forwarded-For Header |

### IRC

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `irc.channel` | Channel | termfield | Channels joined |
| `irc.nick` | Nickname | termfield | Nicknames set |

### ISIS

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `isis.msgType` | isis.msgType | termfield | ISIS Msg Type field |

### KRB5

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `krb5.cname` | cname | termfield | Kerberos 5 cname |
| `krb5.realm` | Realm | termfield | Kerberos 5 Realm |
| `krb5.sname` | sname | termfield | Kerberos 5 sname |

### LDAP

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `ldap.authtype` | Auth Type | termfield | The auth type of ldap bind |
| `ldap.bindname` | Bind Name | termfield | The bind name of ldap bind |

### MODBUS

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `modbus.exccode` | Modbus Exception Code | integer | Modbus Exception Codes |
| `modbus.funccode` | Modbus Function Code | integer | Modbus Function Codes |
| `modbus.protocolid` | Modbus Protocol ID | integer | Modbus Protocol ID (should always be 0) |
| `modbus.transactionid` | Modbus Transaction IDs | integer | Modbus Transaction IDs |
| `modbus.unitid` | Modbus Unit ID | integer | Modbus Unit ID |

### MYSQL

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `mysql.user` | User | termfield | Mysql user name |
| `mysql.ver` | Version | termfield | Mysql server version string |

### ORACLE

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `oracle.host` | Host | termfield | Oracle Host |
| `oracle.host.tokens` | Hostname Tokens | textfield | Oracle Hostname Tokens |
| `oracle.service` | Service | termfield | Oracle Service |
| `oracle.user` | User | termfield | Oracle User |

### POSTGRESQL

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `postgresql.app` | Application | termfield | Postgresql application |
| `postgresql.db` | Database | termfield | Postgresql database |
| `postgresql.user` | User | termfield | Postgresql user name |

### QUIC

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `host.quic` | Hostname | termfield | QUIC host header field |
| `host.quic.tokens` | Hostname Tokens | textfield | QUIC host tokens header field |
| `quic.user-agent` | User-Agent | termfield | User-Agent |
| `quic.version` | Version | termfield | QUIC Version |

### RADIUS

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `radius.endpoint-ip` | Endpoint IP | ip | Radius endpoint ip addresses for session |
| `radius.framed-ip` | Framed IP | ip | Radius framed ip addresses for session |
| `radius.mac` | MAC | termfield | Radius Mac |
| `radius.user` | User | termfield | RADIUS user |

### SMB

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `host.smb` | Hostname | termfield | SMB Host name |
| `host.smb.tokens` | Hostname Tokens | textfield | SMB Host Tokens |
| `smb.dialect` | Dialect | termfield | SMB Dialect information |
| `smb.domain` | Domain | termfield | SMB domain |
| `smb.fn` | Filename | termfield | SMB files opened, created, deleted |
| `smb.os` | OS | termfield | SMB OS information |
| `smb.share` | Share | termfield | SMB shares connected to |
| `smb.user` | User | termfield | SMB User |
| `smb.ver` | Version | termfield | SMB Version information |

### SNMP

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `snmp.community` | Community | termfield | SNMP Community |
| `snmp.error` | Error Code | integer | SNMP Error Code |
| `snmp.type` | Type | termfield | SNMP Type |
| `snmp.variable` | Variable | termfield | SNMP Variable |
| `snmp.version` | Version | integer | SNMP Version |

### SOCKS

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `host.socks` | Host | termfield | SOCKS destination host |
| `host.socks.tokens` | Hostname Tokens | textfield | SOCKS Hostname Tokens |
| `ip.socks` | IP | ip | SOCKS destination IP |
| `port.socks` | Port | integer | SOCKS destination port |
| `socks.user` | User | termfield | SOCKS authenticated user |

### SSH

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `ssh.hassh` | HASSH | termfield | SSH HASSH field |
| `ssh.hasshServer` | HASSH Server | termfield | SSH HASSH Server field |
| `ssh.key` | Key | termfield | SSH Key |
| `ssh.ver` | Version | termfield | SSH Software Version |

### SURICATA

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `suricata.action` | Action | termfield | Suricata Action |
| `suricata.category` | Category | termfield | Suricata Category |
| `suricata.flowId` | Flow Id | termfield | Suricata Flow Id |
| `suricata.gid` | Gid | integer | Suricata Gid |
| `suricata.severity` | Severity | integer | Suricata Severity |
| `suricata.signature` | Signature | termfield | Suricata Signature |
| `suricata.signatureId` | Signature Id | integer | Suricata Signature Id |

### TLS

| 字段ID | 友好名称 | 类型 | 帮助信息 |
|--------|----------|------|----------|
| `tls.cipher` | Cipher | termfield | SSL/TLS cipher field |
| `tls.ja3` | JA3 | termfield | SSL/TLS JA3 field |
| `tls.ja3s` | JA3S | termfield | SSL/TLS JA3S field |
| `tls.ja3sstring` | JA3SSTR | termfield | SSL/TLS JA3S String field |
| `tls.ja3string` | JA3STR | termfield | SSL/TLS JA3 String field |
| `tls.ja4` | JA4 | termfield | SSL/TLS JA4 field |
| `tls.ja4` | JA4 | termfield | SSL/TLS JA4 field |
| `tls.ja4_r` | JA4_r | termfield | SSL/TLS JA4_r field |
| `tls.ja4_r` | JA4_r | termfield | SSL/TLS JA4_r field |
| `tls.sessionid.dst` | Dst Session Id | termfield | SSL/TLS Dst Session Id |
| `tls.sessionid.src` | Src Session Id | termfield | SSL/TLS Src Session Id |
| `tls.version` | Version | termfield | SSL/TLS version field |


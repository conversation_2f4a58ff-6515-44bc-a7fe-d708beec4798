#!/usr/bin/env python3
"""
测试 Arkime Info 列显示功能

这个脚本验证：
1. 字段注册是否包含正确的 dbField 属性
2. 会话数据是否包含对应的字段
3. Info 列配置是否正确
"""

import json
import urllib.request
from datetime import datetime

class InfoColumnTest:
    def __init__(self, es_url="http://localhost:9200"):
        self.es_url = es_url
        
    def test_field_dbfield_mapping(self):
        """测试字段的 dbField 映射"""
        print("=== 测试字段 dbField 映射 ===")
        
        # Info 列中配置的字段
        info_fields = [
            "protocols",
            "host.http", 
            "http.method",
            "http.uri",
            "sdx.linename1",
            "sdx.linename2"
        ]
        
        success = True
        for field_id in info_fields:
            try:
                req = urllib.request.Request(f"{self.es_url}/arkime_fields/_doc/{field_id}")
                with urllib.request.urlopen(req) as response:
                    result = response.read().decode('utf-8')
                    data = json.loads(result)
                    
                    if data.get('found'):
                        source = data['_source']
                        dbField = source.get('dbField')
                        dbField2 = source.get('dbField2')
                        friendlyName = source.get('friendlyName')
                        
                        print(f"✅ {field_id}:")
                        print(f"   friendlyName: {friendlyName}")
                        print(f"   dbField: {dbField}")
                        print(f"   dbField2: {dbField2}")
                    else:
                        print(f"❌ {field_id}: 字段未找到")
                        success = False
                        
            except Exception as e:
                print(f"❌ {field_id}: 检查失败 - {e}")
                success = False
                
        return success
    
    def test_session_data_fields(self):
        """测试会话数据中的字段"""
        print("\n=== 测试会话数据字段 ===")
        
        # 获取今天的索引
        date_str = datetime.now().strftime("%y%m%d")
        index_name = f"arkime_sessions3-{date_str}"
        
        try:
            req = urllib.request.Request(f"{self.es_url}/{index_name}/_search?size=1")
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)
                
                hits = data['hits']['hits']
                if len(hits) > 0:
                    session = hits[0]['_source']
                    print(f"✅ 找到会话数据，检查字段:")
                    
                    # 检查 Info 列需要的字段
                    expected_fields = {
                        'protocols': '协议列表',
                        'http.host': 'HTTP 主机（在 http 对象中）',
                        'http.method': 'HTTP 方法（在 http 对象中）', 
                        'http.uri': 'HTTP URI（在 http 对象中）',
                        'sdx.linename1': 'SDX Line Name 1（在 sdx 对象中）',
                        'sdx.linename2': 'SDX Line Name 2（在 sdx 对象中）'
                    }
                    
                    # 检查顶级字段
                    if 'protocols' in session:
                        print(f"  ✅ protocols: {session['protocols']}")
                    else:
                        print(f"  ❌ protocols: 字段缺失")
                    
                    # 检查 HTTP 字段
                    if 'http' in session:
                        http_data = session['http']
                        print(f"  ✅ http 对象存在: {list(http_data.keys())}")
                        
                        for field in ['host', 'method', 'uri']:
                            if field in http_data:
                                print(f"    ✅ http.{field}: {http_data[field]}")
                            else:
                                print(f"    ❌ http.{field}: 字段缺失")
                    else:
                        print(f"  ❌ http: 对象缺失")
                    
                    # 检查 SDX 字段
                    if 'sdx' in session:
                        sdx_data = session['sdx']
                        print(f"  ✅ sdx 对象存在: {sdx_data}")
                        
                        for field in ['linename1', 'linename2']:
                            if field in sdx_data:
                                print(f"    ✅ sdx.{field}: {sdx_data[field]}")
                            else:
                                print(f"    ❌ sdx.{field}: 字段缺失")
                    else:
                        print(f"  ❌ sdx: 对象缺失")
                    
                    return True
                else:
                    print("❌ 没有找到会话数据")
                    return False
                    
        except Exception as e:
            print(f"❌ 会话数据检查失败: {e}")
            return False
    
    def test_info_column_config(self):
        """测试 Info 列配置"""
        print("\n=== 测试 Info 列配置 ===")
        
        try:
            with open('viewer/vueapp/src/components/sessions/customCols.json', 'r') as f:
                config = json.load(f)
                
            info_config = config.get('info', {})
            children = info_config.get('children', [])
            
            print(f"✅ Info 列配置:")
            print(f"   friendlyName: {info_config.get('friendlyName')}")
            print(f"   dbField: {info_config.get('dbField')}")
            print(f"   children: {children}")
            
            # 检查每个子字段是否在 ES 中注册
            print(f"\n检查子字段注册状态:")
            for child in children:
                try:
                    req = urllib.request.Request(f"{self.es_url}/arkime_fields/_doc/{child}")
                    with urllib.request.urlopen(req) as response:
                        result = response.read().decode('utf-8')
                        data = json.loads(result)
                        
                        if data.get('found'):
                            source = data['_source']
                            print(f"  ✅ {child}: {source.get('friendlyName')} (dbField: {source.get('dbField')})")
                        else:
                            print(f"  ❌ {child}: 字段未注册")
                            
                except Exception as e:
                    print(f"  ❌ {child}: 检查失败 - {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Info 列配置检查失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("Arkime Info 列显示功能测试")
        print("=" * 60)
        
        success = True
        
        # 测试字段 dbField 映射
        success &= self.test_field_dbfield_mapping()
        
        # 测试会话数据字段
        success &= self.test_session_data_fields()
        
        # 测试 Info 列配置
        success &= self.test_info_column_config()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ Info 列测试通过！")
            print("\n分析结果:")
            print("1. 字段注册包含正确的 dbField 属性")
            print("2. 会话数据包含对应的字段")
            print("3. Info 列配置正确")
            print("\n如果 Info 列仍然不显示内容，可能的原因:")
            print("- 前端缓存问题，尝试刷新浏览器")
            print("- Viewer 需要重启以加载新的字段定义")
            print("- 字段映射需要时间生效")
        else:
            print("❌ Info 列测试失败，请检查错误信息")
        
        return success


def main():
    tester = InfoColumnTest()
    success = tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
"""
Arkime 字段注册与数据发送工具

这个脚本提供了完整的 Arkime 字段注册和测试数据发送功能：
1. 创建必需的 ES 索引和别名
2. 注册自定义字段定义
3. 创建管理员用户
4. 发送测试会话数据
5. 验证字段注册和显示效果

使用方法:
    python3 arkime_field_registration_tool.py [选项]

选项:
    --setup-indices     创建索引和别名
    --register-fields   注册字段定义
    --create-user       创建管理员用户
    --send-data         发送测试会话数据
    --send-files        发送文件数据
    --verify            验证注册效果
    --all               执行所有操作（默认）
    --es-url URL        ES服务器地址（默认: http://localhost:9200）
"""

import json
import urllib.request
import urllib.parse
import argparse
import sys
from datetime import datetime

class ArkimeFieldRegistrationTool:
    def __init__(self, es_url="http://localhost:9200"):
        self.es_url = es_url
        
    def create_index_with_alias(self, index_name, alias_name, settings=None, mapping=None):
        """创建索引并设置别名"""
        print(f"创建索引 {index_name} 并设置别名 {alias_name}")
        
        # 删除现有索引（如果存在）
        try:
            req = urllib.request.Request(f"{self.es_url}/{index_name}", method='DELETE')
            with urllib.request.urlopen(req) as response:
                print(f"  删除现有索引: {response.status}")
        except Exception:
            pass  # 索引不存在，忽略错误
        
        # 索引配置
        index_config = {
            "settings": settings or {
                "index.priority": 100,
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "auto_expand_replicas": "0-3"
            }
        }
        
        if mapping:
            index_config["mappings"] = mapping
        
        # 创建索引
        try:
            req = urllib.request.Request(
                f"{self.es_url}/{index_name}",
                data=json.dumps(index_config).encode('utf-8'),
                headers={'Content-Type': 'application/json'},
                method='PUT'
            )
            
            with urllib.request.urlopen(req) as response:
                print(f"  创建索引: {response.status}")
                
        except Exception as e:
            print(f"  创建索引失败: {e}")
            return False
        
        # 创建别名
        try:
            alias_config = {
                "actions": [
                    {
                        "add": {
                            "index": index_name,
                            "alias": alias_name
                        }
                    }
                ]
            }
            
            req = urllib.request.Request(
                f"{self.es_url}/_aliases",
                data=json.dumps(alias_config).encode('utf-8'),
                headers={'Content-Type': 'application/json'},
                method='POST'
            )
            
            with urllib.request.urlopen(req) as response:
                print(f"  创建别名: {response.status}")
                return True
                
        except Exception as e:
            print(f"  创建别名失败: {e}")
            return False

    def setup_indices(self):
        """设置所有必需的索引"""
        print("=== 设置 Arkime 索引和别名 ===")
        
        indices = [
            {
                "index": "arkime_fields_v30",
                "alias": "arkime_fields",
                "mapping": {
                    "dynamic_templates": [
                        {
                            "string_template": {
                                "match_mapping_type": "string",
                                "mapping": {
                                    "type": "keyword"
                                }
                            }
                        }
                    ]
                }
            },
            {
                "index": "arkime_files_v30",
                "alias": "arkime_files",
                "mapping": {
                    "properties": {
                        "num": {"type": "long"},
                        "name": {"type": "keyword"},
                        "first": {"type": "date"},
                        "node": {"type": "keyword"},
                        "filesize": {"type": "long"},
                        "locked": {"type": "long"}
                    }
                }
            },
            {
                "index": "arkime_users_v30",
                "alias": "arkime_users",
                "mapping": {
                    "properties": {
                        "userId": {"type": "keyword"},
                        "userName": {"type": "keyword"},
                        "enabled": {"type": "boolean"},
                        "webEnabled": {"type": "boolean"},
                        "headerAuthEnabled": {"type": "boolean"},
                        "emailSearch": {"type": "boolean"},
                        "removeEnabled": {"type": "boolean"},
                        "packetSearch": {"type": "boolean"}
                    }
                }
            },
            {
                "index": "arkime_sequence_v30",
                "alias": "arkime_sequence"
            }
        ]
        
        success = True
        for idx in indices:
            result = self.create_index_with_alias(
                idx["index"], 
                idx["alias"], 
                mapping=idx.get("mapping")
            )
            success &= result
        
        return success

    def register_fields(self):
        """注册字段定义"""
        print("\n=== 注册字段定义 ===")

        # 完整的字段定义 - 基于 Arkime 实际字段格式
        fields = [
            # SDX 字段
            {
                "field_id": "sdx.linename1",
                "definition": {
                    "friendlyName": "SDX Line Name 1",
                    "group": "sdx",
                    "help": "SDX protocol line name 1",
                    "dbField": "sdx.linename1",
                    "dbField2": "sdx.linename1",
                    "type": "termfield",
                    "category": "sdx"
                }
            },
            {
                "field_id": "sdx.linename2",
                "definition": {
                    "friendlyName": "SDX Line Name 2",
                    "group": "sdx",
                    "help": "SDX protocol line name 2",
                    "dbField": "sdx.linename2",
                    "dbField2": "sdx.linename2",
                    "type": "termfield",
                    "category": "sdx"
                }
            },
            # HTTP 字段 - 使用正确的字段 ID 和格式
            {
                "field_id": "http.method",
                "definition": {
                    "friendlyName": "Request Method",
                    "group": "http",
                    "help": "HTTP Request Method",
                    "dbField": "http.method",
                    "dbField2": "http.method",
                    "type": "termfield"
                }
            },
            {
                "field_id": "host.http",
                "definition": {
                    "friendlyName": "Hostname",
                    "group": "http",
                    "help": "HTTP host header field",
                    "dbField": "http.host",
                    "dbField2": "http.host",
                    "type": "lotermfield",
                    "aliases": ["http.host"],
                    "category": "host"
                }
            },
            {
                "field_id": "http.uri",
                "definition": {
                    "friendlyName": "URI",
                    "group": "http",
                    "help": "URIs for request",
                    "dbField": "http.uri",
                    "dbField2": "http.uri",
                    "type": "termfield",
                    "category": ["url", "host"]
                }
            },
            # HTTP 嵌套字段的顶级映射 - 为 Info 列提供直接访问
            {
                "field_id": "http.host.flat",
                "definition": {
                    "friendlyName": "HTTP Host (Flat)",
                    "group": "http",
                    "help": "HTTP host for info column display",
                    "dbField": "http",
                    "dbField2": "http",
                    "type": "termfield",
                    "category": "host"
                }
            },
            {
                "field_id": "http.method.flat",
                "definition": {
                    "friendlyName": "HTTP Method (Flat)",
                    "group": "http",
                    "help": "HTTP method for info column display",
                    "dbField": "http",
                    "dbField2": "http",
                    "type": "termfield"
                }
            },
            {
                "field_id": "http.uri.flat",
                "definition": {
                    "friendlyName": "HTTP URI (Flat)",
                    "group": "http",
                    "help": "HTTP URI for info column display",
                    "dbField": "http",
                    "dbField2": "http",
                    "type": "termfield",
                    "category": ["url", "host"]
                }
            },

        ]
        
        # 构建 bulk 请求
        bulk_data = []
        for field in fields:
            index_op = {"index": {"_index": "arkime_fields", "_id": field["field_id"]}}
            bulk_data.append(json.dumps(index_op))
            bulk_data.append(json.dumps(field["definition"]))
        
        bulk_body = "\n".join(bulk_data) + "\n"
        
        # 发送到 ES
        try:
            req = urllib.request.Request(
                f"{self.es_url}/_bulk",
                data=bulk_body.encode('utf-8'),
                headers={'Content-Type': 'application/x-ndjson'}
            )
            
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)
                
                print(f"字段注册结果: {response.status}")
                if data.get('errors'):
                    print("注册过程中有错误:")
                    for item in data['items']:
                        if 'error' in item.get('index', {}):
                            print(f"  - {item['index']['error']}")
                    return False
                else:
                    print(f"成功注册 {len(fields)} 个字段")
                    return True
                    
        except Exception as e:
            print(f"字段注册失败: {e}")
            return False

    def create_admin_user(self):
        """创建管理员用户"""
        print("\n=== 创建管理员用户 ===")
        
        # 管理员用户数据（密码: admin）
        admin_user = {
            "userId": "admin",
            "userName": "admin",
            "passStore": "c005b121ffc27f1ce1c8b28712c4460b.d8bc5d10f54e71879a991b6801e51614395bca607e9ea8d030487bd8cb95dd568e43973e400b9714443802fc3eebe75c",
            "enabled": True,
            "webEnabled": True,
            "headerAuthEnabled": False,
            "emailSearch": False,
            "removeEnabled": False,
            "packetSearch": True,
            "welcomeMsgNum": 1,
            "settings": {},
            "roles": ["superAdmin"],
            "createEnabled": False
        }
        
        try:
            req = urllib.request.Request(
                f"{self.es_url}/arkime_users/_doc/admin",
                data=json.dumps(admin_user).encode('utf-8'),
                headers={'Content-Type': 'application/json'},
                method='PUT'
            )
            
            with urllib.request.urlopen(req) as response:
                print(f"创建管理员用户: {response.status}")
                print("用户名: admin, 密码: admin")
                return True
                
        except Exception as e:
            print(f"创建管理员用户失败: {e}")
            return False

    def send_test_data(self):
        """发送测试会话数据"""
        print("\n=== 发送测试会话数据 ===")

        # 当前日期的索引
        date_str = datetime.now().strftime("%y%m%d")
        index_name = f"arkime_sessions3-{date_str}"

        # 测试会话数据
        sessions = [
            {
                "@timestamp": int(datetime.now().timestamp() * 1000),
                "firstPacket": 1041342931300,
                "lastPacket": 1041342932300,
                "length": 1000,
                "ipProtocol": 6,
                "node": "localhost",
                "source": {
                    "ip": "********",
                    "port": 3267,
                    "bytes": 207,
                    "packets": 1,
                    "mac": ["00:09:6b:88:f5:c9"]
                },
                "destination": {
                    "ip": "*************",
                    "port": 80,
                    "bytes": 0,
                    "packets": 0,
                    "mac": ["00:e0:81:00:b0:28"]
                },
                "network": {
                    "packets": 1,
                    "bytes": 207
                },
                "packetPos": [-1, 24],
                "fileId": [1],
                "protocols": ["tcp", "http", "sdx"],
                "http": {
                    "method": ["HEAD"],
                    "host": ["windowsupdate.microsoft.com"],
                    "uri": ["/v4/iuident.cab"]
                },
                "sdx": {
                    "linename1": "11111",
                    "linename2": "22222"
                }
            },
            {
                "@timestamp": int(datetime.now().timestamp() * 1000) + 1000,
                "firstPacket": 1041342931400,
                "lastPacket": 1041342932400,
                "length": 1500,
                "ipProtocol": 6,
                "node": "localhost",
                "source": {
                    "ip": "*************",
                    "port": 8080,
                    "bytes": 512,
                    "packets": 2
                },
                "destination": {
                    "ip": "********",
                    "port": 443,
                    "bytes": 1024,
                    "packets": 3
                },
                "network": {
                    "packets": 5,
                    "bytes": 1536
                },
                "packetPos": [-1, 24],
                "fileId": [1],
                "protocols": ["tcp", "http", "sdx"],
                "http": {
                    "method": ["GET"],
                    "host": ["api.example.com"],
                    "uri": ["/v1/data"]
                },
                "sdx": {
                    "linename1": "33333",
                    "linename2": "44444"
                }
            }
        ]

        # 构建 bulk 请求
        bulk_data = []
        for session in sessions:
            index_op = {"index": {"_index": index_name}}
            bulk_data.append(json.dumps(index_op))
            bulk_data.append(json.dumps(session))

        bulk_body = "\n".join(bulk_data) + "\n"

        # 发送会话数据
        try:
            req = urllib.request.Request(
                f"{self.es_url}/_bulk",
                data=bulk_body.encode('utf-8'),
                headers={'Content-Type': 'application/x-ndjson'}
            )

            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)

                print(f"会话数据发送结果: {response.status}")
                if data.get('errors'):
                    print("发送过程中有错误:")
                    for item in data['items']:
                        if 'error' in item.get('index', {}):
                            print(f"  - {item['index']['error']}")
                    return False
                else:
                    print(f"成功发送 {len(sessions)} 个会话到索引: {index_name}")
                    return True

        except Exception as e:
            print(f"会话数据发送失败: {e}")
            return False

    def send_file_data(self):
        """发送文件数据到 arkime_files 索引"""
        print("\n=== 发送文件数据 ===")

        # 创建文件数据
        file_data = {
            "num": 1,
            "name": "/opt/arkime/raw/test-http.pcap",
            "first": int(datetime.now().timestamp() * 1000),
            "node": "localhost",
            "filesize": 247,
            "locked": 0
        }

        try:
            req = urllib.request.Request(
                f"{self.es_url}/arkime_files/_doc/localhost-1",
                data=json.dumps(file_data).encode('utf-8'),
                headers={'Content-Type': 'application/json'},
                method='PUT'
            )

            with urllib.request.urlopen(req) as response:
                print(f"文件数据发送结果: {response.status}")
                print(f"文件记录: {file_data['name']} (节点: {file_data['node']})")
                return True

        except Exception as e:
            print(f"文件数据发送失败: {e}")
            return False

    def verify_registration(self):
        """验证字段注册和数据"""
        print("\n=== 验证字段注册和数据 ===")

        success = True

        # 验证字段注册
        try:
            req = urllib.request.Request(f"{self.es_url}/arkime_fields/_search?size=100")
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)

                fields_by_group = {}
                for hit in data['hits']['hits']:
                    field_id = hit['_id']
                    source = hit['_source']
                    group = source.get('group', 'unknown')

                    if group not in fields_by_group:
                        fields_by_group[group] = []
                    fields_by_group[group].append({
                        'id': field_id,
                        'friendlyName': source.get('friendlyName', 'unknown')
                    })

                print(f"✅ 总共注册了 {data['hits']['total']['value']} 个字段:")
                for group, fields in sorted(fields_by_group.items()):
                    print(f"  组 '{group}' ({len(fields)} 个字段):")
                    for field in fields:
                        print(f"    - {field['id']}: {field['friendlyName']}")

        except Exception as e:
            print(f"❌ 字段验证失败: {e}")
            success = False

        # 验证会话数据
        try:
            date_str = datetime.now().strftime("%y%m%d")
            index_name = f"arkime_sessions3-{date_str}"

            req = urllib.request.Request(f"{self.es_url}/{index_name}/_search?q=protocols:sdx")
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)

                hit_count = data['hits']['total']['value']
                print(f"\n✅ 找到 {hit_count} 个包含 SDX 协议的会话")

                if hit_count > 0:
                    for hit in data['hits']['hits']:
                        session = hit['_source']
                        print(f"  会话: {session.get('source', {}).get('ip')} -> {session.get('destination', {}).get('ip')}")
                        print(f"    协议: {session.get('protocols', [])}")
                        if 'sdx' in session:
                            print(f"    SDX: {session['sdx']}")
                        if 'http' in session:
                            print(f"    HTTP: {session['http']}")

        except Exception as e:
            print(f"❌ 会话数据验证失败: {e}")
            success = False

        return success

    def run_all(self):
        """执行所有操作"""
        print("Arkime 字段注册与数据发送工具")
        print("=" * 60)

        success = True

        # # 设置索引
        # success &= self.setup_indices()

        # 等待 ES 处理
        import time
        time.sleep(2)

        # 注册字段
        success &= self.register_fields()

        # 创建用户
        success &= self.create_admin_user()

        # 发送数据
        success &= self.send_test_data()

        # 发送文件数据
        success &= self.send_file_data()

        # 验证结果
        success &= self.verify_registration()

        print("\n" + "=" * 60)
        if success:
            print("✅ 所有操作完成成功！")
            print("\n下一步操作:")
            print("1. 创建 SDX 详情模板:")
            print("   cat > capture/parsers/sdx.detail.jade << 'EOF'")
            print("if (session.sdx)")
            print("  div.sessionDetailMeta.bold SDX")
            print("  dl.sessionDetailMeta(suffix=\"sdx\")")
            print("    +arrayList(session.sdx, \"linename1\", \"Line Name 1\", \"sdx.linename1\")")
            print("    +arrayList(session.sdx, \"linename2\", \"Line Name 2\", \"sdx.linename2\")")
            print("EOF")
            print("\n2. 重启 Arkime Viewer 服务:")
            print("   sudo systemctl restart arkimeviewer")
            print("\n3. 访问前端:")
            print("   URL: http://localhost:8005")
            print("   用户名: admin")
            print("   密码: admin")
        else:
            print("❌ 部分操作失败，请检查错误信息")

        return success


def main():
    parser = argparse.ArgumentParser(description='Arkime 字段注册与数据发送工具')
    parser.add_argument('--setup-indices', action='store_true', help='创建索引和别名')
    parser.add_argument('--register-fields', action='store_true', help='注册字段定义')
    parser.add_argument('--create-user', action='store_true', help='创建管理员用户')
    parser.add_argument('--send-data', action='store_true', help='发送测试会话数据')
    parser.add_argument('--send-files', action='store_true', help='发送文件数据')
    parser.add_argument('--verify', action='store_true', help='验证注册效果')
    parser.add_argument('--all', action='store_true', help='执行所有操作（默认）')
    parser.add_argument('--es-url', default='http://localhost:9200', help='ES服务器地址')

    args = parser.parse_args()

    # 如果没有指定任何操作，默认执行所有操作
    if not any([args.setup_indices, args.register_fields, args.create_user,
                args.send_data, args.send_files, args.verify, args.all]):
        args.all = True

    tool = ArkimeFieldRegistrationTool(args.es_url)

    try:
        if args.all:
            success = tool.run_all()
        else:
            success = True
            # if args.setup_indices:
            #     success &= tool.setup_indices()
            if args.register_fields:
                success &= tool.register_fields()
            # if args.create_user:
            #     success &= tool.create_admin_user()
            if args.send_data:
                success &= tool.send_test_data()
            if args.send_files:
                success &= tool.send_file_data()
            if args.verify:
                success &= tool.verify_registration()

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

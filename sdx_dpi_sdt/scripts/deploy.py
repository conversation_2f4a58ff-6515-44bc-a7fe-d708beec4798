from glob import glob
import os
import subprocess
import glob
from datetime import datetime
from pathlib import Path
import shutil

name = "yaDpiSdt"
dirs = ["build_release", "build_debug"]
file_path = os.path.dirname(os.path.abspath(__file__))
remote_prefix = "/mnt/share/00_publish/"

command = "ifconfig -a | grep inet | grep -v 127.0.0.1 | grep -v inet6 | awk '{print $2}' | tr -d 'addr:'"

res = subprocess.Popen(command,
                       stdout=subprocess.PIPE,
                       stderr=subprocess.STDOUT,
                       shell=True, encoding='utf-8')
print(res)
ip = res.communicate()[0].replace("\n", "")

print(file_path)
print("----------")
print(ip)

time = datetime.now().strftime("%Y%m%d")
tmp = os.path.join(remote_prefix, name, time, ip);
print("tmp = %s" % tmp)
remote_path = remote_prefix + name + "/" + time
print(remote_path)

# check path exist, if not exist, create it， path is local path
if not os.path.exists(remote_path):
  os.makedirs(remote_path, exist_ok=True)

# command = "sshpass -p '123123' ssh -o 'StrictHostKeyChecking=no' root@************** '[ -d " + remote_path + " ] && echo ok || mkdir -p " + remote_path + "'"
# print(command)
# res = subprocess.Popen(command,
#                        stdout=subprocess.PIPE,
#                        stderr=subprocess.STDOUT,
#                        shell=True, encoding='utf-8')
# print(res)



for dir in dirs:
  print("dir = %s" % dir)
  # 判定是否为测试节点编译， ************** 为测试编译节点
  if ip.find('105.10') >= 0:
    suffix = "_test.tar.gz"
  else:
    suffix = ".tar.gz"

  if dir.find('_') >= 0:
    tmp = ("_").join(dir.split('_')[1:])
    suffix = "_" + tmp + suffix

  print("suffix = %s" % suffix)
  find_path = file_path + "/../" + dir
  print(file_path)
  file_list = glob.glob(os.path.join(find_path, "*.tar.gz" ))
  for file in file_list:
        # rename file to new name with suffix, use pathlib
    p_file = Path(file)
    r_path = Path(remote_path)
    name_without_suffix = os.path.splitext(p_file.stem)[0]
    new_file = r_path / (name_without_suffix + suffix)
    print("old_file = %s" % p_file)
    print("new_file = %s" % new_file)


    shutil.copy(p_file, new_file)

    # name = os.path.basename(file)
    # name = name.replace(".tar.gz", suffix)
    # print("name = %s" % name)

    # # cp file to remote path
    # command = "yes | cp " + file + " " + remote_path + "/" + name

    # # command = "sshpass -p '123123' scp " + file + " root@**************:" + remote_path + "/" + name
    # print(command)
    # res = subprocess.Popen(command,
    #                    stdout=subprocess.PIPE,
    #                    stderr=subprocess.STDOUT,
    #                    shell=True, encoding='utf-8')
  print(file_list)

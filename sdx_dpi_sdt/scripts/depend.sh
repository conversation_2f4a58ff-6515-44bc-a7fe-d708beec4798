#!/bin/bash

# 项目依赖安装脚本
# 数组中每个元素的格式为：项目名 项目gitlab地址 项目分支名
# 例如：project1 url_1 branch_1
# 项目名和项目分支名可以随意定义，但是项目gitlab地址必须是有效的gitlab地址
# 目前只支持 cmake 构建的项目，如果项目不是 cmake 构建的，需要自行修改脚本

# define an array of dependencies to install, include project name and gitlab url and branch name
declare -a dependenci=(
    "yaconfigweb git@192.168.20.98:dev_dpi/yaconfigweb.git master"
    "yasdxwatch git@192.168.20.98:sdx/yasdxwatch.git master"
    "yaFtypes git@192.168.20.98:infra/libyaftypes.git master"
    "yaProtoRecord git@192.168.20.98:infra/libyaprotorecord.git master"
    # "libsdt git@192.168.20.98:sdx/libsdt.git develop"
)

# define a function to install dependencies
function install_dependencies() {
    # loop through the array and install dependencies
    for i in "${dependenci[@]}"
    do
        # split the string into array
        IFS=' ' read -r -a array <<< "$i"
        # get project name
        project_name=${array[0]}
        # get project url
        project_url=${array[1]}
        # get project branch
        project_branch=${array[2]}
        # print all the information into one line
        echo "project name: $project_name, url: $project_url, branch: $project_branch"

        # get a random name for the tmp folder, and join with the project name
        tmp_folder_name=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1)_$project_name

        # clone the project to a tmp folder with a random name
        git clone -b $project_branch $project_url /tmp/$tmp_folder_name
        cd /tmp/$tmp_folder_name
        cmake3 -B build -S .
        cmake3 --build build --target install -j $(nproc - 2)
        cd ..
        rm -rf $tmp_folder_name
    done
}


# main function
function main() {
    # install dependencies
    install_dependencies
}

# call main function
main

stages:
  - build

.build-script: &buildScript
  script:
    - echo  ci name ${CI_COMMIT_REF_NAME}
    - echo runner tag ${CI_RUNNER_TAGS}
    - echo "begin build"
    # - ./scripts/depend.sh
    - git checkout develop
    - git pull
    - git submodule init
    - git submodule update --init --rebase
    - chmod +x cmake_build.sh
    - ./package.sh
    - echo "end build"
    - echo "deploy"
    - python3 scripts/deploy.py
    - echo "deploy end"

build_test:
  tags:
    - test
  stage: build
  only:
    - develop
  <<: *buildScript

build_build:
  tags:
    - build
  stage: build
  only:
    - develop
  <<: *buildScript

＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊
＊　　　　　　　　　　　　　　　　　　　　　＊
＊　  ＤＮＳ与ＨＴＴＰＳ黑白名单配置格式　　＊
＊　　　　　　　　　　　　　　　　　　　　　＊
＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊


<<<<<<<没有黑白名单需求的可以忽略此文档>>>>>>>


0:DNS的黑白名单相关配置参数共有5个	
	DNS_WHITELIST_SWITCH :dns白名单开关,0不开启,1开启,开启后会把DNS_WHITELIST_FILENAME配置的文件里的域名加入到白名单中
	DNS_BLACKLIST_SWITCH :dns黑名单开关,0不开启,1开启,开启后会把DNS_BLACKLIST_FILENAME配置的文件里的域名加入到黑名单中
	DNS_WHITELIST_FILENAME  :白名单路径,只有上面白名单开关置1时才起作用,可配置为与本文件同一目录内的相对路径,也可配置为绝对路径
	DNS_BLACKLIST_FILENAME  :黑名单路径,只有上面黑名单开关置1时才起作用,可配置为与本文件同一目录内的相对路径,也可配置为绝对路径
	DNS_DEFAULT_SWITCH :黑白名单都没有时默认开关,置0时解析,置1时不解析
	程序逻辑：当以上任何一个黑白名单开关置1<两个可以同时置1>时,程序会读取相应的文件内的域名,在解析数据时,如解出域名位于白名单上则继续,如在黑名单上则停止,如既不在黑名单也不再黑名单上,则需要察看DNS_DEFAULT_SWITCH配置，如置０则继续，置１则停止

　HTTPS的黑白名单五个配置选项是:
	HTTPS_DEFAULT_SWITCH
	HTTPS_WHITELIST_SWITCH 
	HTTPS_BLACKLIST_SWITCH 
	HTTPS_WHITELIST_FILENAME 
	HTTPS_BLACKLIST_FILENAME 
	逻辑与DNS相同


<<黑白名单域名格式>>

1:域名顶格写,每条占一行,字母间不要有空格,标准格式是:
www.baidu.com
不要写成
 www.baidu.com
或者
www. baidu.com

2:'#'表单行注释,程序会忽略本行#后的内容,如:
#www.baidu.com

3:程序支持通配符'*'匹配二级域名,例如:
*skype.com
将会匹配www.skype.com, urlp.asm.skype.com, c-broker-asea-01.broker.skype.com等所有以.skype.com结尾的域名,推荐使用这种方式




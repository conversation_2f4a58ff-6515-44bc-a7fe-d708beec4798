-- X509Cer.lua
local mapping = {
  from_proto_name = "X509Cer",
  to_proto_name = "X509Cer",
  rule_proto_name = "cert",
  common_flag = true,
  field = {
    {from_name = ""                   ,to_name = "certFullText"      },
    {from_name = ""                   ,to_name = "Certype"           },
    {from_name = ""                   ,to_name = "KeyPur"            },
    {from_name = ""                   ,to_name = "basicCons"         },
    {from_name = "basicConsPathLen"   ,to_name = "basicConsPathLen"  ,rule_name = "basic_path_length"        ,tll = 1},
    {from_name = "basicConsCA"        ,to_name = "basicConsCA"       ,rule_name = "basic_ca"                 ,tll = 1},
    {from_name = "authAccess"         ,to_name = "authinfo"          ,rule_name = "auth_info"                ,tll = 1},
    {from_name = "dns_num"            ,to_name = "subAltDNSCnt"      ,tll = 1},
    {from_name = "totaltime"          ,to_name = "daysTotal"         ,tll = 1},
    {from_name = "extension"          ,to_name = "extSet"            ,rule_name = "extend_id_list"           ,tll = 1},
    {from_name = "print"              ,to_name = "hash"              ,rule_name = "fingerprint"              ,tll = 1},
    {from_name = "print_alg"          ,to_name = "fpAlg"             ,rule_name = "fingerprint_algorithm "   ,tll = 1},
    {from_name = "subj_pubkey"        ,to_name = "pubkey"            ,rule_name = "public_key"               ,tll = 1},
    {from_name = "lasttime"           ,to_name = "daysRem"           ,rule_name = "validity_remaining"       ,tll = 1},
    {from_name = "subject"            ,to_name = "subject"           ,rule_name = "subject"                  ,tll = 1},
    {from_name = "issuer"             ,to_name = "issuer"            ,rule_name = "cert_issuer"                   ,tll = 1},
    {from_name = ""                   ,to_name = "Protabname"        },
    {from_name = "ext_sums"           ,to_name = "extCnt"            ,rule_name = "extend_count"             ,tll = 1},
    {from_name = "access_location"    ,to_name = "certAuthInfAccLoc" },
    {from_name = "access_method"      ,to_name = "certAuthInfAccMet" },
    {from_name = "crl_dist_points"    ,to_name = "certRevListSrc"    ,rule_name = "distribute_points"    ,tll = 1},
    {from_name = ""                   ,to_name = "extKeyUsage"       },
    {from_name = ""                   ,to_name = "subDirAtt"         },
    {from_name = "issuer_other"       ,to_name = "issAltName"        },
    {from_name = "issuer_ip"          ,to_name = "issAltIP"          },
    {from_name = "issuer_dns"         ,to_name = "issAltNameSys"     },
    {from_name = "subj_other"         ,to_name = "subAltName"        },
    {from_name = "subj_ip"            ,to_name = "subAltIP"          ,rule_name = "alternative_ip"           ,tll = 1},
    {from_name = "subj_dns"           ,to_name = "subAltDNS"         ,rule_name = "alternative_domain"       ,tll = 1},
    {from_name = "policy"             ,to_name = "certPol"           ,rule_name = "policies"                 ,tll = 1},
    {from_name = "secretkey_endtime"  ,to_name = "priKeyUsaPerNotAft"},
    {from_name = "secretkey_begintime",to_name = "priKeyUsaPerNotBef"},
    {from_name = "key_usage"          ,to_name = "keyUsage"          ,rule_name = "key_usage"                ,tll = 1},
    {from_name = "subj_key_id"        ,to_name = "subKeyID"          ,rule_name = "subject_key_id"           ,tll = 1},
    {from_name = "auth_key_id"        ,to_name = "authKeyID"         ,rule_name = "auth_key_id"              ,tll = 1},
    {from_name = "signature"          ,to_name = "sigVal"            },
    {from_name = "signature_alg"      ,to_name = "sigAlg"            ,rule_name = "algorithm_id"             ,tll = 1},
    {from_name = "dsa_g"              ,to_name = "DSAPubKeyG"        },
    {from_name = "dsa_q"              ,to_name = "DSAPubKeyQ"        },
    {from_name = "dsa_p"              ,to_name = "DSAPubKeyP"        },
    {from_name = "dh_publickey"       ,to_name = "DHPubKey"          },
    {from_name = "dh_base"            ,to_name = "DHPGen"            },
    {from_name = "dh_module"          ,to_name = "DHPriMod"          },
    {from_name = "key_exponent"       ,to_name = "RSAExp"            },
    {from_name = "key_module"         ,to_name = "RSAMod"            },
    {from_name = "endtime"            ,to_name = "valNotAft"         ,rule_name = "validity_notafter"        ,tll = 1},
    {from_name = "begintime"          ,to_name = "valNotBef"         ,rule_name = "validity_notbefore"       ,tll = 1},
    {from_name = "subject_email"      ,to_name = "subPosOffBox"      },
    {from_name = "subject_unit"       ,to_name = "subOrgUniName"     },
    {from_name = "subject_party"      ,to_name = "subOrgName"        ,rule_name = "subject_organization"               ,tll = 1},
    {from_name = "issuer_road"        ,to_name = "subStrAddr"        },
    {from_name = "subject_province"   ,to_name = "subStaOrProName"   },
    {from_name = "subject_locality"   ,to_name = "subLoaName"        },
    {from_name = "subject_nation"     ,to_name = "subConName"        },
    {from_name = "subject_firstname"  ,to_name = "subComName"        ,rule_name = "subject_common_name"      ,tll = 1},
    {from_name = "issuer_email"       ,to_name = "issPosOffBox"      },
    {from_name = "issuer_unit"        ,to_name = "issOrgUniName"     },
    {from_name = "issuer_party"       ,to_name = "issOrgName"        ,rule_name = "issuer_organization"      ,tll = 1},
    {from_name = "issuer_road"        ,to_name = "issStrAddr"        },
    {from_name = "issuer_province"    ,to_name = "issStaOrProName"   },
    {from_name = "issuer_locality"    ,to_name = "issLoaName"        },
    {from_name = "issuer_nation"      ,to_name = "issConName"        },
    {from_name = "issuer_firstname"   ,to_name = "issComName"        ,rule_name = "issuer_common_name"       ,tll = 1},
    {from_name = "issuer_length"      ,to_name = "issDataLen"        },
    {from_name = "sequence"           ,to_name = "srvNum"            ,rule_name = "sn"                       ,tll = 1},
    {from_name = "version"            ,to_name = "ver"               ,rule_name = "version"                  ,tll = 1},
    {from_name = ""                   ,to_name = "ProtabID"          },

    
  }
}
yalua_register_proto(mapping)

#! /usr/bin/python3
import sys
import json

def genLuaAdapterFromJson(jsonFilePath):
    with open(jsonFilePath) as f:
        jsonMapping = json.load(f)
        from_names = [item.get('from_name') for item in jsonMapping.get('field', [])]
        to_names = [item.get('to_name') for item in jsonMapping.get('field', [])]
        rule_names = [item.get('rule_name') for item in jsonMapping.get('field', [])]
        to_types =  [item.get('to_type') for item in jsonMapping.get('field', [])]
        # 获取所有键的最大长度
        from_name_lengths = {}
        for from_value in from_names:
          from_name_lengths[from_value] = max(from_name_lengths.get(from_value, 0), len(from_value))
        from_name_max_len = max(from_name_lengths.values())
        to_name_lengths = {}
        for to_value in to_names:
            to_name_lengths[to_value] =  max(from_name_lengths.get(to_value, 0), len(to_value))
        to_name_max_len = max(to_name_lengths.values())
        rule_name_lengths = {}
        for rule_value in rule_names:
          if rule_value:
              rule_name_lengths[rule_value] = max(rule_name_lengths.get(rule_value, 0), len(rule_value))
        rule_name_max_len =  0 if not rule_name_lengths else max(rule_name_lengths.values())

        to_type_lengths = {}
        for to_value in to_types:
          if to_value:
              to_type_lengths[to_value] = max(to_type_lengths.get(str(to_value), 0), len(str(to_value)))
        to_type_max_len = max(to_type_lengths.values())
        # 格式化field数组中的元素，使得每个键值对上下对齐
        format_str= ""
        formatted_fields = []
        for item in jsonMapping["field"]:
            formatted_item = '    {'
            formatted_item += f'from_name = "{item.get("from_name")}"'
            space = [' '] * (from_name_max_len - len(item.get("from_name")))
            formatted_item += ''.join(space) +  f',to_name = \"{item.get("to_name")}\"'
            space = [' '] * (to_name_max_len - len(item.get("to_name")))
            formatted_item += ''.join(space)
            if item.get("rule_name"):
                formatted_item += f',rule_name = "{item.get("rule_name")}"'
                space = [' '] * (rule_name_max_len - len(item.get("rule_name")))
                formatted_item += ''.join(space)
            if item.get("tll"):
                formatted_item += f',tll = {item.get("tll")}'
            formatted_fields.append(f'{{{formatted_item}}}')
            if item.get("desc"):
                formatted_item += f',desc = "{item.get("desc")}"'
            formatted_fields.append(f'{{{formatted_item}}}')
            format_str += formatted_item
            format_str += "},"
            format_str += "\n"
        # 使用JSON.dumps()美化输出
        formatted_json = json.dumps(jsonMapping, indent=4, ensure_ascii=False)
        #将字符串中的反斜杠引号转义
        formatted_json = formatted_json.replace('\\"', '"')
        formatted_json = formatted_json.replace('"{', '{')
        formatted_json = formatted_json.replace('}"', '}')

        to_proto_name = jsonMapping["to_proto_name"]
        from_proto_name = jsonMapping["from_proto_name"]
        rule_proto_name = jsonMapping["rule_proto_name"]
        lua_out = open("adapt_{}.lua".format(to_proto_name), "w")
        lua_out.write("-- {}.lua\n".format(to_proto_name))
        lua_out.write("local mapping = {\n")
        lua_out.write('  from_proto_name = "{}",\n'.format(from_proto_name))
        lua_out.write('  to_proto_name = "{}",\n'.format(to_proto_name))
        lua_out.write('  rule_proto_name = "{}",\n'.format(rule_proto_name))
        lua_out.write('  common_flag = true,\n')
        lua_out.write('  field = {\n')

        lua_out.write(format_str)

        lua_out.write("  }\n}\n")
        lua_out.write('yalua_register_proto(mapping)')
        lua_out.close()

if __name__ == '__main__':
    genLuaAdapterFromJson(sys.argv[1])

# See PyCharm help at https://www.jetbrains.com/help/pycharm/

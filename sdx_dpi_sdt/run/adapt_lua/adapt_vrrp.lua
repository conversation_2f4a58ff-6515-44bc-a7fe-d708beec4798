-- vrrp.lua
local mapping = {
  from_proto_name = "vrrp",
  to_proto_name = "vrrp",
  rule_proto_name = "vrrp",
  common_flag = true,
  field = {
    {from_name = "virt_ipv6"     ,to_name = "virIPv6Addr"    },
    {from_name = "virt_ipv4"     ,to_name = "virIPv4Addr"    },
    {from_name = "auth_string"   ,to_name = "authData"       },
    {from_name = "virt_ip"       ,to_name = "virIPAddr"      },
    {from_name = "addr_count"    ,to_name = "virRouIPAddrNum"},
    {from_name = "adver_int"     ,to_name = "advInt"         },
    {from_name = "auth_type"     ,to_name = "authType"       },
    {from_name = "virt_rtr_id"   ,to_name = "virRouType"     },
    {from_name = "type"          ,to_name = "msgType"        },

    
  }
}
yalua_register_proto(mapping)
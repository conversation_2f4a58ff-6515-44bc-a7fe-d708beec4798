-- cwmp.lua
local mapping = {
  from_proto_name = "cwmp",
  to_proto_name = "cwmp",
  rule_proto_name = "cwmp",
  common_flag = true,
  field = {
    {from_name = "download_username"       ,to_name = "userName"      ,rule_name = "user"        ,tll = 1},
    {from_name = "download_password"       ,to_name = "password"      ,rule_name = "pwd"         ,tll = 1},
    {from_name = "download_target_filename",to_name = "filename"      ,rule_name = "filename"    ,tll = 1},
    {from_name = "download_status"         ,to_name = "status"        ,rule_name = "status"      ,tll = 1},
    {from_name = "download_url"            ,to_name = "downloadURL"   ,rule_name = "download_url",tll = 1},
    {from_name = "manager_url"             ,to_name = "managerURL"    ,rule_name = "management_server_url"  ,tll = 1},
    {from_name = "soft_ver"                ,to_name = "softVer"       ,rule_name = "software_version"    ,tll = 1},
    {from_name = "hard_ver"                ,to_name = "hardVer"       ,rule_name = "hardware_version"    ,tll = 1},
    {from_name = "summary"                 ,to_name = "summary"       ,rule_name = "summary"     ,tll = 1},
    {from_name = "command_key"             ,to_name = "commandKey"    ,rule_name = "cmd_key"         ,tll = 1},
    {from_name = "eventcode"               ,to_name = "eventCode"     ,rule_name = "event_code"       ,tll = 1},
    {from_name = "serial_number"           ,to_name = "serialNumber"  ,rule_name = "sn"          ,tll = 1},
    {from_name = "product_class"           ,to_name = "productClass"  ,rule_name = "product_class"     ,tll = 1},
    {from_name = "oui"                     ,to_name = "OUI"           ,rule_name = "oui"         ,tll = 1},
    {from_name = "manufacturer"            ,to_name = "manufacturer"  ,rule_name = "manufacturer"        ,tll = 1},
    {from_name = "connect_url"             ,to_name = "connectURL"    },

  }
}
yalua_register_proto(mapping)
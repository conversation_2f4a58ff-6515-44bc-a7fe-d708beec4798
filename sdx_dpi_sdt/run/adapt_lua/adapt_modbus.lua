-- modbus.lua
local mapping = {
  from_proto_name = "modbus",
  to_proto_name = "modbus",
  rule_proto_name = "modbus",
  common_flag = true,
  field = {
    {from_name = "register_content",to_name = "addr"          ,tll = 1},
    {from_name = "unit_id"         ,to_name = "modbus.unitID" ,tll = 1},
    {from_name = "trans_id"        ,to_name = "transID"       ,tll = 1},
    {from_name = "exc_code"        ,to_name = "excp"          ,rule_name = "exception_code"    ,tll = 1},
    {from_name = "func_code"       ,to_name = "func"          ,rule_name = "function_code"     ,tll = 1},
    {from_name = "length"          ,to_name = "len"           },
    {from_name = "prot_id"         ,to_name = "protoID"       },
    {from_name = ""                ,to_name = "regOldVal"     },
    {from_name = ""                ,to_name = "regNewVal"     },
    {from_name = ""                ,to_name = "regChgDlt"     },

    
  }
}
yalua_register_proto(mapping)
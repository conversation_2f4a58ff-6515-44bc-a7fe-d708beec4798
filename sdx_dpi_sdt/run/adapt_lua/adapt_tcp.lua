-- tcp.lua
local mapping = {
  from_proto_name = "tcp",
  to_proto_name = "tcp",
  rule_proto_name = "tcp",
  common_flag = true,
  field = {
    {from_name = "tcp_headerlen" ,to_name = "header_len"    ,rule_name = "header_length" },
    {from_name = "tcp_fin"       ,to_name = "flag.fin"      ,rule_name = "flags_fin"   },
    {from_name = "tcp_syn"       ,to_name = "flag.syn"      ,rule_name = "flags_syn"   },
    {from_name = "tcp_rst"       ,to_name = "flag.rst"      ,rule_name = "flags_rst"   },
    {from_name = "tcp_psh"       ,to_name = "flag.psh"      ,rule_name = "flags_psh"   },
    {from_name = "tcp_ack"       ,to_name = "flag.ack"      ,rule_name = "flags_ack"   },
    {from_name = "tcp_urg"       ,to_name = "flag.urg"      ,rule_name = "flags_urg"   },
    {from_name = "tcp_ece"       ,to_name = "flag.ece"      ,rule_name = "flags_ece"   },
    {from_name = "tcp_cwr"       ,to_name = "flag.cwr"      ,rule_name = "flags_cwr"   },
    {from_name = "tcp_ns"        ,to_name = "flag.ns"       ,rule_name = "flag_ns"    },
    {from_name = "tcp_window"    ,to_name = "windowsize"    ,rule_name = "window_size" },
    {from_name = "tcp_payloadlen",to_name = "payload_len"   ,rule_name = "payload_length"},
    {from_name = "tcp_payload"   ,to_name = "payload"       ,rule_name = "payload"    },
    {from_name = "tcp_flag"      ,to_name = "flag"          ,rule_name = "flags"       },
    {from_name = "tcp_header"    ,to_name = "header"        ,rule_name = "header"     },

  }
}
yalua_register_proto(mapping)

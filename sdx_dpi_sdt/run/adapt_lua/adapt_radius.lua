-- radius.lua
local mapping = {
  from_proto_name = "radius",
  to_proto_name = "radius",
  rule_proto_name = "radius",
  common_flag = true,
  field = {
    {from_name = "Acct_Authentic"    ,to_name = "acctAuth"      },
    {from_name = "Termination_Action",to_name = "terCas"        },
    {from_name = "Login_IPv6_Host"   ,to_name = "loginIPv6Host" },
    {from_name = "NAS_IPv6_Address"  ,to_name = "NASIPv6Addr"   },
    {from_name = "Login_LAT_Port"    ,to_name = "loginLATPort"  },
    {from_name = "Login_LAT_Group"   ,to_name = "loginLATGro"   },
    {from_name = "Login_LAT_Node"    ,to_name = "loginLATNod"   },
    {from_name = "Login_LAT_Service" ,to_name = "loginLATSvc"   },
    {from_name = "Calling_Station_Id",to_name = "calliStaID"    },
    {from_name = "Called_Station_Id" ,to_name = "calleStaID"    },
    {from_name = "Vendor_Specific"   ,to_name = "venSpe"        },
    {from_name = "Framed_IPX_Network",to_name = "fraIPXNet"     },
    {from_name = "Framed_Route"      ,to_name = "fraRout"       },
    {from_name = "NAS_Port_Type"     ,to_name = "NASPortType"   },
    {from_name = "NAS_Identifier"    ,to_name = "NASID"         },
    {from_name = "NAS_Port_Id"       ,to_name = "NASPorID"      },
    {from_name = "Callback_Id"       ,to_name = "calID"         },
    {from_name = "Callback_Number"   ,to_name = "calNum"        },
    {from_name = "Login_TCP_Port"    ,to_name = "logTcpPort"    },
    {from_name = "Login_Service"     ,to_name = "logSvc"        },
    {from_name = "Login_IP_Host"     ,to_name = "logIPHost"     },
    {from_name = "Framed_IP_Netmask" ,to_name = "fraIPNet"      },
    {from_name = "Framed_IP_Address" ,to_name = "fraIPAddr"     },
    {from_name = "Framed_Protocol"   ,to_name = "fraProt"       },
    {from_name = "Service_Type"      ,to_name = "svcType"       },
    {from_name = "NAS_Port"          ,to_name = "NASPort"       },
    {from_name = "NAS_IP_Address"    ,to_name = "NASIPAddr"     },
    {from_name = "CHAP_Password"     ,to_name = "chapPwd"       },
    {from_name = "User_Password"     ,to_name = "pwd"           },
    {from_name = "User_Name"         ,to_name = "usrName"       },
    {from_name = "Authenticator"     ,to_name = "authenticator" },
    {from_name = "Code"              ,to_name = "code"          },

    
  }
}
yalua_register_proto(mapping)
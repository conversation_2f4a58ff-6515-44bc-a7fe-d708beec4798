-- sctp.lua
local mapping = {
  from_proto_name = "sctp",
  to_proto_name = "sctp",
  rule_proto_name = "sctp",
  common_flag = true,
  field = {
    {from_name = "trans_seq"      ,to_name = "chuDataTSN"      },
    {from_name = "stream_seq"     ,to_name = "chuDataStrSeq"   },
    {from_name = "proto_id"       ,to_name = "chuDataPayProtID",rule_name = "proto_id"   },
    {from_name = ""               ,to_name = "hostName"        },
    {from_name = "cookie"         ,to_name = "cookie"          },
    {from_name = "header"         ,to_name = "sctp_header"     ,rule_name = "header"     },
    {from_name = "chunk_type"     ,to_name = "sctp_chunk_type" ,rule_name = "chunk_type" },
    {from_name = "payload"        ,to_name = "sctp_payload"    ,rule_name = "payload"    },
    {from_name = "payload_len"    ,to_name = "sctp_payload_len",rule_name = "payload_length"},
    {from_name = "num_of_outbound",to_name = "outStr"          },
    {from_name = "num_of_inbound" ,to_name = "inbStr"          },
    {from_name = "stream_id"      ,to_name = "chuDataStrID"    ,rule_name = "stream_id"  },
    {from_name = "flag"           ,to_name = "verTag"          },

    
  }
}
yalua_register_proto(mapping)
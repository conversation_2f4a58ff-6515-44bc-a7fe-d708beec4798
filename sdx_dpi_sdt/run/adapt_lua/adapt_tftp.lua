-- tftp.lua
local mapping = {
  from_proto_name = "tftp",
  to_proto_name = "tftp",
  rule_proto_name = "tftp",
  common_flag = true,
  field = {
    {from_name = "FileMd5"       ,to_name = "fileMd5"       ,tll = 1},
    {from_name = "OptionValue"   ,to_name = "opVal"         ,rule_name = "operations_codes" ,tll = 1},
    {from_name = "OptionName"    ,to_name = "opKey"         ,rule_name = "operations_types"   ,tll = 1},
    {from_name = "Option"        ,to_name = "otherOpt"      ,rule_name = "other_opt",tll = 1},
    {from_name = "FileType"      ,to_name = "fileType"      ,tll = 1},
    {from_name = "FileName"      ,to_name = "fileName"      ,rule_name = "file_name" ,tll = 1},
    {from_name = "FileSize"      ,to_name = "fileSize"      ,tll = 1},
    {from_name = "TransMode"     ,to_name = "mode"          ,rule_name = "mode"     ,tll = 1},

  }
}
yalua_register_proto(mapping)
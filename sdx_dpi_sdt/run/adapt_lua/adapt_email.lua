-- email.lua
local mapping = {
  from_proto_name = "email",
  to_proto_name = "email",
  rule_proto_name = "email",
  common_flag = true,
  field = {
    {from_name = "with"           ,to_name = "rcvWit"         ,rule_name = "sender_software" ,tll = 1},
    {from_name = "ByDom"          ,to_name = "ByDom"          ,rule_name = "by_domain"      ,tll = 1},
    {from_name = "ByAsn"          ,to_name = "ByAsn"          ,rule_name = "by_asn"         ,tll = 1},
    {from_name = "ByCountry"      ,to_name = "ByCountry"      ,rule_name = "by_country"     ,tll = 1},
    {from_name = "usrAge"         ,to_name = "usrAge"         ,rule_name = "user_agent"     ,tll = 1},
    {from_name = "conType"        ,to_name = "conType"        ,rule_name = "content_type"   ,tll = 1},
    -- {from_name = "ver"            ,to_name = "ver"            ,rule_name = "version"        ,tll = 1},
    {from_name = "senderDom"      ,to_name = "senderDom"      ,rule_name = "sender_domain"  ,tll = 1},
    {from_name = "received"       ,to_name = "received"       ,rule_name = "received"       ,tll = 1},
    {from_name = "content"        ,to_name = "content"        ,rule_name = "content"        ,tll = 1},
    {from_name = "resentSrvAge"   ,to_name = "resentSrvAge"   ,rule_name = "resent_agent"   ,tll = 1},
    {from_name = "body"           ,to_name = "body"           ,rule_name = "body"           ,tll = 1},
    {from_name = "bodyURL"        ,to_name = "bodyURL"        ,rule_name = "body_url"       ,tll = 1},
    {from_name = "xMailerCnt"     ,to_name = "xMaiCnt"        ,tll = 1},
    {from_name = "subjectCnt"     ,to_name = "subj.cnt"       ,tll = 1},
    {from_name = "rcvrEmailCnt"   ,to_name = "rcvrEmailCnt"   ,tll = 1},
    {from_name = "osVer"          ,to_name = "osVer"          ,rule_name = "imap_os_version" ,tll = 1},
    {from_name = "os"             ,to_name = "os"             ,rule_name = "imap_os"        ,tll = 1},
    {from_name = "ver"            ,to_name = "ver"            ,rule_name = "imap_version"   ,tll = 1},
    {from_name = "vendor"         ,to_name = "vendor"         ,rule_name = "imap_vendor"    ,tll = 1},
    {from_name = "name"           ,to_name = "name"           ,rule_name = "imap_name"      ,tll = 1},
    {from_name = "bodyTexCha"     ,to_name = "bodyTexCha"     ,rule_name = "body_charset"   ,tll = 1},
    {from_name = "bodyTraEnc"     ,to_name = "bodyTraEnc"     ,rule_name = "body_encoding"  ,tll = 1},
    {from_name = "bodyLen"        ,to_name = "bodyLen"        ,rule_name = "body_length"    ,tll = 1},
    {from_name = "resentDate"     ,to_name = "resentDate"     ,rule_name = "resent_date"    ,tll = 1},
    {from_name = "resentTo"       ,to_name = "resentTo"       ,rule_name = "resent_to"      ,tll = 1},
    {from_name = "resentFrom"     ,to_name = "resentFrom"     ,rule_name = "resent_from"    ,tll = 1},
    {from_name = "rcptToDomCnt"   ,to_name = "rcptToDomCnt"   ,tll = 1},
    {from_name = "rcptToDom"      ,to_name = "rcptToDom"      ,rule_name = "envelope_to_domain" ,tll = 1},
    {from_name = "rcptTo"         ,to_name = "rcptTo"         ,rule_name = "envelope_to"        ,tll = 1},
    {from_name = "rcvrDom"        ,to_name = "rcvrDom"        ,rule_name = "receiver_domain"},
    {from_name = "mailFromDomCnt" ,to_name = "mailFromDomCnt" ,rule_name = "envelope_from_domain_count" ,tll = 1},
    {from_name = "mailFromDom"    ,to_name = "mailFromDom"    ,rule_name = "envelope_from_domain"       ,tll = 1},
    {from_name = "mailFrom"       ,to_name = "mailFrom"       ,rule_name = "envelope_from"  ,tll = 1},
    {from_name = "count"          ,to_name = "count"          ,rule_name = "count"          ,tll = 1},
    {from_name = "Command"        ,to_name = "Command"        ,rule_name = "cmd"            ,tll = 1},
    {from_name = "startTLS"       ,to_name = "startTLS"       ,rule_name = "starttls"       ,tll = 1},
    {from_name = "xOriIP"         ,to_name = "xOriIP"         ,rule_name = "x_original_ip"  ,tll = 1},
    {from_name = "deliveredTo"    ,to_name = "deliveredTo"    ,rule_name = "delivered_to"   ,tll = 1},
    {from_name = "host"           ,to_name = "host"           ,rule_name = "host"           ,tll = 1},
    {from_name = "contentLen"     ,to_name = "contentLen"     },
    {from_name = "charset"        ,to_name = "charset"        },
    {from_name = "contentWithHtml",to_name = "contentWithHtml"},
    {from_name = "realTo"         ,to_name = "realTo"         },
    {from_name = "realFrom"       ,to_name = "realFrom"       },
    {from_name = "pwd"            ,to_name = "pwd"            ,rule_name = "pwd"            ,tll = 1},
    {from_name = "login"          ,to_name = "login"          ,rule_name = "login"          ,tll = 1},
    {from_name = "mimeVerCnt"     ,to_name = "mimeVerCnt"     ,tll = 1},
    {from_name = "mimeVer"        ,to_name = "mimeVer"        ,rule_name = "mime_version"   ,tll = 1},
    {from_name = "msgIDCnt"       ,to_name = "msgIDCnt"       ,tll = 1},
    {from_name = "msgID"          ,to_name = "msgID"          ,rule_name = "msg_id"         ,tll = 1},
    {from_name = "headSetCnt"     ,to_name = "headSetCnt"     ,tll = 1},
    {from_name = "headSet"        ,to_name = "headSet"        ,rule_name = "header_set"     ,tll = 1},
    {from_name = "attConSize"     ,to_name = "attConSize"     ,rule_name = "attachment_length"       ,tll = 1},
    {from_name = "attMD5Cnt"      ,to_name = "attMD5Cnt"      ,tll = 1},
    {from_name = "attMD5"         ,to_name = "attMD5"         ,rule_name = "attachment_md5"          ,tll = 1},
    {from_name = "attTypeCnt"     ,to_name = "attTypeCnt"     ,tll = 1},
    {from_name = "attType"        ,to_name = "attType"        ,rule_name = "attachment_content_type" ,tll = 1},
    {from_name = "attFileNameCnt" ,to_name = "attFileNameCnt" ,tll = 1},
    {from_name = "attFileName"    ,to_name = "attFileName"    ,rule_name = "attachment_filename"     ,tll = 1},
    {from_name = "emaInd"         ,to_name = "emaInd"         ,rule_name = "index"          ,tll = 1},
    {from_name = "conTypeCnt"     ,to_name = "conTypeCnt"     ,tll = 1},
    {from_name = "bodyTypeCnt"    ,to_name = "bodyTypeCnt"    ,tll = 1},
    {from_name = "bodyType"       ,to_name = "bodyType"       ,rule_name = "body_type"      ,tll = 1},
    {from_name = "conTexCha"      ,to_name = "conTexCha"      },
    {from_name = "conTraEnc"      ,to_name = "conTraEnc"      },
    {from_name = "xMai"           ,to_name = "xMai"           ,rule_name = "x_mailer"       ,tll = 1},
    {from_name = "subj"           ,to_name = "subj"           ,rule_name = "subject"        ,tll = 1},
    {from_name = "SMTPSrvAge"     ,to_name = "SMTPSrvAge"     ,rule_name = "smtp_server_agent" ,tll = 1},
    {from_name = "SMTPSrv"        ,to_name = "SMTPSrv"        ,rule_name = "smtp_server"    ,tll = 1},
    {from_name = "loginSrv"       ,to_name = "loginsrv"       ,rule_name = "login_server"   ,tll = 1},
    {from_name = "emaProtType"    ,to_name = "emaProtType"    ,rule_name = "proto_type"     ,tll = 1},
    {from_name = "date"           ,to_name = "date"           ,rule_name = "date"           ,tll = 1},
    {from_name = "repTo"          ,to_name = "repTo"          ,rule_name = "reply"          ,tll = 1},
    {from_name = "BCC"            ,to_name = "BCC"            ,rule_name = "bcc"            ,tll = 1},
    {from_name = "CCAli"          ,to_name = "CCAli"          ,rule_name = "cc_alias"       ,tll = 1},
    {from_name = "CC"             ,to_name = "CC"             ,rule_name = "cc"             ,tll = 1},
    {from_name = "ByIP"           ,to_name = "ByIP"           ,rule_name = "by_ip"          ,tll = 1},
    {from_name = "rcvrAli"        ,to_name = "rcvrAli"        ,rule_name = "receiver_alias" ,tll = 1},
    {from_name = "rcvrEmail"      ,to_name = "rcvrEmail"      ,rule_name = "receiver"       ,tll = 1},
    {from_name = "FromCountry"    ,to_name = "FromCountry"    ,rule_name = "from_country"   ,tll = 1},
    {from_name = "FromAsn"        ,to_name = "FromAsn"        ,rule_name = "from_asn"       ,tll = 1},
    {from_name = "FromDomCnt"     ,to_name = "FromDomCnt"     ,tll = 1},
    {from_name = "FromDom"        ,to_name = "FromDom"        ,rule_name = "from_domain"    ,tll = 1},
    {from_name = "FromIpCnt"      ,to_name = "FromIpCnt"      ,tll = 1},
    {from_name = "FromIp"         ,to_name = "FromIp"         ,rule_name = "from_ip"        ,tll = 1},
    {from_name = "senderAli"      ,to_name = "senderAli"      ,rule_name = "sender_alias"   ,tll = 1},
    {from_name = "senderEmail"    ,to_name = "senderEmail"    ,rule_name = "sender"         ,tll = 1},

  }
}
yalua_register_proto(mapping)

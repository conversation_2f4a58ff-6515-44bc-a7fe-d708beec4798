-- ssl_tls.lua
local mapping = {
  from_proto_name = "ssl_n",
  to_proto_name = "ssl_tls",
  rule_proto_name = "tls",
  common_flag = true,
  field = {
    {from_name = "CertificatesNums"          ,to_name = "srvCertNum"         ,tll = 1},
    {from_name = ""                          ,to_name = "cliCertNum"         ,tll = 1},
    {from_name = "ServerCertificateSignature",to_name = "srvCertHashes"      ,rule_name = "cert_hashes_server"     ,tll = 1},
    {from_name = "ClientCertificateSignature",to_name = "cliCertHashes"      ,rule_name = "cert_hashes_client"     ,tll = 1},
    {from_name = ""                          ,to_name = "fullText"           ,tll = 1},
    {from_name = ""                          ,to_name = "sessSecFlag"        },
    {from_name = "certPath"                  ,to_name = "certPath"           ,rule_name = "cert_path"         ,tll = 1},
    {from_name = ""                          ,to_name = "certIntactFlag"     },
    {from_name = ""                          ,to_name = "JoyFp"              },
    {from_name = ""                          ,to_name = "certNonFlag"        },
    {from_name = ""                          ,to_name = "STARTTLS"           },
    {from_name = "JA3S"                      ,to_name = "JOYS"               ,tll = 1},
    {from_name = "JA3C"                      ,to_name = "JOY"                ,tll = 1},
    {from_name = ""                          ,to_name = "SigHashAlg"         },
    {from_name = ""                          ,to_name = "sigAlg"             },
    {from_name = ""                          ,to_name = "SigAlgType"         },
    {from_name = "DHEPubkey"                 ,to_name = "DHEPubKey"          },
    {from_name = "DHEPubKeyLength"           ,to_name = "DHEPubKeyLen"       },
    {from_name = "DHESignature"              ,to_name = "DHESig"             },
    {from_name = "RSASignature"              ,to_name = "RSASig"             },
    {from_name = "RSAExponentLength"         ,to_name = "RSAExpLen"          },
    {from_name = "RSAModulusLength"          ,to_name = "RSAModLen"          },
    {from_name = ""                          ,to_name = "greaseFlag"         },
    {from_name = "RSASignatureHashAlgorithm" ,to_name = "RSASigHash"         },
    {from_name = "DHESignatureHashAlgorithm" ,to_name = "DHESigHash"         },
    {from_name = "ECDHSignatureHashAlgorithm",to_name = "ECDHISigHash"       },
    {from_name = "ClientCipherSuiteCnt"      ,to_name = "cipSuiNum"          ,rule_name = "ciphersuites_client_count"   ,tll = 1},
    {from_name = "ServerCipherSuite"         ,to_name = "srvCipSui"          ,rule_name = "ciphersuite_server"            ,tll = 1},
    {from_name = "newSessTicketData"         ,to_name = "ticDat"             },
    {from_name = ""                          ,to_name = "namLen"             },
    {from_name = ""                          ,to_name = "namType"            },
    {from_name = "ECDHSignatureLength"       ,to_name = "ECDHPubKeyLen"      },
    {from_name = "ServerExtensionsLength"    ,to_name = "srvExtLen"          },
    {from_name = "ClientExtensionsLength"    ,to_name = "cliExtLen"          },
    {from_name = "EncrypedPubkeyLength"      ,to_name = "encPubKeyLen"       },
    {from_name = "EncrypedPubkey"            ,to_name = "encPubKey"          },
    {from_name = "ClientKexLength"           ,to_name = "clikeyExcLen"       },
    {from_name = "DHEg"                      ,to_name = "DHEGLen"            },
    {from_name = "DHEp"                      ,to_name = "DHEPLen"            },
    {from_name = "ECDHSignature"             ,to_name = "ECDHSig"            },
    {from_name = "ECDHCurveType"             ,to_name = "ECDHCurType"        },
    {from_name = "ServerKexLength"           ,to_name = "srvKeyExcLen"       },
    {from_name = "ServerSessionIDLength"     ,to_name = "srvSesIDLen"        },
    {from_name = "ClientSessionIDLength"     ,to_name = "cliSesIDLen"        },
    {from_name = ""                          ,to_name = "ttags"              },
    {from_name = ""                          ,to_name = "etags"              },
    {from_name = ""                          ,to_name = "ecPoiForByServ"     },
    {from_name = ""                          ,to_name = "ecGroupsCli"        },
    {from_name = "CertificatesNums"          ,to_name = "srvCertCnt"         },
    {from_name = "Client_Certificate_length" ,to_name = "cliCertCnt"         },
    {from_name = ""                          ,to_name = "AuthTag"            },
    {from_name = "ServerSessionTick"         ,to_name = "srvSessTicket"      ,rule_name = "session_ticket_server"   ,tll = 1},
    {from_name = "ClientSessionTick"         ,to_name = "cliSessTicket"      ,rule_name = "session_ticket_client"   ,tll = 1},
    {from_name = "JA3S"                      ,to_name = "srvJA3"             ,rule_name = "ja3s_server"              ,tll = 1},
    {from_name = "JA3C"                      ,to_name = "cliJA3"             ,rule_name = "ja3_client"               ,tll = 1},
    {from_name = "extGrease"                 ,to_name = "cliExtGrease"       ,rule_name = "ext_grease"        ,tll = 1},
    {from_name = "ServerExtensions"          ,to_name = "srvExt"             ,rule_name = "extend_types_server"       ,tll = 1},
    {from_name = "ClientExtensions"          ,to_name = "cliExt"             ,rule_name = "extend_types_client"       ,tll = 1},
    {from_name = "ServerHelloLength"         ,to_name = "srvHandSkLen"       ,rule_name = "handshake_length_server"   ,tll = 1},
    {from_name = "ClientHelloLength"         ,to_name = "cliHandSkLen"       ,rule_name = "handshake_length_client"   ,tll = 1},
    {from_name = "ServerExtensionsLength"    ,to_name = "srvExtCnt"          ,tll = 1},
    {from_name = "ClientExtensionsLength"    ,to_name = "cliExtCnt"          ,tll = 1},
    {from_name = "CertificateVerifyLength"   ,to_name = "cliCertLen"         },
    {from_name = "ServerGMTUnixTime"         ,to_name = "srvGMTUniTime"      },
    {from_name = "ClientECDHPubkey"          ,to_name = "cliEllCurDHPubKey"  },
    {from_name = "ECDHPubkey"                ,to_name = "srvEllCurDHPubKey"  },
    {from_name = "srvExtECGroups"            ,to_name = "srvEllCur"          ,rule_name = "handshake_extensions_supported_group"   ,tll = 1},
    {from_name = "srvExtECPoiFor"            ,to_name = "srvEllCurPoiFor"    ,rule_name = "extend_ec_format_server"   ,tll = 1},
    {from_name = "ClientECDHNamedCurve"      ,to_name = "cliEllCur"          ,rule_name = "ext_ec_groups_client"   ,tll = 1},
    {from_name = "ClientECDHCurveType"       ,to_name = "cliEllCurPoiFor"    ,rule_name = "extend_ec_format_client"   ,tll = 1},
    {from_name = "ext_type"                  ,to_name = "extTypeInSSL"       },
    {from_name = ""                          ,to_name = "cliDHPubKey"        },
    {from_name = "RSAPreSharedKey"           ,to_name = "preMasKeyEncryByRSA"},
    {from_name = "DHEPubkey"                 ,to_name = "srvDHPubKey"        },
    {from_name = "ServerKeyExDHGen_g"        ,to_name = "DHGenOfSrvKeyExc"   },
    {from_name = "ServerKeyExDHMod_p"        ,to_name = "DHModOfSrvKeyExc"   },
    {from_name = "RSAExponent"               ,to_name = "RSAExpOfSrvKeyExc"  },
    {from_name = "RSAModulus"                ,to_name = "RSAModOfSrvKeyExc"  },
    {from_name = "Client_Certificate_length" ,to_name = "cliCertLen"         ,tll = 1},
    {from_name = "ClientCertificateTypes"    ,to_name = "certResType"        },
    {from_name = "CertificatesLength"        ,to_name = "srvCertLen"         ,tll = 1},
    {from_name = "ServerCompressionMethod"   ,to_name = "srvComprMet"        ,rule_name = "compress_method_server"      ,tll = 1},
    {from_name = "ServerSessionID"           ,to_name = "srvSesID"           ,rule_name = "session_id_server"       ,tll = 1},
    {from_name = "ServerRandomBytes"         ,to_name = "srvRand"            ,rule_name = "handshake_random_bytes_server",tll = 1},
    {from_name = "ServerNameAttr"            ,to_name = "srvNameAttr"        ,tll = 1},
    {from_name = "ServerName"                ,to_name = "srvName"            ,rule_name = "sni"               ,tll = 1},
    {from_name = "ServerProtocolVersion"     ,to_name = "srvVer"             ,rule_name = "version_server"         ,tll = 1},
    {from_name = "ClientCompressionMethods"  ,to_name = "cliComMet"          ,rule_name = "compress_methods_client"      ,tll = 1},
    {from_name = "ClientCipherSuites"        ,to_name = "cliCipSui"          ,rule_name = "ciphersuites_client"       ,tll = 1},
    {from_name = "ClientSessionID"           ,to_name = "cliSesID"           ,rule_name = "session_id_client"       ,tll = 1},
    {from_name = "ClientRandomBytes"         ,to_name = "cliRand"            ,rule_name = "handshake_random_bytes_client",tll = 1},
    {from_name = "ClientGMTUnixTime"         ,to_name = "cliGMTUniTime"      ,rule_name = "gmttime_c"         ,tll = 1},
    {from_name = "ClientProtocolVersion"     ,to_name = "cliVer"             ,rule_name = "version_client"         ,tll = 1},
    {from_name = "HandshakeType"             ,to_name = "handShaType"        },
    {from_name = "AlertDescription"          ,to_name = "aleDes"             },
    {from_name = "AlertLevel"                ,to_name = "aleLev"             },
    {from_name = "ContentType"               ,to_name = "conType"            },

    
  }
}
yalua_register_proto(mapping)
-- nfs.lua
local mapping = {
  from_proto_name = "nfs",
  to_proto_name = "nfs",
  rule_proto_name = "nfs",
  common_flag = true,
  field = {
    {from_name = "creTime"       ,to_name = "creTime"       },
    {from_name = "modTime"       ,to_name = "modTime"       },
    {from_name = "accTime"       ,to_name = "accTime"       },
    {from_name = "fileType"      ,to_name = "fileType"      },
    {from_name = "mode"          ,to_name = "mode"          },
    {from_name = "fileSize"      ,to_name = "fileSize"      },
    {from_name = "fileID"        ,to_name = "fileID"        },
    {from_name = "fileName"      ,to_name = "fileName"      },
    {from_name = "groID"         ,to_name = "groID"         },
    {from_name = "usrID"         ,to_name = "usrID"         },

  }
}
yalua_register_proto(mapping)
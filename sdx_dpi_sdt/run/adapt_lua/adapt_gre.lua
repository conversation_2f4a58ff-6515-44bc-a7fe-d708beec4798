-- gre.lua
local mapping = {
  from_proto_name = "gre",
  to_proto_name = "gre",
  rule_proto_name = "gre",
  common_flag = true,
  field = {
    {from_name = ""                     ,to_name = "dipCou"        },
    {from_name = ""                     ,to_name = "dipAsn"        },
    {from_name = ""                     ,to_name = "dipCnt"        },
    {from_name = "InnerIPDst"           ,to_name = "dip"           },
    {from_name = ""                     ,to_name = "sipCou"        },
    {from_name = ""                     ,to_name = "sipAsn"        },
    {from_name = ""                     ,to_name = "sipCnt"        },
    {from_name = "InnerIPSrc"           ,to_name = "sip"           },
    {from_name = "Acknowledgment_number",to_name = "ackNum"        },
    {from_name = "Sequence_number"      ,to_name = "seqNum"        },
    {from_name = "Key_key"              ,to_name = "key"           ,rule_name = "key"       ,tll = 1},
    {from_name = "Key_call_id"          ,to_name = "callID"        ,rule_name = "call_id"   ,tll = 1},
    {from_name = "Protocol_type"        ,to_name = "protType"      ,rule_name = "proto",tll = 1},
    {from_name = "Version"              ,to_name = "ver"           ,rule_name = "version"   ,tll = 1},

    
  }
}
yalua_register_proto(mapping)
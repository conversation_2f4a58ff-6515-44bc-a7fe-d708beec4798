-- ssh.lua
local mapping = {
  from_proto_name = "ssh",
  to_proto_name = "ssh",
  rule_proto_name = "ssh",
  common_flag = true,
  field = {
    {from_name = "ServerKexInit_HASSH"         ,to_name = "SrvHASSH"           ,tll = 1},
    {from_name = "ClientKexInit_HASSH"         ,to_name = "HASSH"              ,tll = 1},
    {from_name = "Server_Key"                  ,to_name = "srvhostkeyfp256"    ,tll = 1},
    {from_name = "Server_keyex_ms"             ,to_name = "DHMod"              },
    {from_name = "Server_keyex_scy"            ,to_name = "DHGen"              },
    {from_name = "ServerKexDHReply_Signature"  ,to_name = "sigOfSrvKey"        },
    {from_name = "ServerKexGEX_GROUP_y"        ,to_name = "yBySrvHostKey"      },
    {from_name = "ServerKexGEX_GROUP_g"        ,to_name = "gBySrvHostKey"      },
    {from_name = "ServerKexGEX_GROUP_q"        ,to_name = "qBySrvHostKey"      },
    {from_name = "ServerKexGEX_GROUP_p"        ,to_name = "pBySrvHostKey"      },
    {from_name = "Server_RSA_ms"               ,to_name = "modBySrvHostKey"    ,rule_name = "rsakey_mod"         ,tll = 1},
    {from_name = "Server_RSA_ds"               ,to_name = "expNumBySrvHostKey" ,rule_name = "rsakey_exp"         ,tll = 1},
    {from_name = "ServerKexDHReply_PubKey"     ,to_name = "srvDHPubKey"        },
    {from_name = "SvrKexInit_compressAlgC2S"   ,to_name = "srvComprAlg"        ,rule_name = "algorithm_compress_server"     ,tll = 1},
    {from_name = "SvrKexInit_macAlgS2C"        ,to_name = "srvMsgAuthCodeAlg"  ,rule_name = "algorithm_check_server"      ,tll = 1},
    {from_name = "SvrKexInit_encryptAlgS2C"    ,to_name = "srvEncryAlg"        ,rule_name = "algorithm_encrypt_server"      ,tll = 1},
    {from_name = "SvrKexInit_svr_HostKeyAlg"   ,to_name = "srvHostKeyAlg"      ,rule_name = "algorithm_hostkey_server"      ,tll = 1},
    {from_name = "ServerKexInit_kex_algorithms",to_name = "srvKeyExcAndAuthMet",rule_name = "key_exchange_algorithm_server",tll = 1},
    {from_name = "ServerKexInit_Cookie"        ,to_name = "srvCookie"          ,tll = 1},
    {from_name = "SSHServerVersion"            ,to_name = "srvVer"             ,rule_name = "version_server"          ,tll = 1},
    {from_name = "ClientKexInit_DHPubKey"      ,to_name = "cliDHPubKey"        },
    {from_name = "CltKexInit_CompAlgC2S"       ,to_name = "cliComprAlg"        ,rule_name = "algorithm_compress_client"     ,tll = 1},
    {from_name = "CltKexInit_MacAlgC2S"        ,to_name = "cliMsgAuthCodeAlg"  ,rule_name = "algorithm_check_client"      ,tll = 1},
    {from_name = "CltKexInit_EncryptAlgC2S"    ,to_name = "cliEncryAlg"        ,rule_name = "_algorithm_encrypt_client"      ,tll = 1},
    {from_name = "CltKexInit_svrhostkey_alg"   ,to_name = "cliHostKeyAlg"      ,rule_name = "algorithm_hostkey_client"      ,tll = 1},
    {from_name = "ClientKexInit_kex_algorithms",to_name = "cliKeyExcAndAutMet" ,rule_name = "key_exchange_algorithm_client",tll = 1},
    {from_name = "ClientKexInit_Cookie"        ,to_name = "cliCookie"          ,tll = 1},
    {from_name = "SSHClientVersion"            ,to_name = "cliVer"             ,rule_name = "version_client"          ,tll = 1},

    
  }
}
yalua_register_proto(mapping)

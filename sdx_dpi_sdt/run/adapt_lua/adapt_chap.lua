-- chap.lua
local mapping = {
  from_proto_name = "chap",
  to_proto_name = "chap",
  rule_proto_name = "chap",
  common_flag = true,
  field = {
    {from_name = "code"          ,to_name = "msgType"     },
    {from_name = "resp_host_name",to_name = "respHostName"},
    {from_name = "random_resp"   ,to_name = "resp"        },
    {from_name = "host_name"     ,to_name = "hostName"    },
    {from_name = "random_cha"    ,to_name = "cha"         },
  }
}
yalua_register_proto(mapping)
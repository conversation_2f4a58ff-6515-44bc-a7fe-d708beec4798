-- isakmp.lua
local mapping = {
  from_proto_name = "isakmp",
  to_proto_name = "isakmp",
  rule_proto_name = "isakmp",
  common_flag = true,
  field = {
    {from_name = "idList"                      ,to_name = "IDList"           },
    {from_name = "IdKeyId"                     ,to_name = "keyID"            },
    {from_name = "IdIpAddrSubnet"              ,to_name = "ipSubnet"         },
    {from_name = "IdIPAddr"                    ,to_name = "ipAddr"           },
    {from_name = "IdFQDN"                      ,to_name = "fqdn"             },
    {from_name = "initVector"                  ,to_name = "initVec"          },
    {from_name = "IdProtoID"                   ,to_name = "protoID"          },
    {from_name = "KE_key_len"                  ,to_name = "KeyLen"           },
    {from_name = "AuthData"                    ,to_name = "authData"         },
    {from_name = "AuthMet"                     ,to_name = "AuthMsgMet"       },
    {from_name = "spi"                         ,to_name = "spi"              },
    {from_name = "Nonce"                       ,to_name = "NonceData"        },
    {from_name = "Sig"                         ,to_name = "signData"         },
    {from_name = "Hash"                        ,to_name = "hashData"         },
    {from_name = "IdPort"                      ,to_name = "port"             },
    {from_name = "IdType"                      ,to_name = "IDTyp"            },
    {from_name = ""                            ,to_name = "idDir"            },
    {from_name = "Flags"                       ,to_name = "flags"            },
    {from_name = "PayloadTypes"                ,to_name = "Ver"              },
    {from_name = "ResponseAuthenticationData"  ,to_name = "authDataInResp"   },
    {from_name = "RequestAuthenticationData"   ,to_name = "authDataInInit"   },
    {from_name = "ResponseIdentification"      ,to_name = "IDInResp"         },
    {from_name = "RequestIdentification"       ,to_name = "IDInInit"         },
    {from_name = ""                            ,to_name = "respEncryDataSize"},
    {from_name = ""                            ,to_name = "initEncryDataSize"},
    {from_name = "IKE2_Checksum"               ,to_name = "intAlgInIKE2"     },
    {from_name = "IKE2_Random"                 ,to_name = "pseRandFunInIKE2" },
    {from_name = "IKE2_ENCryptMethod"          ,to_name = "encryAlgInIKE2"   },
    {from_name = "IKE2_Notification"           ,to_name = "notMsgTypeInIKE2" },
    {from_name = "Notification"                ,to_name = "notMsgType"       },
    {from_name = "ResponseRandom"              ,to_name = "respNon"          },
    {from_name = "RequestRandom"               ,to_name = "initNon"          },
    {from_name = "CertificateType"             ,to_name = "certCod"          },
    {from_name = "ResponseKeyExchange"         ,to_name = "respKeyExc"       },
    {from_name = "RequestKeyExchange"          ,to_name = "iniKeyExc"        },
    {from_name = "ResponseVendorID"            ,to_name = "respVenID"        },
    {from_name = "RequestVendorID"             ,to_name = "initVenID"        },
    {from_name = "LifeDuration"                ,to_name = "lifeDur"          },
    {from_name = "LifeType"                    ,to_name = "lifeType"         },
    {from_name = "ResponseAuthenticationMethod",to_name = "respAuthMet"      },
    {from_name = "RequestAuthenticationMethod" ,to_name = "initAuthMet"      },
    {from_name = "ResponseGroupType"           ,to_name = "respGroType"      },
    {from_name = "RequestGroupType"            ,to_name = "initGroType"      },
    {from_name = "ResponseGroupDescription"    ,to_name = "respGroDes"       },
    {from_name = "RequestGroupDescription"     ,to_name = "initGroDes"       },
    {from_name = "ResponseHashAlgorithm"       ,to_name = "respHasAlg"       },
    {from_name = "RequestHashAlgorithm"        ,to_name = "initHasAlg"       },
    {from_name = "ResponseKeyLength"           ,to_name = "respKeyLen"       },
    {from_name = "RequestKeyLength"            ,to_name = "initKeyLen"       },
    {from_name = "ResponseEncryptionAlgorithm" ,to_name = "respEncryAlg"     },
    {from_name = "RequestEncryptionAlgorithm"  ,to_name = "initEncryAlg"     },
    {from_name = "TransformID"                 ,to_name = "traID"            },
    {from_name = "TransformNumber"             ,to_name = "traNum"           },
    {from_name = "ProtocolID"                  ,to_name = "proID"            },
    {from_name = "ProposalNumber"              ,to_name = "proNum"           },
    {from_name = "Situation"                   ,to_name = "situ"             },
    {from_name = "doi"                         ,to_name = "DOI"              },
    {from_name = "MessageID"                   ,to_name = "msgID"            },
    {from_name = "ExchangeType"                ,to_name = "excType"          },
    {from_name = "ReponseVersion"              ,to_name = "respVer"          },
    {from_name = "RequestVersion"              ,to_name = "initVer"          },
    {from_name = "ResponderCookie"             ,to_name = "respCookie"       },
    {from_name = "InitiatorCookie"             ,to_name = "iniCookie"        },

    
  }
}
yalua_register_proto(mapping)
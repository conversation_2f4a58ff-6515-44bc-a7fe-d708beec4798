-- isis.lua
local mapping = {
  from_proto_name = "isis",
  to_proto_name = "isis",
  rule_proto_name = "isis",
  common_flag = true,
  field = {
    {from_name = "mt_ip_metric1"            ,to_name = "MTIPRchIPv6Metr"    },
    {from_name = "mt_ip_distri1"            ,to_name = "MTIPRchIPv6Dist"    },
    {from_name = "mt_ip_prefix1"            ,to_name = "MTIPRchIPv6Perf"    },
    {from_name = ""                         ,to_name = "MTIPRchIPv4Metr"    },
    {from_name = ""                         ,to_name = "MTIPRchIPv4Dist"    },
    {from_name = ""                         ,to_name = "MTIPRchIPv4Pref"    },
    {from_name = ""                         ,to_name = "MTIPRchNeigID"      },
    {from_name = "mt_is_unrsv_bw1"          ,to_name = "MTISRchUnresvBw"    },
    {from_name = "mt_is_reservable_bw1"     ,to_name = "MTISRchResvBw"      },
    {from_name = "mt_is_max_bw1"            ,to_name = "MTISRchMaxBw"       },
    {from_name = "mt_is_neighbor_ipv4_addr1",to_name = "MTISRchIPv4NeigAddr"},
    {from_name = "mt_is_ipv4_addr1"         ,to_name = "MTISRchIPv4Addr"    },
    {from_name = "mt_is_admin_group1"       ,to_name = "MTISRchAdminGrp"    },
    {from_name = ""                         ,to_name = "MTISRchMetr"        },
    {from_name = ""                         ,to_name = "MTISRchNeigID"      },
    {from_name = "mt_is_type"               ,to_name = "MTType"             },
    {from_name = ""                         ,to_name = "TEMetr"             },
    {from_name = ""                         ,to_name = "extdISRchUnresvBw"  },
    {from_name = ""                         ,to_name = "extdISRchResvBw"    },
    {from_name = ""                         ,to_name = "extdISRchMaxBw"     },
    {from_name = "extd_is_neighbor_addr1"   ,to_name = "extdISRchNeigAddr"  },
    {from_name = "extd_is_inter_addr1"      ,to_name = "extdISRchInterfAddr"},
    {from_name = ""                         ,to_name = "extdISRchAdminGrp"  },
    {from_name = "extd_is_metric1"          ,to_name = "extdISRchNeigMetr"  },
    {from_name = "extd_is_id1"              ,to_name = "extdISRchNeigSysID" },
    {from_name = "extd_ip_metric1"          ,to_name = "extdIPRchMetr"      },
    {from_name = "extd_ip_distri1"          ,to_name = "extdIPRchDist"      },
    {from_name = "extd_ip_prefix1"          ,to_name = "extdIPRchPref"      },
    {from_name = ""                         ,to_name = "IPExtlRchPref"      },
    {from_name = ""                         ,to_name = "IPIntlRchPref"      },
    {from_name = "lsp_id"                   ,to_name = "LSPID"              },
    {from_name = "sequence_number"          ,to_name = "seqNum"             },
    {from_name = "hostname"                 ,to_name = "hostName"           },
    {from_name = "is_type"                  ,to_name = "ISType"             },
    {from_name = ""                         ,to_name = "adjNeigID"          },
    {from_name = "protocols_supported"      ,to_name = "protSupport"        },
    {from_name = "area_address"             ,to_name = "areaAddr"           },
    {from_name = "ip6_address"              ,to_name = "IPv6Addr"           },
    {from_name = "ip4_address"              ,to_name = "IPAddr"             },
    {from_name = "disignated_system_id"     ,to_name = "LANID"              },
    {from_name = "ptp_neighbor_id"          ,to_name = "neigID"             },
    {from_name = "auth_info"                ,to_name = "auth"               },
    {from_name = ""                         ,to_name = "TLVVal"             },
    {from_name = ""                         ,to_name = "TLVType"            },
    {from_name = "holding_timer"            ,to_name = "holTime"            },
    {from_name = "sender_system_id"         ,to_name = "srcSysID"           },
    {from_name = "circuit_type"             ,to_name = "cirType"            },
    {from_name = "maximun_area_address"     ,to_name = "maximumAreNum"      },
    {from_name = "pdu_type"                 ,to_name = "PDUType"            },
    {from_name = "system_id_length"         ,to_name = "IDLen"              },
    {from_name = "version1"                 ,to_name = "ver/protIDExt"      },
    {from_name = "protocol_discriminator"   ,to_name = "intRouProtDis"      },

    
  }
}
yalua_register_proto(mapping)
-- pim.lua
local mapping = {
  from_proto_name = "pim",
  to_proto_name = "pim",
  rule_proto_name = "pim",
  common_flag = true,
  field = {
    {from_name = "DRPriority"     ,to_name = "dr<PERSON><PERSON>"         },
    {from_name = "RPIP"           ,to_name = "rpAddr"        },
    {from_name = "BSRIP"          ,to_name = "bsrAddr"       },
    {from_name = "pruneIP"        ,to_name = "pruAddr"       },
    {from_name = "joinIP"         ,to_name = "joiAddr"       },
    {from_name = "groupAddress"   ,to_name = "mulGrpAddr"    },
    {from_name = "sourceAddress"  ,to_name = "mulSrcAddr"    },
    {from_name = "upstreamAddress",to_name = "upStrAddr"     },
    {from_name = "netMode"        ,to_name = "netMode"       },
    {from_name = "type"           ,to_name = "msgType"       },

    
  }
}
yalua_register_proto(mapping)
-- socks.lua
local mapping = {
  from_proto_name = "socks",
  to_proto_name = "socks",
  rule_proto_name = "socks",
  common_flag = true,
  field = {
    {from_name = "remote_domain_name"    ,to_name = "realDstDom"      ,rule_name = "domain_destination",tll = 1},
    {from_name = ""                      ,to_name = "mainFileContent" },
    {from_name = ""                      ,to_name = "mainFileMd5"     },
    {from_name = ""                      ,to_name = "mainFileSize"    },
    {from_name = ""                      ,to_name = "mainFileName"    },
    {from_name = "command"               ,to_name = "protoCode"       },
    {from_name = ""                      ,to_name = "remoteHostPasswd"},
    {from_name = "remote_name"           ,to_name = "remoteHost"      },
    {from_name = "version"               ,to_name = "ipV"             },
    {from_name = "realDstIPs"            ,to_name = "realDstIPs"      },
    {from_name = "password"              ,to_name = "pwd"             ,rule_name = "pwd"            ,tll = 1},
    {from_name = "username"              ,to_name = "usrName"         ,rule_name = "user"           ,tll = 1},
    {from_name = "subnegotiation_version",to_name = "ageType"         ,rule_name = "agent_type"     ,tll = 1},
    {from_name = "remote_port"           ,to_name = "realDstPort"     ,rule_name = "port_destination"  ,tll = 1},
    {from_name = ""                      ,to_name = "realSrcPort"     },
    {from_name = "remote_address"        ,to_name = "realDstIPAddr"   ,rule_name = "real_ips.dst"   },
    {from_name = ""                      ,to_name = "realSrcIPAddr"   },

    
  }
}
yalua_register_proto(mapping)
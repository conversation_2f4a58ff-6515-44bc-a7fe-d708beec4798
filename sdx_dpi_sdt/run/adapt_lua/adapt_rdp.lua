-- rdp.lua
local mapping = {
  from_proto_name = "rdp",
  to_proto_name = "rdp",
  rule_proto_name = "rdp",
  common_flag = true,
  field = {
    {from_name = "ass_path"         ,to_name = "accPath"       ,rule_name = "access_path",tll = 1},
    {from_name = "program"          ,to_name = "prog"          ,rule_name = "application"        ,tll = 1},
    {from_name = "domain"           ,to_name = "dom"           ,rule_name = "domain"     ,tll = 1},
    {from_name = "username"         ,to_name = "srvUsrName"    ,rule_name = "user"       ,tll = 1},
    {from_name = "msgchannelid"     ,to_name = "virCHID"       ,rule_name = "virtual_channel_id",tll = 1},
    {from_name = "client_random"    ,to_name = "cliRand"       },
    {from_name = "server_random"    ,to_name = "srvRand"       },
    {from_name = "servercertificate",to_name = "srvCert"       },
    {from_name = "encryptionlevel"  ,to_name = "encLev"        },
    {from_name = "clientname"       ,to_name = "cliHotsName"   ,rule_name = "host"     ,tll = 1},
    {from_name = "version"          ,to_name = "version"       ,rule_name = "version"    ,tll = 1},

    
  }
}
yalua_register_proto(mapping)
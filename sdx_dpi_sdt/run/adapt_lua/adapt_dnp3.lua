-- dnp3.lua
local mapping = {
  from_proto_name = "dnp3",
  to_proto_name = "dnp3",
  rule_proto_name = "dnp3",
  common_flag = true,
  field = {
    {from_name = "dnp_app_range"  ,to_name = "appRng"        },
    {from_name = "dnp_app_qual"   ,to_name = "appQual"       },
    {from_name = "ObjectArgs0"    ,to_name = "appObjArgs"    },
    {from_name = "Object0"        ,to_name = "appObj"        },
    {from_name = "Func"           ,to_name = "appFunc"       },
    {from_name = "dnp_app_flags"  ,to_name = "appFlags"      },
    {from_name = "dnp_trans_flags",to_name = "transFlags"    },
    {from_name = "dnp_linkcrc"    ,to_name = "linkCRC"       },
    {from_name = "dnp_srcaddr"    ,to_name = "srcAddr"       },
    {from_name = "dnp_dstaddr"    ,to_name = "dstAddr"       },
    {from_name = "linkflags"      ,to_name = "linkFlags"     },
    {from_name = "dnp_length"     ,to_name = "len"           },
    {from_name = "protoType"      ,to_name = "protType"      },

    
  }
}
yalua_register_proto(mapping)
-- wins.lua
local mapping = {
  from_proto_name = "wins-repl",
  to_proto_name = "wins",
  rule_proto_name = "wins",
  common_flag = true,
  field = {
    {from_name = "name_net_address"   ,to_name = "nameNetAddr"   },
    {from_name = "name_broad_address" ,to_name = "nameBroadAddr" },
    {from_name = "name_ip_address"    ,to_name = "nameIpAddr"    },
    {from_name = "name_version_id"    ,to_name = "verID"         },
    {from_name = "name_group_flag"    ,to_name = "groupType"     },
    {from_name = "name_flags_rectype" ,to_name = "recordType"    },
    {from_name = "name_flags_hosttype",to_name = "hostType"      },
    {from_name = "name_type"          ,to_name = "nameType"      },
    {from_name = "owner_min_version"  ,to_name = "ownMinVer"     },
    {from_name = "owner_max_version"  ,to_name = "ownMaxVer"     },
    {from_name = "replication_cmd"    ,to_name = "replCmd"       },
    {from_name = "stop_reason"        ,to_name = "reason"        },
    {from_name = "start_major_version",to_name = "majVer"        },
    {from_name = "start_minor_version",to_name = "minVer"        },
    {from_name = "assoc_ctx"          ,to_name = "assCtx"        },
    {from_name = "packet_size"        ,to_name = "pktSize"       },
    {from_name = "ip_address"         ,to_name = "ownAddrList"   },
    {from_name = "replication_code"   ,to_name = "rplOpCode"     },
    {from_name = "message_type"       ,to_name = "msgType"       },
    {from_name = "opcode"             ,to_name = "opCode"        },
    {from_name = "owner_address"      ,to_name = "WINSRplOwn"    },
    {from_name = "names"              ,to_name = "svcName"       },

  }
}
yalua_register_proto(mapping)
-- rsvp.lua
local mapping = {
  from_proto_name = "rsvp",
  to_proto_name = "rsvp",
  rule_proto_name = "rsvp",
  common_flag = true,
  field = {
    {from_name = "flowMaxPackUint"            ,to_name = ""               },
    {from_name = "flowMinPolicedUint"         ,to_name = ""               },
    {from_name = "flowPeakDataRate"           ,to_name = ""               },
    {from_name = "flowBucketSize"             ,to_name = ""               },
    {from_name = "flowBucketRate"             ,to_name = ""               },
    {from_name = ""                           ,to_name = "recordSrcAddr"  },
    {from_name = "explicitRouteSrcAddress"    ,to_name = "exolicitSrcAddr"},
    {from_name = "minPathLatency"             ,to_name = "minLaten"       },
    {from_name = "tspecBucketSize"            ,to_name = "buckSize"       },
    {from_name = "sender_template_lsp_id"     ,to_name = "lspID"          },
    {from_name = "session_flags"              ,to_name = "sesFlags"       },
    {from_name = "holdPriority"               ,to_name = "priHold"        },
    {from_name = "setupPriority"              ,to_name = "priSetup"       },
    {from_name = "request_label_label"        ,to_name = "l3pid"          },
    {from_name = "eror_value"                 ,to_name = "errValue"       },
    {from_name = "error_code"                 ,to_name = "errCode"        },
    {from_name = "error_flags"                ,to_name = "errFlags"       },
    {from_name = "error_node_address"         ,to_name = "errNoAddr"      },
    {from_name = "timevalues_value"           ,to_name = "frfInterv"      },
    {from_name = "style_option_vector"        ,to_name = "sty"            },
    {from_name = "style_flags"                ,to_name = "styFlags"       },
    {from_name = ""                           ,to_name = "dsum"           },
    {from_name = ""                           ,to_name = "csum"           },
    {from_name = ""                           ,to_name = "dtot"           },
    {from_name = ""                           ,to_name = "ctoc"           },
    {from_name = "minMTU"                     ,to_name = "comMtu"         },
    {from_name = "minBandwidth"               ,to_name = "bw"             },
    {from_name = "tspecMaxPackUint"           ,to_name = "maxPackunit"    },
    {from_name = "tspecMinPolicedUint"        ,to_name = "minPunit"       },
    {from_name = "tspecPeakDataRate"          ,to_name = "peakDRate"      },
    {from_name = "check"                      ,to_name = "chk"            },
    {from_name = "version"                    ,to_name = "ver"            },
    {from_name = "lspTE"                      ,to_name = "TE"             },
    {from_name = ""                           ,to_name = "rSrcAddr"       },
    {from_name = "label_label"                ,to_name = "label"          },
    {from_name = "tspecBucketRate"            ,to_name = "rate"           },
    {from_name = "sessionName"                ,to_name = "sesName"        },
    {from_name = "hop_neighbor_address"       ,to_name = "nexHop"         },
    {from_name = ""                           ,to_name = "intDstAddr"     },
    {from_name = ""                           ,to_name = "intLocAddr"     },
    {from_name = "session_extended_tunnel_id" ,to_name = "extTunID"       },
    {from_name = "session_tunnel_id"          ,to_name = "tunID"          },
    {from_name = "session_dst_address"        ,to_name = "tunDstAddr"     },
    {from_name = "sender_template_src_address",to_name = "tunSrcAddr"     },
    {from_name = ""                           ,to_name = "msgID"          },
    {from_name = "ttl"                        ,to_name = "senTTL"         },
    {from_name = "type"                       ,to_name = "msgType"        },

    
  }
}
yalua_register_proto(mapping)
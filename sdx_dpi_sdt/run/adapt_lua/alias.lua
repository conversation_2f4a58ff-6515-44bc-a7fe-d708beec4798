local M = {}
-- this is common and link alias
M.mapping_common = {
  from_proto_name = "common",
  to_proto_name = "common",
  rule_proto_name = "common",
  field = {
    { from_name = "lineName1",      rule_name = "cable_linename1" },
    { from_name = "lineName2",      rule_name = "cable_linename2" },
    { from_name = "begTime",        rule_name = "ip_time_beg" },
    { from_name = "endTime",        rule_name = "ip_time_end" },
    { from_name = "comDur",         rule_name = "ip_time_len" },
    { from_name = "ipVer",          rule_name = "ip_version" },
    { from_name = "srcAddr",        rule_name = "ip_src" },
    { from_name = "dstAddr",        rule_name = "ip_dst" },
    { from_name = "srcPort",        rule_name = "port_src" },
    { from_name = "dstPort",        rule_name = "port_dst" },
    { from_name = "protNum",        rule_name = "ip_proto" },
    { from_name = "srcAddrV6",      rule_name = "ipv6_src" },
    { from_name = "dstAddrV6",      rule_name = "ipv6_dst" },
    { from_name = "protInfo",       rule_name = "ip_proto_path" },
    { from_name = "protType",       rule_name = "ip_bproto" },
    { from_name = "protName",       rule_name = "ip_uproto" },
    { from_name = "pktNum",         rule_name = "ip_packets" },
    { from_name = "payLen",         rule_name = "ip_databytes" },
    { from_name = "etags",          rule_name = "etags" },
    { from_name = "ttags",          rule_name = "ttags" },
    { from_name = "atags",          rule_name = "atags" },
    { from_name = "utags",          rule_name = "utags" },
    { from_name = "lable1",         rule_name = "mpls_lable1" },
    { from_name = "lable2",         rule_name = "mpls_lable2" },
    { from_name = "lable3",         rule_name = "mpls_lable3" },
    { from_name = "lable4",         rule_name = "mpls_lable4" },
    { from_name = "vlanID1",        rule_name = "vlan_id1" },
    { from_name = "vlanID2",        rule_name = "vlan_id2" },
    { from_name = "srcMac",         rule_name = "mac_source" },
    { from_name = "dstMac",         rule_name = "mac_destination" },
    { from_name = "srcCountry",     rule_name = "ip_country_src" },
    { from_name = "srcState",       rule_name = "ip_state_src" },
    { from_name = "SrcCity",        rule_name = "ip_city_src" },
    { from_name = "srcLongitude",   rule_name = "ip_longitude_src" },
    { from_name = "srcISP",         rule_name = "ip_isp_src" },
    { from_name = "dstISP",         rule_name = "ip_isp_dst" },
    { from_name = "srcASN",         rule_name = "ip_asn_src" },
    { from_name = "dstCountry",     rule_name = "ip_country_dst" },
    { from_name = "dstState",       rule_name = "ip_state_dst" },
    { from_name = "dstCity",        rule_name = "ip_city_dst" },
    { from_name = "dstLongitude",   rule_name = "ip_longitude_dst" },
    { from_name = "dstLatitude",    rule_name = "ip_latitude_dst" },
    { from_name = "dstASN",         rule_name = "ip_asn_dst" },
    { from_name = "outAddrType",    rule_name = "outer_ip_version" },
    { from_name = "outSrcAddr",     rule_name = "outer_ip_src" },
    { from_name = "outDstAddr",     rule_name = "outer_ip_dst" },
    { from_name = "outer.ipv6.src", rule_name = "outer_ipv6_src" },
    { from_name = "outer.ipv6.dst", rule_name = "outer_ipv6_dst" },
    { from_name = "outSrcPort",     rule_name = "outer_port_src" },
    { from_name = "outDstPort",     rule_name = "outer_port_dst" },
    { from_name = "outTransProto",  rule_name = "outer_ip_proto" },
    { from_name = "captureTime",    rule_name = "ctime" },
    { from_name = "dstMacOui",      rule_name = "mac_oui_destination" },
    { from_name = "srcMacOui",      rule_name = "mac_oui_source" }
  }
}

M.mapping_link = {
  from_proto_name = "link",
  to_proto_name = "link",
  rule_proto_name = "link",
  field = {
    { from_name = "sesBytes",            rule_name = "ip_bytes",              },
    { from_name = "downSesBytes",        rule_name = "ip_bytes_dst",          },
    { from_name = "upSesBytes",          rule_name = "ip_bytes_src",          },
    { from_name = "downLinkTcpOpts",     rule_name = "tcp_options_dst",       },
    { from_name = "upLinkTcpOpts",       rule_name = "tcp_options_src",       },
    { from_name = "downLinkSynTcpWins",  rule_name = "tcp_winsize_dst",       },
    { from_name = "upLinkSynTcpWins",    rule_name = "tcp_winsize_src",       },
    { from_name = "downLinkPayLenSet",   rule_name = "trans_paylen_set_dst",  },
    { from_name = "upLinkPayLenSet",     rule_name = "trans_paylen_set_src",  },
    { from_name = "downLinkStream",      rule_name = "ip_stream_dst",         },
    { from_name = "upLinkStream",        rule_name = "ip_stream_src",         },
    { from_name = "downLinkDesBytes",    rule_name = "ip_desiredbytes_dst",   },
    { from_name = "upLinkDesBytes",      rule_name = "ip_desiredbytes_src",   },
    { from_name = "tcpFlagsSynAckCnt",   rule_name = "tcp_flags_syn_ack_cnt", },
    { from_name = "tcpFlagsUrgCnt",      rule_name = "tcp_flags_urg_cnt",     },
    { from_name = "tcpFlagsAckCnt",      rule_name = "tcp_flags_ack_cnt",     },
    { from_name = "tcpFlagsPshCnt",      rule_name = "tcp_flags_psh_cnt",     },
    { from_name = "tcpFlagsRstCnt",      rule_name = "tcp_flags_rst_cnt",     },
    { from_name = "tcpFlagsSynCnt",      rule_name = "tcp_flags_syn_cnt",     },
    { from_name = "tcpFlagsFinCnt",      rule_name = "tcp_flags_fin_cnt",     },
    { from_name = "appDirec",            rule_name = "ip_direction",          },
    { from_name = "firTtlBySrv",         rule_name = "ip_ttl_dst",            },
    { from_name = "firTtlByCli",         rule_name = "ip_ttl_src",            },
    { from_name = "downLinkPktNum",      rule_name = "ip_packets_dst",        },
    { from_name = "upLinkPktNum",        rule_name = "ip_packets_src",        },
    { from_name = "downPayLen",          rule_name = "ip_databytes_dst",      },
    { from_name = "upPayLen",            rule_name = "ip_databytes_src",      }
  }

}

function M.print_sdt()
  print("------------ hello ----------------------- custom")
end

return M

-- slink.lua
local mapping = {
  from_proto_name = "slink",
  to_proto_name = "slink",
  rule_proto_name = "slink",
  common_flag = true,
  field = {
    {from_name = "payloaddsthex",to_name = "payloaddsthex"},
    {from_name = "payloadsrchex",to_name = "payloadsrchex"},
    {from_name = "packetsdst"   ,to_name = "packetsdst"   },
    {from_name = "bytesdst"     ,to_name = "bytesdst"     },
    {from_name = "paylendstset" ,to_name = "paylendstset" },
    {from_name = "paylensrcset" ,to_name = "paylensrcset" },
    {from_name = "packetssrc"   ,to_name = "packetssrc"   },
    {from_name = "bytessrc"     ,to_name = "bytessrc"     },
    {from_name = "tcpflag"      ,to_name = "tcpflag"      },
    {from_name = "endTime"      ,to_name = "endTime"      },
    {from_name = "begTime"      ,to_name = "begTime"      },
    {from_name = "lineNum4"     ,to_name = "lineNum4"     },
    {from_name = "lineNum3"     ,to_name = "lineNum3"     },
    {from_name = "lineNum2"     ,to_name = "lineNum2"     },
    {from_name = "lineNum1"     ,to_name = "lineNum1"     },
    {from_name = "captureTime"  ,to_name = "captureTime"  },
    {from_name = "SesBytes"     ,to_name = "SesBytes"     },
    {from_name = "SesPackets"   ,to_name = "SesPackets"   },
    {from_name = "ipProtocol"   ,to_name = "ipProtocol"   },
    {from_name = "dstPort"      ,to_name = "dstPort"      },
    {from_name = "srcPort"      ,to_name = "srcPort"      },
    {from_name = "dstAddrv6"    ,to_name = "dstAddrv6"    },
    {from_name = "dstAddr"      ,to_name = "dstAddr"      },
    {from_name = "srcAddrv6"    ,to_name = "srcAddrv6"    },
    {from_name = "srcAddr"      ,to_name = "srcAddr"      },
  }
}
yalua_register_proto(mapping)
-- igmp.lua
local mapping = {
  from_proto_name = "igmp",
  to_proto_name = "igmp",
  rule_proto_name = "igmp",
  common_flag = true,
  field = {
    {from_name = "num_grp_recs"  ,to_name = "recNumMul"     },
    {from_name = "num_src"       ,to_name = "recNumSrc"     },
    {from_name = "record_type"   ,to_name = "recType"       },
    {from_name = "maddr"         ,to_name = "mulAddr"       },
    {from_name = "type"          ,to_name = "infoType"      },

  }
}
yalua_register_proto(mapping)
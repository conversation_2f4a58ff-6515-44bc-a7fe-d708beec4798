-- rip.lua
local mapping = {
  from_proto_name = "rip",
  to_proto_name = "rip",
  rule_proto_name = "rip",
  common_flag = true,
  field = {
    {from_name = "AuthType"      ,to_name = "authType"      },
    {from_name = "Metric"        ,to_name = "metric"        },
    {from_name = "NextHop"       ,to_name = "nextHop"       },
    {from_name = "SubnetMask"    ,to_name = "subMask"       },
    {from_name = "RouteTag"      ,to_name = "rouTag"        },
    {from_name = "RouteDstIp"    ,to_name = "IPAddr"        },
    {from_name = "AddrFamily"    ,to_name = "addrFamID"     },
    {from_name = "Version"       ,to_name = "ver"           },
    {from_name = "Command"       ,to_name = "cmd"           },

  }
}
yalua_register_proto(mapping)
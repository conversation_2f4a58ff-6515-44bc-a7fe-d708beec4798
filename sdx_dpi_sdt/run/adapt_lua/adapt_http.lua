-- http.lua
local mapping = {
  from_proto_name = "http",
  to_proto_name = "http",
  rule_proto_name = "http",
  common_flag = true,
  field = {
    {from_name = "conDispDown"        ,to_name = "conDispDown"   ,rule_name = "content_disp_server"   },
    {from_name = "conDispUp"          ,to_name = "conDispUp"     ,rule_name = "content_disp_client"   ,tll = 1},
    {from_name = ""                   ,to_name = "conEncBySrvCnt",tll = 1},
    {from_name = "X_Sinkhole"         ,to_name = "xSinHol"       ,rule_name = "xsinkhole"        ,tll = 1},
    {from_name = "Location"           ,to_name = "Location"      ,rule_name = "loc"         ,tll = 1},
    {from_name = "Content-Encoding-s" ,to_name = "conEncBySrv"   ,rule_name = "content_encoding_server"    ,tll = 1},
    {from_name = "Content-MD5"        ,to_name = "conMD5BySrv"   ,tll = 1},
    {from_name = ""                   ,to_name = "respBodyN"     ,tll = 1},
    {from_name = "rspBody"            ,to_name = "respBody"      ,rule_name = "body_server"           ,tll = 1},
    {from_name = ""                   ,to_name = "respHeadCnt"   ,tll = 1},
    {from_name = "rspHeadFieldsMD5"   ,to_name = "respHeadMd5"   ,tll = 1},
    {from_name = "rspHeadFields"      ,to_name = "respHead"      ,rule_name = "header_server"         ,tll = 1},
    {from_name = ""                   ,to_name = "respVerCnt"    ,tll = 1},
    {from_name = "rspVersion"         ,to_name = "respVer"       ,rule_name = "version_server"        ,tll = 1},
    {from_name = ""                   ,to_name = "xForForCnt"    ,tll = 1},
    {from_name = "imsi"               ,to_name = "imsi"          ,rule_name = "imsi"           ,tll = 1},
    {from_name = "imei"               ,to_name = "imei"          ,rule_name = "imei"           ,tll = 1},
    {from_name = ""                   ,to_name = "cookieKeyCnt"  ,tll = 1},
    {from_name = "CookieKeys"         ,to_name = "cookieKey"     ,rule_name = "cookie_key"       ,tll = 1},
    {from_name = ""                   ,to_name = "conMD5ByCli"   ,tll = 1},
    {from_name = ""                   ,to_name = "reqBodyN"      ,tll = 1},
    {from_name = "reqBody"            ,to_name = "reqBody"       ,rule_name = "body_client"           ,tll = 1},
    {from_name = ""                   ,to_name = "userCnt"       ,tll = 1},
    {from_name = "Username"           ,to_name = "user"          ,rule_name = "user"             ,tll = 1},
    {from_name = "UserAgentCount"     ,to_name = "usrAgeCnt"     ,tll = 1},
    {from_name = "uriSearch"          ,to_name = "uriSearch"     ,rule_name = "uri_search"       ,tll = 1},
    {from_name = "URI-Keys-Count"     ,to_name = "uriKeyCnt"     ,tll = 1},
    {from_name = "URI-Keys"           ,to_name = "uriKey"        ,rule_name = "uri_key"          ,tll = 1},
    {from_name = "URI-Path-Count"     ,to_name = "uriPathCnt"    ,tll = 1},
    {from_name = "URI-Path"           ,to_name = "uriPath"       ,rule_name = "uri_path"         ,tll = 1},
    {from_name = "URI-Count"          ,to_name = "uriCnt"        ,tll = 1},
    {from_name = "Host-Count"         ,to_name = "hostCnt"       ,tll = 1},
    {from_name = ""                   ,to_name = "authCnt"       ,tll = 1},
    {from_name = "Accept-Encoding"    ,to_name = "accEncByCli"   ,rule_name = "accept_encoding",tll = 1},
    {from_name = "Accept-Language"    ,to_name = "accLanByCli"   ,rule_name = "accept_language",tll = 1},
    {from_name = "Accept"             ,to_name = "accByCli"      ,rule_name = "accept"         ,tll = 1},
    {from_name = "reqHeadFieldsCount" ,to_name = "reqHeadCnt"    ,tll = 1},
    {from_name = "reqMethodCount"     ,to_name = "metCnt"        ,tll = 1},
    {from_name = "reqVersionCount"    ,to_name = "reqVerCnt"     ,tll = 1},
    {from_name = ""                   ,to_name = "contDown"      },
    {from_name = ""                   ,to_name = "fileName"      },
    {from_name = "rspFullTextLen"     ,to_name = "fullTextLen"   },
    {from_name = ""                   ,to_name = "fullTextHeader"},
    {from_name = ""                   ,to_name = "httpEmbPro"    },
    {from_name = ""                   ,to_name = "httpRelKey"    },
    {from_name = "rspAccept-Charset"  ,to_name = "accChaDown"    },
    {from_name = "Last-Modified"      ,to_name = "lasMod"        },
    {from_name = "Expires"            ,to_name = "expires"       },
    {from_name = "Allow"              ,to_name = "allow"         },
    {from_name = "rspContent-Type"    ,to_name = "conTypDown"    },
    {from_name = "Refresh"            ,to_name = "refresh"       },
    {from_name = "WWW-Authenticate"   ,to_name = "wwwAuth"       },
    {from_name = "Retry-After"        ,to_name = "retAft"        },
    {from_name = "ETag"               ,to_name = "eTag"          },
    {from_name = "rspAccept-Ranges"   ,to_name = "accRanDown"    },
    {from_name = "Trailer"            ,to_name = "trail"         },
    {from_name = "rspPragma"          ,to_name = "praDown"       },
    {from_name = "rspConnection"      ,to_name = "conDown"       },
    {from_name = "rspCache-Control"   ,to_name = "cacConDown"    },
    {from_name = "TE"                 ,to_name = "te"            },
    {from_name = "Max-Forwards"       ,to_name = "maxFor"        },
    {from_name = "If-Unmodified-Since",to_name = "ifUnModSin"    },
    {from_name = "If-Range"           ,to_name = "ifRan"         },
    {from_name = "If-None-Match"      ,to_name = "ifNonMat"      },
    {from_name = "If-Modified-Since"  ,to_name = "ifModSin"      },
    {from_name = "If-Match"           ,to_name = "ifMat"         },
    {from_name = "Accept-Ranges"      ,to_name = "acctRanUp"     },
    {from_name = "Accept-Charset"     ,to_name = "accChaUp"      },
    {from_name = "Upgrade"            ,to_name = "upg"           },
    {from_name = "Pragma"             ,to_name = "praUp"         },
    {from_name = "Connection"         ,to_name = "conUp"         },
    {from_name = "Cache-Control"      ,to_name = "cacConUp"      },
    {from_name = "reqHeadFieldsMD5"   ,to_name = "reqHeadMd5"    ,tll = 1},
    {from_name = "reqHeadFields"      ,to_name = "reqHead"       ,rule_name = "header_client"         ,tll = 1},
    {from_name = "Version"            ,to_name = "reqVer"        ,rule_name = "version_client"        ,tll = 1},
    {from_name = ""                   ,to_name = "statCodeCnt"   ,tll = 1},
    {from_name = "Via-Count"          ,to_name = "viaCnt"        ,tll = 1},
    {from_name = "Range"              ,to_name = "rangeofCli"    },
    {from_name = ""                  ,to_name = "extHdrs"       },
    {from_name = "X-Powered-By"       ,to_name = "xPowBy"        ,rule_name = "x_powered_by"           ,tll = 1},
    {from_name = "Proxy-Type"         ,to_name = "proAuth"       },
    {from_name = "Age"                ,to_name = "srvAge"        },
    {from_name = "Method"             ,to_name = "met"           ,rule_name = "method"           ,tll = 1},
    {from_name = "Status"             ,to_name = "statCode"      ,rule_name = "statuscode"       ,tll = 1},
    {from_name = "X-Forwarded-For"    ,to_name = "xForFor"       ,rule_name = "xforwarded_for"           ,tll = 1},
    {from_name = "Via"                ,to_name = "via"           ,rule_name = "via"              ,tll = 1},
    {from_name = "User-Agent"         ,to_name = "usrAge"        ,rule_name = "user_agent"       ,tll = 1},
    {from_name = "Transfer-Encoding"  ,to_name = "traEnc"        ,rule_name = "transfer_encoding"        ,tll = 1},
    {from_name = ""                   ,to_name = "setCookieVal"  ,tll = 1},
    {from_name = "Set-Cookie"         ,to_name = "setCookieKey"  ,rule_name = "set_cookie_key"   ,tll = 1},
    {from_name = ""                   ,to_name = "srvCnt"        ,tll = 1},
    {from_name = "Server"             ,to_name = "srv"           ,rule_name = "server"           ,tll = 1},
    {from_name = "Referer"            ,to_name = "refURL"        ,rule_name = "referer"              ,tll = 1},
    {from_name = "Proxy-Authorization",to_name = "proAuthor"     },
    {from_name = "Proxy-Authenticate" ,to_name = "proAuthen"     },
    {from_name = "Location"           ,to_name = "loc"           },
    {from_name = "From"               ,to_name = "from"          ,rule_name = "from"             ,tll = 1},
    {from_name = "Date"               ,to_name = "date"          ,rule_name = "date"             ,tll = 1},
    {from_name = "Cookie2"            ,to_name = "cookie2"       },
    {from_name = "Cookie"             ,to_name = "cookie"        ,rule_name = "cookie"           ,tll = 1},
    {from_name = "Content-Type"       ,to_name = "conType"       },
    {from_name = "Content-MD5"        ,to_name = "conMD5"        },
    {from_name = "Url"                ,to_name = "conURL"        },
    {from_name = "reqContent-Length"  ,to_name = "conLenByCli"   ,rule_name = "content_length_client"    ,tll = 1},
    {from_name = "rspContent-Length"  ,to_name = "conLenSrv"     ,rule_name = "content_length_server"    ,tll = 1},
    {from_name = "Content-Language"   ,to_name = "conLan"        ,rule_name = "content_language"      ,tll = 1},
    {from_name = "Content-Encoding-c" ,to_name = "conEncByCli"   ,rule_name = "content_encoding_client"    ,tll = 1},
    {from_name = "Authorization"      ,to_name = "authInfo"      ,rule_name = "auth"             ,tll = 1},
    {from_name = "Vary"               ,to_name = "varConEnc"     ,rule_name = "vary"             ,tll = 1},
    {from_name = "URI"                ,to_name = "uri"           ,rule_name = "uri"              ,tll = 1},
    {from_name = "Host"               ,to_name = "host"          ,rule_name = "host"             ,tll = 1},

  }
}
yalua_register_proto(mapping)

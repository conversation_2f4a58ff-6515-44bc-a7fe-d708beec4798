-- ldp.lua
local mapping = {
  from_proto_name = "ldp",
  to_proto_name = "ldp",
  rule_proto_name = "ldp",
  common_flag = true,
  field = {
    {from_name = ""                  ,to_name = "fecElePrefixList"  },
    {from_name = "address_list"      ,to_name = "fecEleAddrTypeList"},
    {from_name = "vci"               ,to_name = "vci"               },
    {from_name = "vpi"               ,to_name = "vpi"               },
    {from_name = "dlci"              ,to_name = "dlci"              },
    {from_name = "ip4_transport_addr",to_name = "ipv4TransAddr"     },
    {from_name = "targeted_hello"    ,to_name = "targetHello"       },
    {from_name = "label"             ,to_name = "MPLS"              },
    {from_name = "message_id"        ,to_name = "msgID"             },
    {from_name = "message_type"      ,to_name = "msgType"           },
    {from_name = "label_space_id"    ,to_name = "LabSpaAtt"         },
    {from_name = "lsr_id"            ,to_name = "LSRID"             },
    {from_name = "version"           ,to_name = "LDPVer"            },

    
  }
}
yalua_register_proto(mapping)
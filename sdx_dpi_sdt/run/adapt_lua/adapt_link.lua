local mapping_link = {
  from_proto_name = "link",
  to_proto_name = "link",
  rule_proto_name = "link",
  rule_name_flag = false,
  field = {
    {from_name = "payLenRatio"        ,to_name = "payLenRatio"        ,tll = 1},
    {from_name = "sesBytesRatio"      ,to_name = "sesBytesRatio"      ,tll = 1},
    {from_name = "sesBytes"           ,to_name = "sesBytes"           ,rule_name = "ip_bytes"             ,tll = 1},
    {from_name = "downSesBytes"       ,to_name = "downSesbytes"       ,rule_name = "ip_bytes_dst"         ,tll = 1},
    {from_name = "upSesBytes"         ,to_name = "upSesBytes"         ,rule_name = "ip_bytes_src"         ,tll = 1},
    {from_name = "downLinkTcpOpts"    ,to_name = "downLinkTcpOpts"    ,rule_name = "tcp_options_dst"      ,tll = 1},
    {from_name = "upLinkTcpOpts"      ,to_name = "upLinkTcpOpts"      ,rule_name = "tcp_options_src"      ,tll = 1},
    {from_name = "downLinkSynTcpWins" ,to_name = "downLinkSynTcpWins" ,rule_name = "tcp_winsize_dst"      ,tll = 1},
    {from_name = "upLinkSynTcpWins"   ,to_name = "upLinkSynTcpWins"   ,rule_name = "tcp_winsize_src"      ,tll = 1},
    {from_name = "downLinkPayLenSet"  ,to_name = "downLinkPayLenSet"  ,rule_name = "trans_paylen_set_dst" ,tll = 1},
    {from_name = "upLinkPayLenSet"    ,to_name = "upLinkPayLenSet"    ,rule_name = "trans_paylen_set_src" ,tll = 1},
    {from_name = "downLinkTransPayHex",to_name = "downLinkTransPayHex",tll = 1},
    {from_name = "upLinkTransPayHex"  ,to_name = "upLinkTransPayHex"  ,tll = 1},
    {from_name = "downLinkStream"     ,to_name = "downLinkStream"     ,rule_name = "ip_stream_dst"        ,tll = 1},
    {from_name = "upLinkStream"       ,to_name = "upLinkStream"       ,rule_name = "ip_stream_src"        ,tll = 1},
    {from_name = "stream"             ,to_name = "stream"             ,rule_name = "ip_stream"            ,tll = 1},
    {from_name = "downLinkDesBytes"   ,to_name = "downLinkDesBytes"   ,rule_name = "ip_desiredbytes_dst"  ,tll = 1},
    {from_name = "upLinkDesBytes"     ,to_name = "upLinkDesBytes"     ,rule_name = "ip_desiredbytes_src"  ,tll = 1},
    {from_name = "downLinkChecksum"   ,to_name = "downLinkChecksum"   ,tll = 1},
    {from_name = "upLinkChecksum"     ,to_name = "upLinkChecksum"     ,tll = 1},
    {from_name = "tcpFlagsSynAckCnt"  ,to_name = "tcpFlagsSynAckCnt"  ,rule_name = "tcp_flags_syn_ack_cnt",tll = 1},
    {from_name = "tcpFlagsNSCnt"      ,to_name = "tcpFlagsNSCnt"      ,tll = 1},
    {from_name = "tcpFlagsCwrCnt"     ,to_name = "tcpFlagsCwrCnt"     ,tll = 1},
    {from_name = "tcpFlagsEceCnt"     ,to_name = "tcpFlagsEceCnt"     ,tll = 1},
    {from_name = "tcpFlagsUrgCnt"     ,to_name = "tcpFlagsUrgCnt"     ,rule_name = "tcp_flags_urg_cnt"    ,tll = 1},
    {from_name = "tcpFlagsAckCnt"     ,to_name = "tcpFlagsAckCnt"     ,rule_name = "tcp_flags_ack_cnt"    ,tll = 1},
    {from_name = "tcpFlagsPshCnt"     ,to_name = "tcpFlagsPshCnt"     ,rule_name = "tcp_flags_psh_cnt"    ,tll = 1},
    {from_name = "tcpFlagsRstCnt"     ,to_name = "tcpFlagsRstCnt"     ,rule_name = "tcp_flags_rst_cnt"    ,tll = 1},
    {from_name = "tcpFlagsSynCnt"     ,to_name = "tcpFlagsSynCnt"     ,rule_name = "tcp_flags_syn_cnt"    ,tll = 1},
    {from_name = "tcpFlagsFinCnt"     ,to_name = "tcpFlagsFinCnt"     ,rule_name = "tcp_flags_fin_cnt"    ,tll = 1},
    {from_name = "appDirec"           ,to_name = "appDirec"           ,rule_name = "ip_direction"         ,tll = 1},
    {from_name = "firTtlBySrv"        ,to_name = "firTtlBySrv"        ,rule_name = "ip_ttl_dst"           ,tll = 1},
    {from_name = "firTtlByCli"        ,to_name = "firTtlByCli"        ,rule_name = "ip_ttl_src"           ,tll = 1},
    {from_name = "downLinkSmaPktInt"  ,to_name = "downLinkSmaPktInt"  ,tll = 1},
    {from_name = "downLinkBigPktInt"  ,to_name = "downLinkBigPktInt"  ,tll = 1},
    {from_name = "downLinkSmaPktLen"  ,to_name = "downLinkSmaPktLen"  ,tll = 1},
    {from_name = "downLinkBigPktLen"  ,to_name = "downLinkBigPktLen"  ,tll = 1},
    {from_name = "downLinkPktNum"     ,to_name = "downLinkPktNum"     ,rule_name = "ip_packets_dst"       ,tll = 1},
    {from_name = "upLinkSmaPktInt"    ,to_name = "upLinkSmaPktInt"    ,tll = 1},
    {from_name = "upLinkBigPktInt"    ,to_name = "upLinkBigPktInt"    ,tll = 1},
    {from_name = "upLinkSmaPktLen"    ,to_name = "upLinkSmaPktLen"    ,tll = 1},
    {from_name = "upLinkBigPktLen"    ,to_name = "upLinkBigPktLen"    ,tll = 1},
    {from_name = "upLinkPktNum"       ,to_name = "upLinkPktNum"       ,rule_name = "ip_packets_src"       ,tll = 1},
    {from_name = "downPayLen"         ,to_name = "downPayLen"         ,rule_name = "ip_databytes_dst"     ,tll = 1},
    {from_name = "upPayLen"           ,to_name = "upPayLen"           ,rule_name = "ip_databytes_src"     ,tll = 1},
  }

}

yalua_register_proto(mapping_link)
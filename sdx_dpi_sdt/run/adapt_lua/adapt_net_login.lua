-- net_login.lua
local mapping = {
  from_proto_name = "net_logon",
  to_proto_name = "net_login",
  rule_proto_name = "net_login",
  common_flag = true,
  field = {
    {from_name = "DNSHostName"    ,to_name = "DNSHostName"    },
    {from_name = "loginSrvDNSName",to_name = "loginSrvDNSName"},
    {from_name = "loginDomDNSName",to_name = "loginDomDNSName"},
    {from_name = "trustDomName"   ,to_name = "trustDomName"   },
    {from_name = "DCSitName"      ,to_name = "DCSitName"      },
    {from_name = "loginForDNSName",to_name = "loginForDNSName"},
    {from_name = "loginDomGUID"   ,to_name = "loginDomGUID"   },
    {from_name = "DCAddrType"     ,to_name = "DCAddrType"     },
    {from_name = "DCAddr"         ,to_name = "DCAdd<PERSON>"         },
    {from_name = "loginDomName"   ,to_name = "loginDomName"   },
    {from_name = "loginSrvSitName",to_name = "loginSrvSitName"},
    {from_name = "srvName"        ,to_name = "srvName"        },
    {from_name = "accName"        ,to_name = "accName"        },
    {from_name = "netbiosName"    ,to_name = "netbiosName"    },
    {from_name = "PDCName"        ,to_name = "PDCName"        },

    
  }
}
yalua_register_proto(mapping)
-- ftp.lua
local mapping = {
  from_proto_name = "ftp",
  to_proto_name = "ftp",
  rule_proto_name = "ftp",
  common_flag = true,
  field = {
    {from_name = "software"      ,to_name = "software"      ,rule_name = "software"  ,tll = 1},
    {from_name = ""              ,to_name = "fileCon"       },
    {from_name = ""              ,to_name = "ops"           },
    {from_name = "LoginStatus"   ,to_name = "loginFlg"      ,rule_name = "login_flag",tll = 1},
    {from_name = ""              ,to_name = "cryValid"      },
    {from_name = "Mode"          ,to_name = "mode"          },
    {from_name = "DataIp"        ,to_name = "dataIP"        },
    {from_name = "Return_content",to_name = "resArg"        ,rule_name = "response_argument"  ,tll = 1},
    {from_name = "Return_code"   ,to_name = "resCode"       ,rule_name = "response_code" ,tll = 1},
    {from_name = "Method_content",to_name = "reqArg"        ,rule_name = "request_param"   ,tll = 1},
    {from_name = "Method"        ,to_name = "reqCmd"        ,rule_name = "request_cmd"   ,tll = 1},
    {from_name = "hostName"      ,to_name = "hostName"      ,rule_name = "server_name"  ,tll = 1},
    {from_name = "Content_type"  ,to_name = "conType"       ,rule_name = "content_type"  ,tll = 1},
    {from_name = "FileSize"      ,to_name = "fileSize"      ,rule_name = "file_size"  ,tll = 1},
    {from_name = "Filename"      ,to_name = "fileName"      ,rule_name = "file_name"  ,tll = 1},
    {from_name = "Server_IP"     ,to_name = "srvAddr"       ,rule_name = "server_ip"  ,tll = 1},
    {from_name = "DataPort"      ,to_name = "dataPort"      ,rule_name = "port" },
    {from_name = "Password"      ,to_name = "pwd"           ,rule_name = "pwd"       ,tll = 1},
    {from_name = "Username"      ,to_name = "login"         ,rule_name = "user"      ,tll = 1},

  }
}
yalua_register_proto(mapping)
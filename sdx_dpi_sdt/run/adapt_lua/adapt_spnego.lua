-- spnego.lua
local mapping = {
  from_proto_name = "spnego",
  to_proto_name = "spnego",
  rule_proto_name = "spnego",
  common_flag = true,
  field = {
    {from_name = "supported_mech",to_name = "supMech"       },
    {from_name = "hint_address"  ,to_name = "hintAddr"      },
    {from_name = "hint_name"     ,to_name = "hintName"      },
    {from_name = "mech_type"     ,to_name = "mechType"      },

  }
}
yalua_register_proto(mapping)
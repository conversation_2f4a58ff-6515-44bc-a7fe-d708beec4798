-- dcerpc.lua
local mapping = {
  from_proto_name = "dcerpc",
  to_proto_name = "dcerpc",
  rule_proto_name = "dcerpc",
  common_flag = true,
  field = {
    {from_name = "verMin"        ,to_name = "verMin"        },
    {from_name = "verMas"        ,to_name = "verMas"        },
    {from_name = "svcUUID"       ,to_name = "svcUUID"       },
    {from_name = "authType"      ,to_name = "authType"      },
    {from_name = "opeNum"        ,to_name = "opeNum"        },
    {from_name = "pktFlag"       ,to_name = "pktFlag"       },
    {from_name = "pktType"       ,to_name = "pktType"       },

  }
}
yalua_register_proto(mapping)
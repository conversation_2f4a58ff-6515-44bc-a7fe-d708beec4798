-- mysql.lua
local mapping = {
  from_proto_name = "mysql",
  to_proto_name = "mysql",
  rule_proto_name = "mysql",
  common_flag = true,
  field = {
    {from_name = "RequestCommand"       ,to_name = "reqCom"        },
    {from_name = "TableName"            ,to_name = "tabName"       },
    {from_name = "ClientChallenge"      ,to_name = "cliCha"        },
    {from_name = "ExtClientCapabilities",to_name = "extCliCap"     },
    {from_name = "ClientCapabilities"   ,to_name = "cliCap"        },
    {from_name = "Charset"              ,to_name = "cha"           },
    {from_name = "ServerStatus"         ,to_name = "srvSta"        },
    {from_name = "ServerCapabilities"   ,to_name = "srvCap"        },
    {from_name = "ExtServerChallenge"   ,to_name = "extSrvCha"     },
    {from_name = "ServerChallenge"      ,to_name = "srvCha"        },
    {from_name = "ServerThreadid"       ,to_name = "srvThrID"      },
    {from_name = "ServerVersion"        ,to_name = "srvVer"        },
    {from_name = "ProtocolVersion"      ,to_name = "protVer"       },

    
  }
}
yalua_register_proto(mapping)
-- tll.lua
local mapping = {
  from_proto_name = "tll",
  to_proto_name = "tll",
  rule_proto_name = "tll",
  common_flag = true,
  field = {
    {from_name = "direction"     ,to_name = "direction"     },
    {from_name = "octetsline_s2c",to_name = "octetsline_s2c",rule_name = "octetsline_s2c"},
    {from_name = "octetsline_c2s",to_name = "octetsline_c2s",rule_name = "octetsline_c2s"},
    {from_name = "pktline_s2c"   ,to_name = "pktline_s2c"   ,rule_name = "pktline_s2c"   },
    {from_name = "pktline_c2s"   ,to_name = "pktline_c2s"   ,rule_name = "pktline_c2s"   },
    {from_name = "linkno4_s2c"   ,to_name = "linkno4_s2c"   ,rule_name = "linkno4_s2c"   },
    {from_name = "linkno3_s2c"   ,to_name = "linkno3_s2c"   ,rule_name = "linkno3_s2c"   },
    {from_name = "linkno2_s2c"   ,to_name = "linkno2_s2c"   ,rule_name = "linkno2_s2c"   },
    {from_name = "linkno1_s2c"   ,to_name = "linkno1_s2c"   ,rule_name = "linkno1_s2c"   },
    {from_name = "linkno4_c2s"   ,to_name = "linkno4_c2s"   ,rule_name = "linkno4_c2s"   },
    {from_name = "linkno3_c2s"   ,to_name = "linkno3_c2s"   ,rule_name = "linkno3_c2s"   },
    {from_name = "linkno2_c2s"   ,to_name = "linkno2_c2s"   ,rule_name = "linkno2_c2s"   },
    {from_name = "linkno1_c2s"   ,to_name = "linkno1_c2s"   ,rule_name = "linkno1_c2s"   },
    {from_name = "linkno_count"  ,to_name = "linkno_count"  ,rule_name = "linkno_cnt"    },
    {from_name = "octetsall_s2c" ,to_name = "octetsall_s2c" ,rule_name = "octetsall_s2c" },
    {from_name = "octetsall_c2s" ,to_name = "octetsall_c2s" ,rule_name = "octetsall_c2s" },
    {from_name = "pktall_s2c"    ,to_name = "pktall_s2c"    ,rule_name = "pktall_s2c"    },
    {from_name = "pktall_c2s"    ,to_name = "pktall_c2s"    ,rule_name = "pktall_c2s"    },
    {from_name = "endtime"       ,to_name = "endtime"       ,rule_name = "endtime"       },
    {from_name = "starttime"     ,to_name = "starttime"     ,rule_name = "starttime"     },
    {from_name = "networkid"     ,to_name = "networkid"     ,rule_name = "networkid"     },
    {from_name = "protocol"      ,to_name = "protocol"      ,rule_name = "protocol"      },

  }
}
yalua_register_proto(mapping)
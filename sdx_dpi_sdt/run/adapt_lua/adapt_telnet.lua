-- telnet.lua
local mapping = {
  from_proto_name = "telnet",
  to_proto_name = "telnet",
  rule_proto_name = "telnet",
  common_flag = true,
  field = {
    {from_name = "Negotiations_s",to_name = "cmdBySrv"      ,rule_name = "cmd_server"     ,tll = 1},
    {from_name = "LoginStatus"   ,to_name = "loginFlag"     ,rule_name = "login_flag",tll = 1},
    {from_name = "Banner"        ,to_name = "srvBanner"     ,rule_name = "banner"  ,tll = 1},
    {from_name = "Negotiations_c",to_name = "cmdByCli"      ,rule_name = "cmd_client"     ,tll = 1},
    {from_name = "TelnetData"    ,to_name = "comRespCon"    ,rule_name = "data"  ,tll = 1},
    {from_name = "Terminaltype"  ,to_name = "terType"       ,rule_name = "terminal"  ,tll = 1},
    {from_name = "Passwd"        ,to_name = "pwd"           ,rule_name = "pwd"       ,tll = 1},
    {from_name = "Username"      ,to_name = "login"         ,rule_name = "user"      ,tll = 1},

  }
}
yalua_register_proto(mapping)
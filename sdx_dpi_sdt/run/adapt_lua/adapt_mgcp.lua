-- mgcp.lua
local mapping = {
  from_proto_name = "mgcp",
  to_proto_name = "mgcp",
  rule_proto_name = "mgcp",
  common_flag = true,
  field = {
    {from_name = "media_stream_proto"          ,to_name = "medTraPro"     },
    {from_name = "media_stream_port"           ,to_name = "medPort"       },
    {from_name = "media_stream_address"        ,to_name = "medAddr"       },
    {from_name = "media_stream_rate"           ,to_name = "medRate"       },
    {from_name = "media_stream_encoding_format",to_name = "medEnc"        },
    {from_name = "media_stream_type"           ,to_name = "medType"       },
    {from_name = "link_mode"                   ,to_name = "mode"          },
    {from_name = "session_endtime"             ,to_name = "endTime"       },
    {from_name = "session_begintime"           ,to_name = "startTime"     },
    {from_name = "calling_number"              ,to_name = "calliNum"      },
    {from_name = "endpoint_number"             ,to_name = "endPoi"        },

    
  }
}
yalua_register_proto(mapping)
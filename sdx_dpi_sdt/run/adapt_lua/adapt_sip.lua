-- sip.lua
local mapping = {
  from_proto_name = "sip",
  to_proto_name = "sip",
  rule_proto_name = "sip",
  common_flag = true,
  field = {
    {from_name = "sdp_keep_time1"       ,to_name = "sdp_keep_time1"       },
    {from_name = "sdp_keep_time0"       ,to_name = "sdp_keep_time0"       },
    {from_name = "sdp_t_start_time1"    ,to_name = "sdp_t_start_time1"    },
    {from_name = "call_tagged"          ,to_name = "call_tagged"          },
    {from_name = "called"               ,to_name = "called"               },
    {from_name = "calling"              ,to_name = "calling"              },
    {from_name = "body_media_format2"   ,to_name = "media_format2"        },
    {from_name = "body_media_format1"   ,to_name = "media_format1"        },
    {from_name = "media_protocol"       ,to_name = "media_protocol"       },
    {from_name = "media_port"           ,to_name = "media_port"           },
    {from_name = "media_type"           ,to_name = "media_type"           },
    {from_name = "media_descrip"        ,to_name = "media_descrip"        },
    {from_name = "td_stop_time"         ,to_name = "td_stop_time"         },
    {from_name = "td_start_time"        ,to_name = "td_start_time"        },
    {from_name = "time_descrip"         ,to_name = "time_descrip"         },
    {from_name = "connec_address"       ,to_name = "connec_address"       },
    {from_name = "connec_address_type"  ,to_name = "connec_address_type"  },
    {from_name = "connec_network_type"  ,to_name = "connec_network_type"  },
    {from_name = "connection"           ,to_name = "connection"           },
    {from_name = "owner_address"        ,to_name = "owner_address"        },
    {from_name = "owner_adddress_type"  ,to_name = "owner_adddress_type"  },
    {from_name = "owner_network_type"   ,to_name = "owner_network_type"   },
    {from_name = "owner_session_version",to_name = "owner_session_version"},
    {from_name = "owner_session_id"     ,to_name = "owner_session_id"     },
    {from_name = "owner_name"           ,to_name = "owner_name"           },
    {from_name = "owner_session"        ,to_name = "owner_session"        },
    {from_name = "session_version"      ,to_name = "session_version"      },
    {from_name = ""                     ,to_name = "message_body"         },
    {from_name = ""                     ,to_name = "message_header"       },
    {from_name = "sip_rtp_flowid"       ,to_name = "sip_rtp_flowid"       },
    {from_name = "rseq"                 ,to_name = "rseq"                 },
    {from_name = "timestamp"            ,to_name = "timestamp"            },
    {from_name = "sdp_a_attributes0"    ,to_name = "sdp_a_attributes0"    },
    {from_name = "sdp_c_connections0"   ,to_name = "sdp_c_connections0"   },
    {from_name = "sdp_m_payloads0"      ,to_name = "sdp_m_payloads0"      },
    {from_name = "sdp_m_proto0"         ,to_name = "sdp_m_proto0"         },
    {from_name = "sdp_m_port0"          ,to_name = "sdp_m_port0"          },
    {from_name = "sdp_m_media0"         ,to_name = "sdp_m_media0"         },
    {from_name = "sdp_t_stop_time0"     ,to_name = "sdp_t_stop_time0"     },
    {from_name = "sdp_t_start_time0"    ,to_name = "sdp_t_start_time0"    },
    {from_name = "sdp_b_bandwidths"     ,to_name = "sdp_b_bandwidths"     },
    {from_name = "sdp_i_info"           ,to_name = "sdp_i_info"           },
    {from_name = "sdp_c_addr"           ,to_name = "sdp_c_addr"           },
    {from_name = "sdp_c_addrtype"       ,to_name = "sdp_c_addrtype"       },
    {from_name = "sdp_c_nettype"        ,to_name = "sdp_c_nettype"        },
    {from_name = "sdp_s_name"           ,to_name = "sdp_s_name"           },
    {from_name = "sdp_o_addr"           ,to_name = "sdp_o_addr"           },
    {from_name = "sdp_o_addrtype"       ,to_name = "sdp_o_addrtype"       },
    {from_name = "sdp_o_nettype"        ,to_name = "sdp_o_nettype"        },
    {from_name = "sdp_o_sess_version"   ,to_name = "sdp_o_sess_version"   },
    {from_name = "sdp_o_sess_id"        ,to_name = "sdp_o_sess_id"        },
    {from_name = "sdp_o_username"       ,to_name = "sdp_o_username"       },
    {from_name = "sdp_v_version"        ,to_name = "sdp_v_version"        },
    {from_name = "cseq_method"          ,to_name = "cseq_method"          },
    {from_name = "cseq_sequence_num"    ,to_name = "cseq_sequence_num"    },
    {from_name = "CSeq"                 ,to_name = "CSeq"                 },
    {from_name = "Call-Info"            ,to_name = "Call-Info"            },
    {from_name = "Call-ID"              ,to_name = "Call-ID"              },
    {from_name = "to_tag"               ,to_name = "to_tag"               },
    {from_name = "to_host_port"         ,to_name = "to_host_port"         },
    {from_name = "to_host_part"         ,to_name = "to_host_part"         },
    {from_name = "to_country_code"      ,to_name = "to_country_code"      },
    {from_name = "to_e164_num"          ,to_name = "to_e164_num"          },
    {from_name = "to_user_part"         ,to_name = "to_user_part"         },
    {from_name = "from_tag"             ,to_name = "from_tag"             },
    {from_name = "from_host_port"       ,to_name = "from_host_port"       },
    {from_name = "from_host_part"       ,to_name = "from_host_part"       },
    {from_name = "from_country_code"    ,to_name = "from_country_code"    },
    {from_name = "from_e164_num"        ,to_name = "from_e164_num"        },
    {from_name = "from_user_part"       ,to_name = "from_user_part"       },
    {from_name = "via_recieved"         ,to_name = "via_recieved"         },
    {from_name = "via_rport"            ,to_name = "via_rport"            },
    {from_name = "via_branch"           ,to_name = "via_branch"           },
    {from_name = "via_sent_port"        ,to_name = "via_sent_port"        },
    {from_name = "via_sent_addr"        ,to_name = "via_sent_addr"        },
    {from_name = "via_transport"        ,to_name = "via_transport"        },
    {from_name = "req_uri"              ,to_name = "req_uri"              },
    {from_name = "sip_version"          ,to_name = "sip_version"          },
    {from_name = "sip_method"           ,to_name = "sip_method"           },

    
  }
}
yalua_register_proto(mapping)
-- l2tp.lua
local mapping = {
  from_proto_name = "l2tp",
  to_proto_name = "l2tp",
  rule_proto_name = "l2tp",
  common_flag = true,
  field = {
    {from_name = ""                        ,to_name = "advMsg"        },
    {from_name = ""                        ,to_name = "cauMsg"        },
    {from_name = ""                        ,to_name = "cauCode"       },
    {from_name = ""                        ,to_name = "maxBps"        },
    {from_name = ""                        ,to_name = "minBps"        },
    {from_name = "Private_group_id"        ,to_name = "priGrpID"      },
    {from_name = ""                        ,to_name = "ocrpSFraType"  },
    {from_name = ""                        ,to_name = "ocrpAFraType"  },
    {from_name = ""                        ,to_name = "occnSFraType"  },
    {from_name = ""                        ,to_name = "occnAFraType"  },
    {from_name = ""                        ,to_name = "iccnSFraType"  },
    {from_name = ""                        ,to_name = "iccnAFraType"  },
    {from_name = "Rx_connect_speed"        ,to_name = "rxConSpe"      },
    {from_name = "Tx_connect_speed"        ,to_name = "txConSpe"      },
    {from_name = ""                        ,to_name = "subAddr"       },
    {from_name = "Physical_channel"        ,to_name = "phyCHID"       },
    {from_name = ""                        ,to_name = "dBeaType"      },
    {from_name = ""                        ,to_name = "aBeaType"      },
    {from_name = "Call_serial_number"      ,to_name = "callSN"        },
    {from_name = ""                        ,to_name = "ocrqASID"      },
    {from_name = ""                        ,to_name = "ocrpASID"      },
    {from_name = ""                        ,to_name = "icrqASID"      },
    {from_name = ""                        ,to_name = "icrpASID"      },
    {from_name = ""                        ,to_name = "cdnASID"       },
    {from_name = ""                        ,to_name = "errMsg"        },
    {from_name = ""                        ,to_name = "errCode"       },
    {from_name = ""                        ,to_name = "resCode"       },
    {from_name = "Vendor_name_sccrp"       ,to_name = "rpvenName"     },
    {from_name = "Vendor_name_sccrq"       ,to_name = "rqvenName"     },
    {from_name = ""                        ,to_name = "rpFirRev"      },
    {from_name = ""                        ,to_name = "rqFirRev"      },
    {from_name = ""                        ,to_name = "tieBrek"       },
    {from_name = ""                        ,to_name = "cnChaResp"     },
    {from_name = ""                        ,to_name = "rpChaResp"     },
    {from_name = ""                        ,to_name = "rpChallenge"   },
    {from_name = ""                        ,to_name = "rqChallenge"   },
    {from_name = ""                        ,to_name = "rpReWinSize"   },
    {from_name = ""                        ,to_name = "rqReWinSize"   },
    {from_name = ""                        ,to_name = "rpDBeaCap"     },
    {from_name = ""                        ,to_name = "rpABeaCap"     },
    {from_name = ""                        ,to_name = "rqDBeaCap"     },
    {from_name = ""                        ,to_name = "rqABeaCap"     },
    {from_name = ""                        ,to_name = "stopccnATID"   },
    {from_name = "Assigned_tunnel_id_sccrp",to_name = "rpAssTunID"    },
    {from_name = "Assigned_tunnel_id_sccrq",to_name = "rqAssTunID"    },
    {from_name = ""                        ,to_name = "rpSFraCapRP"   },
    {from_name = ""                        ,to_name = "rpAFraCapRP"   },
    {from_name = ""                        ,to_name = "rqSFraCapRQ"   },
    {from_name = ""                        ,to_name = "rqAFraCapRQ"   },
    {from_name = "Host_name"               ,to_name = "rphostNameRP"  },
    {from_name = "Host_name"               ,to_name = "rqhostName"    },
    {from_name = ""                        ,to_name = "rpRev"         },
    {from_name = ""                        ,to_name = "rpVer"         },
    {from_name = ""                        ,to_name = "rqRev"         },
    {from_name = ""                        ,to_name = "rqVer"         },
    {from_name = "version"                 ,to_name = "ver"           },
    {from_name = "Chap_challenge"          ,to_name = "challenge"     },
    {from_name = "Vendor_name"             ,to_name = "venName"       },
    {from_name = "Calling_number"          ,to_name = "calliNum"      },
    {from_name = "Called_number"           ,to_name = "calleNum"      },
    {from_name = "message_type"            ,to_name = "msgType"       },
    {from_name = "session_id"              ,to_name = "sesID"         },
    {from_name = "tunnel_id"               ,to_name = "tunID"         },

  }
}
yalua_register_proto(mapping)
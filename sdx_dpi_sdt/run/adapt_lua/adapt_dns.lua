-- dns.lua
local mapping = {
  from_proto_name = "dns",
  to_proto_name = "dns",
  rule_proto_name = "dns",
  common_flag = true,
  field = {
    {from_name = "DNSTEX"           ,to_name = "DNSTEX"        ,rule_name = "txt"             ,tll = 1},
    {from_name = "DNSSPF"           ,to_name = "DNSSPF"        ,rule_name = "sfp"             ,tll = 1},
    {from_name = "nameSrvCountry"   ,to_name = "nameSrvCountry",tll = 1},
    {from_name = "nameSrvAsn"       ,to_name = "nameSrvAsn"    ,tll = 1},
    {from_name = "nameSrvIPCnt"     ,to_name = "nameSrvIPCnt"  ,tll = 1},
    {from_name = "nameSrvIp"        ,to_name = "nameSrvIp"     ,rule_name = "namesrv_ip"      ,tll = 1},
    {from_name = "nameSrvHostCnt"   ,to_name = "nameSrvHostCnt",tll = 1},
    {from_name = "nameSrvHost"      ,to_name = "nameSrvHost"   ,rule_name = "namesrv_host"    ,tll = 1},
    {from_name = "mailSrvCountry"   ,to_name = "mailSrvCountry",tll = 1},
    {from_name = "mailSrvAsn"       ,to_name = "mailSrvAsn"    ,tll = 1},
    {from_name = "mailSrvIPCnt"     ,to_name = "mailSrvIPCnt"  ,rule_name = "mx_ipv4_count"  ,tll = 1},
    {from_name = "mailSrvIp"        ,to_name = "mailSrvIp"     ,rule_name = "mx_ipv4"      ,tll = 1},
    {from_name = "mailSrvHostcnt"   ,to_name = "mailSrvHostcnt",rule_name = "mx_host_count",tll = 1},
    {from_name = "mailSrvHost"      ,to_name = "mailSrvHost"   ,rule_name = "mx_host"    ,tll = 1},
    {from_name = "aipCountry"       ,to_name = "aipCountry"    ,tll = 1},
    {from_name = "aipAsn"           ,to_name = "aipAsn"        ,tll = 1},
    {from_name = "AipCnt"           ,to_name = "AipCnt"        ,tll = 1},
    {from_name = "Aip"              ,to_name = "Aip"           ,rule_name = "a_ipv4"             ,tll = 1},
    {from_name = "additional_RRs"   ,to_name = "addCnt"        },
    {from_name = "authorization_RRs",to_name = "autCnt"        },
    {from_name = "ansIPv6"          ,to_name = "ansIPv6"       ,rule_name = "aaaa_ipv6"     ,tll = 1},
    {from_name = "ansCnameCnt"      ,to_name = "ansCnameCnt"   ,tll = 1},
    {from_name = "ansCname"         ,to_name = "ansCname"      ,rule_name = "cname"    ,tll = 1},
    {from_name = "answer_RRs"       ,to_name = "ansCnt"        ,tll = 1},
    {from_name = "addAnsRes"        ,to_name = "addAnsRes"     },
    {from_name = "addAnsType"       ,to_name = "addAnsType"    },
    {from_name = "authAnsRes"       ,to_name = "authAnsRes"    },
    {from_name = "authAnsType"      ,to_name = "authAnsType"   },
    {from_name = "ansRes"           ,to_name = "ansRes"        },
    {from_name = "ansTypes"         ,to_name = "ansTypes"      ,rule_name = "answer_type"    ,tll = 1},
    {from_name = "An_name00"        ,to_name = "ansQue"        ,rule_name = "answer_query"    ,tll = 1},
    {from_name = "flags"            ,to_name = "srvFlag"       },
    {from_name = "identification"   ,to_name = "traID"         },
    {from_name = "Qd_name00"        ,to_name = "queName"       },
    {from_name = "Qd_type00"        ,to_name = "queType"       },

    
  }
}
yalua_register_proto(mapping)
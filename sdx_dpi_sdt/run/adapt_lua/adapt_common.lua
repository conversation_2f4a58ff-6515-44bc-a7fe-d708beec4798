-- -- common 映射文件，只要调用接口即可，map 需要放在 init 脚本中，因为其他脚本需要调用
-- -- adapt_init.lua
local mapping_common = {
  from_proto_name = "common",
  to_proto_name = "common",
  rule_proto_name = "common",
  rule_name_flag = false,       -- 如果 false ，去 init.lua 的静态表里面找 rule_name
  field = {
    {from_name = "dstMacOui"     ,to_name = "dstMacOui"     ,tll = 1},
    {from_name = "srcMacOui"     ,to_name = "srcMacOui"     ,tll = 1},
    {from_name = "captureTime"   ,to_name = "captureTime"   ,tll = 1},
    {from_name = "outTransProto" ,to_name = "outTransProto" ,tll = 1},
    {from_name = "outDstPort"    ,to_name = "outDstPort"    ,tll = 1},
    {from_name = "outSrcPort"    ,to_name = "outSrcPort"    ,tll = 1},
    {from_name = "outer.ipv6.dst",to_name = "outer.ipv6.dst",tll = 1},
    {from_name = "outer.ipv6.src",to_name = "outer.ipv6.src",tll = 1},
    {from_name = "outDstAddr"    ,to_name = "outDstAddr"    ,tll = 1},
    {from_name = "outSrcAddr"    ,to_name = "outSrcAddr"    ,tll = 1},
    {from_name = "outAddrType"   ,to_name = "outAddrType"   ,tll = 1},
    {from_name = "dstASN"        ,to_name = "dstASN"        ,tll = 1},
    {from_name = "dstISP"        ,to_name = "dstISP"        ,tll = 1},
    {from_name = "dstLatitude"   ,to_name = "dstLatitude"   ,tll = 1},
    {from_name = "dstLongitude"  ,to_name = "dstLongitude"  ,tll = 1},
    {from_name = "dstCity"       ,to_name = "dstCity"       ,tll = 1},
    {from_name = "dstState"      ,to_name = "dstState"      ,tll = 1},
    {from_name = "dstCountry"    ,to_name = "dstCountry"    ,tll = 1},
    {from_name = "srcASN"        ,to_name = "srcASN"        ,tll = 1},
    {from_name = "srcISP"        ,to_name = "srcISP"        ,tll = 1},
    {from_name = "srclatitude"   ,to_name = "srcLatitude"   ,tll = 1},
    {from_name = "srcLongitude"  ,to_name = "srcLongitude"  ,tll = 1},
    {from_name = "SrcCity"       ,to_name = "srcCity"       ,tll = 1},
    {from_name = "srcState"      ,to_name = "srcState"      ,tll = 1},
    {from_name = "srcCountry"    ,to_name = "srcCountry"    ,tll = 1},
    {from_name = "tunnelID"      ,to_name = "tunnelID"      },
    {from_name = "dstMac"        ,to_name = "dstMac"        ,tll = 1},
    {from_name = "srcMac"        ,to_name = "srcMac"        ,tll = 1},
    {from_name = "vlanID2"       ,to_name = "vlanID2"       ,tll = 1},
    {from_name = "vlanID1"       ,to_name = "vlanID1"       ,tll = 1},
    {from_name = "lable4"        ,to_name = "lable4"        ,tll = 1},
    {from_name = "lable3"        ,to_name = "lable3"        ,tll = 1},
    {from_name = "lable2"        ,to_name = "lable2"        ,tll = 1},
    {from_name = "lable1"        ,to_name = "lable1"        ,tll = 1},
    {from_name = "utags"         ,to_name = "utags"         ,tll = 1},
    {from_name = "atags"         ,to_name = "atags"         ,tll = 1},
    {from_name = "ttags"         ,to_name = "ttags"         ,tll = 1},
    {from_name = "etags"         ,to_name = "etags"         ,tll = 1},
    {from_name = "streamId"      ,to_name = "streamId"      },
    {from_name = "payLen"        ,to_name = "payLen"        ,tll = 1},
    {from_name = "pktNum"        ,to_name = "pktNum"        ,tll = 1},
    {from_name = "intFlag"       ,to_name = "intFlag"       },
    {from_name = "strDirec"      ,to_name = "strDirec"      },
    {from_name = "mulRouFlag"    ,to_name = "mulRouFlag"    },
    {from_name = "protName"      ,to_name = "protName"      ,tll = 1},
    {from_name = "protType"      ,to_name = "protType"      ,tll = 1},
    {from_name = "protInfo"      ,to_name = "protInfo"      ,tll = 1},
    {from_name = "dstAddrV6"     ,to_name = "dstAddrV6"     ,tll = 1},
    {from_name = "srcAddrV6"     ,to_name = "srcAddrV6"     ,tll = 1},
    {from_name = "protNum"       ,to_name = "protNum"       ,tll = 1},
    {from_name = "dstPort"       ,to_name = "dstPort"       ,tll = 1},
    {from_name = "srcPort"       ,to_name = "srcPort"       ,tll = 1},
    {from_name = "dstAddr"       ,to_name = "dstAddr"       ,tll = 1},
    {from_name = "srcAddr"       ,to_name = "srcAddr"       ,tll = 1},
    -- {from_name = "ttl"           ,to_name = "ttl"           ,tll = 1},
    {from_name = "ipVer"         ,to_name = "ipVer"         ,tll = 1},
    {from_name = "meanID"        ,to_name = "meanID"        },
    {from_name = "siteID"        ,to_name = "siteID"        },
    {from_name = "unitID"        ,to_name = "unitID"        },
    {from_name = "taskID"        ,to_name = "taskID"        },
    {from_name = "guid"          ,to_name = "guid"          },
    {from_name = "stortime"      ,to_name = "stortime"      },
    {from_name = "mdsecdeg"      ,to_name = "mdsecdeg"      },
    {from_name = "filesecdeg"    ,to_name = "filesecdeg"    },
    {from_name = "secdegpro"     ,to_name = "secdegpro"     },
    {from_name = "comDur"        ,to_name = "comDur"        ,tll = 1},
    {from_name = "endTime"       ,to_name = "endTime"       ,tll = 1},
    {from_name = "begTime"       ,to_name = "begTime"       ,tll = 1},
    {from_name = "lineName2"     ,to_name = "lineName2"     ,tll = 1},
    {from_name = "lineName1"     ,to_name = "lineName1"     ,tll = 1},
  }
}
yalua_register_proto(mapping_common)

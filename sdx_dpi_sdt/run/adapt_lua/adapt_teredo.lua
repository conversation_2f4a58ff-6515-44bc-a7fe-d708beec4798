-- teredo.lua
local mapping = {
  from_proto_name = "teredo",
  to_proto_name = "teredo",
  rule_proto_name = "teredo",
  common_flag = true,
  field = {
    {from_name = "InnerDstPort"  ,to_name = "InnerDstPort"  },
    {from_name = "InnerSrcPort"  ,to_name = "InnerSrcPort"  },
    {from_name = "InnerIPProto"  ,to_name = "InnerIPProto"  },
    {from_name = "InnerIPDst"    ,to_name = "InnerIPDst"    },
    {from_name = "InnerIPSrc"    ,to_name = "InnerIPSrc"    },
    {from_name = "OuterDstPort"  ,to_name = "OuterDstPort"  },
    {from_name = "OuterSrcPort"  ,to_name = "OuterSrcPort"  },
    {from_name = "OuterIPProto"  ,to_name = "OuterIPProto"  },
    {from_name = "OuterIPDst"    ,to_name = "OuterIPDst"    },
    {from_name = "OuterIPSrc"    ,to_name = "OuterIPSrc"    },
    {from_name = "InnerDstIP"    ,to_name = "InnerDstIP"    },
    {from_name = "InnerSrcIP"    ,to_name = "InnerSrcIP"    },

  }
}
yalua_register_proto(mapping)
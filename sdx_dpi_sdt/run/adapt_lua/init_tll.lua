
function file_exists(name)
  local f = io.open(name, "r")
  if f ~= nil then
    io.close(f);
    return true
  else
    return false
  end
end

local g_custom_exist = file_exists("adapt_sdt.lua")

function yalua_register_proto(m)
  if g_custom_exist then
  -- 如果没有一个存在 rule_name
    rule_flag = 0
    for k, f in pairs(m.field) do
        if f.rule_name and f.rule_name ~= "" then
          rule_flag= 1
        end
    end
    if rule_flag == 0 and m.from_proto_name ~= "common" then
      return
    end
  end
  local adapter = function(to, from)
    for k, f in pairs(m.field)
    do
      if f.trans_fun then
        to[f.to_name] = f.trans_fun(from)
      else
        -- print(string.format("adapt field %s %s %s", layer_ fip.streamap.from_proto_name, f.to_name, f.from_name))
        to[f.to_name] = from[f.from_name]
      end
    end
  end

  local init = function(to_schema, from_schema)

    for k, f in pairs(m.field)
    do
      -- 如果存在 from 则使用与 from_name 相同的类型
      local field_type = YA_FT_STRING -- field 类型默认为 YA_FT_STRING
      if f.from_name and f.from_name ~= "" then
        field_type = pschema.get_field_type(from_schema, f.from_name)
      end

      if f.tll then
        -- 注册字段
        pschema.register_field(to_schema, f.to_name, field_type, "adapted_proto, no desc")
      end
    end
  end

  yalua_register(m.from_proto_name, m.to_proto_name, adapter, init)
end

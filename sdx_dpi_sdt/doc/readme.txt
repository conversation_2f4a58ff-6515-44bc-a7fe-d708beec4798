
1.在低配置机器(如 virtualBox 虚拟机) 读取 pcap 进行解析：
  1) 虚拟机要求：
     a. 至少 4 个 cpu 核心;
     b. 至少 2G 内存;
  2) 配置文件 config.ini
     将[resource usage config for poor machine] 部分配置全部打开，
     如果不打开将会使用默认值，比如累计需要 40G 巨页;
  3) dpdk 配置：
	 1. svn co http://192.168.101.251/svn/develop/YADT1707001C/01_解析程序/05_Dpdk
	 2. cd 05_Dpdk
	 3. dpdk.install  			   # 每个环境只需要执行一次
     4. ./setup_dpdk_env.sh -b     # 仅加载驱动，配置巨页，不绑定任何网卡
								   # 每次开机需要执行一次
  4) 调整 run/start.sh 脚本:
     使用"read pcaps dir on a poor machine" 部分命令启动程序，必要的话
     修改命令行中：pcap_dir,dir=<pcap dir> 部分;
  5) 启动 run/start.sh

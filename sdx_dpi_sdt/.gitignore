.vscode
vtysh/**/*.o
vtysh/vtysh
run/yaDpi
run/dpi
dpdk/**/x86_64-native-linuxapp-gcc
dpdk/dpdk-stable-18.11.10/x86_64-native-linuxapp-gcc
dpdk/dpdk-stable-*/build
dpdk/env/.if_tbl
dpdk/src
log.txt
src/build
jenkins
etc/rule.sdt
# 自动生成
include/dpi_trailer.h
lib/libtrailer.a
lib/libdpisdt.a

.history
.cache
build
build_*
build_rcp
build_dpdk
libsdt.log*
rule_error.log


run/yaDpiSdt*
run/vtysh
src/run



#/!bin/bash
list="--IP--LIST--"
#list="*********** *********** *********** *********** ***********"

for ip in $list
do
    ssh root@$ip "echo $ip yaDpiSdt pid \$(pidof yaDpiSdt) to stop"
    ssh root@$ip "systemctl stop  yaDpiSdt"
    ssh root@$ip "kill -9 \$(pidof yaDpiSdt)" > /dev/null 2>&1
    scp run/yaDpiSdt root@$ip:/root/program/yaDpiSdt/ > /dev/null 2>&1
    ssh root@$ip "systemctl start yaDpiSdt"
    ssh root@$ip "echo $ip yaDpiSdt pid \$(pidof yaDpiSdt) to start"
done


/* Virtual terminal interface shell.
 * Copyright (C) 2000 <PERSON><PERSON><PERSON>
 *
 * This file is part of GNU Zebra.
 *
 * GNU Zebra is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2, or (at your option) any
 * later version.
 *
 * GNU Zebra is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GNU Zebra; see the file COPYING.  If not, write to the Free
 * Software Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA
 * 02111-1307, USA.  
 */

#include "vty.h"

#ifndef VTYSH_H
#define VTYSH_H



int vtysh_load_config(char *filename);
int vtysh_boot_config(char *filename);
int vtysh_execute (char *line);
void vtysh_init_vty ();
char * vtysh_readline();

extern struct host host;
extern struct vty *vty;



#endif /* VTYSH_H */

/****************************************************************************************
 * 文 件 名 : dpi_gtp_u.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *设计: chenzq   2021/11/11
 *编码: chenzq   2021/11/11 
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2018 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef _DPI_GTP_U_H
#define _DPI_GTP_U_H

// #include "dpi_detect.h"

// void dissect_gtp_u(const uint8_t *payload, uint32_t payload_len, struct acl_tuple *tuple, struct work_process_data *workflow);


#endif   // dpi_gtp_u.h
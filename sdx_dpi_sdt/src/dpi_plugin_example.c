#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/******************************************************************************
 *
 * 说明：
 *    1.插件编译成.so库命名必须为 plugin_xxx.so 其中xxx为本次的协议名
 *    2.插件编译命令gcc -fPIC -shared dpi_plugin_xxx.c -o plugin_xxx.so
 *
 * 注意：
 *    本插件只是做一个示例，解析telnet，解析功能并不完整，但整个流程是全的；
 *    可参考本示例开发出自己的插件协议
 *
*******************************************************************************/


typedef struct plugin_dpi_field_table_t {
    unsigned int        index;
    unsigned char       type;
    const char          *field_name;
}plugin_field_table;

enum plugin_telnet_index_em{
    EM_TELNET_TELNETDATA,
    EM_TELNET_NEGOTIATIONS,
    EM_TELNET_FILEPATH,
    EM_TELNET_TERMINALTYPE,
    EM_TELNET_USERNAME,
    EM_TELNET_PASSWD,
    EM_TELNET_LOGIN_STATUS,
    EM_TELNET_MAX
};

static plugin_field_table  plugin_telnet_field_array[] = {
    {EM_TELNET_TELNETDATA,                       0,     "TelnetData"},
    {EM_TELNET_NEGOTIATIONS,                     0,     "Negotiations"},
    {EM_TELNET_FILEPATH,                         0,     "FilePath"},
    {EM_TELNET_TERMINALTYPE,                     0,     "Terminaltype"},
    {EM_TELNET_USERNAME,                         0,     "login"},
    {EM_TELNET_PASSWD,                           0,     "password"},
    {EM_TELNET_LOGIN_STATUS,                     0,     "LoginStatus"},
};

struct plugin_telnet_info
{
    const unsigned char  *telnet_data;
    unsigned int         telnet_data_len;

    unsigned char        nego_flag;
    char                 negotiations[64];
};

#define get_uint8_t(X,O)  (*(const unsigned char *)(((const unsigned char *)X) + O))


static void plugin_write_telnet_log(char *log, int max_len, struct plugin_telnet_info *info)
{

    int ret=0;
    int offset=0;
    int i;
    for(i=0;i<EM_TELNET_MAX;i++){
        switch(plugin_telnet_field_array[i].index){
        case EM_TELNET_TELNETDATA:
            ret = snprintf(log + offset, max_len - offset, "%u|", info->telnet_data_len);
            offset += ret;
            break;
        case EM_TELNET_NEGOTIATIONS:
            ret = snprintf(log + offset, max_len - offset, "%s|", info->negotiations);
            offset += ret;
            break;
        case EM_TELNET_FILEPATH:
            ret = snprintf(log + offset, max_len - offset, "|");
            offset += ret;
            break;
            
        case EM_TELNET_USERNAME:
            ret = snprintf(log + offset, max_len - offset, "|");
            offset += ret;
            break;
        case EM_TELNET_PASSWD:
            ret = snprintf(log + offset, max_len - offset, "|");
            offset += ret;
            break;
        case EM_TELNET_LOGIN_STATUS:
            ret = snprintf(log + offset, max_len - offset, "|");
            offset += ret;
            break;
        default:
            ret = snprintf(log + offset, max_len - offset, "|");
            offset += ret;
            break;
        }
    }

    log[offset]='\0';

    return;
}


static int plugin_find_unescaped_iac(const unsigned char  *payload, const unsigned int payload_len)
{
    int           iac_offset = -1;
    unsigned int  offset     = 0;

    /* If we find an IAC (0XFF), make sure it is not followed by another 0XFF.
    Such cases indicate that it is not an IAC at all */
    while (offset < payload_len)
    {
        if (  (get_uint8_t(payload, offset) == 255) && (get_uint8_t(payload, offset + 1) != 255)
           ) 
        {
            iac_offset = offset;
            break;
        }
            
        offset++;
    }
    return iac_offset;
}


static const char *plugin_options[] = {
    "Binary Transmission",                      /* RFC 856 */
    "Echo",                                     /* RFC 857 */
    "Reconnection",                             /* DOD Protocol Handbook */
    "Suppress Go Ahead",                        /* RFC 858 */
    "Approx Message Size Negotiation",          /* Ethernet spec(!) */
    "Status",                                   /* RFC 859 */
    "Timing Mark",                              /* RFC 860 */
    "Remote Controlled Trans and Echo",         /* RFC 726 */
    "Output Line Width",                        /* DOD Protocol Handbook */
    "Output Page Size",                         /* DOD Protocol Handbook */
    "Output Carriage-Return Disposition",       /* RFC 652 */
    "Output Horizontal Tab Stops",              /* RFC 653 */
    "Output Horizontal Tab Disposition",        /* RFC 654 */
    "Output Formfeed Disposition",              /* RFC 655 */
    "Output Vertical Tabstops",                 /* RFC 656 */
    "Output Vertical Tab Disposition",          /* RFC 657 */
    "Output Linefeed Disposition",              /* RFC 658 */
    "Extended ASCII",                           /* RFC 698 */
    "Logout",                                   /* RFC 727 */
    "Byte Macro",                               /* RFC 735 */
    "Data Entry Terminal",                      /* RFC 732, RFC 1043 */
    "SUPDUP",                                   /* RFC 734, RFC 736 */
    "SUPDUP Output",                            /* RFC 749 */
    "Send Location",                            /* RFC 779 */
    "Terminal Type",                            /* RFC 1091 */
    "End of Record",                            /* RFC 885 */
    "TACACS User Identification",               /* RFC 927 */
    "Output Marking",                           /* RFC 933 */
    "Terminal Location Number",                 /* RFC 946 */
    "Telnet 3270 Regime",                       /* RFC 1041 */
    "X.3 PAD",                                  /* RFC 1053 */
    "Negotiate About Window Size",              /* RFC 1073, DW183 */
    "Terminal Speed",                           /* RFC 1079 */
    "Remote Flow Control",                      /* RFC 1372 */
    "Linemode",                                 /* RFC 1184 */
    "X Display Location",                       /* RFC 1096 */
    "Environment Option",                       /* RFC 1408, RFC 1571 */
    "Authentication Option",                    /* RFC 2941 */
    "Encryption Option",                        /* RFC 2946 */
    "New Environment Option",                   /* RFC 1572 */
    "TN3270E",                                  /* RFC 1647 */
    "XAUTH",                                    /* XAUTH  */
    "CHARSET",                                  /* CHARSET  */
    "Remote Serial Port",                       /* Remote Serial Port */
    "COM Port Control",                         /* RFC 2217 */
    "Suppress Local Echo",                      /* draft-rfced-exp-atmar-00 */
    "Start TLS",                                /* draft-ietf-tn3270e-telnet-tls-06 */
    "KERMIT",                                   /* RFC 2840 */
    "SEND-URL",                                 /* draft-croft-telnet-url-trans-00 */
    "FORWARD_X",                                /* draft-altman-telnet-fwdx-03 */
};

static void
plugin_telnet_suboption_name(char *optname, int max_len, const unsigned char *payload, unsigned int *offset, const char *type)
{
    unsigned char opt_byte;
    const char *opt;

    opt_byte = get_uint8_t(payload, *offset);
    if (opt_byte >= (sizeof(plugin_options)/ sizeof(plugin_options[0]))) {
        opt = "<unknown option>";
    }
    else {
        opt = plugin_options[opt_byte];
    }

    (*offset)++;
    snprintf(optname, max_len, "%s %s", type, opt);
}

static unsigned int
plugin_telnet_command(const           unsigned char *payload, const unsigned int payload_len, unsigned int start_offset, struct plugin_telnet_info *info)
{
    unsigned int  offset = start_offset;
    unsigned char optcode;

    if (offset + 3 > payload_len) {
        return ++offset;
    }

    offset += 1;  /* skip IAC */
    optcode = get_uint8_t(payload, offset);

    offset++;

    switch(optcode) {
        case 251:
            plugin_telnet_suboption_name(info->negotiations, 64, payload, &offset, "Will");
            break;

        case 252:
            plugin_telnet_suboption_name(info->negotiations, 64, payload, &offset, "Won't");
            break;

        case 253:
            plugin_telnet_suboption_name(info->negotiations, 64, payload, &offset, "Do");
            break;

        case 254:
            plugin_telnet_suboption_name(info->negotiations, 64, payload, &offset, "Don't");
            break;

        case 250:
            plugin_telnet_suboption_name(info->negotiations, 64, payload, &offset, "Suboption");
            break;

        default:
            info->nego_flag=1;
            snprintf(info->negotiations, sizeof(info->negotiations), "%s", "<unknown option>");
            break;
    }

    return offset;
}


//注释掉与当前项目无关的代码
///**
// *  function：
// *     初始化函数，主要初始化协议对应的字段表，将字段名存入二维数组中 
// *
// *  @*len 传回该插件协议字段表数量
// *
// *  return:
// *    ** 返回存储字段名的二维数组
// */
//char **dissect_init(int *len)
//{
//    int i=0;
//    char **fields_array=NULL;
//    fields_array=malloc(EM_TELNET_MAX*sizeof(char *));
//    if(fields_array==NULL){
//        return NULL;
//    }
//    for(i=0;i< EM_TELNET_MAX;i++){
//        fields_array[i]=NULL;
//        fields_array[i]=strdup(plugin_telnet_field_array[i].field_name);
//        if(fields_array==NULL){
//            return NULL;
//        }
//    }
//    *len=EM_TELNET_MAX;
//    return  fields_array;
//}



//注释掉与当前项目无关的代码
///**
// *  @port_src 报文源端口号
// *  @port_dst 报文目的端口
// *  @payload 报文payload数据部分；
// *  @payload_len  报文payload部分数据长度；
// *
// *  return:
// *    1 协议识别成功；
// *    0 没有识别到该协议；
// */
//int dissect_identify(      unsigned short port_src,
//                            unsigned short port_dst,
//                            unsigned char  payload, 
//                            unsigned int   payload_len)
//{
//
//    if(port_src==23 ||  port_dst==23){
//        return 1;
//    }
//
//    return 0;
//}


//注释掉与当前项目无关的代码
///**
// *  @port_src 报文源端口号
// *  @port_dst 报文目的端口
// *  @payload 报文payload数据部分；
// *  @payload_len  报文payload部分数据长度；
// *  @log_content  解析出来的字段值格式存储空间；
// *  @log_max_len  解析出来的字段值格式存储空间最大值；
// *  @log_actual_len 实际解析出来字段格式化成一条tbl的长度；
// *
// *  return
// *    1 有效数据写tbl标志
// *    0 无效数据不写tbl
// */
//int dissect_protocol(     unsigned short port_src, 
//                            unsigned short port_dst,
//                            unsigned char  *payload, 
//                            unsigned int   payload_len, 
//                            char           *log_buff, 
//                            int            log_buff_len)
//{
//
//    unsigned int  offset     = 0;
//    int           iac_offset = 0;
//    unsigned char w_flag     = 0;
//    int           ret        = 0;
//    
//    struct plugin_telnet_info info;
//    memset(&info, 0, sizeof(info));
//            
//    while (offset < payload_len) {
//        iac_offset = plugin_find_unescaped_iac((const unsigned char *)(payload + offset), payload_len - offset);
//        if (iac_offset >= 0) {
//            /*
//            * We found an IAC byte.
//            * If there's any data before it, add that data to the
//            * tree, a line at a time.
//            
//            data_len = iac_offset - offset;
//            if (data_len > 0) {
//                if (is_tn3270) {
//                    next_tvb = tvb_new_subset_length(tvb, offset, data_len);
//                    call_dissector(tn3270_handle, next_tvb, pinfo, telnet_tree);
//                } else if (is_tn5250) {
//                    next_tvb = tvb_new_subset_length(tvb, offset, data_len);
//                    call_dissector(tn5250_handle, next_tvb, pinfo, telnet_tree);
//                } else
//                    telnet_add_text(telnet_tree, tvb, offset, data_len);
//            }*/
//            /*
//            * Now interpret the command.
//            */
//            ret = plugin_telnet_command((const unsigned char *)(payload + offset), payload_len - offset, (unsigned int)iac_offset, &info);
//            if(ret<=0){
//                break;
//            }
//            offset+=ret;
//            if(info.nego_flag!=1){
//                plugin_write_telnet_log(log_buff, log_buff_len, &info);
//                return 1;
//            }
//        } else{
//            break;
//        }
//    }
//
//    return 0;
//}


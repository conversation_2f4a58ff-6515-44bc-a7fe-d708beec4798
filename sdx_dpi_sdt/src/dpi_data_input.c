#include "dpi_data_input.h"
#include "dpi_detect.h"
#include "dpi_tbl_log.h"
#include "libsdt/libsdt_interface.h"

#include <rte_eal.h>
#include <rte_lcore.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <rte_ip.h>
#include <rte_tcp.h>
#include <rte_udp.h>
#include <rte_net.h>

extern struct rte_mempool   *pktmbuf_pool[2];
extern struct rte_ring      *packet_ring[RTE_MAX_LCORE];
extern struct global_config  g_config;

extern int dpi_input_send_mbuf_to_queue(struct rte_mbuf  *mb);

extern int pkt_arrive(const unsigned char*packet, unsigned int packet_len, int use_rss, struct rte_mbuf *mb);

int dpi_input_enqueue_packet(const uint8_t *data, int pkt_len, uint64_t usec, void* userdata)
{
    if (pkt_len > g_config.mbuf_size)
    {
        /* TODO: 需要记录日志，丢弃过长的 packet  */
        return -1;
    }

    struct rte_mbuf *mbuf=NULL;
    while(!mbuf){
        mbuf=rte_pktmbuf_alloc(pktmbuf_pool[0]);
        if(mbuf==NULL)
        {
            usleep(10);
        }
    }
    mbuf->data_len = pkt_len;
    mbuf->pkt_len  = pkt_len;
    mbuf->data_off = 0;
    // mbuf->userdata = userdata;
    // mbuf->timestamp = usec;

    memcpy(rte_pktmbuf_mtod_offset(mbuf, void *, 0), (void*)data, pkt_len);

    // pkt_arrive(data, pkt_len, 0, mbuf);

    dpi_input_send_mbuf_to_queue(mbuf);

    return 1;
}

int dpi_input_write_coupler_log_string(char *log, int *idx, const uint8_t *data,uint64_t int_data)
{
    //return write_coupler_log(log, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, data, int_data);
    return 0;
}

int dpi_input_write_coupler_log_empty(char *log, int *idx, int empty_cnt)
{
    //return write_coupler_log(log, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, empty_cnt);
    return 0;
}

int dpi_input_get_config_data_input_with_yn470_line_info(void)
{
    return g_config.data_input_with_yn470_line_info;
}

uint16_t dpi_input_get_config_data_mbuf_size(void)
{
    return g_config.mbuf_size;
}


const char *dpi_input_get_config_data_finish_suffix(void)
{
    return g_config.finish_suffix;
}

uint8_t dpi_input_get_config_del_file_flag(void)
{
    return g_config.data_input_scaning_del_file_flag;
}

int dpi_input_is_existed_sdtEngine_active_rules(void)
{
    if(sdtEngine_Active_rules()>0)
        return 1;
    else
        return 0;
}

uint8_t dpi_input_get_config_pause_scan_pcap(void)
{
    return g_config.pause_scan_pcap;
}

extern struct rte_ring *tbl_ring[TBL_RING_MAX_NUM];
extern struct rte_ring *app_match_ring[APP_PROTOCOL_RING_MAX_NUM];
extern struct rte_ring *packet_flow_ring[RTE_MAX_LCORE];
extern struct rte_ring *flow_aging_ring[RTE_MAX_LCORE];
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];  //每个解析线程的数据

//是否还存在未处理完的数据；用于scan离线模式
int dpi_existed_data_not_del(void)
{
    struct flow_info *pos;
    struct flow_info *n;
    struct list_head *head;
    int i,j;
    struct work_process_data *process;
    //因存在上个队列取出数据且下一过程未入队的情况  用于避免一次判断错误
    static uint8_t num = 0;

    //离线存在待处理文件数
    if(dpi_input_get_scan_pcap_num()>0){
       num = 3;
       return 1;
    }
    //flow hash数量
    if (get_flow_total_num() > 0)
        return 1;

    for (i = 0; i < (int)g_config.flow_aging_thread_num; ++i) {
        if (!rte_ring_empty(flow_aging_ring[i])){
            return 1;
        }
    }
    for (i = 0; i < (int)g_config.dissector_thread_num; i++) {
        if (!rte_ring_empty(packet_flow_ring[i])){
            return 1;
        }
    }
    for (i = 0; i < g_config.app_match_thread_num; i++) {
        if (!rte_ring_empty(app_match_ring[i])){
            return 1;
        }
    }
    for(i = 0; i < g_config.log_thread_num; i++){
        if (!rte_ring_empty(tbl_ring[i])){
            return 1;
        }
        //tbl输出文件句柄是否都已关闭
        if (g_config.opened_fd_flag[i]){
            return 1;
        }
    }
    if(num != 0){
        num--;
        return 1;
    }
    return 0;

}

const char *dpi_input_get_config_data_scan_dir(void)
{
    if (strncmp(g_config.work_mode, "scan", 4) == 0)
        return (const char*)&g_config.work_mode[5];
    else
        return "";
}

const char *dpi_input_get_config_data_scanning_ignore(void)
{
    return g_config.data_input_scanning_ignore;
}

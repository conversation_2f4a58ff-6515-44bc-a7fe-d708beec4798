#include <stdio.h>
#include <string.h>
#include <sys/queue.h>

#include <rte_ethdev.h>
#include <rte_pci.h>
#include <rte_bus_pci.h>


#include <yaSdxWatch/yaSdxWatch.h>

#include "dpi_log.h"
#include "dpi_sdx_common.h"
#include "sdx/LineConvert.h"

static void printf_ports()
{
    uint16_t i;

    printf("收包端口: ");
    for (i=0; i<g_config.sdx_config.sdx_rx_port_num; i++) {
        printf("%u ", g_config.sdx_config.sdx_rx_port_list[i]);
    }

    printf("\n转发端口: ");
    for (i=0; i<g_config.sdx_config.sdx_tx_port_num; i++) {
        printf("%u ", g_config.sdx_config.sdx_tx_port_list[i]);
    }

    printf("\n");
}


static int pci_addr_to_port_id(const char *addr, uint16_t *port_id)
{
    uint16_t port, nb_ports;
    char name[RTE_ETH_NAME_MAX_LEN];

    nb_ports = rte_eth_dev_count_avail();
    for (port = 0; port < nb_ports; port++)
    {
#if 0
        struct rte_eth_dev_info     dev_info;
        struct rte_pci_addr         *pci_addr = NULL;
        const struct rte_pci_device *pci_dev = NULL;
        const struct rte_bus        *bus = NULL;

        rte_eth_dev_info_get(portid, &dev_info);
        if (dev_info.device)
            bus = rte_bus_find_by_device(dev_info.device);
        if (bus && !strcmp(bus->name, "pci"))
            pci_dev = RTE_DEV_TO_PCI(dev_info.device);
        if (pci_dev)
        {
            pci_addr = &pci_dev->addr;
            snprintf(name, RTE_ETH_NAME_MAX_LEN, "%04x:%02x:%02x.%x",
                pci_addr->domain, pci_addr->bus, pci_addr->devid, pci_addr->function);
        }
#else
        rte_eth_dev_get_name_by_port(port, name);
#endif

        if (strcmp(name, addr) == 0) {
            *port_id = port;
            return 0;
        }
    }

    return -1;
}



int dpi_sdx_init_config(dictionary *ini, struct sdx_config_variable *sdx_config)
{
    if(!sdx_config){
        return -1;
    }

    const char *str;

    sdx_config->sdx_stat_program_mode=PROGRAM_SDT;

    sdx_config->sdx_reflect_rule = iniparser_getint(ini, ":SDT_REFLECT_RULE_SWITCH", 0);

    sdx_config->sdx_match_algorithm        = iniparser_getint(ini, ":SDX_MATCH_ALGORITHM", 0);

    sdx_config->sdx_stat_report_time = iniparser_getint(ini, ":SDX_STAT_REPORT_TIME", 10);
    str = iniparser_getstring(ini, ":SDX_STAT_REPORT_WEB_ADDR", "127.0.0.1:8888");
    if(str){
        snprintf(sdx_config->sdx_stat_web_addr, 64, "%s", str);
    }

    str = iniparser_getstring(ini, ":SDX_WEB_CONFIG_ADDR", "127.0.0.1");
    if(str){
        snprintf((char *)sdx_config->sdx_web_config_addr, 64, "%s", str);
    }

    sdx_config->sdx_web_config_listen_port=iniparser_getint(ini, ":SDX_WEB_CONFIG_LISTEN_PORT", 6789);

    str = iniparser_getstring(ini, ":SDX_WEB_CONFIG_TOPIC_NAME", "yview");
    if(str){
        snprintf((char *)sdx_config->sdx_web_config_topic_name, 64, "%s", str);
    }

    sdx_config->dump_pcap_with_sdx_mac = iniparser_getint(ini, ":DUMP_PCAP_WITH_SDX_MAC", 0);

    return 0;
}


int dpi_sdx_config_after_init_nic(dictionary *ini, struct sdx_config_variable *sdx_config)
{
    if(!sdx_config){
        return -1;
    }

    uint16_t    port_id, nb_ports;
    uint8_t     port_cnt;
    char        buff[256]={0};
    char        *tmp;
    const char  *str;

    str = iniparser_getstring(ini, ":SDX_RX_PORT_LIST", "all");
    if (str)
    {
        port_cnt = 0;
        if (strcasecmp(str, "all") == 0)
        {
            nb_ports = rte_eth_dev_count_avail();
            for (port_cnt = 0; port_cnt < DPI_MIN(nb_ports, SDX_MAX_PORT_NUM); port_cnt++)
            {
                sdx_config->sdx_rx_port_list[port_cnt] = port_cnt;
            }
        }
        else
        {
            strncpy(buff, str, strlen(str));
            tmp = strtok(buff, ",");

            while (tmp && port_cnt < SDX_MAX_PORT_NUM)
            {
                while(isspace(*tmp)) tmp++;

                if (pci_addr_to_port_id(tmp, &port_id) == 0) {
                    sdx_config->sdx_rx_port_list[port_cnt] = port_id;
                    port_cnt ++;
                } else {
                    DPI_LOG(DPI_LOG_ERROR, "SDX_RX_PORT_LIST: 未找到 pci 地址 [%s]", tmp);
                }

                tmp = strtok(NULL, ",");
            }
        }
        sdx_config->sdx_rx_port_num = port_cnt;
    }

    str = iniparser_getstring(ini, ":SDX_TX_PORT_LIST", NULL);
    if (str)
    {
        port_cnt = 0;
        strncpy(buff, str, strlen(str));
        tmp = strtok(buff, ",");

        while (tmp && port_cnt < SDX_MAX_PORT_NUM)
        {
            while (isspace(*tmp)) tmp++;

            if (pci_addr_to_port_id(tmp, &port_id) == 0) {
                sdx_config->sdx_tx_port_list[port_cnt] = port_id;
                port_cnt ++;
            } else {
                DPI_LOG(DPI_LOG_ERROR, "SDX_TX_PORT_LIST: 未找到 pci 地址 [%s]", tmp);
            }

            tmp = strtok(NULL, ",");
        }
        sdx_config->sdx_tx_port_num = port_cnt;
    }

    /* ??
    iniparser_freedict(ini);
    ini=NULL;
    */

    // 从SDX_TX_PORT_LIST列表里重新加载
    if(sdx_config->sdx_tx_port_num)
    {
        //只有当 PICE地址有效时, 才去修改全局的默认配置
        g_config.nb_txq = sdx_config->sdx_tx_port_num;
    }
    else
    {
        printf("WARN: PCI 地址无效. 将无法执行转发报文\n");
    }
    return 0;
}



int init_stat_restful_server(struct sdx_config_variable sdx_config)
{

    /* resutful统计信息定时上报 */
    struct WatchArgs watch;

    memset(&watch, 0, sizeof(struct WatchArgs));
    /* 区分当前程序是yaDpi、yaDpiSdt、yaDpiContent中哪一个*/
    watch.program = sdx_config.sdx_stat_program_mode;

    /* 多节点 多实例, 要求实例名唯一 */
    snprintf(watch.caseName, sizeof(watch.caseName),
                    "%s", sdx_config.sdx_stat_case_name);

    /* 配置定时上报时间，即每隔多长时间上报一次 */
    watch.times   = sdx_config.sdx_stat_report_time;

    /* 是否在此节点执行规则反射 */
    watch.is_reflec_rule = sdx_config.sdx_reflect_rule > 0;

    /* 上报服务器ip地址和端口*/
    strncpy(watch.webAddr,sdx_config.sdx_stat_web_addr,
                      strlen(sdx_config.sdx_stat_web_addr));

    /* 0x01: 通用信息; 0x02: sdt信息; 0x04: 规则命中信息*/
    watch.enable_bits = (0x01 | 0x02 | 0x04);

    yaProgramWatch_start(watch);



    /*
    struct RestfulArgs restful_args;
    memset(&restful_args, 0, sizeof(struct RestfulArgs));

    restful_args.listenPort=g_config.restful_port;
    snprintf(restful_args.webAddr, 32, "%s", g_config.restful_ip);
    snprintf(restful_args.caseName,32, "%s","yaDpi");

    if(0!=restful_server_start(&restful_args)){
        DPI_SYS_LOG(DPI_LOG_DEBUG, "init restful server failed");
    }
    */

    return 0;
}




int dpi_web_init_config(struct web_config_variable  *web_config)
{
    if(!web_config){
        DPI_LOG(DPI_LOG_ERROR, "web_config parameter is NULL");
        exit(-1);
    }

    char path_cfg[256];
    char *path = get_owner_path();
    if (path == NULL)
        exit(-1);


    snprintf(path_cfg, sizeof(path_cfg), "%s/web_config.ini", path);

    dictionary *ini;
    ini = iniparser_load(path_cfg);
    if (ini == NULL)
    {
        DPI_LOG(DPI_LOG_ERROR, "cannot parse %s file", path_cfg);
        exit(-1);
    }


    const char *str;
    int i;
    char buff[256]={0};

    web_config->print_switch = iniparser_getint(ini, "metadata:print_switch", 0);

    for(i=0;i<PROTOCOL_MAX;i++){
        snprintf(buff,256,"metadata:proto_%s_outputtype",protocol_name_array[i]);
        web_config->protocal_list[i].outputtype=iniparser_getint(ini, buff, 1);

        snprintf(buff,256,"metadata:proto_%s_sample",protocol_name_array[i]);
        web_config->protocal_list[i].sample=iniparser_getint(ini, buff, 20);

        snprintf(buff,256,"metadata:proto_%s_flowtimeout",protocol_name_array[i]);
        web_config->protocal_list[i].flowtimeout=iniparser_getint(ini, buff, 100);

        snprintf(buff,256,"metadata:proto_%s_flowtimeout_total",protocol_name_array[i]);
        web_config->protocal_list[i].flowtimeout_total=iniparser_getint(ini, buff, 300);

    }

    web_config->cycle = iniparser_getint(ini, "metadata:cycle", 6);
    str = iniparser_getstring(ini, "metadata:file_path", "/tmp/tbls");
    if(str)
        snprintf(web_config->file_path, sizeof(web_config->file_path), "%s", str);

    web_config->file_size = iniparser_getint(ini, "metadata:file_size", 102400);
    web_config->file_trunction = iniparser_getint(ini, "metadata:file_trunction", 60);

    str = iniparser_getstring(ini, "metadata:kafka_service_ip", "127.0.0.1");
    if(str)
        snprintf(web_config->kafka_service_ip, sizeof(web_config->kafka_service_ip), "%s", str);

    str = iniparser_getstring(ini, "metadata:nfs_service_ip", "/tmp/tbls");
    if(str)
        snprintf(web_config->nfs_service_ip, sizeof(web_config->nfs_service_ip), "%s", str);

    str = iniparser_getstring(ini, "metadata:mac_addr_dst", "11:22:3:44:55:77");
    if(str)
        snprintf(web_config->mac_addr_dst, sizeof(web_config->mac_addr_dst), "%s", str);

    str = iniparser_getstring(ini, "metadata:mac_addr_src", "11:22:3:44:55:66");
    if(str)
        snprintf(web_config->mac_addr_src, sizeof(web_config->mac_addr_src), "%s", str);


    web_config->json_size = iniparser_getint(ini, "metadata:json_size", 10*1024);
    web_config->json_truncation = iniparser_getint(ini, "metadata:json_truncation", 60);

    web_config->pcap_size = iniparser_getint(ini, "metadata:pcap_size", 500*1024);
    web_config->pcap_truncation = iniparser_getint(ini, "metadata:pcap_truncation", 120);

    str = iniparser_getstring(ini, "metadata:data_from", "ML");
    if(str)
        snprintf(web_config->data_from, sizeof(web_config->data_from), "%s", str);

    str = iniparser_getstring(ini, "metadata:sys_from", "0");
    if(str)
        snprintf(web_config->sys_from, sizeof(web_config->sys_from), "%s", str);

    str = iniparser_getstring(ini, "metadata:sig_type", "0x1");
    if(str)
        snprintf(web_config->sig_type, sizeof(web_config->sig_type), "%s", str);




    web_config->duration = iniparser_getint(ini, "sdt:duration",1);
    web_config->traffic  = iniparser_getint(ini, "sdt:traffic", 34816);
    web_config->dynamic_old_time  = iniparser_getint(ini, "sdt:dynamic_old_time", 30);


    iniparser_freedict(ini);
    ini=NULL;

    if(web_config->print_switch){
        dpi_web_config_printf(web_config);
    }

    return 0;
}



void dpi_web_config_printf(struct web_config_variable  *web_config)
{
    if(!web_config){
        return;
    }
    int i;
    printf("\n\n====================================================================\n");
    for(i=0;i<PROTOCOL_MAX;i++){
        printf("metadata:proto_%s_outputtype=%d\n",       protocol_name_array[i],web_config->protocal_list[i].outputtype);
        printf("metadata:proto_%s_sample=%d\n",           protocol_name_array[i],web_config->protocal_list[i].sample);
        printf("metadata:proto_%s_flowtimeout=%d\n",      protocol_name_array[i],web_config->protocal_list[i].flowtimeout);
        printf("metadata:proto_%s_flowtimeout_total=%d\n",protocol_name_array[i],web_config->protocal_list[i].flowtimeout_total);
        printf("\n");
    }
    printf("\n");

    printf("metadata:cycle=%d\n", web_config->cycle);
    printf("metadata:file_path=%s\n", web_config->file_path);
    printf("metadata:file_size=%d\n", web_config->file_size);
    printf("metadata:file_trunction=%d\n\n",web_config->file_trunction);

    printf("metadata:kafka_service_ip=%s\n", web_config->kafka_service_ip);
    printf("metadata:nfs_service_ip=%s\n\n", web_config->nfs_service_ip);

    printf("metadata:mac_addr_dst=%s\n", web_config->mac_addr_dst);
    printf("metadata:mac_addr_src=%s\n\n", web_config->mac_addr_src);

    printf("metadata:json_size=%d\n",web_config->json_size);
    printf("metadata:json_truncation=%d\n\n",web_config->json_truncation);

    printf("metadata:pcap_size=%d\n",web_config->pcap_size);
    printf("metadata:pcap_truncation=%d\n\n",web_config->pcap_truncation);

    printf("metadata:data_from=%s\n",web_config->data_from);
    printf("metadata:sig_type=%s\n\n",web_config->sig_type);

    printf("sdt:duration=%d\n", web_config->duration);
    printf("sdt:traffic=%d\n", web_config->traffic);
    printf("sdt:dynamic_old_time=%d\n", web_config->dynamic_old_time);
    printf("====================================================================\n");

}


int sdx_convert_linename(const uint8_t line_no[], char *line_name, uint32_t HW[4])
{

    HW[0] = get_uint32_ntohl(line_no, 0);
    HW[1] = get_uint32_ntohl(line_no, 4);
    HW[2] = get_uint32_ntohl(line_no, 8);
    HW[3] = get_uint32_ntohl(line_no, 12);

    if (LineConvertHWToname(HW, line_name)) {
        return 0;
    }

    const char    test_line_name[] = "[JX-20]测试线路";
    const uint8_t   test_line_no[] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0X08,
        0x08, 0X07, 0X06, 0X05, 0X04, 0X03, 0X02, 0X01
    };

    if (memcmp(line_no, test_line_no, sizeof(test_line_no)) == 0) {
        strcpy(line_name, test_line_name);
        return 0;
    }

    strcpy(line_name, "undefined");
    //DPI_LOG(DPI_LOG_ERROR, "线路名转换错误,请确认参数! 0x%x 0x%x 0x%x 0x%x", HW[0], HW[1], HW[2], HW[3]);
    return -1;
}



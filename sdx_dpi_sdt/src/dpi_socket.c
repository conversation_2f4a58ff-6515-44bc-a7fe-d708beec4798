#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <inttypes.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <string.h>
#include <glib.h>
#include <errno.h>
#include <getopt.h>
#include <unistd.h>
#include <sys/time.h>
#include <signal.h>

#include <ifaddrs.h>
#include <net/if.h>
#include <linux/if_ether.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <linux/sockios.h>
#include <arpa/inet.h>

#include <rte_eal.h>
#include <rte_lcore.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <rte_ip.h>
#include <rte_tcp.h>
#include <rte_udp.h>
#include <rte_net.h>

#include "dpi_detect.h"
#include "dpi_typedefs.h"
#include "dpi_tbl_log.h"
#include "dpi_proto_ids.h"
#include "dpi_dissector.h"
#include "dpi_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_socket.h"


extern struct rte_mempool *pktmbuf_pool[2];
extern struct rte_ring *packet_flow_ring[RTE_MAX_LCORE];


extern rte_atomic64_t drop_pkts;
extern rte_atomic64_t drop_bytes;
extern rte_atomic64_t receive_pkts;
extern rte_atomic64_t receive_bytes;

extern rte_atomic64_t tbl_fail_pkts;
extern rte_atomic64_t tbl_fail_bytes;
extern rte_atomic64_t dpi_fail_pkts;

static int rte_ring_mp_enqueue_burst_waiting(int ring_id, const void *mb, unsigned int *free_space)
{
    int ret = 0;
    while(ret < 1){
        ret = rte_ring_mp_enqueue_burst(packet_flow_ring[ring_id], (void * const*)mb, 1, free_space); //enqueue ring
        if(ret < 1){
            usleep(10);
        }
    }
    return ret;
}


/*收包线程处理函数*/
int dpi_input_send_mbuf_to_queue(struct rte_mbuf  *mb)
{
    int       i     = 0;
    int       ret   = 0;
    int       check = 0;
    uint8_t   proto = 0;

    unsigned  free_space;
    uint16_t  packet_len;
    uint16_t  ip_offset;
    uint16_t  type;
    uint16_t  ip_len;
    
    const uint8_t *packet;
    
    const struct dpi_ethhdr  *ethhdr = NULL;
    const struct dpi_iphdr   *iph4    = NULL;
    const struct dpi_ipv6hdr *iph6   = NULL;

    unsigned int ring_id = 0;

    rte_atomic64_inc(&receive_pkts);
    rte_atomic64_add(&receive_bytes, mb->data_len);

    packet = (const uint8_t *)mb->buf_addr + mb->data_off;
    packet_len = mb->data_len;

    if(READ_FROM_PCAP == g_config.data_source){
        if((get_uint32_ntohl(packet, 0) == 0x0f000800)){
            if(is_ip4(packet+4, packet_len - 4)){
                type = ETH_P_IP;
                ip_offset = 4;
                goto outer;
            }
            else if(is_ip6(packet+4, packet_len - 4)){
                type = ETH_P_IPV6;
                ip_offset = 4;
                goto outer;
            }
        }
        else if(is_ip4(packet, packet_len)){
            type = ETH_P_IP;
            ip_offset = 0;
            goto outer;
        }
        else if(is_ip6(packet, packet_len)){
            type = ETH_P_IPV6;
            ip_offset = 0;
            goto outer;
        }
    }

    ethhdr = (const struct dpi_ethhdr *)&packet[0];
    ip_offset = sizeof(struct dpi_ethhdr);
    check = ntohs(ethhdr->h_proto);
    
    if (check >= 0x0600){
        type = check;
    }
    else if (check <= 1500){
        ret = rte_ring_mp_enqueue_burst_waiting(0, (void * const*)&mb, &free_space); //enqueue ring 0
        if (ret < 1) {
            rte_atomic64_inc(&dpi_fail_pkts);
            rte_pktmbuf_free(mb);
        }
        return 0;
    }
    else{
        DPI_LOG(DPI_LOG_DEBUG, "unknown eth packet type");
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        return 0;
    }

    switch(type) {
        case VLAN:
            type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
            ip_offset += 4;
            // double tagging for 802.1Q
            if(type == 0x8100) {
                type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
                ip_offset += 4;
            }
            break;
        case MPLS_UNI:
        case MPLS_MULTI:
        {
            uint8_t mpls_cnt;
            ret = skip_mpls((const char*)(packet+ip_offset), packet_len - ip_offset, &type, NULL, &mpls_cnt);
            if(ret <= 0)
                return PKT_DROP; 
            ip_offset += ret;
            break;
        }
        case PPPoE:
            type = ETH_P_IP;
            ip_offset += 8;
            break;
        default:
            break;
    }

outer:                    
    if (type != ETH_P_IP && type != ETH_P_IPV6) {
        DPI_LOG(DPI_LOG_DEBUG, "unknown eth packet type");
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        return PKT_DROP; 
    }

    iph4 = (const struct dpi_iphdr *) &packet[ip_offset];

    if (iph4->version == 4) {
        ip_len = ((uint16_t)iph4->ihl * 4);
        proto = iph4->protocol;
        iph6 = NULL;
    } else if (iph4->version == 6) {
        iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
        proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        ip_len = sizeof(struct dpi_ipv6hdr);
        iph4 = NULL;
        if (proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
            const uint8_t *options = (const uint8_t*)&packet[ip_offset + ip_len];
            proto = options[0];
            ip_len += 8 * (options[1] + 1);
        }
    } else {
        DPI_LOG(DPI_LOG_DEBUG, "unknown ip type");
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        return PKT_DROP; 
    }

    /*gtp协议按照内层ip地址分发*/
    if (proto == IPPROTO_UDP) {
        const struct dpi_udphdr *udp = (const struct dpi_udphdr *)&packet[ip_offset + ip_len];
        uint16_t sport = ntohs(udp->source);
        uint16_t dport = ntohs(udp->dest);

        if (((sport == GTP_U_V1_PORT) || (dport == GTP_U_V1_PORT))
              && (ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8 + 20 <= packet_len)) {
                
            u_int offset = ip_offset + ip_len + sizeof(struct dpi_udphdr);
            uint8_t flags = packet[offset];
            uint8_t message_type = packet[offset + 1];

            if ((((flags & 0xE0) >> 5) == 1 /* GTPv1 */) 
                    && (message_type == 0xFF /* T-PDU */)) {

                ip_offset = ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8; /* GTPv1 header len */
                if (flags & 0x04) ip_offset += 1; /* next_ext_header is present */
                if (flags & 0x02) ip_offset += 4; /* sequence_number is present (it also includes next_ext_header and pdu_number) */
                if (flags & 0x01) ip_offset += 1; /* pdu_number is present */
                iph4 = (const struct dpi_iphdr *) &packet[ip_offset];
                if (iph4->version == 6) {
                    iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
                    iph4 = NULL;
                } else if (iph4->version != 4) {
                    rte_atomic64_inc(&drop_pkts);
                    rte_atomic64_add(&drop_bytes, mb->data_len);
                    rte_pktmbuf_free(mb);
                    return PKT_DROP; 
                }
            }
        } 
    }

    if (iph4) {
        ring_id = (get_uint32_ntohl(iph4->saddr, 0) + get_uint32_ntohl(iph4->daddr, 0)) % g_config.dissector_thread_num;
        ret = rte_ring_mp_enqueue_burst_waiting(ring_id, (void * const*)&mb, &free_space);
        if (ret < 1) {
            DPI_LOG(DPI_LOG_WARNING, "ipv4 enqueue packet ring error, ring_id=%d, ret=%d", ring_id, ret);
            rte_atomic64_inc(&dpi_fail_pkts);
            rte_pktmbuf_free(mb);
        }
    } else if (iph6) {
        ring_id = (*((const uint64_t *)iph6->ip6_src)
            + *((const uint64_t *)iph6->ip6_src + 1)
            + *((const uint64_t *)iph6->ip6_dst)
            + *((const uint64_t *)iph6->ip6_dst + 1)) % g_config.dissector_thread_num;
      
        ret = rte_ring_mp_enqueue_burst_waiting(ring_id, (void * const*)&mb, &free_space);
        if (ret < 1) {
            DPI_LOG(DPI_LOG_WARNING, "ipv6 enqueue packet ring error, ring_id=%d, ret=%d", ring_id, ret);
            rte_atomic64_inc(&dpi_fail_pkts);
            rte_pktmbuf_free(mb);
        }
    } else {
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
    }

    return 0;
}


static int socket_copy_to_mbuf(char *data, int pkt_len)
{
        printf("DEBUG %s\n", __func__); return 0;
    struct rte_mbuf *mbuf=NULL;
    mbuf=rte_mbuf_raw_alloc(pktmbuf_pool[0]);
    if(mbuf==NULL){
        return 0;
    }
    mbuf->data_len=pkt_len;
    mbuf->pkt_len=pkt_len;
    mbuf->data_off=0;
    memcpy((void *)((char *)mbuf->buf_addr+mbuf->data_off),(void*)data, pkt_len);
    dpi_input_send_mbuf_to_queue(mbuf);

    return 1;
}

static uint32_t get_int32_len(char *buff){
    if(buff==NULL){
        return 0;
    }

    uint32_t val=0;
    val=get_uint32_t(buff, 0);
    if(buff[0]==0x00 && buff[1]==0x00){
        val=(((val<<8)&0xFF00FF00) | ((val>>8)&0x00FF00FF));
        val=(val<<16 | val>>16);
    }

    return val;
}


static int on_new_pkt(char *ptr, uint32_t len)
{
    return socket_copy_to_mbuf(ptr,len);
}

static int pcap_over_tcp_length(int connfd)
{
    char buff[16];
    int offset = 0;
    do
    {
        int remain = 4 - offset;
        int rc = read(connfd, buff + offset, remain);
        if(rc <0)
        {
            printf("read len error rc:%d\n", rc);
            break;
        }
        else
        if(rc == 0)
        {
            printf("read len eof rc:%d\n", rc);
            return -1;
        }
        offset += rc;
    }while(offset != 4);
    return *(int*)buff;
}


static int pcap_over_tcp_read_seek(int connfd, int pkt_len)
{
    char buff[256];
    int rc = 0;
    int offset = 0;

    do
    {
        int remain = pkt_len - offset;
        int buff_size = sizeof(buff);
        int bufflen = remain > buff_size ? buff_size : remain;
        int rc = read(connfd, buff, bufflen);
        if(rc <=0)
        {
            printf("pcap_over_tcp seek read data error rc:%d\n", rc);
            return -1;
        }
        offset += rc;
    }while(offset != pkt_len);
    return 0;
}

static int pcap_over_tcp_read_pkt(int connfd, int pkt_len, int (*on_pkt)(char *ptr, uint32_t len))
{
    //read  pkt_data
    int offset = 0;
    char buff[4096];
    do
    {
        int remain = pkt_len - offset;
        int buff_size = sizeof(buff);
        int bufflen = remain > buff_size ? buff_size : remain;
        int rc = read(connfd, buff+offset, bufflen);
        if(rc <=0)
        {
            printf("read data rc:%d\n", rc);
            return -1;
        }
        offset += rc;
    }while(offset != pkt_len);

    return on_pkt(buff, pkt_len);
}

static int pcap_over_tcp_loop(int connfd, int (*on_pkt)(char *ptr, uint32_t len))
{
    while(1)
    {
        //read pkt_len
        int pkt_len = pcap_over_tcp_length(connfd);
        if(pkt_len < 0)
        {
            //sock 统一在这里检错
            printf("ERROR on pcap_over_tcp_length, ret code %u\n", pkt_len);
            return -1;
        }

        //read  pkt_data (太长?)
        if(pkt_len > g_config.mbuf_size)
        {
            printf("pkt_len too long:%u\n", pkt_len);
            pcap_over_tcp_read_seek(connfd, pkt_len); //sock 错误检测 后移
            continue;
        }

        //有效数据
        pcap_over_tcp_read_pkt(connfd, pkt_len, on_pkt); //sock 错误检测 后移
    }
    return 0;
}

/*收包线程处理函数*/
void *socket_receive_packet_thread(void *args)
{
    struct socket_args *child_args=(struct socket_args *)args;
    int conn_id=child_args->conn_fd;

    DPI_LOG(DPI_LOG_WARNING, "start recieve socket data ...");

    pcap_over_tcp_loop(conn_id, on_new_pkt);

    close(conn_id);
    free(child_args);
    pthread_detach(pthread_self());

    return NULL;
}


#include <gtest/gtest.h>
#include <iostream>
#include <string>
#include "dpi_detect.h"
#include "dpi_sdt_eval_field_callback.h"
#include "dpi_high_app_protos.h"

extern unsigned char ethernet_packet_A[];
extern struct global_config g_config;

class eval_link : public ::testing::Test
{
public:
    void eval_link_portinfo()
    {
        struct value_type*var=cb_link_portinfo(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(6, (size_t)var->val);
    }
    void eval_link_portinfoatt()
    {
        struct value_type*var=cb_link_portinfoatt(&rec);
        ASSERT_FALSE(var);
    }
    void eval_link_uppaylen()
    {
        struct value_type*var=cb_link_uppaylen(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(44, (size_t)var->val);
    }
    void eval_link_downpaylen()
    {
        struct value_type*var=cb_link_downpaylen(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(55, (size_t)var->val);
    }
    void eval_link_tcpflag()
    {
        struct value_type*var=cb_link_tcpflag(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(24, (size_t)var->val);
    }
    void eval_link_uplinkpktnum()
    {
        struct value_type*var=cb_link_uplinkpktnum(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(55, (size_t)var->val);
    }
    void eval_link_uplinksize()
    {
        struct value_type*var=cb_link_uplinksize(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(44, (size_t)var->val);
    }
    void eval_link_uplinkbigpktlen()
    {
        struct value_type*var=cb_link_uplinkbigpktlen(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(3330, (size_t)var->val);
    }
    void eval_link_uplinksmapktlen()
    {
        struct value_type*var=cb_link_uplinksmapktlen(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(11, (size_t)var->val);
    }
    void eval_link_uplinkbigpktint()
    {
        struct value_type*var=cb_link_uplinkbigpktint(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(31, (size_t)var->val);
    }
    void eval_link_uplinksmapktint()
    {
        struct value_type*var=cb_link_uplinksmapktint(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(21, (size_t)var->val);
    }
    void eval_link_downlinkpktnum()
    {
        struct value_type*var=cb_link_downlinkpktnum(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(122, (size_t)var->val);
    }
    void eval_link_downlinksize()
    {
        struct value_type*var=cb_link_downlinksize(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(55, (size_t)var->val);
    }
    void eval_link_downlinkbigpktlen()
    {
        struct value_type*var=cb_link_downlinkbigpktlen(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(3331, (size_t)var->val);
    }
    void eval_link_downlinksmapktle()
    {
        struct value_type*var=cb_link_downlinksmapktint(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(22, (size_t)var->val);
    }
    void eval_link_downlinkbigpktint()
    {
        struct value_type*var=cb_link_downlinkbigpktint(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(32, (size_t)var->val);
    }
    void eval_link_downlinksmapktint()
    {
        struct value_type*var=cb_link_downlinksmapktlen(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(3331, (size_t)var->val);
    }
    void eval_link_firttlbycli()
    {
        struct value_type*var=cb_link_firttlbycli(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(128, (size_t)var->val);
    }
    void eval_link_firttlbysrv()
    {
        struct value_type*var=cb_link_firttlbysrv(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(64, (size_t)var->val);
    }
    void eval_link_appdirec()
    {
        struct value_type*var=cb_link_appdirec(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1, (size_t)var->val);
    }
    void eval_link_tcpflagsfincnt()
    {
        struct value_type*var=cb_link_tcpflagsfincnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(15, (size_t)var->val);
    }
    void eval_link_tcpflagssyncnt()
    {
        struct value_type*var=cb_link_tcpflagssyncnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(11, (size_t)var->val);
    }
    void eval_link_tcpflagsrstcnt()
    {
        struct value_type*var=cb_link_tcpflagsrstcnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(16, (size_t)var->val);
    }
    void eval_link_tcpflagspshcnt()
    {
        struct value_type*var=cb_link_tcpflagspshcnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(14, (size_t)var->val);
    }
    void eval_link_tcpflagsackcnt()
    {
        struct value_type*var=cb_link_tcpflagsackcnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(13, (size_t)var->val);
    }
    void eval_link_tcpflagsurgcnt()
    {
        struct value_type*var=cb_link_tcpflagsurgcnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(17, (size_t)var->val);
    }
    void eval_link_tcpflagsececnt()
    {
        struct value_type*var=cb_link_tcpflagsececnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(18, (size_t)var->val);
    }
    void eval_link_tcpflagscwrcnt()
    {
        struct value_type*var=cb_link_tcpflagscwrcnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(19, (size_t)var->val);
    }
    void eval_link_tcpflagsnscnt()
    {
        struct value_type*var=cb_link_tcpflagsnscnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(20, (size_t)var->val);
    }
    void eval_link_tcpflagssynackcnt()
    {
        struct value_type*var=cb_link_tcpflagssynackcnt(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(12, (size_t)var->val);
    }
    void eval_link_etags()
    {
        struct value_type*var=cb_link_etags(&rec);
        ASSERT_FALSE(var);
    }
    void eval_link_ttags()
    {
        struct value_type*var=cb_link_ttags(&rec);
        ASSERT_FALSE(var);
    }
    void eval_link_uplinkchecksum()
    {
        struct value_type*var=cb_link_uplinkchecksum(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(79, (size_t)var->val);
    }
    void eval_link_downlinkchecksum()
    {
        struct value_type*var=cb_link_downlinkchecksum(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(87, (size_t)var->val);
    }
    void eval_link_uplinkdesbytes()
    {
        struct value_type*var=cb_link_uplinkdesbytes(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(44, (size_t)var->val);
    }
    void eval_link_downlinkdesbytes()
    {
        struct value_type*var=cb_link_downlinkdesbytes(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(55, (size_t)var->val);
    }
    void eval_link_stream()
    {
        struct value_type*var=cb_link_stream(&rec);
        ASSERT_TRUE(var);
        const char *p = "HELOSMTPHELOHTTP";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_link_uplinkstream()
    {
        struct value_type*var=cb_link_uplinkstream(&rec);
        ASSERT_TRUE(var);
        const char *p = "HELOSMTP";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_link_downlinkstream()
    {
        struct value_type*var=cb_link_downlinkstream(&rec);
        ASSERT_TRUE(var);
        const char *p = "HELOHTTP";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_link_trans_payload_hex()
    {
        struct value_type*var=cb_link_trans_payload_hex(&rec);
        ASSERT_FALSE(var);
    }
    void eval_link_uplinktranspayhex()
    {
        struct value_type*var=cb_link_uplinktranspayhex(&rec);
        ASSERT_FALSE(var);
    }

protected:
    void SetUp() override
    {
        g_config.ip_position_switch = 2; //2为优先查询MMDB_CITY库，若未查到值则查询ip2region库
        init_ip_location_db(g_config.ip_position_switch);
        memset(&rec, 0, sizeof(rec));
        memset(&flow,0, sizeof(flow));
        flow.create_time                                = 1748484837000;
        flow.end_time                                   = 1748484847000;
        flow.ip_version                                 = 4;
        flow.proto_type                                 = 6;

        flow.src2dst_bytes                              = 2840;
        flow.dst2src_bytes                              = 1420;
        flow.src2dst_max_packet_len                     = 3330;
        flow.dst2src_max_packet_len                     = 3331;
        flow.src2dst_min_packet_len                     = 11;
        flow.dst2src_min_packet_len                     = 91;
        flow.src2dst_max_packet_interval                = 31;
        flow.dst2src_max_packet_interval                = 32;
        flow.src2dst_min_packet_interval                = 21;
        flow.dst2src_min_packet_interval                = 22;
        flow.up_ttl                                     = 128;
        flow.down_ttl                                   = 64;
        flow.tcpsyncounter                              = 11;
        flow.tcpsynackcounter                           = 12;
        flow.tcpackcounter                              = 13;
        flow.tcppshcounter                              = 14;
        flow.tcpfincounter                              = 15;
        flow.tcprstcounter                              = 16;
        flow.tcpurgcounter                              = 17;
        flow.tcpececounter                              = 18;
        flow.tcpcwrcounter                              = 19;
        flow.tcpnscounter                               = 20;
        flow.check[0]                                   = 87;
        flow.check[1]                                   = 79;
        flow.up_stream.index                            = 8;
        flow.down_stream.index                          = 8;
        flow.both_stream.index                          = 16;

        memcpy(flow.up_stream.byte,   "HELOSMTP",8);
        memcpy(flow.down_stream.byte, "HELOHTTP",8);
        memcpy(flow.both_stream.byte, "HELOSMTPHELOHTTP",16);

        //内层
        *(uint32_t*)flow.tuple.inner.ip_src             = (23<<24 | 166 << 16 | 88 << 8 | 114);
        *(uint32_t*)flow.tuple.inner.ip_dst             = (140<<24 | 82 << 16 | 121 << 8 | 3);
        *(uint32_t*)flow.tuple_reverse.inner.ip_src     = (140<<24 | 82 << 16 | 121 << 8 | 3);
        *(uint32_t*)flow.tuple_reverse.inner.ip_dst     = (23<<24 | 166 << 16 | 88 << 8 | 114);
        flow.tuple.inner.ip_version                     = 4;
        flow.tuple.inner.port_src                       = ntohs(80);
        flow.tuple.inner.port_dst                       = ntohs(5055);
        flow.tuple_reverse.inner.port_src               = ntohs(5055);
        flow.tuple_reverse.inner.port_dst               = ntohs(80);
        flow.tuple_reverse.inner.proto                  = 6;
        flow.tuple.inner.proto                          = 6;

        //外层
        *(uint32_t*)flow.tuple.outer.ip_src             = (142<<24 | 251 << 16 | 40 << 8 | 110);
        *(uint32_t*)flow.tuple.outer.ip_dst             = (108<<24 | 61 << 16 | 13 << 8 | 174);
        *(uint32_t*)flow.tuple_reverse.outer.ip_src     = (108<<24 | 61 << 16 | 13 << 8 | 174);
        *(uint32_t*)flow.tuple_reverse.outer.ip_dst     = (142<<24 | 251 << 16 | 40 << 8 | 110);
        flow.tuple.outer.ip_version                     = 4;
        flow.tuple.outer.port_src                       = ntohs(8344);
        flow.tuple.outer.port_dst                       = ntohs(3306);
        flow.tuple_reverse.outer.port_src               = ntohs(3306);
        flow.tuple_reverse.outer.port_dst               = ntohs(8344);
        flow.tuple_reverse.outer.proto                  = 17;
        flow.tuple.outer.proto                          = 17;

        flow.src2dst_packets                            = 111;
        flow.dst2src_packets                            = 122;
        flow.src2dst_payload_len                        = 44;
        flow.dst2src_payload_len                        = 55;
        flow.data_link_layer.mpls_label[0]              = 120;
        flow.data_link_layer.mpls_label[1]              = 121;
        flow.data_link_layer.mpls_label[2]              = 122;
        flow.data_link_layer.mpls_label[3]              = 123;
        flow.data_link_layer.vlan_id[0]                 = 431;
        flow.data_link_layer.vlan_id[1]                 = 432;
        flow.port_src                                   = 5055;
        flow.port_dst                                   = 80;
        static unsigned char packet_bytes_sdt_mac_header[] = {
            0x14, 0xa0, 0xf2, 0xdc, 0xbc, 0xdf, 0x72, 0x10,
            0x06, 0x00, 0x00, 0x46, 0x08, 0x00, 0xbe, 0xc0,
            0x00, 0x8f, 0x02, 0x08, 0x00, 0x00, 0xfa, 0x08,
            0xc0, 0x05, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x31,
            0x00, 0x67, 0x6a, 0x7b, 0x57, 0x00, 0x07, 0x11,
            0x0f, 0x06, 0x0e
        };
        unsigned char packet_bytes_A[] = { 0x8c, 0x83, 0xe1, 0xff, 0x70, 0xfb, 0xFC, 0xD7, 0x33, 0x7d, 0xea, 0x54, 0x08, 0x00};
        unsigned char packet_bytes_B[] = { 0xFC, 0xD7, 0x33, 0x7d, 0xea, 0x54, 0x8c, 0x83, 0xe1, 0xff, 0x70, 0xfb, 0x08, 0x00};
        memcpy(&flow.session_ethhdr[0], packet_bytes_A, sizeof(packet_bytes_A));
        memcpy(&flow.session_ethhdr[1], packet_bytes_B, sizeof(packet_bytes_B));
        flow.pSDTMacHeader[0] = packet_bytes_sdt_mac_header;
        flow.pSDTMacHeader[1] = packet_bytes_sdt_mac_header;

        pkt.tcph = (struct dpi_tcphdr*)(&ethernet_packet_A[14+20]);

        rec.flow = &flow;
        rec.pkt  = &pkt;
    }
    void TearDown() override
    {
    }
private:
    struct flow_info flow;
    struct pkt_info pkt;
    struct ProtoRecord rec;
};

TEST_F(eval_link, eval_link_portinfo)
{
    eval_link_portinfo();
}
TEST_F(eval_link, eval_link_portinfoatt)
{
    eval_link_portinfoatt();
}
TEST_F(eval_link, eval_link_uppaylen)
{
    eval_link_uppaylen();
}
TEST_F(eval_link, eval_link_downpaylen)
{
    eval_link_downpaylen();
}
TEST_F(eval_link, eval_link_tcpflag)
{
    eval_link_tcpflag();
}
TEST_F(eval_link, eval_link_uplinkpktnum)
{
    eval_link_uplinkpktnum();
}
TEST_F(eval_link, eval_link_uplinksize)
{
    eval_link_uplinksize();
}
TEST_F(eval_link, eval_link_uplinkbigpktlen)
{
    eval_link_uplinkbigpktlen();
}
TEST_F(eval_link, eval_link_uplinksmapktlen)
{
    eval_link_uplinksmapktlen();
}
TEST_F(eval_link, eval_link_uplinkbigpktint)
{
    eval_link_uplinkbigpktint();
}
TEST_F(eval_link, eval_link_uplinksmapktint)
{
    eval_link_uplinksmapktint();
}
TEST_F(eval_link, eval_link_downlinkpktnum)
{
    eval_link_downlinkpktnum();
}
TEST_F(eval_link, eval_link_downlinksize)
{
    eval_link_downlinksize();
}
TEST_F(eval_link, eval_link_downlinkbigpktlen)
{
    eval_link_downlinkbigpktlen();
}
TEST_F(eval_link, eval_link_downlinksmapktle)
{
    eval_link_downlinksmapktle();
}
TEST_F(eval_link, eval_link_downlinkbigpktint)
{
    eval_link_downlinkbigpktint();
}
TEST_F(eval_link, eval_link_downlinksmapktint)
{
    eval_link_downlinksmapktint();
}
TEST_F(eval_link, eval_link_firttlbycli)
{
    eval_link_firttlbycli();
}
TEST_F(eval_link, eval_link_firttlbysrv)
{
    eval_link_firttlbysrv();
}
TEST_F(eval_link, eval_link_appdirec)
{
    eval_link_appdirec();
}
TEST_F(eval_link, eval_link_tcpflagsfincnt)
{
    eval_link_tcpflagsfincnt();
}
TEST_F(eval_link, eval_link_tcpflagssyncnt)
{
    eval_link_tcpflagssyncnt();
}
TEST_F(eval_link, eval_link_tcpflagsrstcnt)
{
    eval_link_tcpflagsrstcnt();
}
TEST_F(eval_link, eval_link_tcpflagspshcnt)
{
    eval_link_tcpflagspshcnt();
}
TEST_F(eval_link, eval_link_tcpflagsackcnt)
{
    eval_link_tcpflagsackcnt();
}
TEST_F(eval_link, eval_link_tcpflagsurgcnt)
{
    eval_link_tcpflagsurgcnt();
}
TEST_F(eval_link, eval_link_tcpflagsececnt)
{
    eval_link_tcpflagsececnt();
}
TEST_F(eval_link, eval_link_tcpflagscwrcnt)
{
    eval_link_tcpflagscwrcnt();
}
TEST_F(eval_link, eval_link_tcpflagsnscnt)
{
    eval_link_tcpflagsnscnt();
}
TEST_F(eval_link, eval_link_tcpflagssynackcnt)
{
    eval_link_tcpflagssynackcnt();
}
TEST_F(eval_link, eval_link_etags)
{
    eval_link_etags();
}
TEST_F(eval_link, eval_link_ttags)
{
    eval_link_ttags();
}
TEST_F(eval_link, eval_link_uplinkchecksum)
{
    eval_link_uplinkchecksum();
}
TEST_F(eval_link, eval_link_downlinkchecksum)
{
    eval_link_downlinkchecksum();
}
TEST_F(eval_link, eval_link_uplinkdesbytes)
{
    eval_link_uplinkdesbytes();
}
TEST_F(eval_link, eval_link_downlinkdesbytes)
{
    eval_link_downlinkdesbytes();
}
TEST_F(eval_link, eval_link_stream)
{
    eval_link_stream();
}
TEST_F(eval_link, eval_link_uplinkstream)
{
    eval_link_uplinkstream();
}
TEST_F(eval_link, eval_link_downlinkstream)
{
    eval_link_downlinkstream();
}
TEST_F(eval_link, eval_link_trans_payload_hex)
{
    eval_link_trans_payload_hex();
}
TEST_F(eval_link, eval_link_uplinktranspayhex)
{
    eval_link_uplinktranspayhex();
}

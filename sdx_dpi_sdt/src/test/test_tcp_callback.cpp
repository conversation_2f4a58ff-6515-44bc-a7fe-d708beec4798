#include <gtest/gtest.h>
#include <iostream>
#include <string>
#include "dpi_detect.h"
#include "dpi_sdt_eval_field_callback.h"

extern unsigned char ethernet_packet_A[];

class eval_tcp : public ::testing::Test
{
public:
    void eval_tcp_header()
    {
        struct value_type*var=cb_tcp_header(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0x15, *(const unsigned char*)var->val);
    }
    void eval_tcp_header_len()
    {
        struct value_type*var=cb_tcp_header_len(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(20, (size_t)var->val);
    }
    void eval_tcp_flag()
    {
        struct value_type*var=cb_tcp_flag(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0x018, (size_t)var->val);
    }
    void eval_tcp_flag_fin()
    {
        struct value_type*var=cb_tcp_flag_fin(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_flag_syn()
    {
        struct value_type*var=cb_tcp_flag_syn(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_flag_rst()
    {
        struct value_type*var=cb_tcp_flag_rst(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_flag_psh()
    {
        struct value_type*var=cb_tcp_flag_psh(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1, (size_t)var->val);
    }
    void eval_tcp_flag_ack()
    {
        struct value_type*var=cb_tcp_flag_ack(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1, (size_t)var->val);
    }
    void eval_tcp_flag_ugr()
    {
        struct value_type*var=cb_tcp_flag_ugr(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_flag_ece()
    {
        struct value_type*var=cb_tcp_flag_ece(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_flag_cwr()
    {
        struct value_type*var=cb_tcp_flag_cwr(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_flag_ns()
    {
        struct value_type*var=cb_tcp_flag_ns(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0, (size_t)var->val);
    }
    void eval_tcp_windowsize()
    {
        struct value_type*var=cb_tcp_windowsize(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(256, (size_t)var->val);
    }
    void eval_tcp_payload()
    {
        struct value_type*var=cb_tcp_payload(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0x47, *(const unsigned char*)var->val);
    }
    void eval_tcp_payload_length()
    {
        struct value_type*var=cb_tcp_payload_length(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1277, (size_t)var->val);
    }

protected:
    void SetUp() override
    {
        memset(&rec, 0, sizeof(rec));
        memset(&pkt, 0, sizeof(pkt));
        pkt.tcph = (struct dpi_tcphdr*)(&ethernet_packet_A[14+20]);
        pkt.payload = (const unsigned char*)(&ethernet_packet_A[14+20+20]);
        pkt.payload_len = 1277;
        rec.pkt = &pkt;
    }
    void TearDown() override
    {
    }
private:
    struct pkt_info pkt;
    struct ProtoRecord rec;
};

TEST_F(eval_tcp, eval_tcp_header)
{
    eval_tcp_header();
}
TEST_F(eval_tcp, eval_tcp_header_len)
{
    eval_tcp_header_len();
}
TEST_F(eval_tcp, eval_tcp_flag)
{
    eval_tcp_flag();
}
TEST_F(eval_tcp, eval_tcp_flag_fin)
{
    eval_tcp_flag_fin();
}
TEST_F(eval_tcp, eval_tcp_flag_syn)
{
    eval_tcp_flag_syn();
}
TEST_F(eval_tcp, eval_tcp_flag_rst)
{
    eval_tcp_flag_rst();
}
TEST_F(eval_tcp, eval_tcp_flag_psh)
{
    eval_tcp_flag_psh();
}
TEST_F(eval_tcp, eval_tcp_flag_ack)
{
    eval_tcp_flag_ack();
}
TEST_F(eval_tcp, eval_tcp_flag_ugr)
{
    eval_tcp_flag_ugr();
}
TEST_F(eval_tcp, eval_tcp_flag_ece)
{
    eval_tcp_flag_ece();
}
TEST_F(eval_tcp, eval_tcp_flag_cwr)
{
    eval_tcp_flag_cwr();
}
TEST_F(eval_tcp, eval_tcp_flag_ns)
{
    eval_tcp_flag_ns();
}
TEST_F(eval_tcp, eval_tcp_windowsize)
{
    eval_tcp_windowsize();
}
TEST_F(eval_tcp, eval_tcp_payload)
{
    eval_tcp_payload();
}
TEST_F(eval_tcp, eval_tcp_payload_length)
{
    eval_tcp_payload_length();
}

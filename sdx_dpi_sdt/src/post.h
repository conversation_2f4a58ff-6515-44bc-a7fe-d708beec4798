#ifndef YV_HTTP_POST_PARSE
#define YV_HTTP_POST_PARSE

#ifdef __cplusplus
extern "C"
{
#endif /* __cplusplus */

typedef void * hParsePost;

typedef struct st_PostInfo
{
    unsigned  long long lContentOffset;
    unsigned  long long lContentLength;
    unsigned  char      szContentType[2048];
    unsigned  char      szContentEncoding[1024];
} ST_PostInfo;

/**
 * 函数名  :YV_HttpPostParse_Init
 * 函数功能:解析 ETL 程序输出的 HTTP-POST 文件,首次的初始化.
 * 函数参数:
            参数1: POST 文件的绝对路径
            参数2: 输出一个Handle
            参数3: 运行时日志输出, 0 关闭, 1 输出到stderr, 2 输出到文件
                   日志文件路径:位于post文件同目录, HttpPostFileName.log
 * 函数返回:
            -1, POST 解析库初始化出错
             0, POST 解析库初始化成功
 */
int YV_HttpPostParse_Init(const char *PostFileName, hParsePost *phParsePost, int LogLevel);

int YV_HttpPostParse_Parse(hParsePost phParsePost, ST_PostInfo* pstPostInfo, char *OutBuffer, unsigned int OutBufferLen);

void YV_HttpPostParse_Fini(hParsePost *phParsePost);


/*  单机版 API
    输入参数:
    pstPostInfo->ContentType       : HTTP ContentType
    pstPostInfo->ContentEncoding   : HTTP ContentEncoding
    inPut                          : POST 数据的地址
    inPutLen                       : POST 数据的长度
    OutBuffer                      : 输出的缓存区
    OutBufferSize                  : 输出缓存区大小
    返回                           : 大于0为解析后的长度, 小于0为解析失败
   int YV_HttpPostParse_ParseRAW(ST_PostInfo* pstPostInfo, char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
*/
int YV_HttpPostParse_ParseRAW(ST_PostInfo* pstPostInfo, const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize);

int YV_HttpContentDataParse_ParseRAW(ST_PostInfo* pstPostInfo, const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize);


/*****************************************************************
*Function    :PrivateProto_Is_WeChat_POST
*Description :微信客户端 POST 报文探测
*Input       :POST 原始数据与长度
*Output      :none
*Return      :yes 返回0， no 返回-1
*Others      :微信的POST 内容格式: length(4Byte) KEY(str)  len(4byte) Value(str) len(4Byte) KEY(str) len(4Byte) Value(str)...
*****************************************************************/
int PrivateProto_Is_WeChat_POST(const char *inPut, int inPutLen);

/*****************************************************************
*Function    :PrivateProtoParse_WeChat_POST
*Description :微信客户端 POST 内容 解析
*Input       :POST 原始数据与长度，输出缓冲区的大小
*Output      :解析后的明文
*Return      :返回字符串的实际长度
*Others      :none
*****************************************************************/
int PrivateProtoParse_WeChat_POST(const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize);


/*****************************************************************
*Function    :PrivateProto_Is_WeChat_TCP
*Description :微信 TCP 报文探测
*Input       :TCP Payload 数据与长度
*Output      :none
*Return      :yes 返回0， no 返回-1
*Others      :TCP 微信格式: 0xAB PayloadLength(4字节) ... 20字节 ... [length(4Byte) KEY(str) ... ]
*****************************************************************/
int PrivateProto_Is_WeChat_TCP(const char *inPut, int inPutLen);

/*****************************************************************
*Function    :PrivateProto_Is_WeChat_TCP
*Description :微信TCP 报文内容 解析
*Input       :TCP Payload 数据与长度，输出缓冲区与大小
*Output      :解析后的明文
*Return      :返回字符串的实际长度
*Others      :none
*****************************************************************/
int PrivateProtoParse_WeChat_TCP(const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize);

int isUTF8(const char *pData, int len);


#ifdef __cplusplus
} /* extern "C" */
#endif /* __cplusplus */
#endif /* YV_HTTP_POST_PARSE */



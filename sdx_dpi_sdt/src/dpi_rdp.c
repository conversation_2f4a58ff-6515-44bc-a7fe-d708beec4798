/****************************************************************************************
 * 文 件 名 : dpi_rdp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *修改: wangch  2020/10/11
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <stdint.h>
#include <rte_mbuf.h>

#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_common.h"
#include "dpi_proto_ids.h"
#include "dpi_conversation.h"

#define ED_TPDU             0x1 /* COTP */
#define EA_TPDU             0x2 /* COTP */
#define UD_TPDU             0x4 /* CLTP */
#define RJ_TPDU             0x5 /* COTP */
#define AK_TPDU             0x6 /* COTP */
#define ER_TPDU             0x7 /* COTP */
#define DR_TPDU             0x8 /* COTP */
#define DC_TPDU             0xC /* COTP */
#define CC_TPDU             0xD /* COTP */
#define CR_TPDU             0xE /* COTP */
#define DT_TPDU             0xF /* COTP */


#define CS_CORE            0xc001
#define CS_SECURITY        0xc002
#define CS_NET             0xc003
#define CS_CLUSTER         0xc004
#define CS_MONITOR         0xc005
#define CS_MCS_MSGCHNNEL   0xc006
#define CS_MONITOR_EX      0xc008
#define CS_MULTITRANSPORT  0xc00a

#define SC_CORE            0x0c01
#define SC_SECURITY        0x0c02
#define SC_NET             0x0c03
#define SC_MCS_MSGCHANNEL  0x0c04
#define SC_MULTITRANSPORT  0x0c08


extern struct rte_mempool *tbl_log_mempool;
extern pthread_mutex_t rdp_mutex;


typedef enum _rdp_enum_index {
        EM_RDP_VERSION,
        EM_RDP_VERSIONMAJOR,
        EM_RDP_VERSIONMINOR,

        // sdt
        EM_RDP_CLIENTVER,
        EM_RDP_SERVERVER,
        EM_RDP_CHANNELID,

        EM_RDP_DESKTOPWIDTH,
        EM_RDP_DESKTOPHEIGHT,
        EM_RDP_COLORDEPTH,
        EM_RDP_SASSEQUENCE,
        EM_RDP_KEYBOARDLAYOUT,
        EM_RDP_CLIENTBUILD,
        EM_RDP_CLIENTNAME,
        EM_RDP_KEYBOARDTYPE,
        EM_RDP_KEYBOARDSUBTYPE,
        EM_RDP_KEYBOARDFUNCTIONKEY,
        EM_RDP_IMEFILENAME,
        EM_RDP_ENCRYPTIONMETHODS,
        EM_RDP_EXTENCRYPTIONMETHODS,
        EM_RDP_CLUSTER_FLAGS,
        EM_RDP_REDIRECTEDSESSIONID,
        EM_RDP_MSGCHANNELFLAGS,
        EM_RDP_MONITORCOUNT,
        EM_RDP_MONITOREXFLAGS,
        EM_RDP_MONITORATTRIBUTESIZE,
        EM_RDP_MULTITRANSPORTFLAGS,
        EM_RDP_ENCRYPTIONLEVEL,
        EM_RDP_SERVERRANDOMLEN,
        EM_RDP_SERVERCERTLEN,
        EM_RDP_SERVERCERTIFICATE,
        EM_RDP_MCSCHANNELID,
        EM_RDP_CHANNELCOUNT,
        EM_RDP_PAD,
        EM_RDP_MSGCHANNELID,

        EM_RDP_SERVER_NAME,
        EM_RDP_SERVER_RANDOM,
        EM_RDP_SERVER_SALT,
        EM_RDP_SERVER_BIGNUM,
        EM_RDP_SERVER_KEY,
        EM_RDP_SERVER_CERT,

        EM_RDP_CLIENT_RANDOM,
        EM_RDP_CLIENT_KEY,

        EM_RDP_USERNAME,
        EM_RDP_PASSWORD,
        EM_RDP_PROGRAM,
        EM_RDP_ASS_PATH,
        EM_RDP_CA,
        EM_RDP_DOMAIN,
        EM_RDP_ENC_TYPE,
        EM_RDP_ENC_KEY,
        EM_RDP_KEY_LEN,
        EM_RDP_RSA,
        EM_RDP_OPERATION_TYPE,
        EM_RDP_LOGIN_STATUS,

        EM_RDP_COOKIE,
        EM_RDP_REQ_PROTOCOL,
        EM_RDP_RES_PROTOCOL,
        EM_RDP_FAILURE_CODE,

        EM_RDP_MAX
}rdp_enum_index;

static dpi_field_table rdp_field_array[] = {
    DPI_FIELD_D(EM_RDP_VERSION,                    YA_FT_STRING,                   "version"),
    DPI_FIELD_D(EM_RDP_VERSIONMAJOR,               YA_FT_UINT16,                    "versionmajor"),
    DPI_FIELD_D(EM_RDP_VERSIONMINOR,               YA_FT_UINT16,                    "versionminor"),

    DPI_FIELD_D(EM_RDP_CLIENTVER,                  YA_FT_STRING,                   "clientversion"),
    DPI_FIELD_D(EM_RDP_SERVERVER,                  YA_FT_STRING,                   "serverversion"),
    DPI_FIELD_D(EM_RDP_CHANNELID,                  YA_FT_STRING,                   "channelid"),

    DPI_FIELD_D(EM_RDP_DESKTOPWIDTH,               YA_FT_UINT16,                    "desktopwidth"),
    DPI_FIELD_D(EM_RDP_DESKTOPHEIGHT,              YA_FT_UINT16,                    "desktopheight"),
    DPI_FIELD_D(EM_RDP_COLORDEPTH,                 YA_FT_STRING,                    "colordepth"),
    DPI_FIELD_D(EM_RDP_SASSEQUENCE,                YA_FT_UINT16,                    "sassequence"),
    DPI_FIELD_D(EM_RDP_KEYBOARDLAYOUT,             YA_FT_UINT32,                    "keyboardlayout"),
    DPI_FIELD_D(EM_RDP_CLIENTBUILD,                YA_FT_UINT32,                    "clientbuild"),
    DPI_FIELD_D(EM_RDP_CLIENTNAME,                 YA_FT_STRING,                    "clientname"),
    DPI_FIELD_D(EM_RDP_KEYBOARDTYPE,               YA_FT_UINT32,                    "keyboardtype"),
    DPI_FIELD_D(EM_RDP_KEYBOARDSUBTYPE,            YA_FT_UINT32,                    "keyboardsubtype"),
    DPI_FIELD_D(EM_RDP_KEYBOARDFUNCTIONKEY,        YA_FT_UINT32,                    "keyboardfunctionkey"),
    DPI_FIELD_D(EM_RDP_IMEFILENAME,                YA_FT_STRING,                    "imefilename"),
    DPI_FIELD_D(EM_RDP_ENCRYPTIONMETHODS,          YA_FT_STRING,                    "encryptionmethods"),
    DPI_FIELD_D(EM_RDP_EXTENCRYPTIONMETHODS,       YA_FT_STRING,                    "extencryptionmethods"),
    DPI_FIELD_D(EM_RDP_CLUSTER_FLAGS,              YA_FT_STRING,                    "cluster_flags"),
    DPI_FIELD_D(EM_RDP_REDIRECTEDSESSIONID,        YA_FT_UINT32,                    "redirectedsessionid"),
    DPI_FIELD_D(EM_RDP_MSGCHANNELFLAGS,            YA_FT_UINT32,                    "msgchannelflags"),
    DPI_FIELD_D(EM_RDP_MONITORCOUNT,               YA_FT_UINT32,                    "monitorcount"),
    DPI_FIELD_D(EM_RDP_MONITOREXFLAGS,             YA_FT_UINT32,                    "monitorexflags"),
    DPI_FIELD_D(EM_RDP_MONITORATTRIBUTESIZE,       YA_FT_UINT32,                    "monitorattributesize"),
    DPI_FIELD_D(EM_RDP_MULTITRANSPORTFLAGS,        YA_FT_UINT32,                    "multitransportflags"),

    DPI_FIELD_D(EM_RDP_ENCRYPTIONLEVEL,            YA_FT_STRING,                    "encryptionlevel"),
    DPI_FIELD_D(EM_RDP_SERVERRANDOMLEN,            YA_FT_UINT32,                    "serverrandomlen"),
    DPI_FIELD_D(EM_RDP_SERVERCERTLEN,              YA_FT_UINT32,                    "servercertlen"),
    DPI_FIELD_D(EM_RDP_SERVERCERTIFICATE,          YA_FT_STRING,                    "servercertificate"),
    DPI_FIELD_D(EM_RDP_MCSCHANNELID,               YA_FT_UINT16,                    "mcschannelid"),
    DPI_FIELD_D(EM_RDP_CHANNELCOUNT,               YA_FT_UINT16,                    "channelcount"),
    DPI_FIELD_D(EM_RDP_PAD,                        YA_FT_STRING,                    "pad"),
    DPI_FIELD_D(EM_RDP_MSGCHANNELID,               YA_FT_STRING,                   "msgchannelid"),

    DPI_FIELD_D(EM_RDP_SERVER_NAME,                YA_FT_STRING,                    "servername"),
    DPI_FIELD_D(EM_RDP_SERVER_RANDOM,              YA_FT_STRING,                    "server_random"),
    DPI_FIELD_D(EM_RDP_SERVER_SALT,                YA_FT_STRING,                    "server_salt"),
    DPI_FIELD_D(EM_RDP_SERVER_BIGNUM,              YA_FT_STRING,                    "server_bignum"),
    DPI_FIELD_D(EM_RDP_SERVER_KEY,                 YA_FT_STRING,                    "server_key"),
    DPI_FIELD_D(EM_RDP_SERVER_CERT,                YA_FT_STRING,                    "server_cert"),
    DPI_FIELD_D(EM_RDP_CLIENT_RANDOM,              YA_FT_STRING,                    "client_random"),
    DPI_FIELD_D(EM_RDP_CLIENT_KEY,                 YA_FT_STRING,                    "client_key"),
    DPI_FIELD_D(EM_RDP_USERNAME,                   YA_FT_STRING,                    "username"),
    DPI_FIELD_D(EM_RDP_PASSWORD,                   YA_FT_STRING,                    "password"),
    DPI_FIELD_D(EM_RDP_PROGRAM,                    YA_FT_STRING,                    "program"),
    DPI_FIELD_D(EM_RDP_ASS_PATH,                   YA_FT_STRING,                    "ass_path"),
    DPI_FIELD_D(EM_RDP_CA,                         YA_FT_STRING,                    "CA"),
    DPI_FIELD_D(EM_RDP_DOMAIN,                     YA_FT_STRING,                    "domain"),
    DPI_FIELD_D(EM_RDP_ENC_TYPE,                   YA_FT_STRING,                    "enc_type"),
    DPI_FIELD_D(EM_RDP_ENC_KEY,                    YA_FT_STRING,                    "enc_key"),
    DPI_FIELD_D(EM_RDP_KEY_LEN,                    YA_FT_UINT16,                    "keylength"),
    DPI_FIELD_D(EM_RDP_RSA,                        YA_FT_STRING,                    "rsa"),
    DPI_FIELD_D(EM_RDP_OPERATION_TYPE,             YA_FT_STRING,                    "operation_type"),
    DPI_FIELD_D(EM_RDP_LOGIN_STATUS,               YA_FT_UINT8,                     "LoginStatus"),
    DPI_FIELD_D(EM_RDP_COOKIE,                     YA_FT_STRING,                    "cookie"),
    DPI_FIELD_D(EM_RDP_REQ_PROTOCOL,               YA_FT_STRING,                    "client_requested_protocol"),
    DPI_FIELD_D(EM_RDP_RES_PROTOCOL,               YA_FT_STRING,                    "server_selected_protocol"),
    DPI_FIELD_D(EM_RDP_FAILURE_CODE,               YA_FT_STRING,                    "failure_code"),

};



enum rdp_type{
    RDP_NULL = 0,
    RDP_CLIENT_INFO,
    RDP_SERVER_INFO,
    RDP_CONNECTION_REQUEST,
    RDP_CONNECTION_CONFIRM,
    RDP_CONNECTION_FAILURE,
};

static struct int_to_string rdp_type_map[] = {
    {RDP_CLIENT_INFO,        "Client MCS Connect Initial"},
    {RDP_SERVER_INFO,        "Server MCS Connect Response"},
    {RDP_CONNECTION_REQUEST, "X.224 Connection Request"},
    {RDP_CONNECTION_CONFIRM, "X.224 Connection Confirm"},
    {RDP_CONNECTION_FAILURE, "X.224 Connection Failure"},
    {RDP_NULL,               NULL},
};

#define TYPE_RDP_NEG_REQ          0x01
#define TYPE_RDP_NEG_RSP          0x02
#define TYPE_RDP_NEG_FAILURE      0x03
#define TYPE_RDP_CORRELATION_INFO 0x06

static struct int_to_string neg_type_vals[] = {
  { TYPE_RDP_NEG_REQ,          "RDP Negotiation Request" },
  { TYPE_RDP_NEG_RSP,          "RDP Negotiation Response" },
  { TYPE_RDP_NEG_FAILURE,      "RDP Negotiation Failure" },
  { TYPE_RDP_CORRELATION_INFO, "RDP Correlation Info" },
  { 0, NULL }
};

static struct int_to_string rdp_protocols[] = {
  {   0, "Standard_RDP" },
  {   1, "TLS"          },
  {   2, "CredSSP"      }, //Credential Security Support Provider protocol
  {   4, "RDSTLS"       },
  {   8, "CredSSP_Ex"   },
  {   0, NULL},
};

static struct int_to_string failure_code_vals[] = {
  { 0x00000001, "TLS required by server" },
  { 0x00000002, "TLS not allowed by server" },
  { 0x00000003, "TLS certificate not on server" },
  { 0x00000004, "Inconsistent flags" },
  { 0x00000005, "Server requires Enhanced RDP Security with CredSSP" },
  { 0x00000006, "Server requires Enhanced RDP Security with TLS and certificate-based client authentication" },
  { 0, NULL }
};

static struct int_to_string color_depth_type[] = {
    {0xca00, "4 bpp"},
    {0xca01, "8 bpp"},
    {0, 0}
};

static struct int_to_string enc_method_type[] = {
    {0x01, "40 bit"},
    {0x02, "128 bit"},
    {0x08, "56 bit"},
    {0x10, "FIPS"},
    {0, 0}
};

static struct int_to_string cluster_flags_type[] = {
    {0x01, "redirection_supported"},
    {0x3c, "server_session_redirection_version_mask"},
    {0x02, "redirected_session_id_field_valid"},
    {0x40, "redirected_smartcard"},
    {0, 0}
};

static struct int_to_string multitransport_channel_flags_type[] = {
    {0x01,    "UDP FECR"},
    {0x04,    "UDP FECL"},
    {0x100,   "UDP Prefreed"},
    {0x200,   "TCP to UDP"},
    {0, 0}
};

const char* enc_level_type[] = {"NONE", "LOW", "Client-Compatible", "HIGH", "FIPS"};

struct rdp_info{
    int        write;
    int        should_write;
    int        written;
    int        index;
    uint16_t   version;
    uint16_t   subversion;
    char        serverver[8];
    char        clientver[8];
    uint16_t   width;
    uint16_t   height;
    const char* color_depth;
    uint16_t   seq;
    uint32_t   keylayout;
    uint32_t   clientbuild;
    uint32_t   keytype;
    uint32_t   keysubtype;
    uint32_t   keyfunction;
    uint32_t   cluster_redirected_session_id;
    const char* cluster_flags;
    const char* enc_method;
    const char* ext_enc_method;
    const char* enc_level;
    uint32_t   multitransport_channel_flags;
    uint32_t   channel_count;
    uint16_t   channel_id_count;
    uint16_t   channel_id;
    uint16_t   mcs_channel_id;
    uint16_t   msg_channel_id;
    uint32_t   server_random_len;
    uint32_t   server_cer_len;
    uint32_t   message_channel_flags;
    uint32_t   monitor_flags;
    uint32_t   ext_monitor_flags;
    uint32_t   monitor_attribute_size;
    uint32_t   monitor_count;
    const uint8_t *monitors;
    const uint8_t *monitors_attributes;
    const uint8_t *server_cer;
    const uint8_t *server_random;
    const uint8_t *channels;
    const uint8_t *channel_ids;
    const char    *failure_code;
    uint32_t   requested_protocol;
    uint32_t   selected_protocol;
    int        connect_confirm_dissected;
    int        operation_type;
    char       cookie[32];
    char       hostname[33];
    struct     conversation_value *conv;


    char        channelid[8];
};



static
int write_rdp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    struct rdp_info *info=(struct rdp_info *)field_info;
    if(!info){
        return 0;
    }

    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "rdp");

    for(i=0; i<EM_RDP_MAX;i++){
        switch(i){
        case EM_RDP_VERSION:
        {
            char _str[8];
            int len = snprintf(_str, 8, "%d.%d", info->version, info->subversion);
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, _str, len);
        }
            break;
        case EM_RDP_VERSIONMAJOR:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->version);
            break;
        case EM_RDP_VERSIONMINOR:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->subversion);
            break;
        case EM_RDP_CLIENTVER:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->clientver, strlen(info->clientver));
            break;
        case EM_RDP_SERVERVER:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->serverver, strlen(info->serverver));
            break;
        case EM_RDP_DESKTOPWIDTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->width);
            break;
        case EM_RDP_DESKTOPHEIGHT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->height);
            break;
        case EM_RDP_COLORDEPTH:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->color_depth);
            break;
        case EM_RDP_SASSEQUENCE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->seq);
            break;
        case EM_RDP_KEYBOARDLAYOUT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->keylayout);
            break;
        case EM_RDP_CLIENTBUILD:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->clientbuild);
            break;
        case EM_RDP_CLIENTNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->hostname, strlen(info->hostname));
            break;
        case EM_RDP_KEYBOARDTYPE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->keytype);
            break;
        case EM_RDP_KEYBOARDSUBTYPE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->keysubtype);
            break;
        case EM_RDP_KEYBOARDFUNCTIONKEY:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->keyfunction);
            break;
        case EM_RDP_ENCRYPTIONMETHODS:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->enc_method);
            break;
        case EM_RDP_EXTENCRYPTIONMETHODS:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ext_enc_method);
            break;
        case EM_RDP_CLUSTER_FLAGS:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_flags);
            break;
        case EM_RDP_MSGCHANNELFLAGS:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->message_channel_flags);
            break;
        case EM_RDP_MONITORCOUNT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->monitor_count);
            break;
        case EM_RDP_MONITOREXFLAGS:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ext_monitor_flags);
            break;
        case EM_RDP_MONITORATTRIBUTESIZE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->monitor_attribute_size);
            break;
        case EM_RDP_REDIRECTEDSESSIONID:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_redirected_session_id);
            break;
        case EM_RDP_SERVERRANDOMLEN:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->server_random_len);
            break;
        case EM_RDP_SERVERCERTLEN:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->server_cer_len);
            break;
        case EM_RDP_MCSCHANNELID:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->mcs_channel_id);
            break;
        case EM_RDP_MSGCHANNELID:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)&info->msg_channel_id,2);
            break;
        case EM_RDP_CHANNELCOUNT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->channel_id_count);
            break;
        case EM_RDP_ENCRYPTIONLEVEL:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->enc_level);
            break;
        case EM_RDP_OPERATION_TYPE:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, val_to_string(info->operation_type, rdp_type_map));
            break;
        case EM_RDP_MULTITRANSPORTFLAGS:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, val_to_string(info->multitransport_channel_flags, multitransport_channel_flags_type));
            break;
        case EM_RDP_LOGIN_STATUS:
            if(info->write) //报文级
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            //else if(flow->src2dst_packets + flow->dst2src_packets >= g_config.rdp_login_valid_num && (flow->timestamp - flow->create_time) / 1e6 > g_config.login_success_time) //会话级
            else if(flow->src2dst_packets + flow->dst2src_packets >= g_config.rdp_login_valid_num)
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "YES", 3);
            else
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "NO",  2);     //会话级
            break;
        case EM_RDP_COOKIE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cookie, strlen(info->cookie));
            break;
        case EM_RDP_REQ_PROTOCOL:
            if(info->requested_protocol & 0x01){
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "TLS,", 4);
                idx -= 1;
            }
            else{
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "Standard_RDP,", 13);
                idx -= 1;
            }

            if(info->requested_protocol & 0x02){
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "CredSSP,", 8);
                idx -= 1;
            }
            if(info->requested_protocol & 0x04){
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "RDSTLS,", 7);
                idx -= 1;
            }
            if(info->requested_protocol & 0x08){
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "CredSSP_Ex,", 11);
                idx -= 1;
            }
            idx -= 1; // ',' GOD BLESS ME
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_RDP_RES_PROTOCOL:
            if(info->connect_confirm_dissected)
                write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, val_to_string(info->selected_protocol, rdp_protocols));
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_RDP_FAILURE_CODE:
            write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->failure_code);
            break;
        default:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }


    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_RDP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static int get_length(const uint8_t *payload, uint32_t payload_len, uint32_t *offset, uint32_t *length)
{
    if(*offset + 4 > payload_len){
        return 1;
    }
    else if(payload[*offset] < 0x80){
        *length = payload[*offset];
        *offset += 1;
        return 0;
    }
    else if(payload[*offset] == 0x81){
        *length = payload[*offset + 1];
        *offset += 2;
        return 0;
    }
    else if(payload[*offset] == 0x82){
        *length = get_uint16_ntohs(payload, *offset + 1);
        *offset += 3;
        return 0;
    }
    else{
        return 1;
    }
}


static void dissect_server_core_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 4)
        return;

    info->version      = get_uint16_t(payload, 0);
    info->subversion   = get_uint16_t(payload, 2);
    snprintf(info->serverver, sizeof(info->serverver), "%u.%u", info->version, info->subversion);
}

static void dissect_server_network_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 4)
        return;

    info->mcs_channel_id   = get_uint16_t(payload, 0);
    snprintf(info->channelid, sizeof(info->channelid), "0x%04x", info->mcs_channel_id);
    info->channel_id_count = get_uint16_t(payload, 2);
    if(info->channel_id_count && info->channel_id_count * 2 + 4 <= payload_len)
        info->channel_ids = payload + 4;
}

static void dissect_server_multitransport_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if(payload_len >= 4)
        info->multitransport_channel_flags = get_uint32_t(payload, 0);
}

static void dissect_server_message_channel_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if(payload_len >= 2)
        info->msg_channel_id = get_uint16_t(payload, 0);
}

static void dissect_server_security_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 8)
        return;

    uint32_t offset;
    uint32_t enc_method = get_uint32_t(payload, 0);
    uint32_t enc_level  = get_uint32_t(payload, 4);

    if((enc_method || enc_level) && payload_len > 16){
        info->enc_method = val_to_string(enc_method, enc_method_type);
        info->enc_level  = enc_level_type[enc_level % 5];
        info->server_random_len = get_uint32_t(payload, 8);
        info->server_cer_len    = get_uint32_t(payload, 12);
        offset = 16;
        if(offset + info->server_random_len <= payload_len){
            info->server_random = payload + offset;
            offset += info->server_random_len;
        }
        if(offset + info->server_cer_len <= payload_len){
            info->server_cer = payload + offset;
        }
    }
}

static int dissect_client_core_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 132)
        return 1;

    info->version     = get_uint16_t(payload, 0);
    info->subversion  = get_uint16_t(payload, 2);
    snprintf(info->clientver, sizeof(info->clientver), "%u.%u", info->version, info->subversion);
    info->width       = get_uint16_t(payload, 4);
    info->height      = get_uint16_t(payload, 6);
    info->color_depth = val_to_string(get_uint16_t(payload, 8), color_depth_type);
    info->seq         = get_uint16_t(payload, 10);
    info->keylayout   = get_uint32_t(payload, 12);
    info->clientbuild = get_uint32_t(payload, 16);
    uint16_t offset = 20;

    int i,tmp;
    for(tmp = 0, i = 0; tmp < 32; tmp++){
        if(payload[offset + tmp]){
            info->hostname[i++] = payload[offset + tmp];
        }
    }
    info->hostname[i] = 0;
    offset += 32;

    info->keytype     = get_uint32_t(payload, offset);
    info->keysubtype  = get_uint32_t(payload, offset+4);
    info->keyfunction = get_uint32_t(payload, offset+8);
    offset += 12 + 64;

    return 0;
}

static int dissect_client_security_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if(payload_len == 8){
        info->enc_method     = val_to_string(get_uint32_t(payload, 0), enc_method_type);
        info->ext_enc_method = val_to_string(get_uint32_t(payload, 4), enc_method_type);
    }
    return 0;
}

static void dissect_client_mcs_msgchannel_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if(payload_len == 4)
        info->message_channel_flags= get_uint32_t(payload, 0);
}

static void dissect_client_multitransport_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if(payload_len >= 4)
        info->multitransport_channel_flags = get_uint32_t(payload, 0);
}

static void dissect_client_cluster_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if(payload_len >= 8){
        info->cluster_flags = val_to_string(get_uint32_t(payload, 0), cluster_flags_type);
        info->cluster_redirected_session_id = get_uint32_t(payload, 0);
    }
}

static void dissect_client_monitor_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 8)
        return;
    info->monitor_flags = get_uint32_t(payload, 0);
    info->monitor_count = get_uint32_t(payload, 4);
    if(info->monitor_count && info->monitor_count * 20 + 6 == payload_len)
        info->monitors = payload + 8;

}

static void dissect_client_ext_monitor_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 12)
        return;

    info->ext_monitor_flags     = get_uint32_t(payload, 0);
    info->monitor_attribute_size = get_uint32_t(payload, 4);
    info->monitor_count         = get_uint32_t(payload, 8);
    if(info->monitor_count && info->monitor_attribute_size * 20 + 12 == payload_len)
        info->monitors_attributes = payload + 12;
}

static void dissect_client_network_data(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{
    if (payload_len < 4)
        return;

    info->channel_count = get_uint32_t(payload, 0);
    if(info->channel_count && info->channel_count * 12 + 4 == payload_len)
        info->channels = payload + 4;
}

static void dissect_rdp_connect(const uint8_t *payload, uint16_t payload_len, struct rdp_info *info)
{

    uint32_t offset = 0;

    uint16_t data_type;
    uint16_t data_len;

    info->write = 1;

    while(offset + 4 < payload_len){
        data_type = get_uint16_t(payload, offset);
        data_len  = get_uint16_t(payload, offset+2); //including itself

        if(offset + data_len > payload_len)
            break;

        switch(data_type){
        case CS_CORE:
            dissect_client_core_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_SECURITY:
            dissect_client_security_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_NET:
            dissect_client_network_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_CLUSTER:
            dissect_client_cluster_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_MCS_MSGCHNNEL:
            dissect_client_mcs_msgchannel_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_MONITOR:
            dissect_client_monitor_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_MONITOR_EX:
            dissect_client_ext_monitor_data(payload + offset + 4, data_len - 4, info);
            break;
        case CS_MULTITRANSPORT:
            dissect_client_multitransport_data(payload + offset + 4, data_len - 4, info);
            break;

        case SC_CORE:
            dissect_server_core_data(payload + offset + 4, data_len - 4, info);
            break;
        case SC_SECURITY:
            dissect_server_security_data(payload + offset + 4, data_len - 4, info);
            break;
        case SC_NET:
            dissect_server_network_data(payload + offset + 4, data_len - 4, info);
            break;
        case SC_MCS_MSGCHANNEL:
            dissect_server_message_channel_data(payload + offset + 4, data_len - 4, info);
            break;
        case SC_MULTITRANSPORT:
            dissect_server_multitransport_data(payload + offset + 4, data_len - 4, info);
            break;
        default:
            break;
        }

        offset += data_len;
    }
}

static int jump_client_mcs_tag_to_userdata(const uint8_t *payload, uint32_t payload_len, struct rdp_info *info)
{
    uint32_t length, offset = 0;
    if(get_length(payload, payload_len, &offset, &length))
        return 1;
    if(offset + length > payload_len)
        return 1;
    // 04<calling domain selector> --octet string
    if(payload[offset++] != 0x04 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    // 04<called domain selector>
    if(payload[offset++] != 0x04 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    // 01<upward flag>             --boolean
    if(payload[offset++] != 0x01 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    // 30<target parameters>       --sequence
    if(payload[offset++] != 0x30 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    // 30<minimum parameters>
    if(payload[offset++] != 0x30 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    // 30<maxumum parameters>
    if(payload[offset++] != 0x30 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    // 04<user data>
    if(payload[offset++] != 0x04 || get_length(payload, payload_len, &offset, &length))
        return 1;

    if(offset + length > payload_len)
        return 1;

    const uint8_t *tmp = memstr(payload + offset, "Duca", payload_len - offset);
    if(tmp){
        info->operation_type = RDP_CLIENT_INFO;
        offset = get_uint16_ntohs(tmp, 4) & 0x7ff;
        if(tmp + 6 + offset <= payload + payload_len)
            dissect_rdp_connect(tmp + 6, offset, info);
    }

    return 0;
}

static int jump_server_mcs_tag_to_userdata(const uint8_t *payload, uint32_t payload_len, struct rdp_info *info)
{
    uint32_t length, offset = 0;
    if(get_length(payload, payload_len, &offset, &length))
        return 1;
    if(offset + length > payload_len)
        return 1;
    //0a<result>               --enum
    if(payload[offset++] != 0x0a || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    //02<called connect id>    --integer
    if(payload[offset++] != 0x02 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    //30<domain parameters>
    if(payload[offset++] != 0x30 || get_length(payload, payload_len, &offset, &length))
        return 1;
    offset += length;
    //04<user data>
    if(payload[offset++] != 0x04 || get_length(payload, payload_len, &offset, &length))
        return 1;

    if(offset + length > payload_len)
        return 1;

    const uint8_t *tmp = memstr(payload + offset, "McDn", payload_len - offset);
    if(tmp){
        info->operation_type = RDP_SERVER_INFO;
        offset = get_uint16_ntohs(tmp, 4) & 0x7ff;
        if(tmp + 6 + offset <= payload + payload_len)
            dissect_rdp_connect(tmp + 6, offset, info);
    }
    return 1;
}

#define MCS_CONNECT_INITIAL  101
#define MCS_CONNECT_RESPONSE 102
/* MCS <multipoint communication service> */
static void dissect_mcs(const uint8_t *payload, uint32_t payload_len, struct rdp_info *info)
{
    if(payload_len < 10)
        return;

    uint32_t offset = 0;
    uint8_t  type   = payload[offset++] >> 2;
    if(type == 31) //connect initial<client core info> & connect response<server core info>
        type = payload[offset++];

    switch(type){
    case MCS_CONNECT_INITIAL:
        jump_client_mcs_tag_to_userdata(payload + offset, payload_len - offset, info);
        break;
    case MCS_CONNECT_RESPONSE:
        jump_server_mcs_tag_to_userdata(payload + offset, payload_len - offset, info);
        break;
    default:
        break;
    }
}

static void dissect_rdp_connect_x224(const uint8_t *payload, uint32_t payload_len, uint8_t tpdu_type, struct rdp_info *info)
{
    if(payload_len < 8)
        return;

    if(tpdu_type == CC_TPDU){
        if(payload[0] == TYPE_RDP_NEG_RSP){
            info->operation_type = RDP_CONNECTION_CONFIRM;
            info->selected_protocol = get_uint32_t(payload, 4);
        }
        else if(payload[0] == TYPE_RDP_NEG_FAILURE){
            info->operation_type = RDP_CONNECTION_FAILURE;
            info->failure_code   = val_to_string(get_uint32_t(payload, 4), failure_code_vals);
        }
    }
    else if(tpdu_type == CR_TPDU){
        uint32_t offset = 0;
        info->operation_type = RDP_CONNECTION_REQUEST;
        if(memcmp(payload, "Cookie: ", 8) == 0){
            const uint8_t * end = memstr(payload, "\r\n", payload_len);
            if(!end)
                return;
            int len = DPI_MIN(31, end - payload - 8);
            memcpy(info->cookie, payload + 8, len);
            info->cookie [len] = 0;
            offset += (end + 2 - payload);
        }
        if(offset + 8 <= payload_len && payload[offset] == TYPE_RDP_NEG_REQ){
            info->requested_protocol = get_uint32_t(payload, offset + 4);
        }
    }
}

static int dissect_rdp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if(payload_len < 7)
        return PKT_DROP;

    if(!flow->app_session){
        flow->app_session = malloc(sizeof(struct rdp_info));
        if(flow->app_session)
            memset(flow->app_session, 0, sizeof(struct rdp_info));
        else
            return PKT_DROP;
    }
    struct rdp_info *info = flow->app_session;

    if(info->written)
        return 0;

    if(info->should_write
        && flow->src2dst_packets + flow->dst2src_packets >= g_config.rdp_login_valid_num)
        //&& (flow->timestamp - flow->create_time) / 1e6 > g_config.login_success_time) //会话级TBL
    {
        if(info->conv && info->conv->conv_session && g_config.rdp_mutex_switch){
            pthread_mutex_lock(&rdp_mutex);
            struct rdp_conv *rdp_c = info->conv->conv_session;
            rdp_c->state[info->index % RDP_MAX_FLOW_PER_CLIENT] = 1;
            pthread_mutex_unlock(&rdp_mutex);
        }

        write_rdp_log(flow, direction, info, NULL);
        info->should_write = 0;
        info->written = 1;
    }
     /*
     * ---------------------------- 1, tpkt header --------------------------
     * tpkt <OSI transport protocol over TCP/IP> emulate OSI tsap <transport service access point>
     * 格式：version:1 resv:1 length:2 共4字节, 切割TCP
     * version为3, resv是0, length指header<4> + tpdu<transport protocol data units>
     */
    if(payload[0] != 3 || payload[1] != 0 || get_uint16_ntohs(payload, 2) > payload_len)
        return PKT_DROP;

    /*
     *------------------------------ 2, tpdu type ---------------------------
     *CR<connect request>     ~~ syn
     *CC<connect confirm>     ~~ syn + ack
     *DR<disconnect reques>   ~~ fin
     *DC<disconnect confirm>  ~~ fin + ack
     */
    uint8_t tpdu_len  = payload[4];  //excluding itself
    uint8_t tpdu_type = payload[5] >> 4;

    uint32_t offset = 5; // tpkt(4) + tpdu_len(1)
    if(offset + tpdu_len > payload_len)
        return PKT_DROP;

    switch(tpdu_type){

        case CR_TPDU:
            info->write = 1;
            direction = FLOW_DIR_SRC2DST;
            if(tpdu_len >= 7) //Minimum len of X.224 class 0 header
                dissect_rdp_connect_x224(payload + offset + 6, tpdu_len - 6, tpdu_type, info);
            break;
        case CC_TPDU:
            info->write = 1;
            info->connect_confirm_dissected = 1;
            if(tpdu_len < 7) //Minimum len of X.224 class 0 header
                break;

            dissect_rdp_connect_x224(payload + offset + 6, tpdu_len - 6, tpdu_type, info);
            if((info->selected_protocol == 2 || info->selected_protocol == 8) && g_config.rdp_mutex_switch && !info->conv){
                struct conversation_tuple tuple;
                memset(&tuple, 0, sizeof(tuple));
                tuple.proto = IPPROTO_TCP;

                if(direction == FLOW_DIR_DST2SRC){
                   tuple.port_dst = 0;
                   tuple.port_src = flow->tuple.inner.port_dst;
                   memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
                   memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
                }
                else{
                   tuple.port_dst = 0;
                   tuple.port_src = flow->tuple.inner.port_src;
                   memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
                   memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
                }

#if 0
                printf("direction: %d. flow direction: %d, src port:%u, dst port: %u, src ip: %u.%u.%u.%u, dst ip: %u.%u.%u.%u\n",
                       direction, flow->direction, ntohs(tuple.port_src), ntohs(tuple.port_dst),
                       tuple.ip_src.ip6[0], tuple.ip_src.ip6[1], tuple.ip_src.ip6[2], tuple.ip_src.ip6[3],
                       tuple.ip_dst.ip6[0], tuple.ip_dst.ip6[1], tuple.ip_dst.ip6[2], tuple.ip_dst.ip6[3]);
#endif
                struct rdp_conv *rdp_c = malloc(sizeof(struct rdp_conv));
                if(!rdp_c) break;
                memset(rdp_c, 0, sizeof(struct rdp_conv));
                pthread_mutex_lock(&rdp_mutex);
                info->conv = find_or_create_conversation(&tuple, NO_PORT_B, PROTOCOL_RDP, rdp_c);
                if(info->conv){
                    if(info->conv->conv_session != rdp_c){ //find
                        //printf("find---%u:%d\n", ntohs(flow->tuple.inner.port_src), rdp_c->count);
                        free(rdp_c);
                        rdp_c = info->conv->conv_session;
                        rdp_c->count += 1;
                        rdp_c->num += 1;
                        info->index  = rdp_c->count;
                    }
                    else{                   //create
                        //printf("create--%p--%u:0\n", info->conv, ntohs(flow->tuple.inner.port_src));
                        /* count num index, c'est tout zero*/
                        ;
                    }
                }
                pthread_mutex_unlock(&rdp_mutex);
            }
            direction = FLOW_DIR_DST2SRC;
            break;
        case DT_TPDU:
            info->connect_confirm_dissected = 1;
            if(tpdu_len == 2 && payload[6] == 0x80){ //8 means EOT <end of TPDU unit>, 2 & 0 means class 0
                offset += tpdu_len;
                dissect_mcs(payload + offset, payload_len - offset, info);
            }

            if(info->operation_type == RDP_CLIENT_INFO)
                direction = FLOW_DIR_SRC2DST;
            else if(info->operation_type == RDP_SERVER_INFO)
                direction = FLOW_DIR_DST2SRC;

            break;
        default:   //the others, disconnect,ack,reject,error etc
            break;
    }

    if(info->write == 1){
        if(g_config.rdp_packet_mode)
            write_rdp_log(flow, direction, info, NULL);

        info->write   = 0;
        info->should_write = 1;
    }

    return 0;
}

static void identify_rdp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_RDP] == 0)
        return;
    if(payload_len < 11)
        return;

    if(payload[0] != 3 || payload[1] != 0 || get_uint16_ntohs(payload, 2) != payload_len)
        return;

    if(payload[4] + 5 == payload_len){ //X.224 CR, CC
        if(payload_len == 11 && (ntohs(flow->tuple.inner.port_src) == 3389 || ntohs(flow->tuple.inner.port_dst) == 3389)){
            flow->real_protocol_id = PROTOCOL_RDP;
        }
        else if(payload_len >= 19){
            if(memcmp(payload+11, "Cookie: ", 8) == 0)
                flow->real_protocol_id = PROTOCOL_RDP;
            else if(payload[11] == TYPE_RDP_NEG_REQ){
                if((payload[12] & 0x08) && payload_len == 55)
                    flow->real_protocol_id = PROTOCOL_RDP;
                else if(payload_len == 19)
                    flow->real_protocol_id = PROTOCOL_RDP;
            }
            else if(payload[11] == TYPE_RDP_NEG_RSP && payload_len == 19)
                flow->real_protocol_id = PROTOCOL_RDP;
            else if(payload[11] == TYPE_RDP_NEG_FAILURE && payload_len == 19)
                flow->real_protocol_id = PROTOCOL_RDP;
        }
    }
    else if(payload[4] == 0x02 && payload[5] == 0xf0 && payload[6] == 0x80){
        uint16_t mcs_head = get_uint16_ntohs(payload, 7);
        if(mcs_head == 0x7f65 || mcs_head == 0x7f66)
            flow->real_protocol_id = PROTOCOL_RDP;
    }

    return;
}

extern struct rte_hash *conversation_hash_no_port2;

static void rdp_destructor(struct flow_info *flow, void* session)
{
    struct rdp_info *info = session;
    if(info->conv && info->conv->conv_session && g_config.rdp_mutex_switch)
    {
        pthread_mutex_lock(&rdp_mutex);
        struct rdp_conv *rdp_pub = info->conv->conv_session;
        if(info->should_write && (!(rdp_pub->count > info->index && rdp_pub->state[info->index + 1])))
            write_rdp_log(flow, flow->direction, info, NULL);

        rdp_pub->num -= 1;
        if(!rdp_pub->num){ //最后一个
#if 0
            struct rdp_conv *rdp_pub = info->conv->conv_session;
            printf("count-------------------------------------%d\n", rdp_pub ? rdp_pub->count + 1 : 9999999);
            for(int i=0; i <= rdp_pub->count; i++)
                printf("%u ", rdp_pub->state[i % RDP_MAX_FLOW_PER_CLIENT]);
            printf("\n");
#endif
            free(rdp_pub);
            info->conv->conv_session = 0;
       }
       pthread_mutex_unlock(&rdp_mutex);
     }
     else if(info->should_write){
        write_rdp_log(flow, flow->direction, info, NULL);
        //write_rdp_fields_storage(flow, flow->direction, info);
     }
}

static int timeout_rdp(struct flow_info *flow)
{
    if (flow && flow->app_session)
    {
        rdp_destructor(flow, flow->app_session);
        free(flow->app_session);
        flow->app_session = NULL;
    }

    return 0;
}


static void init_rdp_dissector(void)
{
    dpi_register_proto_schema(rdp_field_array, EM_RDP_MAX, "rdp");

    port_add_proto_head(IPPROTO_TCP, 3389, PROTOCOL_RDP);
    tcp_detection_array[PROTOCOL_RDP].identify_type = DPI_IDENTIFY_CONTENT;
    tcp_detection_array[PROTOCOL_RDP].proto         = PROTOCOL_RDP;
    tcp_detection_array[PROTOCOL_RDP].identify_func = identify_rdp;
    tcp_detection_array[PROTOCOL_RDP].dissect_func  = dissect_rdp;
    tcp_detection_array[PROTOCOL_RDP].flow_timeout  = timeout_rdp;
    // tcp_detection_array[PROTOCOL_RDP].exit_func     = rdp_destructor;

    port_add_proto_head(IPPROTO_UDP, 3389, PROTOCOL_RDP);
    udp_detection_array[PROTOCOL_RDP].identify_type = DPI_IDENTIFY_CONTENT;
    udp_detection_array[PROTOCOL_RDP].proto         = PROTOCOL_RDP;
    udp_detection_array[PROTOCOL_RDP].identify_func = identify_rdp;
    udp_detection_array[PROTOCOL_RDP].dissect_func  = dissect_rdp;
    udp_detection_array[PROTOCOL_RDP].flow_timeout  = timeout_rdp;
    // udp_detection_array[PROTOCOL_RDP].exit_func     = rdp_destructor;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_RDP].excluded_protocol_bitmask, PROTOCOL_RDP);


    map_fields_info_register(rdp_field_array,PROTOCOL_RDP, EM_RDP_MAX,"rdp");
}

static __attribute((constructor)) void before_init_rdp(void) {
        register_tbl_array(TBL_LOG_RDP, 0, "rdp", init_rdp_dissector);
}

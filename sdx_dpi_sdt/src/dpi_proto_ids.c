#include "dpi_proto_ids.h"
#include "dpi_detect.h"



const char *protocol_name_array[PROTOCOL_MAX] =
{
    "UNKNOWN",
    "MAC_PHEADER",
    "IPP",
    "IP",
    "UDP",
    "TCP",
    "LINK",
    "HTTP",
    "DNS",
    "FTP_CONTROL",
    "FTP_DATA",
    "SMTP",
    "ESMTP",
    "SSL",
    "IMAP",
    "POP",
    "SIP",
    "WEIXIN",
    "TELNET",
    "SSH",
    "TFTP",
    "RIP",
    "L2TP",
    "PPTP",
    "RADIUS",
    "DTLS",
    "CLASSICSTUN",
    "STUN",
    "MYSQL",
    "TDS",
    "H323",
    "SMB",
    "GTP_CONTROL",
    "SNMP",
    "ISAKMP",
    "IAX2",
    "GRE",
    "ESP",
    "SDP_L5",
    "ISUP_L5",
    "OSPF",
    "DHCP",
    "VNC",
    "LDAP",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>ER<PERSON>",
    "FLOW",
    "OCSP",
    "RTSP",
    "S1AP",
    "EIGRP",
    "CDP",
    "RTP",
    "AH",
    "TURN",
    "RDP",
    "SKIP",
    "X509",
    "MGCP",
    "MEGACO",
    "KERBEROS",
    "BGP",
    "MOBILE_LOG",
    "AH_N",
    "ESP_N",
    "LCP",
    "CHAP",
    "PAP",
    "NSPI",
    "CLOUDSTACK",
    "LOTUS_NRPC",
    "TEAMVIEWER",
    "UDPENCAP",
    "ICMP",
    "ANYDESK",
    "OPENVPN",
    "RSVP",
    "JT808",
    "ISIS",
    "LDP",
    "SMPP",
    "CMPP",
    "SSDP",
    "STP",
    "RTCP",
    "SCCP",
    "OICQ",
    "SFTP",
    "SGMP",
    "DNP3",
    "S7COMM",
    "IEC104",
    "SCTP",
    "FTPS",
    "PGSQL",
    "TNS",
    "MODBUS",
    "GTP_U",
    "TEREDO",
    "IPIP",
    "TLL",
    "VRRP",
    "CFLOW",
    "IGMP",
    "DIAMETER",
    "SHARE_HEADER",
    "CWMP",
    "SYSLOG",
    "SOCKS",
    "DBBASIC",
    "ARP",
    "EMAIL",
    "P327_COMMON",
};



struct guess_proto_data tcp_port_proto[65536];
struct guess_proto_data udp_port_proto[65536];
struct guess_proto_data sctp_port_proto[65536];


struct check_proto_data tcp_detection_array[PROTOCOL_MAX];
struct check_proto_data udp_detection_array[PROTOCOL_MAX];
struct check_proto_data sctp_detection_array[PROTOCOL_MAX];



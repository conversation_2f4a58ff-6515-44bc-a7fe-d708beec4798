/****************************************************************************************
 * 文 件 名 : dpi_dns.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <glib.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_pschema.h"
#define FLAGS_MASK     0x8000
#define MAX_DNS_REQUESTS    8
#define MAX_DNS_RESPONSES  16
#define MAX_META_NUM       32
#define MAXDNAME         1025     /* maximum domain name length */

#define T_A                 1     /* host address */
#define T_NS                2     /*authoritative name server*/
#define T_CNAME             5     /* canonical name */
#define T_SOA               6     /*atart of authority*/
#define T_PTR              12     /*domain name pointer*/
#define T_AAAA             28     /* IPv6 address (RFC 1886) */

#define T_MX            15              /* mail routing information */
#define T_TXT           16              /* text strings */


GHashTable *dns_filter_table = NULL;
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
gpointer white_filter(const char* servername, uint16_t len, GHashTable *whitelist_table);

enum  dns_index_em{
    EM_DNS_IDENTIFICATION,
    EM_DNS_RESPONSE,
    EM_DNS_OPCODE,
    EM_DNS_AUTH_ANSWER,
    EM_DNS_TRUNCATED,
    EM_DNS_RECURSION_REQ,
    EM_DNS_RECURSION_AVAIL,
    EM_DNS_RETURN_CODE,
    EM_DNS_QUESTIONS,
    EM_DNS_ANSWER_RRS,
    EM_DNS_AUTHORIZATION_RRS,
    EM_DNS_ADDITIONAL_RRS,
    EM_DNS_QD_LEN00,
    EM_DNS_QD_NAME00,
    EM_DNS_QD_TYPE00,
    EM_DNS_QD_CLS00,
    EM_DNS_QD_LEN01,
    EM_DNS_QD_NAME01,
    EM_DNS_QD_TYPE01,
    EM_DNS_QD_CLS01,
    EM_DNS_QD_LEN02,
    EM_DNS_QD_NAME02,
    EM_DNS_QD_TYPE02,
    EM_DNS_QD_CLS02,
    EM_DNS_QD_LEN03,
    EM_DNS_QD_NAME03,
    EM_DNS_QD_TYPE03,
    EM_DNS_QD_CLS03,
    EM_DNS_QD_LEN04,
    EM_DNS_QD_NAME04,
    EM_DNS_QD_TYPE04,
    EM_DNS_QD_CLS04,
    EM_DNS_QD_LEN05,
    EM_DNS_QD_NAME05,
    EM_DNS_QD_TYPE05,
    EM_DNS_QD_CLS05,
    EM_DNS_QD_LEN06,
    EM_DNS_QD_NAME06,
    EM_DNS_QD_TYPE06,
    EM_DNS_QD_CLS06,
    EM_DNS_QD_LEN07,
    EM_DNS_QD_NAME07,
    EM_DNS_QD_TYPE07,
    EM_DNS_QD_CLS07,
    EM_DNS_QD_OTHER,
    EM_DNS_AN_TYPE00,
    EM_DNS_AN_CLS00,
    EM_DNS_AN_NAME00,
    EM_DNS_AN_TTL00,
    EM_DNS_AN_LEN00,
    EM_DNS_AN_RDATA00,
    EM_DNS_AN_TYPE01,
    EM_DNS_AN_CLS01,
    EM_DNS_AN_NAME01,
    EM_DNS_AN_TTL01,
    EM_DNS_AN_LEN01,
    EM_DNS_AN_RDATA01,
    EM_DNS_AN_TYPE02,
    EM_DNS_AN_CLS02,
    EM_DNS_AN_NAME02,
    EM_DNS_AN_TTL02,
    EM_DNS_AN_LEN02,
    EM_DNS_AN_RDATA02,
    EM_DNS_AN_TYPE03,
    EM_DNS_AN_CLS03,
    EM_DNS_AN_NAME03,
    EM_DNS_AN_TTL03,
    EM_DNS_AN_LEN03,
    EM_DNS_AN_RDATA03,
    EM_DNS_AN_TYPE04,
    EM_DNS_AN_CLS04,
    EM_DNS_AN_NAME04,
    EM_DNS_AN_TTL04,
    EM_DNS_AN_LEN04,
    EM_DNS_AN_RDATA04,
    EM_DNS_AN_TYPE05,
    EM_DNS_AN_CLS05,
    EM_DNS_AN_NAME05,
    EM_DNS_AN_TTL05,
    EM_DNS_AN_LEN05,
    EM_DNS_AN_RDATA05,
    EM_DNS_AN_TYPE06,
    EM_DNS_AN_CLS06,
    EM_DNS_AN_NAME06,
    EM_DNS_AN_TTL06,
    EM_DNS_AN_LEN06,
    EM_DNS_AN_RDATA06,
    EM_DNS_AN_TYPE07,
    EM_DNS_AN_CLS07,
    EM_DNS_AN_NAME07,
    EM_DNS_AN_TTL07,
    EM_DNS_AN_LEN07,
    EM_DNS_AN_RDATA07,
    EM_DNS_AN_TYPE08,
    EM_DNS_AN_CLS08,
    EM_DNS_AN_NAME08,
    EM_DNS_AN_TTL08,
    EM_DNS_AN_LEN08,
    EM_DNS_AN_RDATA08,
    EM_DNS_AN_TYPE09,
    EM_DNS_AN_CLS09,
    EM_DNS_AN_NAME09,
    EM_DNS_AN_TTL09,
    EM_DNS_AN_LEN09,
    EM_DNS_AN_RDATA09,
    EM_DNS_AN_TYPE10,
    EM_DNS_AN_CLS10,
    EM_DNS_AN_NAME10,
    EM_DNS_AN_TTL10,
    EM_DNS_AN_LEN10,
    EM_DNS_AN_RDATA10,
    EM_DNS_AN_TYPE11,
    EM_DNS_AN_CLS11,
    EM_DNS_AN_NAME11,
    EM_DNS_AN_TTL11,
    EM_DNS_AN_LEN11,
    EM_DNS_AN_RDATA11,
    EM_DNS_AN_TYPE12,
    EM_DNS_AN_CLS12,
    EM_DNS_AN_NAME12,
    EM_DNS_AN_TTL12,
    EM_DNS_AN_LEN12,
    EM_DNS_AN_RDATA12,
    EM_DNS_AN_TYPE13,
    EM_DNS_AN_CLS13,
    EM_DNS_AN_NAME13,
    EM_DNS_AN_TTL13,
    EM_DNS_AN_LEN13,
    EM_DNS_AN_RDATA13,
    EM_DNS_AN_TYPE14,
    EM_DNS_AN_CLS14,
    EM_DNS_AN_NAME14,
    EM_DNS_AN_TTL14,
    EM_DNS_AN_LEN14,
    EM_DNS_AN_RDATA14,
    EM_DNS_AN_TYPE15,
    EM_DNS_AN_CLS15,
    EM_DNS_AN_NAME15,
    EM_DNS_AN_TTL15,
    EM_DNS_AN_LEN15,
    EM_DNS_AN_RDATA15,
    EM_DNS_AN_OTHER,
    EM_DNS_AU_TYPE00,
    EM_DNS_AU_CLS00,
    EM_DNS_AU_NAME00,
    EM_DNS_AU_TTL00,
    EM_DNS_AU_LEN00,
    EM_DNS_AU_RDATA00,
    EM_DNS_AU_TYPE01,
    EM_DNS_AU_CLS01,
    EM_DNS_AU_NAME01,
    EM_DNS_AU_TTL01,
    EM_DNS_AU_LEN01,
    EM_DNS_AU_RDATA01,
    EM_DNS_AU_TYPE02,
    EM_DNS_AU_CLS02,
    EM_DNS_AU_NAME02,
    EM_DNS_AU_TTL02,
    EM_DNS_AU_LEN02,
    EM_DNS_AU_RDATA02,
    EM_DNS_AU_TYPE03,
    EM_DNS_AU_CLS03,
    EM_DNS_AU_NAME03,
    EM_DNS_AU_TTL03,
    EM_DNS_AU_LEN03,
    EM_DNS_AU_RDATA03,
    EM_DNS_AU_TYPE04,
    EM_DNS_AU_CLS04,
    EM_DNS_AU_NAME04,
    EM_DNS_AU_TTL04,
    EM_DNS_AU_LEN04,
    EM_DNS_AU_RDATA04,
    EM_DNS_AU_TYPE05,
    EM_DNS_AU_CLS05,
    EM_DNS_AU_NAME05,
    EM_DNS_AU_TTL05,
    EM_DNS_AU_LEN05,
    EM_DNS_AU_RDATA05,
    EM_DNS_AU_TYPE06,
    EM_DNS_AU_CLS06,
    EM_DNS_AU_NAME06,
    EM_DNS_AU_TTL06,
    EM_DNS_AU_LEN06,
    EM_DNS_AU_RDATA06,
    EM_DNS_AU_TYPE07,
    EM_DNS_AU_CLS07,
    EM_DNS_AU_NAME07,
    EM_DNS_AU_TTL07,
    EM_DNS_AU_LEN07,
    EM_DNS_AU_RDATA07,
    EM_DNS_AU_TYPE08,
    EM_DNS_AU_CLS08,
    EM_DNS_AU_NAME08,
    EM_DNS_AU_TTL08,
    EM_DNS_AU_LEN08,
    EM_DNS_AU_RDATA08,
    EM_DNS_AU_TYPE09,
    EM_DNS_AU_CLS09,
    EM_DNS_AU_NAME09,
    EM_DNS_AU_TTL09,
    EM_DNS_AU_LEN09,
    EM_DNS_AU_RDATA09,
    EM_DNS_AU_TYPE10,
    EM_DNS_AU_CLS10,
    EM_DNS_AU_NAME10,
    EM_DNS_AU_TTL10,
    EM_DNS_AU_LEN10,
    EM_DNS_AU_RDATA10,
    EM_DNS_AU_TYPE11,
    EM_DNS_AU_CLS11,
    EM_DNS_AU_NAME11,
    EM_DNS_AU_TTL11,
    EM_DNS_AU_LEN11,
    EM_DNS_AU_RDATA11,
    EM_DNS_AU_TYPE12,
    EM_DNS_AU_CLS12,
    EM_DNS_AU_NAME12,
    EM_DNS_AU_TTL12,
    EM_DNS_AU_LEN12,
    EM_DNS_AU_RDATA12,
    EM_DNS_AU_TYPE13,
    EM_DNS_AU_CLS13,
    EM_DNS_AU_NAME13,
    EM_DNS_AU_TTL13,
    EM_DNS_AU_LEN13,
    EM_DNS_AU_RDATA13,
    EM_DNS_AU_TYPE14,
    EM_DNS_AU_CLS14,
    EM_DNS_AU_NAME14,
    EM_DNS_AU_TTL14,
    EM_DNS_AU_LEN14,
    EM_DNS_AU_RDATA14,
    EM_DNS_AU_TYPE15,
    EM_DNS_AU_CLS15,
    EM_DNS_AU_NAME15,
    EM_DNS_AU_TTL15,
    EM_DNS_AU_LEN15,
    EM_DNS_AU_RDATA15,
    EM_DNS_AU_OTHER,
    EM_DNS_AD_TYPE00,
    EM_DNS_AD_CLS00,
    EM_DNS_AD_NAME00,
    EM_DNS_AD_TTL00,
    EM_DNS_AD_LEN00,
    EM_DNS_AD_RDATA00,
    EM_DNS_AD_TYPE01,
    EM_DNS_AD_CLS01,
    EM_DNS_AD_NAME01,
    EM_DNS_AD_TTL01,
    EM_DNS_AD_LEN01,
    EM_DNS_AD_RDATA01,
    EM_DNS_AD_TYPE02,
    EM_DNS_AD_CLS02,
    EM_DNS_AD_NAME02,
    EM_DNS_AD_TTL02,
    EM_DNS_AD_LEN02,
    EM_DNS_AD_RDATA02,
    EM_DNS_AD_TYPE03,
    EM_DNS_AD_CLS03,
    EM_DNS_AD_NAME03,
    EM_DNS_AD_TTL03,
    EM_DNS_AD_LEN03,
    EM_DNS_AD_RDATA03,
    EM_DNS_AD_TYPE04,
    EM_DNS_AD_CLS04,
    EM_DNS_AD_NAME04,
    EM_DNS_AD_TTL04,
    EM_DNS_AD_LEN04,
    EM_DNS_AD_RDATA04,
    EM_DNS_AD_TYPE05,
    EM_DNS_AD_CLS05,
    EM_DNS_AD_NAME05,
    EM_DNS_AD_TTL05,
    EM_DNS_AD_LEN05,
    EM_DNS_AD_RDATA05,
    EM_DNS_AD_TYPE06,
    EM_DNS_AD_CLS06,
    EM_DNS_AD_NAME06,
    EM_DNS_AD_TTL06,
    EM_DNS_AD_LEN06,
    EM_DNS_AD_RDATA06,
    EM_DNS_AD_TYPE07,
    EM_DNS_AD_CLS07,
    EM_DNS_AD_NAME07,
    EM_DNS_AD_TTL07,
    EM_DNS_AD_LEN07,
    EM_DNS_AD_RDATA07,
    EM_DNS_AD_TYPE08,
    EM_DNS_AD_CLS08,
    EM_DNS_AD_NAME08,
    EM_DNS_AD_TTL08,
    EM_DNS_AD_LEN08,
    EM_DNS_AD_RDATA08,
    EM_DNS_AD_TYPE09,
    EM_DNS_AD_CLS09,
    EM_DNS_AD_NAME09,
    EM_DNS_AD_TTL09,
    EM_DNS_AD_LEN09,
    EM_DNS_AD_RDATA09,
    EM_DNS_AD_TYPE10,
    EM_DNS_AD_CLS10,
    EM_DNS_AD_NAME10,
    EM_DNS_AD_TTL10,
    EM_DNS_AD_LEN10,
    EM_DNS_AD_RDATA10,
    EM_DNS_AD_TYPE11,
    EM_DNS_AD_CLS11,
    EM_DNS_AD_NAME11,
    EM_DNS_AD_TTL11,
    EM_DNS_AD_LEN11,
    EM_DNS_AD_RDATA11,
    EM_DNS_AD_TYPE12,
    EM_DNS_AD_CLS12,
    EM_DNS_AD_NAME12,
    EM_DNS_AD_TTL12,
    EM_DNS_AD_LEN12,
    EM_DNS_AD_RDATA12,
    EM_DNS_AD_TYPE13,
    EM_DNS_AD_CLS13,
    EM_DNS_AD_NAME13,
    EM_DNS_AD_TTL13,
    EM_DNS_AD_LEN13,
    EM_DNS_AD_RDATA13,
    EM_DNS_AD_TYPE14,
    EM_DNS_AD_CLS14,
    EM_DNS_AD_NAME14,
    EM_DNS_AD_TTL14,
    EM_DNS_AD_LEN14,
    EM_DNS_AD_RDATA14,
    EM_DNS_AD_TYPE15,
    EM_DNS_AD_CLS15,
    EM_DNS_AD_NAME15,
    EM_DNS_AD_TTL15,
    EM_DNS_AD_LEN15,
    EM_DNS_AD_RDATA15,
    EM_DNS_AD_OTHER,
    EM_DNS_FLAGS,
    EM_DNS_JUDGE,

    EM_DNS_DOMAIN,
    EM_DNS_DOMAINIP,
    EM_DNS_QRY,
    EM_DNS_ANS,

    //sdx need
    EM_DNS_ANS_CNAME,
    EM_DNS_ANS_CNAME_CNT,
    EM_DNS_ANS_IPV6,
    EM_DNS_ANS_AIP,
    EM_DNS_ANS_AIP_CNT,
    EM_DNS_ANS_AIP_ASN,
    EM_DNS_ANS_API_COUNTRY,
    EM_DNS_ANS_MAIL_SRV_HOST,
    EM_DNS_ANS_MAIL_SRV_HOST_CNT,
    EM_DNS_ANS_MAIL_SRV_IP,
    EM_DNS_ANS_MAIL_SRV_IP_CNT,
    EM_DNS_ANS_MAIL_SRV_ASN,
    EM_DNS_ANS_MAIL_SRV_COUNTRY,
    EM_DNS_ANS_NAME_SRV_HOST,
    EM_DNS_ANS_NAME_SRV_HOST_CNT,
    EM_DNS_ANS_NAME_SRV_IP,
    EM_DNS_ANS_NAME_SRV_IP_CNT,
    EM_DNS_ANS_NAME_SRV_ASN,
    EM_DNS_ANS_NAME_SRV_COUNTTRY,
    EM_DNS_ANS_DNS_SPF,
    EM_DNS_ANS_DNS_TEX,
    EM_DNS_ANS_TYPES,


    EM_DNS_MAX
};


static dpi_field_table  dns_field_array[] = {
    DPI_FIELD_D(EM_DNS_IDENTIFICATION,           YA_FT_UINT16,               "identification"),
    DPI_FIELD_D(EM_DNS_RESPONSE,                 YA_FT_UINT16,               "response"),
    DPI_FIELD_D(EM_DNS_OPCODE,                   YA_FT_UINT16,               "opcode"),
    DPI_FIELD_D(EM_DNS_AUTH_ANSWER,              YA_FT_UINT16,               "auth_answer"),
    DPI_FIELD_D(EM_DNS_TRUNCATED,                YA_FT_UINT16,               "truncated"),
    DPI_FIELD_D(EM_DNS_RECURSION_REQ,            YA_FT_UINT16,               "recursion_req"),
    DPI_FIELD_D(EM_DNS_RECURSION_AVAIL,          YA_FT_UINT16,               "recursion_avail"),
    DPI_FIELD_D(EM_DNS_RETURN_CODE,              YA_FT_UINT16,               "return_code"),
    DPI_FIELD_D(EM_DNS_QUESTIONS,                YA_FT_INT32,                "questions"),
    DPI_FIELD_D(EM_DNS_ANSWER_RRS,               YA_FT_INT32,                "answer_RRs"),
    DPI_FIELD_D(EM_DNS_AUTHORIZATION_RRS,        YA_FT_INT32,                "authorization_RRs"),
    DPI_FIELD_D(EM_DNS_ADDITIONAL_RRS,           YA_FT_INT32,                "additional_RRs"),


    DPI_FIELD_D(EM_DNS_QD_LEN00,                 YA_FT_UINT16,               "Qd_len00"),
    DPI_FIELD_D(EM_DNS_QD_NAME00,                YA_FT_STRING,               "Qd_name00"),
    DPI_FIELD_D(EM_DNS_QD_TYPE00,                YA_FT_UINT16,               "Qd_type00"),
    DPI_FIELD_D(EM_DNS_QD_CLS00,                 YA_FT_UINT16,               "Qd_cls00"),
    DPI_FIELD_D(EM_DNS_QD_LEN01,                 YA_FT_UINT16,               "Qd_len01"),
    DPI_FIELD_D(EM_DNS_QD_NAME01,                YA_FT_STRING,               "Qd_name01"),
    DPI_FIELD_D(EM_DNS_QD_TYPE01,                YA_FT_UINT16,               "Qd_type01"),
    DPI_FIELD_D(EM_DNS_QD_CLS01,                 YA_FT_UINT16,               "Qd_cls01"),
    DPI_FIELD_D(EM_DNS_QD_LEN02,                 YA_FT_UINT16,               "Qd_len02"),
    DPI_FIELD_D(EM_DNS_QD_NAME02,                YA_FT_STRING,               "Qd_name02"),
    DPI_FIELD_D(EM_DNS_QD_TYPE02,                YA_FT_UINT16,               "Qd_type02"),
    DPI_FIELD_D(EM_DNS_QD_CLS02,                 YA_FT_UINT16,               "Qd_cls02"),
    DPI_FIELD_D(EM_DNS_QD_LEN03,                 YA_FT_UINT16,               "Qd_len03"),
    DPI_FIELD_D(EM_DNS_QD_NAME03,                YA_FT_STRING,               "Qd_name03"),
    DPI_FIELD_D(EM_DNS_QD_TYPE03,                YA_FT_UINT16,               "Qd_type03"),
    DPI_FIELD_D(EM_DNS_QD_CLS03,                 YA_FT_UINT16,               "Qd_cls03"),
    DPI_FIELD_D(EM_DNS_QD_LEN04,                 YA_FT_UINT16,               "Qd_len04"),
    DPI_FIELD_D(EM_DNS_QD_NAME04,                YA_FT_STRING,               "Qd_name04"),
    DPI_FIELD_D(EM_DNS_QD_TYPE04,                YA_FT_UINT16,               "Qd_type04"),
    DPI_FIELD_D(EM_DNS_QD_CLS04,                 YA_FT_UINT16,               "Qd_cls04"),
    DPI_FIELD_D(EM_DNS_QD_LEN05,                 YA_FT_UINT16,               "Qd_len05"),
    DPI_FIELD_D(EM_DNS_QD_NAME05,                YA_FT_STRING,               "Qd_name05"),
    DPI_FIELD_D(EM_DNS_QD_TYPE05,                YA_FT_UINT16,               "Qd_type05"),
    DPI_FIELD_D(EM_DNS_QD_CLS05,                 YA_FT_UINT16,               "Qd_cls05"),
    DPI_FIELD_D(EM_DNS_QD_LEN06,                 YA_FT_UINT16,               "Qd_len06"),
    DPI_FIELD_D(EM_DNS_QD_NAME06,                YA_FT_STRING,               "Qd_name06"),
    DPI_FIELD_D(EM_DNS_QD_TYPE06,                YA_FT_UINT16,               "Qd_type06"),
    DPI_FIELD_D(EM_DNS_QD_CLS06,                 YA_FT_UINT16,               "Qd_cls06"),
    DPI_FIELD_D(EM_DNS_QD_LEN07,                 YA_FT_UINT16,               "Qd_len07"),
    DPI_FIELD_D(EM_DNS_QD_NAME07,                YA_FT_STRING,               "Qd_name07"),
    DPI_FIELD_D(EM_DNS_QD_TYPE07,                YA_FT_UINT16,               "Qd_type07"),
    DPI_FIELD_D(EM_DNS_QD_CLS07,                 YA_FT_UINT16,               "Qd_cls07"),
    DPI_FIELD_D(EM_DNS_QD_OTHER,                 YA_FT_NONE,                 "Qd_Other"),
    DPI_FIELD_D(EM_DNS_AN_TYPE00,                YA_FT_UINT16,               "An_type00"),
    DPI_FIELD_D(EM_DNS_AN_CLS00,                 YA_FT_UINT16,               "An_cls00"),
    DPI_FIELD_D(EM_DNS_AN_NAME00,                YA_FT_STRING,               "An_name00"),
    DPI_FIELD_D(EM_DNS_AN_TTL00,                 YA_FT_UINT32,               "An_ttl00"),
    DPI_FIELD_D(EM_DNS_AN_LEN00,                 YA_FT_UINT16,               "An_len00"),
    DPI_FIELD_D(EM_DNS_AN_RDATA00,               YA_FT_STRING,               "An_rdata00"),
    DPI_FIELD_D(EM_DNS_AN_TYPE01,                YA_FT_UINT16,               "An_type01"),
    DPI_FIELD_D(EM_DNS_AN_CLS01,                 YA_FT_UINT16,               "An_cls01"),
    DPI_FIELD_D(EM_DNS_AN_NAME01,                YA_FT_STRING,               "An_name01"),
    DPI_FIELD_D(EM_DNS_AN_TTL01,                 YA_FT_UINT32,               "An_ttl01"),
    DPI_FIELD_D(EM_DNS_AN_LEN01,                 YA_FT_UINT16,               "An_len01"),
    DPI_FIELD_D(EM_DNS_AN_RDATA01,               YA_FT_STRING,               "An_rdata01"),
    DPI_FIELD_D(EM_DNS_AN_TYPE02,                YA_FT_UINT16,               "An_type02"),
    DPI_FIELD_D(EM_DNS_AN_CLS02,                 YA_FT_UINT16,               "An_cls02"),
    DPI_FIELD_D(EM_DNS_AN_NAME02,                YA_FT_STRING,               "An_name02"),
    DPI_FIELD_D(EM_DNS_AN_TTL02,                 YA_FT_UINT32,               "An_ttl02"),
    DPI_FIELD_D(EM_DNS_AN_LEN02,                 YA_FT_UINT16,               "An_len02"),
    DPI_FIELD_D(EM_DNS_AN_RDATA02,               YA_FT_STRING,               "An_rdata02"),
    DPI_FIELD_D(EM_DNS_AN_TYPE03,                YA_FT_UINT16,               "An_type03"),
    DPI_FIELD_D(EM_DNS_AN_CLS03,                 YA_FT_UINT16,               "An_cls03"),
    DPI_FIELD_D(EM_DNS_AN_NAME03,                YA_FT_STRING,               "An_name03"),
    DPI_FIELD_D(EM_DNS_AN_TTL03,                 YA_FT_UINT32,               "An_ttl03"),
    DPI_FIELD_D(EM_DNS_AN_LEN03,                 YA_FT_UINT16,               "An_len03"),
    DPI_FIELD_D(EM_DNS_AN_RDATA03,               YA_FT_STRING,               "An_rdata03"),
    DPI_FIELD_D(EM_DNS_AN_TYPE04,                YA_FT_UINT16,               "An_type04"),
    DPI_FIELD_D(EM_DNS_AN_CLS04,                 YA_FT_UINT16,               "An_cls04"),
    DPI_FIELD_D(EM_DNS_AN_NAME04,                YA_FT_STRING,               "An_name04"),
    DPI_FIELD_D(EM_DNS_AN_TTL04,                 YA_FT_UINT32,               "An_ttl04"),
    DPI_FIELD_D(EM_DNS_AN_LEN04,                 YA_FT_UINT16,               "An_len04"),
    DPI_FIELD_D(EM_DNS_AN_RDATA04,               YA_FT_STRING,               "An_rdata04"),
    DPI_FIELD_D(EM_DNS_AN_TYPE05,                YA_FT_UINT16,               "An_type05"),
    DPI_FIELD_D(EM_DNS_AN_CLS05,                 YA_FT_UINT16,               "An_cls05"),
    DPI_FIELD_D(EM_DNS_AN_NAME05,                YA_FT_STRING,               "An_name05"),
    DPI_FIELD_D(EM_DNS_AN_TTL05,                 YA_FT_UINT32,               "An_ttl05"),
    DPI_FIELD_D(EM_DNS_AN_LEN05,                 YA_FT_UINT16,               "An_len05"),
    DPI_FIELD_D(EM_DNS_AN_RDATA05,               YA_FT_STRING,               "An_rdata05"),
    DPI_FIELD_D(EM_DNS_AN_TYPE06,                YA_FT_UINT16,               "An_type06"),
    DPI_FIELD_D(EM_DNS_AN_CLS06,                 YA_FT_UINT16,               "An_cls06"),
    DPI_FIELD_D(EM_DNS_AN_NAME06,                YA_FT_STRING,               "An_name06"),
    DPI_FIELD_D(EM_DNS_AN_TTL06,                 YA_FT_UINT32,               "An_ttl06"),
    DPI_FIELD_D(EM_DNS_AN_LEN06,                 YA_FT_UINT16,               "An_len06"),
    DPI_FIELD_D(EM_DNS_AN_RDATA06,               YA_FT_STRING,               "An_rdata06"),
    DPI_FIELD_D(EM_DNS_AN_TYPE07,                YA_FT_UINT16,               "An_type07"),
    DPI_FIELD_D(EM_DNS_AN_CLS07,                 YA_FT_UINT16,               "An_cls07"),
    DPI_FIELD_D(EM_DNS_AN_NAME07,                YA_FT_STRING,               "An_name07"),
    DPI_FIELD_D(EM_DNS_AN_TTL07,                 YA_FT_UINT32,               "An_ttl07"),
    DPI_FIELD_D(EM_DNS_AN_LEN07,                 YA_FT_UINT16,               "An_len07"),
    DPI_FIELD_D(EM_DNS_AN_RDATA07,               YA_FT_STRING,               "An_rdata07"),
    DPI_FIELD_D(EM_DNS_AN_TYPE08,                YA_FT_UINT16,               "An_type08"),
    DPI_FIELD_D(EM_DNS_AN_CLS08,                 YA_FT_UINT16,               "An_cls08"),
    DPI_FIELD_D(EM_DNS_AN_NAME08,                YA_FT_STRING,               "An_name08"),
    DPI_FIELD_D(EM_DNS_AN_TTL08,                 YA_FT_UINT32,               "An_ttl08"),
    DPI_FIELD_D(EM_DNS_AN_LEN08,                 YA_FT_UINT16,               "An_len08"),
    DPI_FIELD_D(EM_DNS_AN_RDATA08,               YA_FT_STRING,               "An_rdata08"),
    DPI_FIELD_D(EM_DNS_AN_TYPE09,                YA_FT_UINT16,               "An_type09"),
    DPI_FIELD_D(EM_DNS_AN_CLS09,                 YA_FT_UINT16,               "An_cls09"),
    DPI_FIELD_D(EM_DNS_AN_NAME09,                YA_FT_STRING,               "An_name09"),
    DPI_FIELD_D(EM_DNS_AN_TTL09,                 YA_FT_UINT32,               "An_ttl09"),
    DPI_FIELD_D(EM_DNS_AN_LEN09,                 YA_FT_UINT16,               "An_len09"),
    DPI_FIELD_D(EM_DNS_AN_RDATA09,               YA_FT_STRING,               "An_rdata09"),
    DPI_FIELD_D(EM_DNS_AN_TYPE10,                YA_FT_UINT16,               "An_type10"),
    DPI_FIELD_D(EM_DNS_AN_CLS10,                 YA_FT_UINT16,               "An_cls10"),
    DPI_FIELD_D(EM_DNS_AN_NAME10,                YA_FT_STRING,               "An_name10"),
    DPI_FIELD_D(EM_DNS_AN_TTL10,                 YA_FT_UINT32,               "An_ttl10"),
    DPI_FIELD_D(EM_DNS_AN_LEN10,                 YA_FT_UINT16,               "An_len10"),
    DPI_FIELD_D(EM_DNS_AN_RDATA10,               YA_FT_STRING,               "An_rdata10"),
    DPI_FIELD_D(EM_DNS_AN_TYPE11,                YA_FT_UINT16,               "An_type11"),
    DPI_FIELD_D(EM_DNS_AN_CLS11,                 YA_FT_UINT16,               "An_cls11"),
    DPI_FIELD_D(EM_DNS_AN_NAME11,                YA_FT_STRING,               "An_name11"),
    DPI_FIELD_D(EM_DNS_AN_TTL11,                 YA_FT_UINT32,               "An_ttl11"),
    DPI_FIELD_D(EM_DNS_AN_LEN11,                 YA_FT_UINT16,               "An_len11"),
    DPI_FIELD_D(EM_DNS_AN_RDATA11,               YA_FT_STRING,               "An_rdata11"),
    DPI_FIELD_D(EM_DNS_AN_TYPE12,                YA_FT_UINT16,               "An_type12"),
    DPI_FIELD_D(EM_DNS_AN_CLS12,                 YA_FT_UINT16,               "An_cls12"),
    DPI_FIELD_D(EM_DNS_AN_NAME12,                YA_FT_STRING,               "An_name12"),
    DPI_FIELD_D(EM_DNS_AN_TTL12,                 YA_FT_UINT32,               "An_ttl12"),
    DPI_FIELD_D(EM_DNS_AN_LEN12,                 YA_FT_UINT16,               "An_len12"),
    DPI_FIELD_D(EM_DNS_AN_RDATA12,               YA_FT_STRING,               "An_rdata12"),
    DPI_FIELD_D(EM_DNS_AN_TYPE13,                YA_FT_UINT16,               "An_type13"),
    DPI_FIELD_D(EM_DNS_AN_CLS13,                 YA_FT_UINT16,               "An_cls13"),
    DPI_FIELD_D(EM_DNS_AN_NAME13,                YA_FT_STRING,               "An_name13"),
    DPI_FIELD_D(EM_DNS_AN_TTL13,                 YA_FT_UINT32,               "An_ttl13"),
    DPI_FIELD_D(EM_DNS_AN_LEN13,                 YA_FT_UINT16,               "An_len13"),
    DPI_FIELD_D(EM_DNS_AN_RDATA13,               YA_FT_STRING,               "An_rdata13"),
    DPI_FIELD_D(EM_DNS_AN_TYPE14,                YA_FT_UINT16,               "An_type14"),
    DPI_FIELD_D(EM_DNS_AN_CLS14,                 YA_FT_UINT16,               "An_cls14"),
    DPI_FIELD_D(EM_DNS_AN_NAME14,                YA_FT_STRING,               "An_name14"),
    DPI_FIELD_D(EM_DNS_AN_TTL14,                 YA_FT_UINT32,               "An_ttl14"),
    DPI_FIELD_D(EM_DNS_AN_LEN14,                 YA_FT_UINT16,               "An_len14"),
    DPI_FIELD_D(EM_DNS_AN_RDATA14,               YA_FT_STRING,               "An_rdata14"),
    DPI_FIELD_D(EM_DNS_AN_TYPE15,                YA_FT_UINT16,               "An_type15"),
    DPI_FIELD_D(EM_DNS_AN_CLS15,                 YA_FT_UINT16,               "An_cls15"),
    DPI_FIELD_D(EM_DNS_AN_NAME15,                YA_FT_STRING,               "An_name15"),
    DPI_FIELD_D(EM_DNS_AN_TTL15,                 YA_FT_UINT32,               "An_ttl15"),
    DPI_FIELD_D(EM_DNS_AN_LEN15,                 YA_FT_UINT16,               "An_len15"),
    DPI_FIELD_D(EM_DNS_AN_RDATA15,               YA_FT_STRING,               "An_rdata15"),
    DPI_FIELD_D(EM_DNS_AN_OTHER,                 YA_FT_NONE,                 "An_Other"),
    DPI_FIELD_D(EM_DNS_AU_TYPE00,                YA_FT_UINT16,               "Au_type00"),
    DPI_FIELD_D(EM_DNS_AU_CLS00,                 YA_FT_UINT16,               "Au_cls00"),
    DPI_FIELD_D(EM_DNS_AU_NAME00,                YA_FT_STRING,               "Au_name00"),
    DPI_FIELD_D(EM_DNS_AU_TTL00,                 YA_FT_UINT32,               "Au_ttl00"),
    DPI_FIELD_D(EM_DNS_AU_LEN00,                 YA_FT_UINT16,               "Au_len00"),
    DPI_FIELD_D(EM_DNS_AU_RDATA00,               YA_FT_STRING,               "Au_rdata00"),
    DPI_FIELD_D(EM_DNS_AU_TYPE01,                YA_FT_UINT16,               "Au_type01"),
    DPI_FIELD_D(EM_DNS_AU_CLS01,                 YA_FT_UINT16,               "Au_cls01"),
    DPI_FIELD_D(EM_DNS_AU_NAME01,                YA_FT_STRING,               "Au_name01"),
    DPI_FIELD_D(EM_DNS_AU_TTL01,                 YA_FT_UINT32,               "Au_ttl01"),
    DPI_FIELD_D(EM_DNS_AU_LEN01,                 YA_FT_UINT16,               "Au_len01"),
    DPI_FIELD_D(EM_DNS_AU_RDATA01,               YA_FT_STRING,               "Au_rdata01"),
    DPI_FIELD_D(EM_DNS_AU_TYPE02,                YA_FT_UINT16,               "Au_type02"),
    DPI_FIELD_D(EM_DNS_AU_CLS02,                 YA_FT_UINT16,               "Au_cls02"),
    DPI_FIELD_D(EM_DNS_AU_NAME02,                YA_FT_STRING,               "Au_name02"),
    DPI_FIELD_D(EM_DNS_AU_TTL02,                 YA_FT_UINT32,               "Au_ttl02"),
    DPI_FIELD_D(EM_DNS_AU_LEN02,                 YA_FT_UINT16,               "Au_len02"),
    DPI_FIELD_D(EM_DNS_AU_RDATA02,               YA_FT_STRING,               "Au_rdata02"),
    DPI_FIELD_D(EM_DNS_AU_TYPE03,                YA_FT_UINT16,               "Au_type03"),
    DPI_FIELD_D(EM_DNS_AU_CLS03,                 YA_FT_UINT16,               "Au_cls03"),
    DPI_FIELD_D(EM_DNS_AU_NAME03,                YA_FT_STRING,               "Au_name03"),
    DPI_FIELD_D(EM_DNS_AU_TTL03,                 YA_FT_UINT32,               "Au_ttl03"),
    DPI_FIELD_D(EM_DNS_AU_LEN03,                 YA_FT_UINT16,               "Au_len03"),
    DPI_FIELD_D(EM_DNS_AU_RDATA03,               YA_FT_STRING,               "Au_rdata03"),
    DPI_FIELD_D(EM_DNS_AU_TYPE04,                YA_FT_UINT16,               "Au_type04"),
    DPI_FIELD_D(EM_DNS_AU_CLS04,                 YA_FT_UINT16,               "Au_cls04"),
    DPI_FIELD_D(EM_DNS_AU_NAME04,                YA_FT_STRING,               "Au_name04"),
    DPI_FIELD_D(EM_DNS_AU_TTL04,                 YA_FT_UINT32,               "Au_ttl04"),
    DPI_FIELD_D(EM_DNS_AU_LEN04,                 YA_FT_UINT16,               "Au_len04"),
    DPI_FIELD_D(EM_DNS_AU_RDATA04,               YA_FT_STRING,               "Au_rdata04"),
    DPI_FIELD_D(EM_DNS_AU_TYPE05,                YA_FT_UINT16,               "Au_type05"),
    DPI_FIELD_D(EM_DNS_AU_CLS05,                 YA_FT_UINT16,               "Au_cls05"),
    DPI_FIELD_D(EM_DNS_AU_NAME05,                YA_FT_STRING,               "Au_name05"),
    DPI_FIELD_D(EM_DNS_AU_TTL05,                 YA_FT_UINT32,               "Au_ttl05"),
    DPI_FIELD_D(EM_DNS_AU_LEN05,                 YA_FT_UINT16,               "Au_len05"),
    DPI_FIELD_D(EM_DNS_AU_RDATA05,               YA_FT_STRING,               "Au_rdata05"),
    DPI_FIELD_D(EM_DNS_AU_TYPE06,                YA_FT_UINT16,               "Au_type06"),
    DPI_FIELD_D(EM_DNS_AU_CLS06,                 YA_FT_UINT16,               "Au_cls06"),
    DPI_FIELD_D(EM_DNS_AU_NAME06,                YA_FT_STRING,               "Au_name06"),
    DPI_FIELD_D(EM_DNS_AU_TTL06,                 YA_FT_UINT32,               "Au_ttl06"),
    DPI_FIELD_D(EM_DNS_AU_LEN06,                 YA_FT_UINT16,               "Au_len06"),
    DPI_FIELD_D(EM_DNS_AU_RDATA06,               YA_FT_STRING,               "Au_rdata06"),
    DPI_FIELD_D(EM_DNS_AU_TYPE07,                YA_FT_UINT16,               "Au_type07"),
    DPI_FIELD_D(EM_DNS_AU_CLS07,                 YA_FT_UINT16,               "Au_cls07"),
    DPI_FIELD_D(EM_DNS_AU_NAME07,                YA_FT_STRING,               "Au_name07"),
    DPI_FIELD_D(EM_DNS_AU_TTL07,                 YA_FT_UINT32,               "Au_ttl07"),
    DPI_FIELD_D(EM_DNS_AU_LEN07,                 YA_FT_UINT16,               "Au_len07"),
    DPI_FIELD_D(EM_DNS_AU_RDATA07,               YA_FT_STRING,               "Au_rdata07"),
    DPI_FIELD_D(EM_DNS_AU_TYPE08,                YA_FT_UINT16,               "Au_type08"),
    DPI_FIELD_D(EM_DNS_AU_CLS08,                 YA_FT_UINT16,               "Au_cls08"),
    DPI_FIELD_D(EM_DNS_AU_NAME08,                YA_FT_STRING,               "Au_name08"),
    DPI_FIELD_D(EM_DNS_AU_TTL08,                 YA_FT_UINT32,               "Au_ttl08"),
    DPI_FIELD_D(EM_DNS_AU_LEN08,                 YA_FT_UINT16,               "Au_len08"),
    DPI_FIELD_D(EM_DNS_AU_RDATA08,               YA_FT_STRING,               "Au_rdata08"),
    DPI_FIELD_D(EM_DNS_AU_TYPE09,                YA_FT_UINT16,               "Au_type09"),
    DPI_FIELD_D(EM_DNS_AU_CLS09,                 YA_FT_UINT16,               "Au_cls09"),
    DPI_FIELD_D(EM_DNS_AU_NAME09,                YA_FT_STRING,               "Au_name09"),
    DPI_FIELD_D(EM_DNS_AU_TTL09,                 YA_FT_UINT32,               "Au_ttl09"),
    DPI_FIELD_D(EM_DNS_AU_LEN09,                 YA_FT_UINT16,               "Au_len09"),
    DPI_FIELD_D(EM_DNS_AU_RDATA09,               YA_FT_STRING,               "Au_rdata09"),
    DPI_FIELD_D(EM_DNS_AU_TYPE10,                YA_FT_UINT16,               "Au_type10"),
    DPI_FIELD_D(EM_DNS_AU_CLS10,                 YA_FT_UINT16,               "Au_cls10"),
    DPI_FIELD_D(EM_DNS_AU_NAME10,                YA_FT_STRING,               "Au_name10"),
    DPI_FIELD_D(EM_DNS_AU_TTL10,                 YA_FT_UINT32,               "Au_ttl10"),
    DPI_FIELD_D(EM_DNS_AU_LEN10,                 YA_FT_UINT16,               "Au_len10"),
    DPI_FIELD_D(EM_DNS_AU_RDATA10,               YA_FT_STRING,               "Au_rdata10"),
    DPI_FIELD_D(EM_DNS_AU_TYPE11,                YA_FT_UINT16,               "Au_type11"),
    DPI_FIELD_D(EM_DNS_AU_CLS11,                 YA_FT_UINT16,               "Au_cls11"),
    DPI_FIELD_D(EM_DNS_AU_NAME11,                YA_FT_STRING,               "Au_name11"),
    DPI_FIELD_D(EM_DNS_AU_TTL11,                 YA_FT_UINT32,               "Au_ttl11"),
    DPI_FIELD_D(EM_DNS_AU_LEN11,                 YA_FT_UINT16,               "Au_len11"),
    DPI_FIELD_D(EM_DNS_AU_RDATA11,               YA_FT_STRING,               "Au_rdata11"),
    DPI_FIELD_D(EM_DNS_AU_TYPE12,                YA_FT_UINT16,               "Au_type12"),
    DPI_FIELD_D(EM_DNS_AU_CLS12,                 YA_FT_UINT16,               "Au_cls12"),
    DPI_FIELD_D(EM_DNS_AU_NAME12,                YA_FT_STRING,               "Au_name12"),
    DPI_FIELD_D(EM_DNS_AU_TTL12,                 YA_FT_UINT32,               "Au_ttl12"),
    DPI_FIELD_D(EM_DNS_AU_LEN12,                 YA_FT_UINT16,               "Au_len12"),
    DPI_FIELD_D(EM_DNS_AU_RDATA12,               YA_FT_STRING,               "Au_rdata12"),
    DPI_FIELD_D(EM_DNS_AU_TYPE13,                YA_FT_UINT16,               "Au_type13"),
    DPI_FIELD_D(EM_DNS_AU_CLS13,                 YA_FT_UINT16,               "Au_cls13"),
    DPI_FIELD_D(EM_DNS_AU_NAME13,                YA_FT_STRING,               "Au_name13"),
    DPI_FIELD_D(EM_DNS_AU_TTL13,                 YA_FT_UINT32,               "Au_ttl13"),
    DPI_FIELD_D(EM_DNS_AU_LEN13,                 YA_FT_UINT16,               "Au_len13"),
    DPI_FIELD_D(EM_DNS_AU_RDATA13,               YA_FT_STRING,               "Au_rdata13"),
    DPI_FIELD_D(EM_DNS_AU_TYPE14,                YA_FT_UINT16,               "Au_type14"),
    DPI_FIELD_D(EM_DNS_AU_CLS14,                 YA_FT_UINT16,               "Au_cls14"),
    DPI_FIELD_D(EM_DNS_AU_NAME14,                YA_FT_STRING,               "Au_name14"),
    DPI_FIELD_D(EM_DNS_AU_TTL14,                 YA_FT_UINT32,               "Au_ttl14"),
    DPI_FIELD_D(EM_DNS_AU_LEN14,                 YA_FT_UINT16,               "Au_len14"),
    DPI_FIELD_D(EM_DNS_AU_RDATA14,               YA_FT_STRING,               "Au_rdata14"),
    DPI_FIELD_D(EM_DNS_AU_TYPE15,                YA_FT_UINT16,               "Au_type15"),
    DPI_FIELD_D(EM_DNS_AU_CLS15,                 YA_FT_UINT16,               "Au_cls15"),
    DPI_FIELD_D(EM_DNS_AU_NAME15,                YA_FT_STRING,               "Au_name15"),
    DPI_FIELD_D(EM_DNS_AU_TTL15,                 YA_FT_UINT32,               "Au_ttl15"),
    DPI_FIELD_D(EM_DNS_AU_LEN15,                 YA_FT_UINT16,               "Au_len15"),
    DPI_FIELD_D(EM_DNS_AU_RDATA15,               YA_FT_STRING,               "Au_rdata15"),
    DPI_FIELD_D(EM_DNS_AU_OTHER,                 YA_FT_NONE,                 "Au_Other"),
    DPI_FIELD_D(EM_DNS_AD_TYPE00,                YA_FT_UINT16,               "Ad_type00"),
    DPI_FIELD_D(EM_DNS_AD_CLS00,                 YA_FT_UINT16,               "Ad_cls00"),
    DPI_FIELD_D(EM_DNS_AD_NAME00,                YA_FT_STRING,               "Ad_name00"),
    DPI_FIELD_D(EM_DNS_AD_TTL00,                 YA_FT_UINT32,               "Ad_ttl00"),
    DPI_FIELD_D(EM_DNS_AD_LEN00,                 YA_FT_UINT16,               "Ad_len00"),
    DPI_FIELD_D(EM_DNS_AD_RDATA00,               YA_FT_STRING,               "Ad_rdata00"),
    DPI_FIELD_D(EM_DNS_AD_TYPE01,                YA_FT_UINT16,               "Ad_type01"),
    DPI_FIELD_D(EM_DNS_AD_CLS01,                 YA_FT_UINT16,               "Ad_cls01"),
    DPI_FIELD_D(EM_DNS_AD_NAME01,                YA_FT_STRING,               "Ad_name01"),
    DPI_FIELD_D(EM_DNS_AD_TTL01,                 YA_FT_UINT32,               "Ad_ttl01"),
    DPI_FIELD_D(EM_DNS_AD_LEN01,                 YA_FT_UINT16,               "Ad_len01"),
    DPI_FIELD_D(EM_DNS_AD_RDATA01,               YA_FT_STRING,               "Ad_rdata01"),
    DPI_FIELD_D(EM_DNS_AD_TYPE02,                YA_FT_UINT16,               "Ad_type02"),
    DPI_FIELD_D(EM_DNS_AD_CLS02,                 YA_FT_UINT16,               "Ad_cls02"),
    DPI_FIELD_D(EM_DNS_AD_NAME02,                YA_FT_STRING,               "Ad_name02"),
    DPI_FIELD_D(EM_DNS_AD_TTL02,                 YA_FT_UINT32,               "Ad_ttl02"),
    DPI_FIELD_D(EM_DNS_AD_LEN02,                 YA_FT_UINT16,               "Ad_len02"),
    DPI_FIELD_D(EM_DNS_AD_RDATA02,               YA_FT_STRING,               "Ad_rdata02"),
    DPI_FIELD_D(EM_DNS_AD_TYPE03,                YA_FT_UINT16,               "Ad_type03"),
    DPI_FIELD_D(EM_DNS_AD_CLS03,                 YA_FT_UINT16,               "Ad_cls03"),
    DPI_FIELD_D(EM_DNS_AD_NAME03,                YA_FT_STRING,               "Ad_name03"),
    DPI_FIELD_D(EM_DNS_AD_TTL03,                 YA_FT_UINT32,               "Ad_ttl03"),
    DPI_FIELD_D(EM_DNS_AD_LEN03,                 YA_FT_UINT16,               "Ad_len03"),
    DPI_FIELD_D(EM_DNS_AD_RDATA03,               YA_FT_STRING,               "Ad_rdata03"),
    DPI_FIELD_D(EM_DNS_AD_TYPE04,                YA_FT_UINT16,               "Ad_type04"),
    DPI_FIELD_D(EM_DNS_AD_CLS04,                 YA_FT_UINT16,               "Ad_cls04"),
    DPI_FIELD_D(EM_DNS_AD_NAME04,                YA_FT_STRING,               "Ad_name04"),
    DPI_FIELD_D(EM_DNS_AD_TTL04,                 YA_FT_UINT32,               "Ad_ttl04"),
    DPI_FIELD_D(EM_DNS_AD_LEN04,                 YA_FT_UINT16,               "Ad_len04"),
    DPI_FIELD_D(EM_DNS_AD_RDATA04,               YA_FT_STRING,               "Ad_rdata04"),
    DPI_FIELD_D(EM_DNS_AD_TYPE05,                YA_FT_UINT16,               "Ad_type05"),
    DPI_FIELD_D(EM_DNS_AD_CLS05,                 YA_FT_UINT16,               "Ad_cls05"),
    DPI_FIELD_D(EM_DNS_AD_NAME05,                YA_FT_STRING,               "Ad_name05"),
    DPI_FIELD_D(EM_DNS_AD_TTL05,                 YA_FT_UINT32,               "Ad_ttl05"),
    DPI_FIELD_D(EM_DNS_AD_LEN05,                 YA_FT_UINT16,               "Ad_len05"),
    DPI_FIELD_D(EM_DNS_AD_RDATA05,               YA_FT_STRING,               "Ad_rdata05"),
    DPI_FIELD_D(EM_DNS_AD_TYPE06,                YA_FT_UINT16,               "Ad_type06"),
    DPI_FIELD_D(EM_DNS_AD_CLS06,                 YA_FT_UINT16,               "Ad_cls06"),
    DPI_FIELD_D(EM_DNS_AD_NAME06,                YA_FT_STRING,               "Ad_name06"),
    DPI_FIELD_D(EM_DNS_AD_TTL06,                 YA_FT_UINT32,               "Ad_ttl06"),
    DPI_FIELD_D(EM_DNS_AD_LEN06,                 YA_FT_UINT16,               "Ad_len06"),
    DPI_FIELD_D(EM_DNS_AD_RDATA06,               YA_FT_STRING,               "Ad_rdata06"),
    DPI_FIELD_D(EM_DNS_AD_TYPE07,                YA_FT_UINT16,               "Ad_type07"),
    DPI_FIELD_D(EM_DNS_AD_CLS07,                 YA_FT_UINT16,               "Ad_cls07"),
    DPI_FIELD_D(EM_DNS_AD_NAME07,                YA_FT_STRING,               "Ad_name07"),
    DPI_FIELD_D(EM_DNS_AD_TTL07,                 YA_FT_UINT32,               "Ad_ttl07"),
    DPI_FIELD_D(EM_DNS_AD_LEN07,                 YA_FT_UINT16,               "Ad_len07"),
    DPI_FIELD_D(EM_DNS_AD_RDATA07,               YA_FT_STRING,               "Ad_rdata07"),
    DPI_FIELD_D(EM_DNS_AD_TYPE08,                YA_FT_UINT16,               "Ad_type08"),
    DPI_FIELD_D(EM_DNS_AD_CLS08,                 YA_FT_UINT16,               "Ad_cls08"),
    DPI_FIELD_D(EM_DNS_AD_NAME08,                YA_FT_STRING,               "Ad_name08"),
    DPI_FIELD_D(EM_DNS_AD_TTL08,                 YA_FT_UINT32,               "Ad_ttl08"),
    DPI_FIELD_D(EM_DNS_AD_LEN08,                 YA_FT_UINT16,               "Ad_len08"),
    DPI_FIELD_D(EM_DNS_AD_RDATA08,               YA_FT_STRING,               "Ad_rdata08"),
    DPI_FIELD_D(EM_DNS_AD_TYPE09,                YA_FT_UINT16,               "Ad_type09"),
    DPI_FIELD_D(EM_DNS_AD_CLS09,                 YA_FT_UINT16,               "Ad_cls09"),
    DPI_FIELD_D(EM_DNS_AD_NAME09,                YA_FT_STRING,               "Ad_name09"),
    DPI_FIELD_D(EM_DNS_AD_TTL09,                 YA_FT_UINT32,               "Ad_ttl09"),
    DPI_FIELD_D(EM_DNS_AD_LEN09,                 YA_FT_UINT16,               "Ad_len09"),
    DPI_FIELD_D(EM_DNS_AD_RDATA09,               YA_FT_STRING,               "Ad_rdata09"),
    DPI_FIELD_D(EM_DNS_AD_TYPE10,                YA_FT_UINT16,               "Ad_type10"),
    DPI_FIELD_D(EM_DNS_AD_CLS10,                 YA_FT_UINT16,               "Ad_cls10"),
    DPI_FIELD_D(EM_DNS_AD_NAME10,                YA_FT_STRING,               "Ad_name10"),
    DPI_FIELD_D(EM_DNS_AD_TTL10,                 YA_FT_UINT32,               "Ad_ttl10"),
    DPI_FIELD_D(EM_DNS_AD_LEN10,                 YA_FT_UINT16,               "Ad_len10"),
    DPI_FIELD_D(EM_DNS_AD_RDATA10,               YA_FT_STRING,               "Ad_rdata10"),
    DPI_FIELD_D(EM_DNS_AD_TYPE11,                YA_FT_UINT16,               "Ad_type11"),
    DPI_FIELD_D(EM_DNS_AD_CLS11,                 YA_FT_UINT16,               "Ad_cls11"),
    DPI_FIELD_D(EM_DNS_AD_NAME11,                YA_FT_STRING,               "Ad_name11"),
    DPI_FIELD_D(EM_DNS_AD_TTL11,                 YA_FT_UINT32,               "Ad_ttl11"),
    DPI_FIELD_D(EM_DNS_AD_LEN11,                 YA_FT_UINT16,               "Ad_len11"),
    DPI_FIELD_D(EM_DNS_AD_RDATA11,               YA_FT_STRING,               "Ad_rdata11"),
    DPI_FIELD_D(EM_DNS_AD_TYPE12,                YA_FT_UINT16,               "Ad_type12"),
    DPI_FIELD_D(EM_DNS_AD_CLS12,                 YA_FT_UINT16,               "Ad_cls12"),
    DPI_FIELD_D(EM_DNS_AD_NAME12,                YA_FT_STRING,               "Ad_name12"),
    DPI_FIELD_D(EM_DNS_AD_TTL12,                 YA_FT_UINT32,               "Ad_ttl12"),
    DPI_FIELD_D(EM_DNS_AD_LEN12,                 YA_FT_UINT16,               "Ad_len12"),
    DPI_FIELD_D(EM_DNS_AD_RDATA12,               YA_FT_STRING,               "Ad_rdata12"),
    DPI_FIELD_D(EM_DNS_AD_TYPE13,                YA_FT_UINT16,               "Ad_type13"),
    DPI_FIELD_D(EM_DNS_AD_CLS13,                 YA_FT_UINT16,               "Ad_cls13"),
    DPI_FIELD_D(EM_DNS_AD_NAME13,                YA_FT_STRING,               "Ad_name13"),
    DPI_FIELD_D(EM_DNS_AD_TTL13,                 YA_FT_UINT32,               "Ad_ttl13"),
    DPI_FIELD_D(EM_DNS_AD_LEN13,                 YA_FT_UINT16,               "Ad_len13"),
    DPI_FIELD_D(EM_DNS_AD_RDATA13,               YA_FT_STRING,               "Ad_rdata13"),
    DPI_FIELD_D(EM_DNS_AD_TYPE14,                YA_FT_UINT16,               "Ad_type14"),
    DPI_FIELD_D(EM_DNS_AD_CLS14,                 YA_FT_UINT16,               "Ad_cls14"),
    DPI_FIELD_D(EM_DNS_AD_NAME14,                YA_FT_STRING,               "Ad_name14"),
    DPI_FIELD_D(EM_DNS_AD_TTL14,                 YA_FT_UINT32,               "Ad_ttl14"),
    DPI_FIELD_D(EM_DNS_AD_LEN14,                 YA_FT_UINT32,               "Ad_len14"),
    DPI_FIELD_D(EM_DNS_AD_RDATA14,               YA_FT_STRING,               "Ad_rdata14"),
    DPI_FIELD_D(EM_DNS_AD_TYPE15,                YA_FT_UINT16,               "Ad_type15"),
    DPI_FIELD_D(EM_DNS_AD_CLS15,                 YA_FT_UINT16,               "Ad_cls15"),
    DPI_FIELD_D(EM_DNS_AD_NAME15,                YA_FT_STRING,               "Ad_name15"),
    DPI_FIELD_D(EM_DNS_AD_TTL15,                 YA_FT_UINT32,               "Ad_ttl15"),
    DPI_FIELD_D(EM_DNS_AD_LEN15,                 YA_FT_UINT16,               "Ad_len15"),
    DPI_FIELD_D(EM_DNS_AD_RDATA15,               YA_FT_STRING,               "Ad_rdata15"),
    DPI_FIELD_D(EM_DNS_AD_OTHER,                 YA_FT_NONE,                 "Ad_Other"),
    DPI_FIELD_D(EM_DNS_FLAGS,                    YA_FT_UINT16,               "flags"),
    DPI_FIELD_D(EM_DNS_JUDGE,                    YA_FT_UINT8,                "dns_judge"), //是否为标准DNS
    DPI_FIELD_D(EM_DNS_DOMAIN,                   YA_FT_STRING,               "dns_domain"),
    DPI_FIELD_D(EM_DNS_DOMAINIP,                 YA_FT_STRING,               "dns_domainip"),
    DPI_FIELD_D(EM_DNS_QRY,                      YA_FT_STRING,               "dns_qry"),
    DPI_FIELD_D(EM_DNS_ANS,                      YA_FT_STRING,               "dns_ans"),


    DPI_FIELD_D(EM_DNS_ANS_CNAME,                YA_FT_STRING,               "ansCname"),
    DPI_FIELD_D(EM_DNS_ANS_CNAME_CNT,            YA_FT_UINT16,               "ansCnameCnt"),
    DPI_FIELD_D(EM_DNS_ANS_IPV6,                 YA_FT_STRING,               "ansIPv6"),
    DPI_FIELD_D(EM_DNS_ANS_AIP,                  YA_FT_STRING,               "Aip"),
    DPI_FIELD_D(EM_DNS_ANS_AIP_CNT,              YA_FT_UINT16,               "AipCnt"),
    DPI_FIELD_D(EM_DNS_ANS_AIP_ASN,              YA_FT_STRING,               "aipAsn"),
    DPI_FIELD_D(EM_DNS_ANS_API_COUNTRY,          YA_FT_STRING,               "aipCountry"),
    DPI_FIELD_D(EM_DNS_ANS_MAIL_SRV_HOST,        YA_FT_STRING,               "mailSrvHost"),
    DPI_FIELD_D(EM_DNS_ANS_MAIL_SRV_HOST_CNT,    YA_FT_UINT16,               "mailSrvHostcnt"),
    DPI_FIELD_D(EM_DNS_ANS_MAIL_SRV_IP,          YA_FT_STRING,               "mailSrvIp"),
    DPI_FIELD_D(EM_DNS_ANS_MAIL_SRV_IP_CNT,      YA_FT_UINT16,               "mailSrvIPCnt"),
    DPI_FIELD_D(EM_DNS_ANS_MAIL_SRV_ASN,         YA_FT_STRING,               "mailSrvAsn"),
    DPI_FIELD_D(EM_DNS_ANS_MAIL_SRV_COUNTRY,     YA_FT_STRING,               "mailSrvCountry"),
    DPI_FIELD_D(EM_DNS_ANS_NAME_SRV_HOST,        YA_FT_STRING,               "nameSrvHost"),
    DPI_FIELD_D(EM_DNS_ANS_NAME_SRV_HOST_CNT,    YA_FT_UINT16,               "nameSrvHostCnt"),
    DPI_FIELD_D(EM_DNS_ANS_NAME_SRV_IP,          YA_FT_STRING,               "nameSrvIp"),
    DPI_FIELD_D(EM_DNS_ANS_NAME_SRV_IP_CNT,      YA_FT_UINT16,               "nameSrvIPCnt"),
    DPI_FIELD_D(EM_DNS_ANS_NAME_SRV_ASN,         YA_FT_STRING,               "nameSrvAsn"),
    DPI_FIELD_D(EM_DNS_ANS_NAME_SRV_COUNTTRY,    YA_FT_STRING,               "nameSrvCountry"),
    DPI_FIELD_D(EM_DNS_ANS_DNS_SPF,              YA_FT_STRING,               "DNSSPF"),
    DPI_FIELD_D(EM_DNS_ANS_DNS_TEX,              YA_FT_STRING,               "DNSTEX"),
    DPI_FIELD_D(EM_DNS_ANS_TYPES,                YA_FT_STRING,               "ansTypes"),


};




struct dns_request_info {
    uint16_t req_type;
    uint16_t req_class;
    uint16_t req_name_len;
    uint8_t  req_name[MAXDNAME];
};

struct dns_response_info {
    uint16_t res_type;
    uint16_t res_class;
    uint16_t res_name_len;
    uint16_t res_data_len;
    uint32_t res_ttl;
    uint8_t  res_data[MAXDNAME];
    uint8_t  res_name[MAXDNAME];
};

struct dns_au_response_info {
    uint16_t res_type;
    uint16_t res_class;
    uint16_t res_name_len;
    uint16_t res_data_len;
    uint32_t res_ttl;
    uint8_t  res_data[MAXDNAME];
    uint8_t  res_name[MAXDNAME];
};

struct dns_ad_response_info {
    uint16_t res_type;
    uint16_t res_class;
    uint16_t res_name_len;
    uint16_t res_data_len;
    uint32_t res_ttl;
    uint8_t  res_data[MAXDNAME];
    uint8_t  res_name[MAXDNAME];
};
struct dns_info {
    uint16_t tran_id;
    uint16_t flags;
    int req_num;
    int res_num;
    int au_res_num;
    int ad_res_num;
    struct dns_request_info     req[MAX_DNS_REQUESTS];
    struct dns_response_info    res[MAX_DNS_RESPONSES];
    struct dns_au_response_info au[MAX_DNS_RESPONSES];
    struct dns_ad_response_info ad[MAX_DNS_RESPONSES];

    const uint8_t *ans_cname[MAX_META_NUM];
    uint32_t ans_cname_len[MAX_META_NUM];

	uint16_t ans_cname_cnt;

	const uint8_t *ans_ipv6[MAX_META_NUM];
	uint32_t ans_ipv6_len[MAX_META_NUM];
    uint16_t ans_ipv6_cnt;

	const uint8_t *aip[MAX_META_NUM];
	uint32_t aip_len[MAX_META_NUM];

	uint16_t aip_cnt;

	const uint8_t *aip_asn;
	uint32_t aip_asn_len;
	const uint8_t *aip_country;
	uint32_t aip_country_len;
	const uint8_t *mail_srv_host[MAX_META_NUM];
	uint32_t mail_srv_host_len[MAX_META_NUM];

	uint16_t mail_srv_host_cnt;

	const uint8_t *mail_srv_ip[MAX_META_NUM];
	uint32_t mail_srv_ip_len[MAX_META_NUM];

	uint16_t mail_srv_ip_cnt;

	const uint8_t *mail_srv_asn;
	uint32_t mail_srv_asn_len;

	const uint8_t *mail_srv_country;
	uint32_t mail_srv_country_len;

	const uint8_t *name_srv_host[MAX_META_NUM];
	uint32_t name_srv_host_len[MAX_META_NUM];

	uint16_t name_srv_host_cnt;

	const uint8_t *name_srv_ip[MAX_META_NUM];
	uint32_t name_srv_ip_len[MAX_META_NUM];

	uint16_t name_srv_ip_cnt;

	char name_srv_asn[256];

	char name_srv_country[256];

	const uint8_t *dnstex;
	uint32_t dnstex_len;


};

void identify_dns(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);
int  dissect_dns_udp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int  get_dns_name(const uint8_t *payload, const uint16_t payload_len, uint16_t *name_offset, uint8_t *name, uint16_t *name_len);

/*
*dns的识别
*/
void identify_dns(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_DNS] == 0)
        return;

    int x;
    uint8_t is_query;
    uint16_t s_port = 0, d_port = 0;

    if(flow->tuple.inner.proto == IPPROTO_UDP || flow->tuple.inner.proto == IPPROTO_TCP) {
        s_port = ntohs(flow->tuple.inner.port_src);
        d_port = ntohs(flow->tuple.inner.port_dst);
        x = 0;
    } else {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_DNS);
        return;
    }

    //if ((s_port == 53 || d_port == 53 || d_port == 5355)
    //        && (payload_len > sizeof(struct dpi_dns_packet_header) + x)) {
    if((payload_len > sizeof(struct dpi_dns_packet_header) + x)) {
        struct dpi_dns_packet_header dns_header;
        int invalid = 0;

        memcpy(&dns_header, (const struct dpi_dns_packet_header*) &payload[x], sizeof(struct dpi_dns_packet_header));
        dns_header.tr_id = ntohs(dns_header.tr_id);
        dns_header.flags = ntohs(dns_header.flags);
        dns_header.num_queries = ntohs(dns_header.num_queries);
        dns_header.num_answers = ntohs(dns_header.num_answers);
        dns_header.authority_rrs = ntohs(dns_header.authority_rrs);
        dns_header.additional_rrs = ntohs(dns_header.additional_rrs);
        x += sizeof(struct dpi_dns_packet_header);

        /* 0x0000 QUERY */
        if((dns_header.flags & FLAGS_MASK) == 0x0000)
            is_query = 1;
        /* 0x8000 RESPONSE */
        else if((dns_header.flags & FLAGS_MASK) == 0x8000)
            is_query = 0;
        else
            invalid = 1;

        if(!invalid) {
            if(is_query) {
                /* DNS Request */
                if((dns_header.num_queries > 0) && (dns_header.num_queries <= MAX_DNS_REQUESTS)
                        && (((dns_header.flags & 0x2800) == 0x2800 /* Dynamic DNS Update */)
                        || ((dns_header.num_answers == 0) && (dns_header.authority_rrs == 0)))) {
                    /* This is a good query */
/*
                    if(dns_header.num_queries > 0) {
                        while(x < flow->packet.payload_packet_len) {
                            if(flow->packet.payload[x] == '\0') {
                                x++;
                                flow->protos.dns.query_type = get16(&x, flow->packet.payload);
                                break;
                            } else
                                x++;
                        }
                    }
*/
                } else
                    invalid = 1;

            } else {
                /* DNS Reply */

        //        flow->protos.dns.reply_code = dns_header.flags & 0x0F;

                if((dns_header.num_queries > 0) && (dns_header.num_queries <= MAX_DNS_REQUESTS) /* Don't assume that num_queries must be zero */
                        && (((dns_header.num_answers > 0) && (dns_header.num_answers <= MAX_DNS_REQUESTS))
                        || ((dns_header.authority_rrs > 0) && (dns_header.authority_rrs <= MAX_DNS_REQUESTS))
                        || ((dns_header.additional_rrs > 0) && (dns_header.additional_rrs <= MAX_DNS_REQUESTS)))
                        ) {
                    /* This is a good reply */
                        /*
                    if(ndpi_struct->dns_dissect_response) {
                        x++;

                        if(flow->packet.payload[x] != '\0') {
                            while((x < flow->packet.payload_packet_len)
                                && (flow->packet.payload[x] != '\0')) {
                                x++;
                            }

                            x++;
                        }

                        x += 4;

                        if(dns_header.num_answers > 0) {
                            uint16_t rsp_type;
                            uint16_t num;

                            for(num = 0; num < dns_header.num_answers; num++) {
                                uint16_t data_len;

                                if((x+6) >= flow->packet.payload_packet_len) {
                                    break;
                                }

                                if((data_len = getNameLength(x, flow->packet.payload, flow->packet.payload_packet_len)) == 0) {
                                    break;
                                } else
                                    x += data_len;

                                rsp_type = get16(&x, flow->packet.payload);
                                flow->protos.dns.rsp_type = rsp_type;
                                break;
                            }
                        }
                    }*/
                }else
                    invalid = 1;
            }
        }

        if (!invalid) {
            flow->real_protocol_id = PROTOCOL_DNS;
        } else {
            DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_DNS);
        }
    }
}

/*
*获取dns的域名名字，目前只支持字符串和指针法
*/
int get_dns_name(const uint8_t *payload, const uint16_t payload_len, uint16_t *name_offset, uint8_t *name, uint16_t *name_len)
{
    uint8_t *np = name;
    int     pointers_count  = 0;
    int     component_len;
    int     indir_offset;
    int     maxname = *name_len;
    int     offset = *name_offset;
    int     len = -1;

    *name_len = 0;
    maxname--;   /* reserve space for the trailing '\0' */

    for (;;) {
        if (offset >= payload_len || *name_len >= maxname)
            break;

        component_len = get_uint8_t(payload, offset);
        offset++;
        if (component_len == 0) {
            break;
        }

        switch (component_len & 0xc0) {
            case 0x00:
                /* Not the first component - put in a '.'.*/
                if (*name_len != 0 && *name_len < maxname) {
                    *np++ = '.';
                    (*name_len)++;
                }
                while (component_len > 0) {
                    if (offset >= payload_len)
                        break;
                    if (*name_len < maxname) {
                        *np++ = get_uint8_t(payload, offset);
                        (*name_len)++;
                    }
                    component_len--;
                    offset++;
                }
                break;

            case 0x40:
                //todo
                return -1;
                break;

            case 0x80:
                return -1;
                break;

            case 0xc0:
                /* Pointer. */
                indir_offset = ((component_len & ~0xc0) << 8) | get_uint8_t(payload, offset);
                offset++;
                pointers_count++;

                /* If "len" is negative, we are still working on the original name,
                not something pointed to by a pointer, and so we should set "len"
                to the length of the original name. */
                if (len < 0) {
                    len = offset - *name_offset;
                }
                /*
                * If we find a pointer to itself, it is a trivial loop. Otherwise if we
                * processed a large number of pointers, assume an indirect loop.
                */
                if (indir_offset == offset + 2 || pointers_count > MAXDNAME / 4) {
    //                *name="<Name contains a pointer that loops>";
    //                *name_len = (guint)strlen(*name);
                    return -1;
                }

                offset = indir_offset;
                break;   /* now continue processing from there */
        }
    }

    if (len < 0)
        len = offset - *name_offset;

    *name_offset = (uint16_t)len;

    if((*name_len) && (!g_config.dns_filter_only_request) && (g_config.dns_whitelist_switch || g_config.dns_blacklist_switch)){
        long ret = (long)white_filter((const char*)name, *name_len, dns_filter_table);
        if(ret == 0 && g_config.dns_default_switch)
            return -1;
        else if(ret == 2)
            return -1;
    }

    return 0;
}

static int get_dns_name_type_class(struct flow_info *flow,const uint8_t *payload, const uint16_t payload_len, uint16_t *offset,
        uint8_t *name, uint16_t *name_len, uint16_t *type, uint16_t *dns_class)
{
    uint16_t name_offset = *offset;
    long ret;

    ret = get_dns_name(payload, payload_len, &name_offset, name, name_len);
    if (ret < 0)
        return -1;

    if((*name_len) && g_config.dns_filter_only_request && (g_config.dns_whitelist_switch || g_config.dns_blacklist_switch)){
        ret = (long)white_filter((const char*)name, *name_len, dns_filter_table);
        if(ret == 0){
            if(g_config.dns_default_switch){
                flow->blacklist_flag = 1;
                return -1;
            }
            else{
                flow->whitelist_flag = 1;
            }
        }
        else if(ret == 1)
            flow->whitelist_flag = 1;
        else if(ret == 2){
            flow->blacklist_flag = 1;
            return -1;
        }
    }

    *offset += name_offset;

    *type = ntohs(get_uint16_t(payload, *offset));
    *offset += 2;

    *dns_class = ntohs(get_uint16_t(payload, *offset));
    *offset += 2;

    return 0;
}

static int dissect_dns_answer(const uint8_t *payload, const uint16_t payload_len, uint16_t *offset,
        uint8_t *name, uint16_t *name_len, uint16_t *type, uint16_t *dns_class, uint32_t *ttl, uint8_t *data, uint16_t *data_len)
{
    uint16_t name_offset = *offset;
    int retval;
    uint16_t res_data_len;

    retval = get_dns_name(payload, payload_len, &name_offset, name, name_len);
    if (retval < 0)
        return -1;
    *offset += name_offset;

    if (*offset + 10 > payload_len)
        return -1;

    *type = ntohs(get_uint16_t(payload, *offset));
    *offset += 2;

    *dns_class = ntohs(get_uint16_t(payload, *offset));
    *offset += 2;

    *ttl = ntohl(get_uint32_t(payload, *offset));
    *offset += 4;

    res_data_len = ntohs(get_uint16_t(payload, *offset));
    *offset += 2;

    if (*offset + res_data_len > payload_len)
        return -1;

    switch (*type) {
        case T_A:
            {
                //uint32_t ip_addr = get_uint32_t(payload, *offset);
                char ip_str[64];
                snprintf(ip_str, sizeof(ip_str), "%u.%u.%u.%u", payload[*offset], payload[*offset + 1], payload[*offset + 2], payload[*offset + 3]);
                *data_len = strlen(ip_str);
                memcpy(data, ip_str, *data_len);
            }
            break;
        case T_NS:
            {
                name_offset = *offset;
                retval = get_dns_name(payload, payload_len, &name_offset, data, data_len);
                if (retval < 0)
                    return -1;
            }
            break;
        case T_CNAME:
            {
                name_offset = *offset;
                retval = get_dns_name(payload, payload_len, &name_offset, data, data_len);
                if (retval < 0)
                    return -1;
            }
            break;

        case T_AAAA:
            {
                if (res_data_len != 16)
                    break;
                struct in6_addr addr;
                memcpy(&addr, &payload[*offset], 16);
                char ip_str[64];
                memset(ip_str, 0, sizeof(ip_str));
                inet_ntop(AF_INET6, &addr, ip_str, sizeof(ip_str));
                *data_len = strlen(ip_str);
                memcpy(data, ip_str, *data_len);
            }
            break;
        case T_MX:
            {
                uint16_t preference = ntohs(get_uint16_t(payload, *offset));
                name_offset = *offset + 2;
                retval = get_dns_name(payload, payload_len, &name_offset, data, data_len);
                if (retval < 0)
                    return -1;
            }
            break;
        case T_TXT:
            {
                *data_len = get_uint8_t(payload, *offset);
                *offset = *offset + 1;
                memcpy(data, payload + *offset, *data_len);
            }
            break;
        default:
            break;
    }

    *offset += res_data_len;
    return 0;
}

static int dissect_query_records(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len, uint16_t *offset, int count, struct dns_info *info)
{
    int i = 0;
    int retval;

    while (i < count) {
        if (i >= MAX_DNS_REQUESTS) {
            //    i = MAX_DNS_REQUESTS - 1;
            break;
        }
        info->req[i].req_name_len = MAXDNAME;
        retval = get_dns_name_type_class(flow, payload, payload_len, offset, info->req[i].req_name, &info->req[i].req_name_len,
                &info->req[i].req_type, &info->req[i].req_class);

        if (retval < 0)
            return -1;
        i++;
        info->req_num = i;
    }
    return 0;
}

static int dissect_answer_records(const uint8_t *payload, const uint16_t payload_len, uint16_t *offset, int count, struct dns_info *info)
{
    int i = 0;
    int retval;

    while (i < count) {
        if (i >= MAX_DNS_RESPONSES) {
            //i = MAX_DNS_RESPONSES - 1;
            break;
        }
        info->res[i].res_name_len = MAXDNAME;
        info->res[i].res_data_len = MAXDNAME;
        retval = dissect_dns_answer(payload, payload_len, offset, info->res[i].res_name, &info->res[i].res_name_len,
                &info->res[i].res_type, &info->res[i].res_class, &info->res[i].res_ttl, info->res[i].res_data, &info->res[i].res_data_len);
        if (retval < 0)
            return -1;

        i++;
        info->res_num = i;
    }
    return 0;
}

static int dissect_au_answer_records(const uint8_t *payload, const uint16_t payload_len, uint16_t *offset, int count, struct dns_info *info)
{
    int i = 0;
    int retval;

    while(i < count){
        if(i >= MAX_DNS_RESPONSES) break;
        info->au[i].res_name_len = MAXDNAME;
        info->au[i].res_data_len = MAXDNAME;

        retval = dissect_dns_answer(payload, payload_len, offset, info->au[i].res_name, &info->au[i].res_name_len,
                &info->au[i].res_type, &info->au[i].res_class, &info->au[i].res_ttl, info->au[i].res_data, &info->au[i].res_data_len);

        if(retval < 0)    return -1;

        i++;
        info->au_res_num = i;
    }
    return 0;
}

static int dissect_ad_answer_records(const uint8_t *payload, const uint16_t payload_len, uint16_t *offset, int count, struct dns_info *info)
{
    int i = 0;
    int retval;

    while(i < count){
        if(i >= MAX_DNS_RESPONSES)
            break;
        info->ad[i].res_name_len = MAXDNAME;
        info->ad[i].res_data_len = MAXDNAME;

        retval = dissect_dns_answer(payload, payload_len, offset, info->ad[i].res_name, &info->ad[i].res_name_len,
                &info->ad[i].res_type, &info->ad[i].res_class, &info->ad[i].res_ttl, info->ad[i].res_data, &info->ad[i].res_data_len);

        if(retval < 0)    return -1;

        i++;
        info->ad_res_num = i;
    }
    return 0;
}

static void get_res_type_from_response(char *tmp, int *len, int max_len, void *res_arr, int res_num){
    if(!tmp || !res_arr || !res_num)
	    return;

    int ret,i;
    struct dns_response_info *res = (struct dns_response_info *)res_arr;
    for(i=0; i<res_num; ++i){
	    if(*len==0)
            ret = snprintf(tmp+*len, 2048-*len,"%04x", res[i].res_type);
	    else
            ret = snprintf(tmp+*len, 2048-*len," %04x", res[i].res_type);
        *len += ret;
        if(ret< 0 || *len > 2048) break;
    }
    return;
}

static void
dns_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct dns_info *info, int *idx, int i)
{
    char tmp[2048];
    int ret,len;
    switch(i){
    case EM_DNS_QD_TYPE00:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->req[0].req_type);
        break;
    case EM_DNS_QD_NAME00:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->req[0].req_name, info->req[0].req_name_len);
        break;
    case EM_DNS_AN_NAME00:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->res[0].res_name, info->res[0].res_name_len);
        break;
    case EM_DNS_AN_RDATA00:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->res[0].res_data, info->res[0].res_data_len);
        break;
    case EM_DNS_AU_TYPE00:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->au[0].res_type);
        break;
    case EM_DNS_AU_RDATA00:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->au[0].res_data, info->au[0].res_data_len);
        break;
    case EM_DNS_AD_TYPE00:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ad[0].res_type);
        break;
    case EM_DNS_AD_RDATA00:
         write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->ad[0].res_data, info->ad[0].res_data_len);
        break;
    case EM_DNS_IDENTIFICATION:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,  info->tran_id);
        break;
    case EM_DNS_FLAGS:
        write_one_hexnum_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->flags);
        break;
    case EM_DNS_ANSWER_RRS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,  info->res_num);
        break;
    case EM_DNS_AUTHORIZATION_RRS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,  info->au_res_num);
        break;
    case EM_DNS_ADDITIONAL_RRS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,  info->ad_res_num);
        break;

    case EM_DNS_ANS_CNAME:
        len=0;
        for(i=0; i<info->ans_cname_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->ans_cname[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->ans_cname[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_CNAME_CNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ans_cname_cnt);
        break;
    case EM_DNS_ANS_IPV6:
        len=0;
        for(i=0; i<info->ans_ipv6_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->ans_ipv6[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->ans_ipv6[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_AIP:
        len=0;
        for(i=0; i<info->aip_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->aip[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->aip[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_AIP_CNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->aip_cnt);
        break;
    case EM_DNS_ANS_AIP_ASN:
        if(info->aip_asn)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->aip_asn, info->aip_country_len);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_API_COUNTRY:
        if(info->aip_country)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->aip_country, info->aip_country_len);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_MAIL_SRV_HOST:
        len=0;
        for(i=0; i<info->mail_srv_host_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->mail_srv_host[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->mail_srv_host[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_MAIL_SRV_HOST_CNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->mail_srv_host_cnt);
        break;
    case EM_DNS_ANS_MAIL_SRV_IP:
        len=0;
        for(i=0; i<info->mail_srv_ip_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->mail_srv_ip[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->mail_srv_ip[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_MAIL_SRV_IP_CNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->mail_srv_ip_cnt);
        break;
    case EM_DNS_ANS_MAIL_SRV_ASN:
        if(info->mail_srv_asn)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->mail_srv_asn, info->mail_srv_asn_len);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_MAIL_SRV_COUNTRY:
        if(info->mail_srv_country)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->mail_srv_country, info->mail_srv_country_len);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_NAME_SRV_HOST:
        len=0;
        for(i=0; i<info->name_srv_host_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->name_srv_host[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->name_srv_host[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_NAME_SRV_HOST_CNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->name_srv_host_cnt);
        break;
    case EM_DNS_ANS_NAME_SRV_IP:
        len=0;
        for(i=0; i<info->name_srv_ip_cnt; ++i){
	        if(i==0)
                ret = snprintf(tmp+len, 2048-len,"%s", info->name_srv_ip[i]);
	        else
                ret = snprintf(tmp+len, 2048-len,",%s", info->name_srv_ip[i]);
                len += ret;
                if(ret< 0 || len > 2048) break;
        }
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;
    case EM_DNS_ANS_NAME_SRV_IP_CNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->name_srv_ip_cnt);
        break;
    case EM_DNS_ANS_NAME_SRV_ASN:
        if(strlen(info->name_srv_asn))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->name_srv_asn, strlen(info->name_srv_asn));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_NAME_SRV_COUNTTRY:
       if(strlen(info->name_srv_country))
           write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->name_srv_country, strlen(info->name_srv_country));
       else
           write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_DNS_SPF:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_DNS_TEX:
        if(info->dnstex)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (char *)info->dnstex, info->dnstex_len);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_DNS_ANS_TYPES:
        len=0;
	    get_res_type_from_response(tmp, &len, sizeof(tmp),(void*)info->res, info->res_num);
	    get_res_type_from_response(tmp, &len, sizeof(tmp),(void*)info->au, info->au_res_num);
	    get_res_type_from_response(tmp, &len, sizeof(tmp),(void*)info->ad, info->ad_res_num);
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, DPI_MIN(len, 2048));
        break;

    default:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;

    }

    return;
}

static int write_dns_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    struct dns_info *info=(struct dns_info *)field_info;
    if(!info){
        return PKT_DROP;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "dns");

    for(i=0; i<EM_DNS_MAX;i++){
        dns_field_element(log_ptr,flow, direction, info, &idx,i);
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_DNS;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}


static void get_value_from_answer_info(struct dns_info *info)
{
    if(!info)
	    return;

    int i,j;
    for(i=0; i < info->res_num; i++){
        switch (info->res[i].res_type) {
            case T_A:
                if(info->aip_cnt < MAX_META_NUM) {
                    info->aip[info->aip_cnt] = info->res[i].res_data;
                    info->aip_len[info->aip_cnt] = info->res[i].res_data_len;
                    info->aip_cnt++;
                }
                break;
            case T_NS:
                if(info->name_srv_host_cnt < MAX_META_NUM) {
                    for(j=0;j < info->name_srv_host_cnt; j++){
                        if(memcmp(info->res[i].res_data, info->name_srv_host[j], (info->name_srv_host_len[j] > info->res[i].res_data_len?info->name_srv_host_len[j]:info->res[i].res_data_len)) == 0)
                            break;
                    }
                    if(j==info->name_srv_host_cnt){
                        info->name_srv_host[info->name_srv_host_cnt] = info->res[i].res_data;
                        info->name_srv_host_len[info->name_srv_host_cnt] = info->res[i].res_data_len;
                        info->name_srv_host_cnt++;
                    }
                }
                break;
            case T_CNAME:
                if(info->ans_cname_cnt < MAX_META_NUM) {
                    for(j=0;j < info->ans_cname_cnt; j++){
                        if(memcmp(info->res[i].res_data, info->ans_cname[j], (info->ans_cname_len[j] > info->res[i].res_data_len?info->ans_cname_len[j]:info->res[i].res_data_len)) == 0)
                            break;
                    }
                    if(j==info->ans_cname_cnt){
                        info->ans_cname[info->ans_cname_cnt] = info->res[i].res_data;
                        info->ans_cname_len[info->ans_cname_cnt] = info->res[i].res_data_len;
                        info->ans_cname_cnt++;
                    }
                }
                break;

            case T_AAAA:
                if(info->ans_ipv6_cnt < MAX_META_NUM) {
                    info->ans_ipv6[info->ans_ipv6_cnt] = info->res[i].res_data;
                    info->ans_ipv6_len[info->ans_ipv6_cnt] = info->res[i].res_data_len;
                    info->ans_ipv6_cnt++;
                }
                break;
            case T_MX:
                if(info->mail_srv_host_cnt < MAX_META_NUM) {
                    info->mail_srv_host[info->mail_srv_host_cnt] = info->res[i].res_data;
                    info->mail_srv_host_len[info->mail_srv_host_cnt] = info->res[i].res_data_len;
                    info->mail_srv_host_cnt++;
                }
                break;
            case T_TXT:
                {
                    info->dnstex = info->res[i].res_data;
                    info->dnstex_len = info->res[i].res_data_len;
                }
                break;
            default:
                break;
        }
    }
    return;
}

static void get_value_from_au_or_ad_info(struct dns_info *info, void *res_arr, int res_num)
{
    if(!info || !res_arr || !res_num)
	    return;

    struct dns_response_info *res = (struct dns_response_info *)res_arr;
    int i,j;
    for(i=0; i < res_num; i++){
        switch (res[i].res_type) {
            case T_NS:
                if(info->name_srv_host_cnt < MAX_META_NUM) {
                    for(j=0;j < info->name_srv_host_cnt; j++){
                        if(memcmp(res[i].res_data, info->name_srv_host[j], (info->name_srv_host_len[j] > res[i].res_data_len?info->name_srv_host_len[j]:res[i].res_data_len)) == 0)
                            break;
                    }
                    if(j==info->name_srv_host_cnt){
                        info->name_srv_host[info->name_srv_host_cnt] = res[i].res_data;
                        info->name_srv_host_len[info->name_srv_host_cnt] = res[i].res_data_len;
                        info->name_srv_host_cnt++;
                    }
                }
                break;
            case T_AAAA:
                if(info->ans_ipv6_cnt < MAX_META_NUM) {
                    info->ans_ipv6[info->ans_ipv6_cnt] = res[i].res_data;
                    info->ans_ipv6_len[info->ans_ipv6_cnt] = res[i].res_data_len;
                    info->ans_ipv6_cnt++;
                }
                break;
            case T_MX:
                if(info->mail_srv_host_cnt < MAX_META_NUM) {
                    info->mail_srv_host[info->mail_srv_host_cnt] = res[i].res_data;
                    info->mail_srv_host_len[info->mail_srv_host_cnt] = res[i].res_data_len;
                    info->mail_srv_host_cnt++;
                }
                break;
            case T_TXT:
                {
                    info->dnstex = res[i].res_data;
                    info->dnstex_len = res[i].res_data_len;
                }
                break;
            default:
                break;
        }
    }
    return;
}

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
static void dissect_ip_country(struct flow_info *flow, const uint8_t **ip_arr, uint32_t *ip_len, uint16_t ip_cnt, char *country, int max_country_len)
{
    if(!flow || !ip_arr || !country || !ip_cnt)
        return;

    IPINFO ip_position;
    char __str[64];
    int len = 0;
    uint32_t ip;
    for(int i=0;i<ip_cnt; i++){
        memset(__str, 0, sizeof(__str));
        memset(&ip_position, 0, sizeof(IPINFO));
        memcpy(__str, ip_arr[i], sizeof(__str));
        if(g_config.ip_position_switch == 1){
            ip = dotted_to_addr((const char*)__str);
            dissect_ip2region(&g_config.entry, (uint8_t*)&ip, &ip_position);
		}else
            get_ip_position_from_ip(__str, &ip_position);
    	if(strlen(ip_position.country)){
    	    if(len == 0)
    	        len+=snprintf(country + len, max_country_len - len, "%s", ip_position.country);
            else
    	        len+=snprintf(country + len, max_country_len - len, ",%s", ip_position.country);
        }
    }
    return;
}

static void dissect_ip_asn(const uint8_t **ip_arr, uint32_t *ip_len, uint16_t ip_cnt, char *asn, int max_asn_len)
{
    if(!ip_arr || !asn || !ip_cnt)
        return;

    char __str[64], asn_tmp[32];
    int len = 0;
    for(int i=0;i<ip_cnt; i++){
        memset(__str, 0, sizeof(__str));
        memcpy(__str, ip_arr[i], sizeof(__str));
        memset(asn_tmp, 0, sizeof(asn_tmp));
        get_asn_from_ip((const char*)__str, asn_tmp, sizeof(asn_tmp));
    	if(strlen(asn_tmp)){
    	    if(len == 0)
    	        len+=snprintf(asn + len, max_asn_len - len, "%s", asn_tmp);
            else
    	        len+=snprintf(asn + len, max_asn_len - len, ",%s", asn_tmp);
        }
    }
    return;
}

static int get_special_value_from_info(struct dns_info *info)
{
    int i, j;
    get_value_from_answer_info(info);
    get_value_from_au_or_ad_info(info, info->au, info->au_res_num);
    get_value_from_au_or_ad_info(info, info->ad, info->ad_res_num);

    for(j=0; j < info->name_srv_host_cnt; j++){
    	for(i=0; i < info->res_num; i++){
           if(info->res[i].res_type == T_A && memcmp(info->name_srv_host[j], (const uint8_t*)info->res[i].res_name, (info->res[i].res_name_len > info->name_srv_host_len[j]?info->res[i].res_name_len:info->name_srv_host_len[j])) == 0){
               info->name_srv_ip[info->name_srv_ip_cnt] = info->res[i].res_data;
               info->name_srv_ip_len[info->name_srv_ip_cnt] = info->res[i].res_data_len;
               info->name_srv_ip_cnt++;
           }
        }
    	for(i=0; i < info->ad_res_num; i++){
           if(info->ad[i].res_type == T_A && memcmp(info->name_srv_host[j], (const uint8_t*)info->ad[i].res_name, (info->ad[i].res_name_len > info->name_srv_host_len[j]?info->ad[i].res_name_len:info->name_srv_host_len[j])) == 0){
               info->name_srv_ip[info->name_srv_ip_cnt] = info->ad[i].res_data;
               info->name_srv_ip_len[info->name_srv_ip_cnt] = info->ad[i].res_data_len;
               info->name_srv_ip_cnt++;
           }
        }
    }
    for(j=0; j < info->mail_srv_host_cnt; j++ ){
    	for(i=0; i < info->ad_res_num; i++){
           if(info->ad[i].res_type == T_A && memcmp(info->mail_srv_host[j], (const uint8_t*)info->ad[i].res_name, (info->ad[i].res_name_len > info->mail_srv_host_len[j]?info->ad[i].res_name_len:info->mail_srv_host_len[j])) == 0){
               info->mail_srv_ip[info->mail_srv_ip_cnt] = info->ad[i].res_data;
               info->mail_srv_ip_len[info->mail_srv_ip_cnt] = info->ad[i].res_data_len;
               info->mail_srv_ip_cnt++;
           }
        }
    }

    return 0;
}


int dissect_dns_udp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(flag);
    UNUSED(seq);

    if(g_config.dns_filter && ((payload[3] & 0x80) == 0) )
        return PKT_OK;

    int retval;
    uint16_t offset = sizeof(struct dpi_dns_packet_header);
    struct dpi_dns_packet_header dns_header;
    struct dns_info info;

    memset(&info, 0, sizeof(info));
    if (payload_len < sizeof(struct dpi_dns_packet_header))
        return PKT_DROP;

    memcpy(&dns_header, (const struct dpi_dns_packet_header*) &payload[0], sizeof(struct dpi_dns_packet_header));
    dns_header.tr_id = ntohs(dns_header.tr_id);
    dns_header.flags = ntohs(dns_header.flags);
    dns_header.num_queries = ntohs(dns_header.num_queries);
    dns_header.num_answers = ntohs(dns_header.num_answers);
    dns_header.authority_rrs = ntohs(dns_header.authority_rrs);
    dns_header.additional_rrs = ntohs(dns_header.additional_rrs);

    if(flow->blacklist_flag)
        return PKT_DROP;

    info.tran_id = dns_header.tr_id;
    info.flags   = dns_header.flags;

    if (dns_header.num_queries > 0) {
        retval = dissect_query_records(flow, payload, payload_len, &offset, dns_header.num_queries, &info);
        if (retval < 0)
            return PKT_DROP;
    }
    if (dns_header.num_answers > 0) {
        retval = dissect_answer_records(payload, payload_len, &offset, dns_header.num_answers, &info);
        if (retval < 0)
            return PKT_DROP;
    }
    if(dns_header.authority_rrs > 0) {
        retval = dissect_au_answer_records(payload, payload_len, &offset, dns_header.authority_rrs, &info);
        if (retval < 0)
            return PKT_DROP;
    }
    if(dns_header.additional_rrs> 0) {
        retval = dissect_ad_answer_records(payload, payload_len, &offset, dns_header.additional_rrs, &info);
        if (retval < 0)
            return PKT_DROP;
    }

    get_special_value_from_info(&info);
    //通过ip获取国家
    dissect_ip_country(flow, info.name_srv_ip, info.name_srv_ip_len, info.name_srv_ip_cnt, info.name_srv_country, sizeof(info.name_srv_country));
    //通过ip获取asn
    dissect_ip_asn(info.name_srv_ip, info.name_srv_ip_len, info.name_srv_ip_cnt, info.name_srv_asn, sizeof(info.name_srv_asn));

    write_dns_log(flow, direction, &info, NULL);

    return PKT_OK;
}


static void init_dns_dissector(void)
{
    dpi_register_proto_schema(dns_field_array,EM_DNS_MAX,"dns");

    port_add_proto_head(IPPROTO_UDP, 53, PROTOCOL_DNS);
    udp_detection_array[PROTOCOL_DNS].proto = PROTOCOL_DNS;
    udp_detection_array[PROTOCOL_DNS].identify_func = identify_dns;
    udp_detection_array[PROTOCOL_DNS].dissect_func = dissect_dns_udp;
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_DNS].excluded_protocol_bitmask, PROTOCOL_DNS);

    port_add_proto_head(IPPROTO_TCP, 53, PROTOCOL_DNS);
    tcp_detection_array[PROTOCOL_DNS].proto = PROTOCOL_DNS;
    tcp_detection_array[PROTOCOL_DNS].identify_func = identify_dns;
    tcp_detection_array[PROTOCOL_DNS].dissect_func = dissect_dns_udp;
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_DNS].excluded_protocol_bitmask, PROTOCOL_DNS);


    map_fields_info_register(dns_field_array,PROTOCOL_DNS,EM_DNS_MAX,"dns");
    pschema_t *schema = dpi_pschema_get_proto("dns");
    pschema_register_field(schema, "addAnsRes", YA_FT_STRING, "desc");
    pschema_register_field(schema, "addAnsType", YA_FT_STRING, "desc");
    pschema_register_field(schema, "authAnsRes", YA_FT_STRING, "desc");
    pschema_register_field(schema, "authAnsType", YA_FT_STRING, "desc");
    pschema_register_field(schema, "ansRes", YA_FT_STRING, "desc");
    return;
}


static __attribute((constructor)) void before_init_dns(void){
    register_tbl_array(TBL_LOG_DNS, 0, "dns", init_dns_dissector);
}



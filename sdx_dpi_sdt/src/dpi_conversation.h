#ifndef _DPI_CONVERSATION_H_
#define _DPI_CONVERSATION_H_

#include <stdint.h>
#include <stddef.h>
#include <arpa/inet.h>
#include "dpi_common.h"


#define NO_ADDR_B 0x01
#define NO_PORT_B 0x02

#define CONVERSATION_TIMEOUT 30


#define COMMON_BIG_SIZE          100*1024

struct conversation_tuple
{
    uint8_t proto;
    uint16_t port_src;
    uint16_t port_dst;
    union {
        uint32_t ip4;
        uint8_t ip6[16];
    } ip_src;
    union {
        uint32_t ip4;
        uint8_t ip6[16];
    } ip_dst;
};

struct conversation_value
{
    uint16_t protocol;
    uint32_t createtime;
    void *conv_session;  // add by liugh
};


/* conversation for ftp add by liugh*/
struct ftp_session
{
    int filesize;
    char file_flag;
    char username[COMMON_NAME_PASSWD_LEN];
    char password[COMMON_NAME_PASSWD_LEN];

    char filetype[COMMON_SOME_TYPE];
    char storpath[COMMON_FILE_PATH];
    char filepath[COMMON_FILE_PATH];
    
    uint16_t port_src;
    uint16_t port_dst;
    uint32_t ftp_ip;
    uint16_t ftp_port;
    uint32_t total_len;
    uint32_t real_len;
    char     login_status[COMMON_STATUS_LEN];
};


/* conversation for sip rtp add by liugh*/
struct sip_session
{
    char     filepath[COMMON_FILE_PATH];
    uint16_t port_src;
    uint16_t port_dst;
    uint32_t ip_src;
    uint32_t ip_dst;
    uint8_t  sip_flag;   //当解析sip报文收集齐rtp信息则不写sip数据
    
};


/* conversation for tftp add by liugh*/
struct tftp_session
{
    char     opt_str[COMMON_SOME_TYPE];
    char     transmode[COMMON_SOME_TYPE];
    char     filename[COMMON_FILE_NAME];
    char     filepath[COMMON_FILE_PATH];
    char     filetype[COMMON_SOME_TYPE];
    uint16_t blocksize;
    int      timeout;
    int      filesize;
    uint64_t now_time_usec;

    uint8_t  convert;
    char     conv_c;         /* convert support data */
    int      last_block;
    uint8_t  lost;

    uint16_t port_src;
    uint16_t port_dst;
};


/* conversation for telnet add by liugh*/
struct telnet_session
{ 
    char     filepath[COMMON_FILE_PATH];
    uint8_t  w_flag;
    char     terminal_type[COMMON_SOME_TYPE];
    char     store[COMMON_BIG_SIZE ];
};

#define RDP_MAX_FLOW_PER_CLIENT 100
struct rdp_conv
{
    int     count;
    int     num;    
    uint8_t state[RDP_MAX_FLOW_PER_CLIENT]; 
};

struct conversation_value * find_conversation(struct conversation_tuple *tuple, uint8_t options);
struct conversation_value *find_or_create_conversation(struct conversation_tuple *tuple, uint8_t options, uint16_t protocol, void *session);

void timeout_conversation_hash(void);
void init_conversation(void);

#endif

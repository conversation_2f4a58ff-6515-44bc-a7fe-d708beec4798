﻿/****************************************************************************************
 * 文 件 名 : dpi_socks.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *修改: hongll  2022/05/05
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <stdint.h>
#include <limits.h>
#include <rte_mbuf.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_common.h"
#include "dpi_proto_ids.h"
#include "dpi_conversation.h"
#include "dpi_pschema.h"


#define TCP_PORT_SOCKS 1080

#define SOCKS_NEED_MORE			(0)
#define SOCKS_ERROR				(-1)

/**************** Socks commands ******************/

#define CONNECT_COMMAND         1
#define BIND_COMMAND            2
#define UDP_ASSOCIATE_COMMAND   3
#define PING_COMMAND            0x80
#define TRACERT_COMMAND         0x81


/********** V5 Authentication methods *************/

#define NO_AUTHENTICATION           0
#define GSS_API_AUTHENTICATION      1
#define USER_NAME_AUTHENTICATION    2
#define CHAP_AUTHENTICATION         3
#define AUTHENTICATION_FAILED       0xff

#define AUTH_METHOD_MAX				(6)  // 暂时 6个

extern struct rte_mempool *tbl_log_mempool;

extern rte_atomic64_t rsm_fail_get;

typedef enum _socks_enum_index {
    EM_SOCKS_VERSION,
    EM_SOCKS_ADDRESS_TYPE,
    EM_SOCKS_REMOTE_ADDRESS,
    EM_SOCKS_REMOTE_DOAMIN_NAME,
    EM_SOCKS_REMOTE_PORT,
    EM_SOCKS_AUTH_METHOD_COUNT,
    EM_SOCKS_AUTH_METHOD,
    EM_SOCKS_ACCEPTED_AUTH_METHOD,
    EM_SOCKS_AUTH_STATUS,
    EM_SOCKS_SUBNEGOTIATION_VERSION,
    EM_SOCKS_USERNAME,
    EM_SOCKS_PASSWORD,
    EM_SOCKS_GSSAPI_COMMAND,
    EM_SOCKS_GSSAPI_DATA,
    EM_SOCKS_GSSAPI_DATA_LENGTH,
    EM_SOCKS_COMMAND,
    EM_SOCKS_RESULTS_V4,
    EM_SOCKS_RESULTS_V5,
    EM_SOCKS_RESERVED,
    EM_SOCKS_RESERVED2,
    EM_SOCKS_REMOTE_NAME,
    EM_SOCKS_FRAGMENT_NUMBER,
    EM_SOCKS_MAX
} socks_enum_index;

static dpi_field_table socks_field_array[] = {

        DPI_FIELD_D(EM_SOCKS_VERSION,                   YA_FT_UINT32,         "version"),
        DPI_FIELD_D(EM_SOCKS_ADDRESS_TYPE,              YA_FT_STRING,         "address_type"),
        DPI_FIELD_D(EM_SOCKS_REMOTE_ADDRESS,            YA_FT_STRING,         "remote_address"),
        DPI_FIELD_D(EM_SOCKS_REMOTE_DOAMIN_NAME,        YA_FT_STRING,         "remote_domain_name"),
        DPI_FIELD_D(EM_SOCKS_REMOTE_PORT,               YA_FT_UINT32,         "remote_port"),
        DPI_FIELD_D(EM_SOCKS_AUTH_METHOD_COUNT,         YA_FT_UINT32,         "auth_method_count"),
        DPI_FIELD_D(EM_SOCKS_AUTH_METHOD,               YA_FT_STRING,         "auth_method"),
        DPI_FIELD_D(EM_SOCKS_ACCEPTED_AUTH_METHOD,      YA_FT_UINT8,          "accepted_auth_method"),
        DPI_FIELD_D(EM_SOCKS_AUTH_STATUS,               YA_FT_UINT32,         "auth_status"),
        DPI_FIELD_D(EM_SOCKS_SUBNEGOTIATION_VERSION,    YA_FT_UINT32,         "subnegotiation_version"),
        DPI_FIELD_D(EM_SOCKS_USERNAME,                  YA_FT_STRING,         "username"),
        DPI_FIELD_D(EM_SOCKS_PASSWORD,                  YA_FT_STRING,         "password"),
        DPI_FIELD_D(EM_SOCKS_GSSAPI_COMMAND,            YA_FT_STRING,         "gssapi_command"),
        DPI_FIELD_D(EM_SOCKS_GSSAPI_DATA,               YA_FT_STRING,         "gssapi_data"),
        DPI_FIELD_D(EM_SOCKS_GSSAPI_DATA_LENGTH,        YA_FT_UINT32,         "gssapi_data_length"),
        DPI_FIELD_D(EM_SOCKS_COMMAND,                   YA_FT_STRING,         "command"),
        DPI_FIELD_D(EM_SOCKS_RESULTS_V4,                YA_FT_STRING,         "results_v4"),
        DPI_FIELD_D(EM_SOCKS_RESULTS_V5,                YA_FT_STRING,         "results_v5"),
        DPI_FIELD_D(EM_SOCKS_RESERVED,                  YA_FT_STRING,         "reserved"),
        DPI_FIELD_D(EM_SOCKS_RESERVED2,                 YA_FT_STRING,         "reserved2"),
        DPI_FIELD_D(EM_SOCKS_REMOTE_NAME,               YA_FT_STRING,         "remote_name"),
        DPI_FIELD_D(EM_SOCKS_FRAGMENT_NUMBER,           YA_FT_UINT32,         "fragment_number"),
};


/** socks address type **/
#define ATYP_NONE       0x00
#define ATYP_IPv4       0x01    /* IPv4 */
#define ATYP_DOMAIN     0x03    /* 域名 */
#define ATYP_IPv6       0x04    /* IPv6 */

#define DEF_NONE_VALUE  0xff

typedef enum
{
    AUTH_SUCCESS = 0,
    AUTH_FAILED,

    AUTH_NONE = DEF_NONE_VALUE,
} socks_auth_status;


typedef struct {
    int            len;
    const uint8_t *ptr;
} DString;

/************* State Machine names ***********/

enum ClientState {
    clientNoInit = -1,
    clientStart = 0,
    clientWaitForAuthReply,
    clientV5Command,
    clientUserNameRequest,
    clientGssApiAuthRequest,
    clientDone,
    clientError
};

enum ServerState {
    serverNoInit = -1,
    serverStart = 0,
    serverInitReply,
    serverCommandReply,
    serverUserReply,
    serverGssApiReply,
    serverBindReply,
    serverDone,
    serverError
};

struct config_socks
{
	int         error_pcap_dump;	//转储Error  PCAP 开关
	int         tcp_out_of_order;	//TCP 抖动容忍
	int         tcp_padding_len;	//TCP 缺包小于N时 允许padding
	char        tcp_padding_str[512]; //TCP PADDING 字符

	int         strip_cache_size;	// 切割缓存大小
	uint8_t     switch_store_file;	//是否存储文件开关
	int         http_file_size;		// 存储文件最大值，要小于cache szie

	int         num;
	uint8_t     drop_no_content_type;
};

struct socks_cache
{
	char     *cache;
	int       cache_size;
	int       cache_hold;
};


struct config_socks socks_tcp = {
	.tcp_out_of_order = 400,  // TCP 报文乱序最大容忍值(建议20 ~ 800, 值越大,重组效果越好,越耗内存)
	.tcp_padding_len = 0,
	.error_pcap_dump = 0,
};

typedef struct {
	int init_flag;
    int in_socks_dissector_flag;
    enum ClientState client;
    enum ServerState server;
} sock_state_t;

typedef struct _address {
	int           type;         /* type of address */
	int           len;          /* length of address, in bytes */
	const uint8_t *data;         /* pointer to address data */

	/* private */
	void         *priv;
} address;


typedef struct {
    sock_state_t    state_info;
    int             version;
    int             command;
    uint8_t         accepted_auth_method;
    int             authentication_method;
    uint32_t        start_done_frame;
    uint32_t        server_port;
    uint32_t        port;
    uint32_t        udp_port;
    uint32_t        udp_remote_port;
    ///////////     ///////////
    uint16_t        port_src;
    uint16_t        port_dst;
    int             direction[2];
    char            host[256];
    char            auth[64];
    char           *uri;

	struct socks_cache  cache[2];

}socks_session;

struct socks_info
{
    socks_session   *session;

    uint32_t        server_port;
    uint32_t        udp_port;
    uint32_t        udp_remote_port;

    uint8_t         version;
    uint8_t         remote_addr_type;

    const uint8_t  *remote_addr_ipv4;
    const uint8_t  *remote_addr_ipv6;
    DString         remote_domain;
    uint16_t        remote_port;

    uint8_t         authentication_method_count;
	const uint8_t  *authentication_methods;
    uint8_t         accepted_auth_method;
    uint8_t         auth_status;

    uint8_t         subnegotiation_version;
    DString         username;
    DString         password;

    uint8_t         gssapi_command;
    DString         gssapi_payload;

    uint8_t         command;
    uint8_t         results_v4;
    uint8_t         results_v5;

    uint8_t         reserved;
    uint16_t        reserved2;
    uint8_t         fragment_number;

    struct     conversation_value *conv;
};


static const char* address_type_table(uint8_t type) {
    switch (type) {
        case 1:     return "IPv4";
        case 3:     return "Domain Name";
        case 4:     return "IPv6";
        default:    return "";
    }
};

/* String table for the V4 reply status messages */

static const char* reply_table_v4(uint8_t type) {
	switch (type) {
	case 90:	return "Granted";
	case 91:	return "Rejected or Failed";
	case 92:	return "Rejected because SOCKS server cannot connect to identd on the client";
	case 93:	return "Rejected because the client program and identd report different user-ids";
	default:	return "";
	}
};

/* String table for the V5 reply status messages */

static const char* reply_table_v5(uint8_t type) {
	switch (type) {
		case 0:		return "Succeeded";
		case 1:		return "General SOCKS server failure";
		case 2:		return "Connection not allowed by ruleset";
		case 3:		return "Network unreachable";
		case 4:		return "Host unreachable";
		case 5:		return "Connection refused";
		case 6:		return "TTL expired";
		case 7:		return "Command not supported";
		case 8:		return "Address type not supported";
		default:	return "";
	}
};

static const char* cmd_strings(uint8_t type) {
	switch (type) {
		case CONNECT_COMMAND:			return "Connect";
		case BIND_COMMAND:				return "Bind";
		case UDP_ASSOCIATE_COMMAND:		return "UdpAssociate";
		case PING_COMMAND:				return "Ping";
		case TRACERT_COMMAND:			return "Traceroute";
		default:						return "";
	}
};

static const char* gssapi_command_table(uint8_t type) {
	switch (type) {
		case 1:		return "Authentication";
		case 0xFF:	return "Failure";
		default:	return "";
	}
};


/************************* Support routines ***************************/

static const char *get_auth_method_name( uint8_t Number){

/* return the name of the authentication method */

    if ( Number == 0) return "No authentication";
    if ( Number == 1) return "GSSAPI";
    if ( Number == 2) return "Username/Password";
    if ( Number == 3) return "Chap";
    if (( Number >= 4) && ( Number <= 0x7f))return "IANA assigned";
    if (( Number >= 0x80) && ( Number <= 0xfe)) return "private method";
    if ( Number == 0xff) return "no acceptable method";

    /* shouldn't reach here */

    return "Bad method number (not 0-0xff)";
}


static void
update_client_state(socks_session *session, enum ClientState state)
{
    sock_state_t *state_info = &session->state_info;
    state_info->client = state;
}

static void
update_server_state(socks_session *session, enum ServerState state)
{
    sock_state_t *state_info = &session->state_info;
    state_info->server = state;
}

/* decode and display the v5 address*/
static int get_address_v5(const uint8_t *payload, int offset, struct socks_info *info)
{
    info->remote_addr_type = payload[offset];
    offset += 1;

    switch (info->remote_addr_type)
    {
    case ATYP_IPv4: /* IPv4 address */
        info->remote_addr_ipv4  = payload + offset;
        offset += 4;
        break;
    case ATYP_DOMAIN: /* domain name address */
        info->remote_domain.len = payload[offset];
        offset += 1;

        info->remote_domain.ptr = payload + offset;
        offset += info->remote_domain.len;
        break;
    case ATYP_IPv6: /* IPv6 address */
        info->remote_addr_ipv6  = payload + offset;
        offset += 16;
        break;
    }

    return offset;
}

/********************* V5 UDP Associate handlers ***********************/
/*
static int
socks_udp_dissector(tvbuff_t *tvb, packet_info *pinfo, proto_tree *tree, void* data _U_) {

// Conversation dissector called from UDP dissector. Decode and display
// the socks header, the pass the rest of the data to the udp port
// decode routine to  handle the payload. 

    int                 offset = 0;
    guint32            *ptr;
    socks_hash_entry_t *hash_info;
    conversation_t     *conversation;
    proto_tree         *socks_tree;
    proto_item         *ti;

    conversation = find_conversation_pinfo( pinfo, 0);

    DISSECTOR_ASSERT( conversation);    // should always find a conversation

    hash_info = (socks_hash_entry_t *)conversation_get_proto_data(conversation, proto_socks);

    col_set_str(pinfo->cinfo, COL_PROTOCOL, "Socks");
    col_set_str(pinfo->cinfo, COL_INFO, "Version: 5, UDP Associated packet");

    if ( tree) {
        ti = proto_tree_add_protocol_format( tree, proto_socks, tvb, offset, -1, "Socks" );

        socks_tree = proto_item_add_subtree(ti, ett_socks);

        proto_tree_add_item(socks_tree, hf_socks_reserved2, tvb, offset, 2, ENC_BIG_ENDIAN);
        offset += 2;

        proto_tree_add_item(socks_tree, hf_socks_fragment_number, tvb, offset, 1, ENC_BIG_ENDIAN);
        offset += 1;

        offset = display_address(pinfo, tvb, offset, socks_tree);
        hash_info->udp_remote_port = tvb_get_ntohs(tvb, offset);

        proto_tree_add_uint( socks_tree, hf_socks_dstport, tvb,
            offset, 2, hash_info->udp_remote_port);

        offset += 2;
    }
    else {      // no tree, skip past the socks header
        offset += 3;
        offset = get_address_v5( tvb, offset, 0) + 2;
    }

    // set pi src/dst port and call the udp sub-dissector lookup

    if ( pinfo->srcport == hash_info->port)
        ptr = &pinfo->destport;
    else
        ptr = &pinfo->srcport;

    *ptr = hash_info->udp_remote_port;

    decode_udp_ports( tvb, offset, pinfo, tree, pinfo->srcport, pinfo->destport, -1);

    *ptr = hash_info->udp_port;
    return tvb_captured_length(tvb);
}


static void
new_udp_conversation( socks_hash_entry_t *hash_info, packet_info *pinfo){

    conversation_t *conversation = conversation_new( pinfo->num, &pinfo->src, &pinfo->dst, ENDPOINT_UDP,
            hash_info->udp_port, hash_info->port, 0);

    DISSECTOR_ASSERT( conversation);

    conversation_add_proto_data(conversation, proto_socks, hash_info);
    conversation_set_dissector(conversation, socks_udp_handle);
}
*/

/*
static void
save_client_state(packet_info *pinfo, enum ClientState state)
{
    sock_state_t* state_info = (sock_state_t *)p_get_proto_data(wmem_file_scope(), pinfo, proto_socks, 0);
    if ((state_info != NULL) && (state_info->client == clientNoInit)) {
        state_info->client = state;
    }
}

static void
save_server_state(packet_info *pinfo, enum ServerState state)
{
    sock_state_t* state_info = (sock_state_t *)p_get_proto_data(wmem_file_scope(), pinfo, proto_socks, 0);
    if ((state_info != NULL) && (state_info->server == serverNoInit)) {
        state_info->server = state;
    }
}
*/

#if 0
#define DEBUG_BLOCK(...) do {   \
    printf("\033[33m");         \
    printf("[%d] ", __LINE__);  \
    printf(__VA_ARGS__);        \
    printf("\033[0m\n");        \
} while(0);
#else
#define DEBUG_BLOCK(...)
#endif


/**************** Protocol Tree Display routines  ******************/
static void
dissect_socks_v4(int is_req, const uint8_t* payload, const uint16_t payload_len, int* offset, sock_state_t* state_info, struct socks_info* info)
{
    /* Either there is an error, or we're done with the state machine
      (so there's nothing to display) */
    if (state_info == NULL)
        return;

	if (is_req)
    {
        /* Client side */
        switch (state_info->client)
        {
        case clientStart:
            info->version = payload[*offset];
            *offset += 1;

            info->command = payload[*offset];
            *offset += 1;

            /* Do remote port */
            info->remote_port = get_uint16_ntohs(payload, *offset);
            *offset += 2;

            /* Do destination address */
            info->remote_addr_ipv4 = payload + *offset;
            *offset += 4;

            /* display user name */
            info->username.ptr = payload + *offset;
            info->username.len = (int)strlen((const char*)payload + *offset);

            *offset += info->username.len + 1;

            if (info->remote_addr_ipv4[0] == 0 && info->remote_addr_ipv4[1] == 0 &&
                info->remote_addr_ipv4[2] == 0 && info->remote_addr_ipv4[3] != 0)
            {
                /* 0.0.0.x , where x!=0 means v4a support */
                info->remote_domain.ptr = payload + *offset;
                info->remote_domain.len = (int)strlen((const char*)payload + *offset);

                *offset += info->remote_domain.len +1;
            }
            break;
        default:
            break;
        }
    } else {
        /* Server side */
        switch (state_info->server)
        {
        case serverStart:
            info->version = payload[*offset];
            *offset += 1;

            /* Do results code */
            info->results_v4 = payload[*offset];
            *offset += 1;

            /* Do remote port */
            info->remote_port = get_uint16_ntohs(payload, *offset);
            *offset += 2;

            /* Do destination address */
            info->remote_addr_ipv4 = &payload[*offset];
            *offset += 4;
            state_info->server = serverDone;
            state_info->client = clientDone;
            DEBUG_BLOCK("v4: serverDone")
            DEBUG_BLOCK("v4: clientDone")
            break;
        default:
            break;
        }
    }
}

static void
client_dissect_socks_v5(const uint8_t* payload, const uint16_t payload_len, int *offset,
                        int auth_method, sock_state_t* state_info, struct socks_info* info)
{

/* Display the protocol tree for the version. This routine uses the */
/* stored conversation information to decide what to do with the row.   */
/* Per packet information would have been better to do this, but we */
/* didn't have that when I wrote this. And I didn't expect this to get  */
/* so messy.                                */

    int           len;
    sock_state_t  new_state_info;

    /* Either there is an error, or we're done with the state machine
      (so there's nothing to display) */
    if (state_info == NULL)
        return;

    if (state_info->client == clientStart)
    {
        info->version = payload[*offset];
        *offset += 1;

        info->authentication_method_count = payload[*offset];
        *offset += 1;

		info->authentication_methods = &payload[*offset];
        *offset += info->authentication_method_count;

        if (info->authentication_method_count == 1 && payload[*offset + 2] == 0
             && (*offset + 2 + info->authentication_method_count) < payload_len)
        {
                new_state_info.client = clientV5Command;
                client_dissect_socks_v5(payload, payload_len, offset, auth_method, &new_state_info, info);
        }
        else
        {
            update_client_state(info->session, clientWaitForAuthReply);
            DEBUG_BLOCK("v5: clientWaitForAuthReply")
        }
    }
    else if (state_info->client == clientV5Command)
    {
        info->version = payload[*offset];
        *offset += 1;

        info->command = payload[*offset];
        *offset += 1;

        info->reserved = payload[*offset];
        *offset += 1;

        *offset = get_address_v5(payload, *offset, info);

        /* client port */
		info->remote_port = get_uint16_ntohs(payload, *offset);
        *offset += 2;

        update_server_state(info->session, serverCommandReply);
        DEBUG_BLOCK("v5: serverCommandReply")
    }
    else if ((state_info->client == clientWaitForAuthReply) &&
             (state_info->server == serverInitReply))
    {

        info->subnegotiation_version = payload[*offset];
        *offset += 1;

        switch(info->session->accepted_auth_method)
        {
        case NO_AUTHENTICATION:
            break;
        case USER_NAME_AUTHENTICATION:
            /* process user name */
            len = payload[*offset];
            *offset += 1;

            info->username.len = len;
            info->username.ptr = payload + *offset;
            *offset += len;

            /* process password */
            len = payload[*offset];
            *offset += 1;

            info->password.len = len;
            info->password.ptr = payload + *offset;
            *offset += len;

            update_server_state(info->session, serverUserReply);
            DEBUG_BLOCK("v5: serverUserReply")
            break;
        case GSS_API_AUTHENTICATION:
            info->gssapi_command = payload[*offset];
            *offset += 1;

            info->gssapi_payload.len = get_uint16_ntohs(payload, *offset);
            *offset += 2;

            if (info->gssapi_payload.len > 0) {
                info->gssapi_payload.ptr = payload + *offset;
                *offset += info->gssapi_payload.len;
            }
            break;
        default:
            break;
        }
    }
    else {
        DEBUG_BLOCK("v5: client 未处理的分支: [%u]", state_info->client);
    }
}

static void
server_dissect_socks_v5(const uint8_t* payload, const uint16_t payload_len, int *offset, sock_state_t* state_info, struct socks_info* info)
{

/* Display the protocol tree for the version. This routine uses the */
/* stored conversation information to decide what to do with the row.   */
/* Per packet information would have been better to do this, but we */
/* didn't have that when I wrote this. And I didn't expect this to get  */
/* so messy.                                */

    int len;
    int auth;

    /* Either there is an error, or we're done with the state machine
      (so there's nothing to display) */
    if (state_info == NULL)
        return;

    switch(state_info->server)
    {
    case serverStart:
        info->version = payload[*offset];
        *offset += 1;

        auth = payload[*offset];
        *offset += 1;

        info->accepted_auth_method = auth;
		info->session->accepted_auth_method = auth;

        switch(auth)
        {
        case NO_AUTHENTICATION:
            update_client_state(info->session, clientV5Command);
            update_server_state(info->session, serverCommandReply);
            DEBUG_BLOCK("v5: clientV5Command, serverCommandReply")
            break;
        case USER_NAME_AUTHENTICATION:
            update_server_state(info->session, serverInitReply);
            DEBUG_BLOCK("v5: serverInitReply")
            break;
        case GSS_API_AUTHENTICATION:
            update_server_state(info->session, serverInitReply);
            DEBUG_BLOCK("v5: serverInitReply")
            break;
        default:
            update_server_state(info->session, serverError);
            DEBUG_BLOCK("v5: serverError")
            break;
        }
        break;
    case serverUserReply:
        info->subnegotiation_version = payload[*offset];
        *offset += 1;

        info->auth_status = payload[*offset];
        *offset += 1;

        if(info->auth_status == AUTH_SUCCESS) {
            update_client_state(info->session, clientV5Command);
            DEBUG_BLOCK("v5: clientV5Command")
        }
        else {
            update_client_state(info->session, clientError);
            update_server_state(info->session, serverError);
            DEBUG_BLOCK("v5: clientError, serverError")
        };
        break;
    case serverGssApiReply:
        /* 以下未验证 */
        info->subnegotiation_version = payload[*offset];
        *offset += 1;

        info->gssapi_command = payload[*offset];
        if (info->gssapi_command != 0xFF) {
            info->subnegotiation_version = get_uint16_ntohs(payload, *offset + 1);
            *offset += 3;

            len = get_uint16_ntohs(payload, *offset + 1);
            info->gssapi_payload.len = len;
            *offset += 3;

            info->gssapi_payload.ptr = &payload[*offset];
            *offset += len + 1;
        }

        update_client_state(info->session, clientV5Command);
        DEBUG_BLOCK("v5: clientError, clientV5Command")
        break;

    case serverCommandReply:
        info->version = payload[*offset];
        *offset += 1;

        info->results_v5 = payload[*offset];
        *offset += 1;

        info->reserved = payload[*offset];
        *offset += 1;

        *offset = get_address_v5(payload, *offset, info);

        info->remote_port = get_uint16_ntohs(payload, *offset);
        *offset += 2;

        update_client_state(info->session, clientDone);
        update_server_state(info->session, serverDone);
        DEBUG_BLOCK("v5: serverDone")
        DEBUG_BLOCK("v5: clientDone")
        break;

    case serverBindReply:
        info->version = payload[*offset];
        *offset += 1;

        info->results_v5 = payload[*offset];
        *offset += 1;

        info->reserved = payload[*offset];
        *offset += 1;

        *offset = get_address_v5(payload, *offset, info);

        info->remote_port = get_uint16_ntohs(payload, *offset);
        *offset += 2;
        break;

    default:
        DEBUG_BLOCK("v5: server 未处理的分支 [%u]", state_info->server);
        break;
    }
}

#if 0
static void
state_machine_v4( socks_hash_entry_t *hash_info, const uint8_t payload, int offset, packet_info *pinfo) {

// Decode V4 protocol.  This is done on the first pass through the
// list.  Based upon the current state, decode the packet and determine
// what the next state should be.
    address addr;

    if (hash_info->clientState != clientDone)
        save_client_state(pinfo, hash_info->clientState);

    if (hash_info->serverState != serverDone)
        save_server_state(pinfo, hash_info->serverState);

    if (hash_info->server_port == pinfo->destport) {
        // Client side, only a single request
        hash_info->command = payload[offset + 1];

        // get remote port
        if ( hash_info->command == CONNECT_COMMAND)
            hash_info->port =  get_uint16_ntohs(payload, offset + 2);

        // get remote address
        addr.type = AT_IPV4;
        addr.len = 4;
        addr.data = payload + offset;
        copy_address_wmem(wmem_file_scope(), &hash_info->dst_addr, &addr);

        hash_info->clientState = clientDone;
    }
    else {
        if (payload[offset + 1] == 90)
            hash_info->serverState = serverDone;
        else
            hash_info->serverState = serverError;
    }
}


static void
client_state_machine_v5( socks_hash_entry_t *hash_info, const uint8_t payload, const int payload_len
                         , int offset, packet_info *pinfo, int start_of_frame) {

// Decode client side of V5 protocol.  This is done on the first pass through the
// list.  Based upon the current state, decode the packet and determine
// what the next state should be.

    if (start_of_frame == 1) {
        save_client_state(pinfo, hash_info->clientState);
        save_server_state(pinfo, hash_info->serverState);
    }

    if (hash_info->clientState == clientStart)
    {
        uint8_t num_auth_methods;

        num_auth_methods = payload[offset + 1];
        // skip past auth methods

        if (num_auth_methods == 0 || (num_auth_methods == 1 && payload[offset + 1] == 0)) {
            // No authentication needed
            hash_info->clientState = clientV5Command;
            if ((offset + 2 + num_auth_methods) < payload_len) {
                client_state_machine_v5(hash_info, payload, payload_len, offset + 2 + num_auth_methods, pinfo, 0);
            }
        } else {
            hash_info->clientState = clientWaitForAuthReply;
        }
    } else if (hash_info->clientState == clientWaitForAuthReply && hash_info->serverState == serverInitReply) {
        switch(hash_info->authentication_method)
        {
        case NO_AUTHENTICATION:
            hash_info->clientState = clientV5Command;
            hash_info->serverState = serverCommandReply;
            break;
        case USER_NAME_AUTHENTICATION:
            hash_info->clientState = clientV5Command;
            hash_info->serverState = serverUserReply;
            break;
        case GSS_API_AUTHENTICATION:
            hash_info->clientState = clientV5Command;
            hash_info->serverState = serverGssApiReply;
            break;
        default:
            hash_info->clientState = clientError;   //Auth failed or error
            break;
        }
    } else if (hash_info->clientState == clientV5Command) {
        hash_info->command = payload[offset + 1]; // get command
        offset += 3;            // skip to address type

        get_address_v5(tvb, &offset, hash_info);

        //temp = tvb_get_guint8(tvb, offset);  XX: what was this for ?/

        if (hash_info->command == CONNECT_COMMAND || hash_info->command == UDP_ASSOCIATE_COMMAND)
            hash_info->port =  get_uint16_ntohs(payload, offset); // get remote port

        hash_info->clientState = clientDone;
    }
}

static void
server_state_machine_v5( socks_hash_entry_t *hash_info, const uint8_t* payload, uint32_t payload_len
                            int offset, packet_info *pinfo, int start_of_frame) {

// Decode server side of V5 protocol.  This is done on the first pass through the
// list.  Based upon the current state, decode the packet and determine
// what the next state should be.

    if (start_of_frame == 1)
        save_server_state(pinfo, hash_info->serverState);

    switch (hash_info->serverState) {
    case serverStart:
        hash_info->authentication_method = payload[offset + 1];
        switch (hash_info->authentication_method)
        {
        case NO_AUTHENTICATION:
            // If there is no authentication, client should expect command immediately
            hash_info->serverState = serverCommandReply;
            hash_info->clientState = clientV5Command;
            break;
        case USER_NAME_AUTHENTICATION:
            hash_info->serverState = serverInitReply;
            break;
        case GSS_API_AUTHENTICATION:
            hash_info->serverState = serverInitReply;
            break;
        default:
            hash_info->serverState = serverError;
            break;
        }
        break;
    case serverUserReply:
        hash_info->serverState = serverCommandReply;
        break;
    case serverGssApiReply:
        if (payload[offset + 1] == 0xFF) {
            hash_info->serverState = serverError;
        } else {
            if (get_uint16_ntohs(payload, offset + 2) == 0)
                hash_info->serverState = serverCommandReply;
        }
        break;
    case serverCommandReply:
        switch(hash_info->command)
        {
        case CONNECT_COMMAND:
        case PING_COMMAND:
        case TRACERT_COMMAND:
            hash_info->serverState = serverDone;
            break;

        case BIND_COMMAND:
            hash_info->serverState = serverBindReply;
            if (payload[offset + 2] == 0) &&
                (payload_len - offset > 5) {
                    offset = display_address(pinfo, tvb, offset, NULL);
                    client_state_machine_v5(hash_info, payload, offset, pinfo, 0);
            }
            break;

        case UDP_ASSOCIATE_COMMAND:
            offset += 3;        // skip to address type
            get_address_v5(tvb, offset, hash_info);

            // save server udp port and create udp conversation
            hash_info->udp_port =  get_uint16_ntohs(payload, offset);

//            if (!pinfo->fd->visited)
//                new_udp_conversation( hash_info, pinfo);

            break;
        }
        break;
    case serverBindReply:
        break;
    default:
        break;
    }
}
#endif

#if 0
static void
display_ping_and_tracert(tvbuff_t *tvb, int offset, packet_info *pinfo, proto_tree *tree, socks_hash_entry_t *hash_info) {

// Display the ping/trace_route conversation

    const guchar *data, *dataend;
    const guchar *lineend, *eol;
    int           linelen;

                // handle the end command
    if ( pinfo->destport == TCP_PORT_SOCKS){
        col_append_str(pinfo->cinfo, COL_INFO, ", Terminate Request");

        proto_tree_add_item(tree, (hash_info->command  == PING_COMMAND) ? hf_socks_ping_end_command : hf_socks_traceroute_end_command, tvb, offset, 1, ENC_NA);
    }
    else {      // display the PING or Traceroute results
        col_append_str(pinfo->cinfo, COL_INFO, ", Results");

        if ( tree){
            proto_tree_add_item(tree, (hash_info->command  == PING_COMMAND) ? hf_socks_ping_results : hf_socks_traceroute_results, tvb, offset, -1, ENC_NA);

            data = tvb_get_ptr(tvb, offset, -1);
            dataend = data + tvb_captured_length_remaining(tvb, offset);

            while (data < dataend) {

                lineend = find_line_end(data, dataend, &eol);
                linelen = (int)(lineend - data);

                proto_tree_add_format_text( tree, tvb, offset, linelen);
                offset += linelen;
                data = lineend;
            }
        }
    }
}

static void clear_in_socks_dissector_flag(void *s)
{
    sock_state_t* state_info = (sock_state_t*)s;
    state_info->in_socks_dissector_flag = 0; //avoid recursive overflow
}

static void call_next_dissector(tvbuff_t *tvb, int offset, packet_info *pinfo,
    proto_tree *tree, proto_tree *socks_tree,
    socks_hash_entry_t *hash_info, sock_state_t* state_info, struct tcpinfo *tcpinfo)
{

// Display the results for PING and TRACERT extensions or
// Call TCP dissector for the port that was passed during the
// connect process
// Load pointer to pinfo->XXXport depending upon the direction,
// change pinfo port to the remote port, call next dissector to decode
// the payload, and restore the pinfo port after that is done.

    guint32 *ptr;
    guint16 save_can_desegment;
    struct tcp_analysis *tcpd=NULL;


    if (( hash_info->command  == PING_COMMAND) ||
        ( hash_info->command  == TRACERT_COMMAND))

        display_ping_and_tracert(tvb, offset, pinfo, tree, hash_info);

    else {      // call the tcp port decoder to handle the payload

//XXX may want to load dest address here

        if (pinfo->destport == TCP_PORT_SOCKS) {
            ptr = &pinfo->destport;
        } else {
            ptr = &pinfo->srcport;
        }

        *ptr = hash_info->port;

        tcpd = get_tcp_conversation_data(NULL, pinfo);
// 2003-09-18 JCFoster Fixed problem with socks tunnel in socks tunnel

        state_info->in_socks_dissector_flag = 1; // avoid recursive overflow
        CLEANUP_PUSH(clear_in_socks_dissector_flag, state_info);

        save_can_desegment = pinfo->can_desegment;
        pinfo->can_desegment = pinfo->saved_can_desegment;
        dissect_tcp_payload(tvb, pinfo, offset, tcpinfo->seq,
            tcpinfo->nxtseq, pinfo->srcport, pinfo->destport,
            tree, socks_tree, tcpd, tcpinfo);
        pinfo->can_desegment = save_can_desegment;

        CLEANUP_CALL_AND_POP;

        *ptr = TCP_PORT_SOCKS;
    }
}

static int
dissect_socks_tls(tvbuff_t *tvb, packet_info *pinfo, proto_tree *tree, void *data) {
    if (data != NULL) {
        return dissect_socks(tvb, pinfo, tree, data);
    } else {
        // lets fake a tcpinfo, which TLS does not give us
        struct tcpinfo tmp;
        tmp.flags = 0;
        tmp.is_reassembled = FALSE;
        tmp.lastackseq = 0;
        tmp.nxtseq = 0;
        tmp.seq = 0;
        tmp.urgent_pointer = 0;
        return dissect_socks(tvb, pinfo, tree, &tmp);
    }
}

void
proto_reg_handoff_socks(void) {

    // dissector install routine
    socks_udp_handle = create_dissector_handle(socks_udp_dissector, proto_socks);
    socks_handle = create_dissector_handle(dissect_socks, proto_socks);
    socks_handle_tls = register_dissector("SOCKS over TLS", dissect_socks_tls, proto_socks);

    dissector_add_uint_with_preference("tcp.port", TCP_PORT_SOCKS, socks_handle);

    ssl_dissector_add(0, socks_handle_tls);
}
#endif

static void
socks_field_element(struct tbl_log* log_ptr, struct flow_info *flow, int direction, struct socks_info * info, int *idx, int ele)
{
    int i;
    char s1[200];
    char str[2048];

    switch (ele) {
        case EM_SOCKS_VERSION:
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->session->version);
            break;
        case EM_SOCKS_REMOTE_ADDRESS:
            if (info->remote_addr_ipv4 != NULL)
                write_one_ip_reconds(log_ptr->record, idx, 4, info->remote_addr_ipv4);
            else if (info->remote_addr_ipv6 != NULL)
                write_one_ip_reconds(log_ptr->record, idx, 6, info->remote_addr_ipv6);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_REMOTE_DOAMIN_NAME:
            if (info->remote_domain.len > 0) {
                write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                    (const char*)info->remote_domain.ptr, info->remote_domain.len);
            }
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_GSSAPI_COMMAND:
            write_string_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, gssapi_command_table(info->gssapi_command));
            break;
        case EM_SOCKS_GSSAPI_DATA:
            if (info->gssapi_payload.len > 0)
                write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                (const char*)info->gssapi_payload.ptr, info->gssapi_payload.len);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_GSSAPI_DATA_LENGTH:
            if (info->gssapi_payload.len > 0)
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->gssapi_payload.len);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_REMOTE_PORT:
            if (info->remote_port > 0)
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->remote_port);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_COMMAND:
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, cmd_strings(info->command), strlen(cmd_strings(info->command)));
            break;
        case EM_SOCKS_RESULTS_V4:
            memset(str, 0, sizeof(str));
            if (info->results_v4 != DEF_NONE_VALUE) {
                snprintf(str, sizeof(str), "%s (%d)", reply_table_v4(info->results_v4), info->results_v4);
                write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, strlen(str));
            }
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_RESULTS_V5:
            if (info->results_v5 != DEF_NONE_VALUE) {
                memset(str, 0, sizeof(str));
                snprintf(str, sizeof(str), "%s (%d)", reply_table_v5(info->results_v5), info->results_v5);
                write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, strlen(str));
            }
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_AUTH_METHOD_COUNT:
            if (info->authentication_method_count > 0)
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->authentication_method_count);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_AUTH_METHOD:
            memset(str, 0, sizeof(str));
            for (i = 0; i < info->authentication_method_count; i++) {
                memset(s1, 0, sizeof(s1));
                snprintf(s1, sizeof(s1), "Method %d: %s", i, get_auth_method_name(info->authentication_methods[i]));
                strcat(str, s1);
                if (i != info->authentication_method_count - 1)
                    strcat(str, ", ");
            }
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, strlen(str));
            break;
        case EM_SOCKS_ACCEPTED_AUTH_METHOD:
            if (DEF_NONE_VALUE != info->accepted_auth_method) {
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->accepted_auth_method);
            }
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_AUTH_STATUS:
            if (info->auth_status != AUTH_NONE)
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->auth_status);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_SUBNEGOTIATION_VERSION:
            if (info->subnegotiation_version > 0)
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->subnegotiation_version);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SOCKS_USERNAME:
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char*)info->username.ptr, info->username.len);
            break;
        case EM_SOCKS_PASSWORD:
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char*)info->password.ptr, info->password.len);
            break;
        case EM_SOCKS_ADDRESS_TYPE:
            memset(str, 0, sizeof(str));
            if (info->remote_addr_type)
                snprintf(str, sizeof(str), "%s", address_type_table(info->remote_addr_type));
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, strlen(str));
            break;
        case EM_SOCKS_FRAGMENT_NUMBER:
            if (info->fragment_number > 0)
                write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->fragment_number);
            else
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        default:
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
    }
}


static int write_socks_log(struct flow_info *flow, int direction, struct socks_info *info)
{
	int ele;
	int idx = 0;
	struct tbl_log* log_ptr;

	if (rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0) {
		DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
		return PKT_OK;
	}

    init_log_ptr_data(log_ptr, flow,PROTOCOL_SOCKS);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "socks");

	for (ele = 0; ele < EM_SOCKS_MAX; ele++) {
		socks_field_element(log_ptr, flow, direction, info, &idx, ele);
	}

	log_ptr->thread_id = flow->thread_id;
	log_ptr->log_type = TBL_LOG_SOCKS;
	log_ptr->log_len = idx;
	log_ptr->content_len = 0;
	log_ptr->content_ptr = NULL;

	log_ptr->flow = flow;

	if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
		rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
	}

	return 0;
}

static void state_init(socks_session *ss, struct flow_info *flow, uint8_t ver)
{
    g_assert(ss && flow);

    ss->version           = ver;
	ss->port              = 0;
    ss->server_port       = ntohs(flow->tuple.inner.port_dst);
    ss->port_dst          = ntohs(flow->tuple.inner.port_dst);
	ss->port_src          = ntohs(flow->tuple.inner.port_src);
	ss->start_done_frame  = INT_MAX;
	ss->state_info.client = clientStart;
	ss->state_info.server = serverStart;

	ss->state_info.init_flag = 1;
	ss->state_info.in_socks_dissector_flag = 0;
    DEBUG_BLOCK("v%u: clientStart, serverStart", ver);

	//		ss->server_port = ;
}

static void flow_finish_socks(struct flow_info *flow);

static int dissect_socks(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag) {

    if (payload_len < 2)
        return PKT_DROP;

    int dissect_ok = 0;
    int offset = 0;
	struct socks_info info;

	socks_session *session = (socks_session *)flow->app_session;
	socks_session *ss = session;

	memset(&info, 0, sizeof(struct socks_info));
    info.version = payload[offset];

    flow->match_data_len = payload_len;

	if (ss->state_info.init_flag == 0)
		state_init(ss, flow, info.version);

    if (ss->state_info.in_socks_dissector_flag)
        return 0;

    info.session     = session;
    info.auth_status = AUTH_NONE; // 默认值， 一个必错的值
    info.results_v4  = DEF_NONE_VALUE;
    info.results_v5  = DEF_NONE_VALUE;
    info.accepted_auth_method = DEF_NONE_VALUE;

    // client side
    if((ss->state_info.client == clientDone) && (ss->state_info.server == serverDone))
    {
        return 0;
    }

    if(direction == FLOW_DIR_SRC2DST)
    {
        if (ss->state_info.client == clientError ||
            ss->state_info.client == clientDone)
        {
            return 0;
        }

        if (ss->version == 0x04)
        {
            dissect_socks_v4(true , payload, payload_len, &offset, &ss->state_info, &info);
            dissect_ok = 1;
        }
        else if (ss->version == 0x05)
        {
            client_dissect_socks_v5(payload, payload_len, &offset, ss->authentication_method, &ss->state_info, &info);
            dissect_ok = 1;
        }
    }
    else
    {
        if (ss->state_info.server == serverError ||
            ss->state_info.server == serverDone)
        {
            return 0;
        }

        if (ss->version == 0x04)
        {
            dissect_socks_v4(false, payload, payload_len, &offset, &ss->state_info, &info);
            dissect_ok = 1;
        }
        else if (ss->version == 0x05)
        {
            server_dissect_socks_v5(payload, payload_len, &offset, &ss->state_info, &info);
            dissect_ok = 1;
        }
    }

    if ((ss->state_info.client == clientDone) &&
        (ss->state_info.server == serverDone)) {   // if done now
        ss->start_done_frame = flow->src2dst_packets + flow->dst2src_packets;
    }


	if (dissect_ok == 1 && info.authentication_method_count <= AUTH_METHOD_MAX)
		write_socks_log(flow, direction, &info);

    return 0;
}

static int is_socks(struct flow_info *flow, int c2s, const uint8_t *paoload, int paylaod_len) {

	return (flow->port_dst == TCP_PORT_SOCKS || flow->port_src == TCP_PORT_SOCKS);
}

static int64_t get_len(const uint8_t *payload, uint32_t payload_len, struct flow_info *flow, int direction) {

	return (int64_t)payload_len;
}

static void flow_finish_socks(struct flow_info *flow)
{
	if (flow->app_session)
	{
		socks_session *session = (socks_session *)flow->app_session;

		if (session->uri)
        {
			free(session->uri);
            session->uri = NULL;
        }

		if (session->cache[0].cache)
        {
			free(session->cache[0].cache);
            session->cache[0].cache = NULL;
        }

        if (session->cache[1].cache)
        {
            free(session->cache[1].cache);
            session->cache[1].cache = NULL;
        }

		free(flow->app_session);
		flow->app_session = NULL;
	}
}

static int dissect_socks_pipeline(struct flow_info *f, uint8_t C2S, const uint8_t *p, uint32_t pl)
{
	char                 t = 0;
	int64_t              hl = 0;
	int64_t              offset = 0;
	int64_t              l = pl;
	socks_session *s = NULL;
	struct socks_cache *c = NULL;

	if (NULL == f->app_session)
	{
		f->app_session = dpi_malloc(sizeof(socks_session));
		if (NULL == f->app_session)
		{
			goto NEED_MORE_PKT;
		}
		memset(f->app_session, 0, sizeof(socks_session));
	}
	s = (socks_session *)f->app_session;
	c = s->cache + C2S;

	// 是否开启缓存
	if (c->cache)
	{
		if (1 == is_socks(f, C2S, p, l))
		{
			// 说明这是 HEAD 请求的响应
			dissect_socks(f, C2S, 0, (const uint8_t*)c->cache, (uint32_t)c->cache_hold, 0);
			c->cache_hold = 0; //reset
		}

		if (l >= (c->cache_size - c->cache_hold))
		{
			// 缓存撑爆前,  解析数据, 释放
			dissect_socks(f, C2S, 0, (const uint8_t*)c->cache, (uint32_t)c->cache_hold, 0);
			goto SOCKS_DROP;
		}

		//正常 拼装
		memcpy(c->cache + c->cache_hold, p, l);
		c->cache_hold += l;
		c->cache[c->cache_hold] = '\0';
		p = (const uint8_t*)c->cache;
		l = c->cache_hold;
	}

	// 专业切割机
	while (offset < l)
	{
		hl = get_len(p + offset, l - offset, f, C2S);

		if (hl > 0 && l - offset >= hl)
		{
			dissect_socks(f, C2S, 0, (const uint8_t*)p + offset, (uint32_t)hl, 0);
			offset += hl;
		}
		else if (hl == SOCKS_ERROR)
		{
			goto SOCKS_DROP;
		}
		else if (hl == SOCKS_NEED_MORE)
		{
			break;
		}
		else if (hl > l - offset)
		{
			break;
		}
	}

	// 有没有剩料?
	if (offset>0 && offset < l)
	{
		if (NULL != c->cache && offset > 0)  //已开启缓存, 直接将剩料挪到前面
		{
			memmove(c->cache, c->cache + offset, c->cache_hold - offset);
			c->cache_hold -= offset;
		}
		else if (NULL == c->cache)          //未开启缓存, 创建缓存, 把剩料放在前面
		{
			c->cache_size = g_config.http.http_strip_cache_size;
			c->cache_hold = l - offset;
			c->cache = dpi_malloc(c->cache_size);
			memcpy(c->cache, p + offset, l - offset);
		}
		goto NEED_MORE_PKT;
	}
	else
	{
		if (NULL != c->cache)
		{
			free(c->cache);
			c->cache = NULL;
			c->cache_hold = 0;
		}
		goto NEED_MORE_PKT;
	}


	// HTTP  太长, 只解析被缓存的部分.
SOCKS_DROP:
	if (NULL != c->cache)
	{
		free(c->cache);
		c->cache = NULL;
		c->cache_hold = 0;
	}

	// 解析,需要更多报文
NEED_MORE_PKT:
	return 0;
}

// 已缓存的数据直接输出
static int miss_socks(struct flow_info *f,  uint8_t C2S, uint32_t miss)
{
	socks_session *s = NULL;
	struct   socks_cache   *c = NULL;
	int      paddinglen = 0;
	int      safelen = 0;
	int      miss_len = (int)miss;

    UNUSED(paddinglen)
    UNUSED(safelen)
    UNUSED(miss_len);
  if(miss){
    f->intflag = 0;
  }
	if (f->app_session)
	{
        s = (socks_session *)f->app_session;
        c = s->cache + C2S;

        if (c->cache)
        {
            dissect_socks(f, C2S, 0, (const uint8_t*)c->cache, (uint32_t)c->cache_hold, 0);
            free(c->cache);
            c->cache = NULL;
            c->cache_hold = 0;
        }
	}
	return 0;
}

static int identify_socks(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, const uint32_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_SOCKS] == 0)
    {
        return PROTOCOL_UNKNOWN;
    }

    if (payload_len < 2)
    {
        return PROTOCOL_UNKNOWN;
    }

	uint16_t s_port = 0, d_port = 0;

	s_port = ntohs(flow->tuple.inner.port_src);
	d_port = ntohs(flow->tuple.inner.port_dst);
	if (d_port == TCP_PORT_SOCKS || s_port == TCP_PORT_SOCKS)
    {
		flow->real_protocol_id = PROTOCOL_SOCKS;
        return PROTOCOL_SOCKS;
	}

    return PROTOCOL_UNKNOWN;
}

extern struct decode_t decode_socks;
static int initial_socks(struct decode_t *decode)
{
    decode_on_port_tcp(TCP_PORT_SOCKS, &decode_socks);
    dpi_register_proto_schema((dpi_field_table*)socks_field_array, EM_SOCKS_MAX, "socks");
	map_fields_info_register(socks_field_array, PROTOCOL_SOCKS, EM_SOCKS_MAX, "socks");
    register_tbl_array(TBL_LOG_SOCKS, 0, "socks", NULL);

    
    pschema_t *schema = dpi_pschema_get_proto("socks");
    pschema_register_field(schema, "realDstIPs", YA_FT_STRING, "desc");
    return 0;
}

static int destroy_socks(struct decode_t *decode)
{
    UNUSED(decode);
    return 0;
}


struct decode_t decode_socks = {
    .name           =   "socks",
    .decode_initial =   initial_socks,
    .pkt_identify   =   identify_socks,
    .pkt_dissect    =   dissect_socks_pipeline,
    .pkt_miss       =   miss_socks,
    .flow_finish    =   flow_finish_socks,
    .decode_destroy =   destroy_socks,
};


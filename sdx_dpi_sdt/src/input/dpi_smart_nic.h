#ifndef DPI_INPUT_SMART_NIC_H_
#define DPI_INPUT_SMART_NIC_H_

#include <stdint.h>
#include "dpi_dpdk_wrapper.h"

struct capture_config
{
    uint16_t    port_id;
    uint16_t    queue_id;
    uint32_t    lcore;
};


/**
 * @param nic_num   返回网卡数量
 * @param nb_rxq    设置收包队列
 */
int dpi_smart_nic_init(int *nic_num, uint16_t nb_rxq);

/**
 * @param start_lcore   用于收包线程的 首个可用的lcore index,
 *                      通过调用 rte_get_next_lcore 获取下一个可用的lcore, 直到启动所有的收包线程.
 *                      调用结束后, start_lcore 指向下一个可用的 lcore index
 */
int dpi_smart_nic_start(uint32_t *start_lcore, uint16_t nb_rxq);

void dpi_smart_nic_stop(void);

void dpi_smart_nic_display_state(int dev_num);


#endif // DPI_INPUT_SMART_NIC_H_
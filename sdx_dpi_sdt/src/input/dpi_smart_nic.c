#include <dpi_smart_nic.h>


#include <smart_nic/nic_api.h>

#include "dpi_log.h"


#define SMART_NIC_LOG(...) {fprintf(stdout, "\033[32m"); fprintf(stdout, __VA_ARGS__); fprintf(stdout, "\033[0m\n");}

#define SMART_RTE_ETH_FOREACH_DEV(p) for (p = 0; p < nic_get_dev_num(); p++)

#ifndef MAX_PKT_BURST
#define MAX_PKT_BURST 1024
#endif // MAX_PKT_BURST

extern rte_atomic64_t drop_pkts;
extern rte_atomic64_t drop_bytes;
extern rte_atomic64_t receive_pkts;
extern rte_atomic64_t receive_bytes;
extern rte_atomic64_t dpi_fail_pkts;

extern struct global_config g_config;
extern struct rte_mempool *pktmbuf_pool[2];
extern struct rte_ring *packet_flow_ring[RTE_MAX_LCORE];

static bool volatile stop_condition = false;

static struct capture_config *capture_config_list = NULL;
static int capture_config_num = 0;

static struct rte_eth_dev_tx_buffer *tx_buffer[RTE_MAX_ETHPORTS];

static struct rte_eth_conf port_conf = {
    .rxmode = {
#if RTE_VER_YEAR == 19
		.split_hdr_size = 0,
#endif
	},
    .txmode = {
        .mq_mode = RTE_ETH_MQ_TX_NONE,
    },
};


static void smart_nic_config(int dev_num)
{
    int ret;

    for(int c=0; c < dev_num; c++)
    {
        SMART_NIC_LOG("nic %d configure:", c);

        ret = nic_set_hash_mode(c, 0, HASH_FIVE_TUPLE);
        SMART_NIC_LOG(" - %s set hash mode: five_tuple", ret ? "Failed" : "Successed");

        ret = nic_set_filter_mode(c, 1);
        SMART_NIC_LOG(" - %s set inner tuple match", ret ? "Failed" : "Successed");

        ret = nic_set_default_action(c, ACTION_FORWARD);
        SMART_NIC_LOG(" - %s set FORWARD as default action", ret ? "Failed" : "Successed");
    }
}


void dpi_smart_nic_display_state(int dev_num)
{
	int ret, port_num;
	int status, speed;

	SMART_NIC_LOG("smart nic num = %d", dev_num);
	if (dev_num <= 0)
		return;

	for(int c=0; c < dev_num; c++)
	{
		port_num = nic_get_port_num(c);
		SMART_NIC_LOG("smart nic NO.%d, port num = %d", c, port_num);	
		
		for(int j=0; j<port_num; j++)
		{
			status = speed = 0;

			SMART_NIC_LOG("smart nic NO.%d, port NO.%d:", c, j);

			ret = nic_get_port_linkstatus(c, j, &status);
			if (ret < 0) {
				SMART_NIC_LOG("  error get status");
				continue;
			}

			SMART_NIC_LOG("  status: %d", status);


			ret = nic_get_port_speed(c, j, &speed);
			if (ret < 0) {
				SMART_NIC_LOG("  error get speed");
				continue;
			}

			SMART_NIC_LOG("  speed: %d", speed);
		}

		/* ret = nic_set_hash_mode(c, 1, 0);
		if (ret < 0) {
			SMART_NIC_LOG("Error set hash mode: five tuple, dev:%d", c);
		}
		else {	
			SMART_NIC_LOG("Success set hash mode: five tuple, dev:%d", c);
		}
	
		ret = nic_set_filter_mode(c, 1);
		if (ret < 0){
			SMART_NIC_LOG("Error set filter mode: 1, dev:%d", c);
		}
		else {	
			SMART_NIC_LOG("Success set filter mode: 1, dev:%d", c);
		}

		ret = nic_set_default_action(c, 1);
		if (ret) {
			SMART_NIC_LOG("Error set action forward, dev:%d", c);
		}
		else {	
			SMART_NIC_LOG("Success set action forward, dev:%d", c);
		} */
			
	}

}


static int dpi_packet_distribute(struct rte_mbuf *mb)
{
    struct rte_ring *ring = packet_flow_ring[mb->hash.rss % g_config.dissector_thread_num];

    return rte_ring_enqueue(ring, mb);
}


static int receive_thread(void *arg)
{
    struct capture_config *config = (struct capture_config*)arg;

    uint16_t nb_ports;
	uint16_t queueid = config->queue_id;
	uint16_t portid = config->port_id;
	uint16_t cnt_recv_frames, i;

	uint64_t receive_bytes_per_burst;
	uint16_t local_fail_pkts = 0;
	uint32_t local_faile_bytes = 0;

	struct rte_mbuf *pkts_burst[MAX_PKT_BURST], *mb;

    int ret;
	int burst_num = DPI_MIN(MAX_PKT_BURST, g_config.max_pkt_burst);

	while (1)
	{
		if (unlikely(stop_condition))
			break;

        cnt_recv_frames = rte_eth_rx_burst(portid, queueid, pkts_burst, burst_num);

        if (cnt_recv_frames <= 0)
            continue;

        receive_bytes_per_burst = 0;

        for (i = 0; i < cnt_recv_frames; i++)
        {
            mb = pkts_burst[i];
            receive_bytes_per_burst += mb->data_len;

            if (dpi_packet_distribute(mb) != 0)
            {
                local_fail_pkts++;
                local_faile_bytes += mb->data_len;
                rte_pktmbuf_free(mb);
            }
        }

        rte_atomic64_add(&receive_pkts,  cnt_recv_frames);
        rte_atomic64_add(&receive_bytes, receive_bytes_per_burst);

        if (local_fail_pkts) {
            rte_atomic64_add(&dpi_fail_pkts, local_fail_pkts);
            local_fail_pkts = 0;
            local_faile_bytes = 0;
        }
	}

    return 0;
} 

int dpi_smart_nic_port_config(uint16_t nb_rxq)
{
    struct lcore_queue_conf *qconf;
    int ret = 0;
    uint16_t nb_ports, nb_devs;
    uint16_t nb_ports_available = 0;
    uint16_t portid, last_port;
    unsigned lcore_id, rx_lcore_id;
    unsigned nb_ports_in_mask = 0;
    unsigned int nb_lcores = 0;
    unsigned int nb_mbufs;
	uint16_t nb_rxd = 1024, nb_txd = 1024;

    nb_ports = nic_get_dev_num();
    if (nb_ports <= 0)
        rte_exit(EXIT_FAILURE, "No smart nic port - bye\n");

    rx_lcore_id = 0;
    qconf = NULL;

    /* Initialise each port */
    for (portid = 0; portid < nb_ports; portid++)
    {
        struct rte_eth_rxconf rxq_conf;
        struct rte_eth_txconf txq_conf;
        struct rte_eth_conf local_port_conf = port_conf;
        struct rte_eth_dev_info dev_info;

        /* init port */
        printf("Initializing port %u... ", portid);
        fflush(stdout);

        ret = rte_eth_dev_info_get(portid, &dev_info);
        if (ret != 0) {
            rte_exit(EXIT_FAILURE,
                "Error during getting device (port %u) info: %s\n",
                portid, strerror(-ret));
        }

        local_port_conf.rxmode.offloads = dev_info.default_rxconf.offloads;

        if (dev_info.tx_offload_capa & RTE_ETH_TX_OFFLOAD_MBUF_FAST_FREE)
			local_port_conf.txmode.offloads |=
				RTE_ETH_TX_OFFLOAD_MBUF_FAST_FREE;
        ret = rte_eth_dev_configure(portid, nb_rxq, 1, &local_port_conf);
        if (ret < 0)
            rte_exit(EXIT_FAILURE, "Cannot configure device: err=%d, port=%u\n",
                  ret, portid);

        ret = rte_eth_dev_adjust_nb_rx_tx_desc(portid, &nb_rxd, &nb_txd);
        if (ret < 0)
            rte_exit(EXIT_FAILURE,
                 "Cannot adjust number of descriptors: err=%d, port=%u\n",
                 ret, portid);

        /* init one RX queue */
        fflush(stdout);
        rxq_conf = dev_info.default_rxconf;
        rxq_conf.offloads = local_port_conf.rxmode.offloads;
        // 设置 n 个收包队列
        for (uint32_t queue_id = 0; queue_id < nb_rxq; queue_id++)
        {
            ret = rte_eth_rx_queue_setup(portid, queue_id, nb_rxd,
                            g_config.socketid,
                            &rxq_conf,
                            pktmbuf_pool[g_config.socketid]);
            if (ret < 0)
                rte_exit(EXIT_FAILURE, "rte_eth_rx_queue_setup:err=%d, port=%u, queue=%u\n",
                    ret, portid, queue_id);
        }
        /* init one TX queue on each port */
        fflush(stdout);
        txq_conf = dev_info.default_txconf;
        txq_conf.offloads = local_port_conf.txmode.offloads;
        ret = rte_eth_tx_queue_setup(portid, 0, nb_txd,
                rte_eth_dev_socket_id(portid),
                &txq_conf);
        if (ret < 0)
            rte_exit(EXIT_FAILURE, "rte_eth_tx_queue_setup:err=%d, port=%u\n",
                ret, portid);

        /*
        tx_buffer[portid] = rte_zmalloc_socket("tx_buffer",
				RTE_ETH_TX_BUFFER_SIZE(MAX_PKT_BURST), 0,
				rte_eth_dev_socket_id(portid));
		if (tx_buffer[portid] == NULL)
			rte_exit(EXIT_FAILURE, "Cannot allocate buffer for tx on port %u\n",
					portid);

		rte_eth_tx_buffer_init(tx_buffer[portid], MAX_PKT_BURST);


        ret = rte_eth_dev_set_ptypes(portid, RTE_PTYPE_UNKNOWN, NULL, 0);
	    if (ret < 0)
		    printf("Failed to disable Ptype parsing\n");
        */
        /* Start device */
        ret = rte_eth_dev_start(portid);
        if (ret < 0)
            rte_exit(EXIT_FAILURE, "rte_eth_dev_start:err=%d, port=%u\n",
                  ret, portid);

        // ret = rte_eth_promiscuous_enable(portid);

        printf("Success\n");
    }

    fflush(stdout);

    return ret;
}


int dpi_smart_nic_init(int *nic_num, uint16_t nb_rxq)
{    
	*nic_num = nic_get_dev_num();

    if (*nic_num <= 0) {
        return -1;
    }

    dpi_smart_nic_display_state(*nic_num);

    // smart_nic_config(*nic_num);

    return dpi_smart_nic_port_config(nb_rxq);
}


int dpi_smart_nic_start(uint32_t *start_lcore, uint16_t nb_rxq)
{
    uint16_t nb_ports, portid;
    uint16_t queue_id;
    uint32_t lcore = *start_lcore;
    int ret = 0;

    nb_ports = nic_get_dev_num();
    if (nb_ports <= 0)
        rte_exit(EXIT_FAILURE, "No smart nic port - bye\n");

    capture_config_num  = nb_rxq * nb_ports;
    capture_config_list = dpi_malloc(sizeof(struct capture_config) * capture_config_num);
    stop_condition = false;

    /** 启动多队列收包 */
    for (portid=0; portid < nb_ports; portid++)
    {
        for (queue_id=0; queue_id < nb_rxq; queue_id++)
        {
            if (lcore == RTE_MAX_LCORE)
                break;

            struct capture_config *config = &capture_config_list[portid * nb_rxq + queue_id];
            config->port_id  = portid;
            config->queue_id = queue_id;
            config->lcore    = lcore;

            if (rte_eal_remote_launch(receive_thread, (void*)config, lcore) < 0) {
                rte_exit(EXIT_FAILURE,
                    "Could not launch capture process on lcore %u.\n", lcore);
            }

            printf("Create capture process on lcore %u\n", lcore);
            lcore = rte_get_next_lcore(lcore, 1, 0);
        }

        if (queue_id < nb_rxq) {
            rte_exit(EXIT_FAILURE,
                "No enough lcore, capture process at least need %d lcore.\n", nb_ports * nb_rxq);
        }
    }

    *start_lcore = lcore;
    return ret;
}

void dpi_smart_nic_stop(void)
{
    uint16_t queue_id, prot_id;;
    unsigned lcore_id;
    int ret = 0;
    struct capture_config *config;

    stop_condition = true;

    for (int i=0; i < capture_config_num; i++)
    {
        lcore_id = capture_config_list[i].lcore;
        if (rte_eal_wait_lcore(lcore_id) < 0) {
            ret = -1;
            break;
        }
    }

    for (prot_id = 0; prot_id < nic_get_dev_num(); prot_id++)
    {
        printf("Closing port %d...", prot_id);
        rte_eth_dev_stop(prot_id);
        rte_eth_dev_close(prot_id);
        printf(" Done\n");
    }

    dpi_free(capture_config_list);
    capture_config_list = NULL;
    capture_config_num  = 0;
}

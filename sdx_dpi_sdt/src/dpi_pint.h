/* pint.h
 * Definitions for extracting and translating integers safely and portably
 * via pointers.
 *
 * Wireshark - Network traffic analyzer
 * By <PERSON> <<EMAIL>>
 * Copyright 1998 <PERSON>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
 */

#ifndef __DPI_PINT_H__
#define __DPI_PINT_H__

#include <stdint.h>
#include "dpi_common.h"

/* Pointer versions of g_ntohs and g_ntohl.  Given a pointer to a member of a
 * byte array, returns the value of the two or four bytes at the pointer.
 * The pletohXX versions return the little-endian representation.
 */

#define pntoh16(p)  ((uint16_t)                       \
                     ((uint16_t)*((const uint8_t *)(p)+0)<<8|  \
                      (uint16_t)*((const uint8_t *)(p)+1)<<0))

#define pntoh24(p)  ((uint32_t)*((const uint8_t *)(p)+0)<<16|  \
                     (uint32_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint32_t)*((const uint8_t *)(p)+2)<<0)

#define pntoh32(p)  ((uint32_t)*((const uint8_t *)(p)+0)<<24|  \
                     (uint32_t)*((const uint8_t *)(p)+1)<<16|  \
                     (uint32_t)*((const uint8_t *)(p)+2)<<8|   \
                     (uint32_t)*((const uint8_t *)(p)+3)<<0)

#define pntoh40(p)  ((uint64_t)*((const uint8_t *)(p)+0)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+4)<<0)

#define pntoh48(p)  ((uint64_t)*((const uint8_t *)(p)+0)<<40|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+4)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+5)<<0)

#define pntoh56(p)  ((uint64_t)*((const uint8_t *)(p)+0)<<48|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<40|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+4)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+5)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+6)<<0)

#define pntoh64(p)  ((uint64_t)*((const uint8_t *)(p)+0)<<56|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<48|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<40|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+4)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+5)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+6)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+7)<<0)


#define pletoh16(p) ((uint16_t)                       \
                     ((uint16_t)*((const uint8_t *)(p)+1)<<8|  \
                      (uint16_t)*((const uint8_t *)(p)+0)<<0))

#define pletoh24(p) ((uint32_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint32_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint32_t)*((const uint8_t *)(p)+0)<<0)

#define pletoh32(p) ((uint32_t)*((const uint8_t *)(p)+3)<<24|  \
                     (uint32_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint32_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint32_t)*((const uint8_t *)(p)+0)<<0)

#define pletoh40(p) ((uint64_t)*((const uint8_t *)(p)+4)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+0)<<0)

#define pletoh48(p) ((uint64_t)*((const uint8_t *)(p)+5)<<40|  \
                     (uint64_t)*((const uint8_t *)(p)+4)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+0)<<0)

#define pletoh56(p) ((uint64_t)*((const uint8_t *)(p)+6)<<48|  \
                     (uint64_t)*((const uint8_t *)(p)+5)<<40|  \
                     (uint64_t)*((const uint8_t *)(p)+4)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+0)<<0)

#define pletoh64(p) ((uint64_t)*((const uint8_t *)(p)+7)<<56|  \
                     (uint64_t)*((const uint8_t *)(p)+6)<<48|  \
                     (uint64_t)*((const uint8_t *)(p)+5)<<40|  \
                     (uint64_t)*((const uint8_t *)(p)+4)<<32|  \
                     (uint64_t)*((const uint8_t *)(p)+3)<<24|  \
                     (uint64_t)*((const uint8_t *)(p)+2)<<16|  \
                     (uint64_t)*((const uint8_t *)(p)+1)<<8|   \
                     (uint64_t)*((const uint8_t *)(p)+0)<<0)

/* Pointer routines to put items out in a particular byte order.
 * These will work regardless of the byte alignment of the pointer.
 */

#define phton16(p, v) \
                    {                                       \
                    ((uint8_t*)(p))[0] = (uint8_t)((v) >> 8); \
                    ((uint8_t*)(p))[1] = (uint8_t)((v) >> 0); \
                    }

#define phton32(p, v) \
                    {                                         \
                    ((uint8_t*)(p))[0] = (uint8_t)((v) >> 24);  \
                    ((uint8_t*)(p))[1] = (uint8_t)((v) >> 16);  \
                    ((uint8_t*)(p))[2] = (uint8_t)((v) >> 8);   \
                    ((uint8_t*)(p))[3] = (uint8_t)((v) >> 0);   \
                    }

static inline void phton64(uint8_t *p, uint64_t v) {
    p[0] = (uint8_t)(v >> 56);
    p[1] = (uint8_t)(v >> 48);
    p[2] = (uint8_t)(v >> 40);
    p[3] = (uint8_t)(v >> 32);
    p[4] = (uint8_t)(v >> 24);
    p[5] = (uint8_t)(v >> 16);
    p[6] = (uint8_t)(v >> 8);
    p[7] = (uint8_t)(v >> 0);
}

/* Subtract two uint32_ts with respect to wraparound */
#define uint32_t_wraparound_diff(higher, lower) ((higher>lower)?(higher-lower):(higher+0xffffffff-lower+1))

uint8_t dpi_get_bits8(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits);
uint16_t dpi_get_bits16(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits,const uint32_t encoding);
uint32_t dpi_get_bits32(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits, const uint32_t encoding);
uint64_t dpi_get_bits64(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits, const uint32_t encoding);
uint32_t tvb_get_bits(struct dpi_pkt_st *pkt, const uint32_t bit_offset, const uint32_t no_of_bits, const uint32_t encoding);


#endif /* PINT_H */

/*
 * Editor modelines  -  http://www.wireshark.org/tools/modelines.html
 *
 * Local Variables:
 * c-basic-offset: 4
 * tab-width: 8
 * indent-tabs-mode: nil
 * End:
 *
 * ex: set shiftwidth=4 tabstop=8 expandtab:
 * :indentSize=4:tabSize=8:noTabs=true:
 */


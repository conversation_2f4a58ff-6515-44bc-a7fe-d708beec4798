#include <stdio.h>

#include "dpi_per.h"
#include "dpi_log.h"
#include "charsets.h"

#define SEQ_MAX_COMPONENTS 128

#define BYTE_ALIGN_OFFSET(offset) if (offset & 0x07) {offset = (offset & 0xfffffff8) + 8;}

/* this function reads a single bit */
uint32_t dpi_per_boolean(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, uint8_t *bool_val)
{
    uint8_t ch, mask;
    uint8_t value;

    if (dpi_get_uint8(pkt, offset >> 3, &ch) == -1)
        return offset;

    mask = 1 << (7 - (offset & 0x07));
    if (ch & mask) {
        value = 1;
    } else {
        value = 0;
    }

    if (bool_val) {
        *bool_val = value;
    }
    return offset + 1;
}


/* 10.9 General rules for encoding a length determinant --------------------

      NOTE 1 - (Tutorial) The procedures of this subclause are invoked when an explicit length field is needed
                for some part of the encoding regardless of whether the length count is bounded above
                (by PER-visible constraints) or not. The part of the encoding to which the length applies may
                be a bit string (with the length count in bits), an octet string (with the length count in octets),
                a known-multiplier character string (with the length count in characters), or a list of fields
                (with the length count in components of a sequence-of or set-of).

      NOTE 2 - (Tutorial) In the case of the ALIGNED variant if the length count is bounded above by an upper bound
                that is less than 64K, then the constrained whole number encoding is used for the length.
                For sufficiently small ranges the result is a bit-field, otherwise the unconstrained length ("n" say)
                is encoded into an octet-aligned bit-field in one of three ways (in order of increasing size):
        a)    ("n" less than 128) a single octet containing "n" with bit 8 set to zero;
        b)    ("n" less than 16K) two octets containing "n" with bit 8 of the first octet set to 1 and bit 7 set to zero;
        c)    (large "n") a single octet containing a count "m" with bit 8 set to 1 and bit 7 set to 1.
            The count "m" is one to four, and the length indicates that a fragment of the material follows
            (a multiple "m" of 16K items). For all values of "m", the fragment is then followed by another length encoding
            for the remainder of the material.

      NOTE 3 - (Tutorial) In the UNALIGNED variant, if the length count is bounded above by an upper bound that is less
            than 64K, then the constrained whole number encoding is used to encode the length in the minimum number of
            bits necessary to represent the range. Otherwise, the unconstrained length ("n" say) is encoded into a bit
            field in the manner described above in Note 2.

 */
uint32_t dpi_per_length_determinant(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, uint32_t *length, uint8_t *is_fragmented)
{
    uint8_t byte;
    uint32_t len;
    int num_bits;
    int i, bit, str_length, str_index;
    uint8_t tmp;

    if (!length) {
        length = &len;
    }
    if (is_fragmented) {
        *is_fragmented = 0;
    }

    /* byte aligned */
    if (ctx->aligned) {
        BYTE_ALIGN_OFFSET(offset);
        if (dpi_get_uint8(pkt, offset >> 3, &byte) == -1)
            return offset;
        offset += 8;
    } else {
        char str[256 + 64 + 1 + 1];
        uint32_t val;

        val = 0;

        /* prepare the string (max number of bits + quartet separators + prepended space) */
        str_length = 256+64+1;
        //str=(char *)wmem_alloc(wmem_packet_scope(), str_length+1);
        str_index = 0;

        str_length = snprintf(str, str_length + 1, " ");
        for (bit = 0; bit < ((int)(offset & 0x07)); bit++){
            if (bit && (!(bit % 4))) {
                if (str_index < str_length) str[str_index++] = ' ';
            }
            if (str_index < str_length) str[str_index++] = '.';
        }
        /* read the bits for the int */
        num_bits = 8;
        for (i = 0; i < num_bits; i++) {
            if(bit && (!(bit % 4))) {
                if (str_index < str_length) str[str_index++] = ' ';
            }
            if(bit&&(!(bit%8))){
                if (str_index < str_length) str[str_index++] = ' ';
            }
            bit++;
            offset = dpi_per_boolean(pkt, offset, ctx, &tmp);
            val <<= 1;
            if (tmp) {
                val |= 1;
                if (str_index < str_length) str[str_index++] = '1';
                if (i == 0) { /* bit 8 is 1, so not a single byte length */
                    num_bits = 16;
                }
                else if (i == 1 && val == 3) { /* bits 8 and 7 both 1, so unconstrained */
                    if (!is_fragmented) {
                        *length = 0;
                        DPI_LOG(DPI_LOG_DEBUG, "10.9 Unconstrained");
                        return offset;
                    } else {
                        num_bits = 8;
                        *is_fragmented = 1;
                    }
                }
            } else {
                if (str_index < str_length) str[str_index++] = '0';
            }
        }
        str[str_index] = '\0'; /* Terminate string */
        if (is_fragmented && *is_fragmented == 1) {
            *length = val & 0x3f;
            if (*length > 4 || *length == 0) {
                *length = 0;
                *is_fragmented = 0;
                DPI_LOG(DPI_LOG_DEBUG, "10.9 Unconstrained unexpected fragment count");
                return offset;
            }
            *length *= 0x4000;
            return offset;
        }
        else if ((val & 0x80) == 0 && num_bits == 8) {
            *length = val;
            return offset;
        }
        else if (num_bits == 16) {
            *length = val & 0x3fff;
            return offset;
        }
        *length = 0;
        DPI_LOG(DPI_LOG_DEBUG, "10.9 Unaligned");
        return offset;
    }

    /* 10.9.3.6 */
    if ((byte & 0x80)==0) {
        *length=byte;
        return offset;
    }

    /* 10.9.3.7 */
    if ((byte & 0xc0) == 0x80) {
        uint8_t u8;
        if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
            return offset;
        *length = (byte & 0x3f);
        *length = ((*length) << 8) + u8;
        offset += 8;
        return offset;
    }
    /* 10.9.3.8.1 */
    else if (is_fragmented){
        *length = byte & 0x3f;
        if (*length > 4 || *length == 0) {
            *length = 0;
            DPI_LOG(DPI_LOG_DEBUG, "10.9 Unconstrained unexpected fragment count");
            return offset;
        }
        *length *= 0x4000;
        *is_fragmented = 1;
        return offset;
    }
    *length = 0;
    DPI_LOG(DPI_LOG_DEBUG, "10.9.3.8.1");
    return offset;
}


/* we currently only handle integers up to 32 bits in length. */
uint32_t dpi_per_integer(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, int *value)
{
    uint32_t i, length;
    uint32_t val;
    uint8_t u8;

    /* 12.2.6 b */
    offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);
    /* gassert here? */
    if (length > 4) {
        DPI_LOG(DPI_LOG_DEBUG, "too long integer(per_integer)");
        length = 4;
    }

    val = 0;
    for (i = 0; i < length; i++){
        if (i == 0) {
            if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
                return offset;
            u8 &= 0x80;
            if (u8) {
                /* negative number */
                val = 0xffffffff;
            } else {
                /* positive number */
                val = 0;
            }
        }
        if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
            return offset;
        
        val = (val << 8) | u8;
        offset += 8;
    }

    if(value){
        *value=val;
    }

    return offset;
}


/* this function reads a constrained integer  with or without a
   PER visible extension marker present

   has_extension==TRUE  would map to asn constructs such as:
        rfc-number    INTEGER (1..32768, ...)
   while has_extension==FALSE would map to:
        t35CountryCode    INTEGER (0..255)

   it only handles integers that fit inside a 32 bit integer
10.5.1 info only
10.5.2 info only
10.5.3 range=ub-lb+1
10.5.4 empty range
10.5.5 info only
    10.5.6 unaligned version
10.5.7 aligned version
10.5.7.1 decoding of 0-255 1-8 bits
10.5.7.2 decoding og 0-256 8 bits
10.5.7.3 decoding of 0-65535 16 bits
    10.5.7.4
*/
uint32_t dpi_per_constrained_integer(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx,
        uint32_t min, uint32_t max, uint32_t *value, uint8_t has_extension)
{
    uint8_t u8;
    uint32_t range, val;
    int val_start, val_length;
//    nstime_t timeval;
    int num_bits;

    if (has_extension) {
        uint8_t extension_present;
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
        if(extension_present){
            offset = dpi_per_integer(pkt, offset, ctx, (int*)value);
            return offset;
        }
    }

    /* 10.5.3 Let "range" be defined as the integer value ("ub" - "lb"   1), and let the value to be encoded be "n".
     * 10.5.7    In the case of the ALIGNED variant the encoding depends on whether
     *            d)    "range" is greater than 64K (the indefinite length case).
     */
    if (((max-min) > 65536) && (ctx->aligned)) {
        /* just set range really big so it will fall through
           to the bottom of the encoding */
        range = 1000000;
    } else {
        /* Really ugly hack.
         * We should really use guint64 as parameters for min/max.
         * This is to prevent range from being 0 if
         * the range for a signed integer spans the entire 32 bit range.
         * Special case the 2 common cases when this can happen until
         * a real fix is implemented.
         */
        if ((max == 0x7fffffff && min == 0x80000000) || (max == 0xffffffff && min == 0x00000000)) {
            range = 0xffffffff;
        } else {
            range = max - min + 1;
        }
    }

    val = 0;
    /* 10.5.4 If "range" has the value 1, then the result of the encoding shall be an empty bit-field (no bits).*/

    if (range == 1) {
        val_start = offset >> 3;
        val_length = 0;
        val = min;
    } else if ((range <= 255) || (!ctx->aligned)) {
        /* 10.5.7.1
         * 10.5.6    In the case of the UNALIGNED variant the value ("n" - "lb") shall be encoded
         * as a non-negative  binary integer in a bit field as specified in 10.3 with the minimum
         * number of bits necessary to represent the range.
         */
        int i, length;
        uint32_t mask,mask2;
        /* We only handle 32 bit integers */
        mask  = 0x80000000;
        mask2 = 0x7fffffff;
        i = 32;
        while ((range & mask) == 0) {
            i = i - 1;
            mask = mask >> 1;
            mask2 = mask2 >> 1;
        }
        if ((range & mask2) == 0)
            i = i - 1;

        num_bits = i;
        length = (num_bits + 7) >> 3;
        if (range <= 2) {
            num_bits = 1;
        }

        val_start = (offset) >> 3;
        val_length = length;
        val = (uint32_t)dpi_get_bits64(pkt, offset, num_bits, ENC_BIG_ENDIAN);

        /* The actual value */
        val += min;
        offset = offset + num_bits;
    } else if (range == 256) {
        /* 10.5.7.2 */

        /* in the aligned case, align to byte boundary */
        BYTE_ALIGN_OFFSET(offset);
        if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
            return offset;
        val = u8;
        offset += 8;

        val_start = (offset >> 3) - 1;
        val_length = 1;
        val += min;
    } else if (range <= 65536) {
        /* 10.5.7.3 */

        /* in the aligned case, align to byte boundary */
        BYTE_ALIGN_OFFSET(offset);
        if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
            return offset;
        val = u8;
        val <<= 8;
        offset += 8;
        if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
            return offset;        
        val |= u8;
        offset += 8;
        val_start = (offset >> 3) - 2;
        val_length = 2;
        val += min;
    } else {
        int i,num_bytes;
        uint8_t bit;

        /* 10.5.7.4 */
        /* 12.2.6 */
        offset = dpi_per_boolean(pkt, offset, ctx, &bit);
        num_bytes = bit;
        offset = dpi_per_boolean(pkt, offset, ctx, &bit);
        num_bytes = (num_bytes << 1) | bit;

        num_bytes++;  /* lower bound for length determinant is 1 */
        /* byte aligned */
        BYTE_ALIGN_OFFSET(offset);
        val = 0;
        for (i = 0; i < num_bytes; i++){
            if (dpi_get_uint8(pkt, offset >> 3, &u8) == -1)
                return offset;            
            val = (val << 8) | u8;
            offset += 8;
        }
        val_start = (offset >> 3) - (num_bytes + 1);
        val_length = num_bytes + 1;
        val += min;
    }

    if (value) *value = val;
    return offset;
}

/* 10.6   normally small non-negative whole number */
uint32_t dpi_per_normally_small_nonnegative_whole_number(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *actx, uint32_t *length)
{
    uint8_t u8;
    uint8_t u16;
    uint8_t u32;
    uint8_t small_number, length_bit;
    uint32_t len, length_determinant;

    if (!length) {
        length = &len;
    }

    offset = dpi_per_boolean(pkt, offset, actx, &small_number);
    if (!small_number) {
        int i;
        /* 10.6.1 */
        *length=0;
        for (i = 0; i < 6; i++){
            offset = dpi_per_boolean(pkt, offset, actx, &length_bit);
            *length <<= 1;
            if (length_bit) {
                *length |= 1;
            }
        }
        return offset;
    }

    /* 10.6.2 */
    offset = dpi_per_length_determinant(pkt, offset, actx, &length_determinant, NULL);
    switch (length_determinant) {
        case 0:
            *length = 0;
            break;
        case 1:
            *length = dpi_get_bits8(pkt, offset, 8);
            offset += 8;
            break;
        case 2:
            *length = dpi_get_bits16(pkt, offset, 16, ENC_BIG_ENDIAN);
            offset += 16;
            break;
        case 3:
            *length = dpi_get_bits32(pkt, offset, 24, ENC_BIG_ENDIAN);
            offset += 24;
            break;
        case 4:
            *length = dpi_get_bits32(pkt, offset, 32, ENC_BIG_ENDIAN);
            offset += 32;
            break;
        default:
            //dissect_per_not_decoded_yet(tree, actx->pinfo, tvb, "too long integer(per_normally_small_nonnegative_whole_number)");
            offset += 8 * length_determinant;
            *length = 0;
            return offset;
    }
    return offset;
}

/* 13 Encoding the enumerated type */
uint32_t dpi_per_enumerated(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, uint32_t root_num, 
        uint32_t *value, uint8_t has_extension, uint32_t ext_num, uint32_t *value_map)
{
    uint32_t enum_index, val;
    uint32_t start_offset = offset;
    uint8_t extension_present = 0;

    if (has_extension) {
        /* Extension bit */
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
    }

    if (!extension_present) {
        /* 13.2  */
        offset = dpi_per_constrained_integer(pkt, offset, ctx, 0, root_num - 1, &enum_index, 0);
    } else {
        /* 13.3  */
        if (ext_num == 1) {
            /* 10.5.4    If "range" has the value 1,
             * then the result of the encoding shall be
             * an empty bit-field (no bits).
             */
            enum_index = 0;
        } else {
            /* 13.3 ".. and the value shall be added to the field-list as a
             * normally small non-negative whole number whose value is the
             * enumeration index of the additional enumeration and with "lb" set to 0.."
             */
            offset = dpi_per_normally_small_nonnegative_whole_number(pkt, offset, ctx, &enum_index);
        }
        enum_index += root_num;
    }
    val = (value_map && (enum_index < (root_num + ext_num))) ? value_map[enum_index] : enum_index;
    if (value) 
        *value = val;
    return offset;
}


/* 10 Encoding procedures -------------------------------------------------- */

/* 10.2 Open type fields --------------------------------------------------- */
uint32_t dpi_per_open_type_internal(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, void* type_cb/*, asn1_cb_variant variant*/)
{
    uint32_t type_length, start_offset, end_offset, fragmented_length = 0, pdu_length, pdu_offset;
    uint8_t is_fragmented;
    uint8_t append[1500];
    struct dpi_pkt_op_st dst;
    struct dpi_pkt_op_st *pkt_dst=&dst;
    pkt_dst->payload = &append[0];
    pkt_dst->payload_len = 0;
        
    start_offset = offset;
    do {
        offset = dpi_per_length_determinant(pkt, offset, ctx, &type_length, &is_fragmented);
        if (ctx->aligned)
            BYTE_ALIGN_OFFSET(offset);
        if (is_fragmented) {
            if (fragmented_length == 0) {
            }
            dpi_append_octet_aligned(pkt, offset, 8 * type_length, pkt_dst, sizeof(append));
            offset += 8*type_length;
            fragmented_length += type_length;
        }
    } while (is_fragmented);
    if (fragmented_length) {
        if (type_length) {
            dpi_append_octet_aligned(pkt, offset, 8 * type_length, pkt_dst, sizeof(append));
            fragmented_length += type_length;
        }
        pdu_offset = 0;
        pdu_length = fragmented_length;
    } else {
        ((struct dpi_pkt_st*)pkt_dst)->payload = pkt->payload;
        pkt_dst->payload_len = pkt->payload_len;
        pdu_offset = offset;
        pdu_length = type_length;
    }
    end_offset = offset + type_length * 8;

    if (type_cb) {
        ((per_type_fn)type_cb)((struct dpi_pkt_st*)pkt_dst, pdu_offset, ctx);
    }

    return end_offset;
}

/* 22 Encoding the choice type */
uint32_t dpi_per_choice(struct dpi_pkt_st *pkt, uint32_t offset, const per_choice_t *choice, per_ctx_t *ctx, int *value)
{
    uint8_t /*extension_present,*/ extension_flag;
    int extension_root_entries;
    int extension_addition_entries;
    uint32_t choice_index;
    int i, idx, cidx;
    uint32_t ext_length;
    uint32_t old_offset = offset;

    if (value) *value = -1;

    /* 22.5 */
    if (choice[0].extension == ASN1_NO_EXTENSIONS){
        /*extension_present = FALSE; ?? */
        extension_flag = 0;
    } else {
        /*extension_present = TRUE; ?? */
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_flag);
    }

    /* count the number of entries in the extension root and extension addition */
    extension_root_entries = 0;
    extension_addition_entries = 0;
    for (i=0; choice[i].p_id; i++) {
        switch(choice[i].extension){
            case ASN1_NO_EXTENSIONS:
            case ASN1_EXTENSION_ROOT:
                extension_root_entries++;
                break;
            case ASN1_NOT_EXTENSION_ROOT:
                extension_addition_entries++;
                break;
        }
    }

    if (!extension_flag) {  /* 22.6, 22.7 */
        if (extension_root_entries == 1) {  /* 22.5 */
            choice_index = 0;
        } else {
            offset = dpi_per_constrained_integer(pkt, offset, ctx, 0, extension_root_entries - 1, &choice_index, 0);
        }

        idx = -1; cidx = choice_index;
        for (i=0; choice[i].p_id; i++) {
            if(choice[i].extension != ASN1_NOT_EXTENSION_ROOT){
                if (!cidx) { idx = i; break; }
                cidx--;
            }
        }
    } else {  /* 22.8 */
        offset = dpi_per_normally_small_nonnegative_whole_number(pkt, offset, ctx, &choice_index);
        offset = dpi_per_length_determinant(pkt, offset, ctx, &ext_length, NULL);

        idx = -1; cidx = choice_index;
        for (i=0; choice[i].p_id; i++) {
            if(choice[i].extension == ASN1_NOT_EXTENSION_ROOT){
                if (!cidx) { idx = i; break; }
                cidx--;
            }
        }
    }

    if (idx != -1) {
        if (!extension_flag) {
            if (choice[idx].func)
                offset = choice[idx].func(pkt, offset, ctx);
        } else {
            if (choice[idx].func)
                choice[idx].func(pkt, offset, ctx);
            offset += ext_length * 8;
        }
    } else {
        if (!extension_flag) {
            DPI_LOG(DPI_LOG_DEBUG, "unknown extension root index in choice");
        } else {
            offset += ext_length * 8;
        }
    }

    if (value && (idx != -1))
        *value = choice[idx].value;
    return offset;
}


/* this functions decodes a SEQUENCE
   it can only handle SEQUENCES with at most 32 DEFAULT or OPTIONAL fields
18.1 extension bit
18.2 optional/default items in root
18.3 we ignore the case where n>64K
18.4 the root sequence
       18.5
       18.6
       18.7
       18.8
       18.9
*/
uint32_t dpi_per_sequence(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, const per_sequence_t *sequence)
{
    uint8_t /*extension_present,*/ extension_flag, optional_field_flag;
    uint32_t old_offset = offset;
    uint32_t i, j, num_opts;
    uint32_t optional_mask[SEQ_MAX_COMPONENTS >> 5];

    /* first check if there should be an extension bit for this CHOICE.
       we do this by just checking the first choice arm
     */
    /* 18.1 */
    extension_flag = 0;
    if (sequence[0].extension==ASN1_NO_EXTENSIONS) {
        /*extension_present=0;  ?? */
    } else {
        /*extension_present=1; ?? */
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_flag);
    }
    /* 18.2 */
    num_opts = 0;
    for (i = 0; sequence[i].p_id; i++) {
        if ((sequence[i].extension != ASN1_NOT_EXTENSION_ROOT) && (sequence[i].optional == ASN1_OPTIONAL)) {
            num_opts++;
        }
    }
    if (num_opts > SEQ_MAX_COMPONENTS) {
        DPI_LOG(DPI_LOG_DEBUG, "too many optional/default components");
    }

    memset(optional_mask, 0, sizeof(optional_mask));
    for (i = 0; i < num_opts; i++) {
        offset = dpi_per_boolean(pkt, offset, ctx, &optional_field_flag);
        if (optional_field_flag) {
            optional_mask[i >> 5] |= 0x80000000 >> (i & 0x1f);
        }
    }


    /* 18.4 */
    for (i = 0, j = 0; sequence[i].p_id; i++){
        if ((sequence[i].extension == ASN1_NO_EXTENSIONS)
                ||(sequence[i].extension == ASN1_EXTENSION_ROOT)) {
            if (sequence[i].optional == ASN1_OPTIONAL) {
                uint8_t is_present;
                if (num_opts == 0){
                    continue;
                }
                is_present = (0x80000000 >> (j & 0x1f)) & optional_mask[j >> 5];
                num_opts--;
                j++;
                if (!is_present) {
                    continue;
                }
            }
            if (sequence[i].func) {
                offset = sequence[i].func(pkt, offset, ctx);
            } else {
                DPI_LOG(DPI_LOG_DEBUG, "per sequence func is null");
            }
        }
    }


    if (extension_flag) {
        uint8_t extension_bit;
        uint32_t num_known_extensions;
        uint32_t num_extensions;
        uint32_t extension_mask;

        offset = dpi_per_normally_small_nonnegative_whole_number(pkt, offset, ctx, &num_extensions);
        /* the X.691 standard is VERY unclear here.
           there is no mention that the lower bound lb for this
           (apparently) semiconstrained value is 1,
           apart from the NOTE: comment in 18.8 that this value can
           not be 0.
           In my book, there is a semantic difference between having
           a comment that says that the value can not be zero
           and stating that the lb is 1.
           I don't know if this is right or not but it makes
           some of the very few captures I have decode properly.

           It could also be that the captures I have are generated by
           a broken implementation.
           If this is wrong and you don't report it as a bug
           then it won't get fixed!
        */
        num_extensions += 1;
        if (num_extensions > 32) {
            DPI_LOG(DPI_LOG_DEBUG, "too many extensions");
        }

        extension_mask = 0;
        for (i = 0; i < num_extensions; i++) {
            offset = dpi_per_boolean(pkt, offset, ctx, &extension_bit);
            extension_mask = (extension_mask << 1) | extension_bit;
        }

        /* find how many extensions we know about */
        num_known_extensions = 0;
        for (i = 0; sequence[i].p_id; i++) {
            if (sequence[i].extension == ASN1_NOT_EXTENSION_ROOT){
                num_known_extensions++;
            }
        }

        /* decode the extensions one by one */
        for (i = 0; i < num_extensions; i++){
            uint32_t length;
            uint32_t new_offset;
            uint32_t difference;
            uint32_t extension_index;
            uint32_t k;

            if (!((1U << (num_extensions - 1 - i)) & extension_mask)) {
                /* this extension is not encoded in this PDU */
                continue;
            }

            offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);

            if (i >= num_known_extensions) {
                /* we don't know how to decode this extension */
                offset += length * 8;
                continue;
            }

            extension_index = 0;
            for (j = 0, k = 0; sequence[j].p_id; j++) {
                if (sequence[j].extension == ASN1_NOT_EXTENSION_ROOT) {
                    if (k == i) {
                        extension_index = j;
                        break;
                    }
                    k++;
                }
            }

            if (sequence[extension_index].func) {
                new_offset = sequence[extension_index].func(pkt, offset, ctx);
                offset += length*8;
                difference = offset - new_offset;
                /* A difference of 7 or less might be byte aligning */
                /* Difference could be 8 if open type has no bits and the length is 1 */
                if ((length > 1) && (difference > 7)) {

                }
            } else {
                offset += length * 8;
            }
        }
    }
    return offset;
}


/* 19 this function dissects a sequence of */
static uint32_t dpi_per_sequence_of_helper(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *actx, per_type_fn func, uint32_t length)
{
    uint32_t i;

    for (i = 0; i < length; i++) {
        //uint32_t lold_offset = offset;
        offset = (*func)(pkt, offset, actx);
    }
    
    return offset;
}


/* this function dissects a constrained sequence of */
uint32_t dpi_per_constrained_sequence_of(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, 
        const per_sequence_t *seq, int min_len, int max_len, uint8_t has_extension)
{
    uint32_t old_offset=offset;
    uint32_t length;

    /* 19.4    If there is a PER-visible constraint and an extension marker is present in it,
     * a single bit shall be added to the field-list in a bit-field of length one
     */
    if (has_extension) {
        uint8_t extension_present;
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
    }

    /* 19.5 if min==max and min,max<64k ==> no length determinant */
    if ((min_len == max_len) && (min_len < 65536)){
        length = min_len;
        goto call_sohelper;
    }

    /* 19.6 ub>=64k or unset */
    if ((max_len >= 65536) || (max_len == NO_BOUND)) {
        /* no constraint, see 10.9.4.2 */
        offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);
        goto call_sohelper;
    }

    /* constrained whole number for number of elements */
    offset = dpi_per_constrained_integer(pkt, offset, ctx, min_len, max_len, &length, 0);
    
call_sohelper:

    old_offset = offset;
    offset = dpi_per_sequence_of_helper(pkt, offset, ctx, seq->func, length);

    if (offset == old_offset)
        length = 0;
    else if (offset >> 3 == old_offset >> 3)
        length = 1;
    else
        length = (offset >> 3) - (old_offset >> 3);

    return offset;
}



/* this function dissects an OCTET STRING
    16.1
    16.2
    16.3
    16.4
    16.5
    16.6
    16.7
    16.8

   max_len or min_len == NO_BOUND means there is no lower/upper constraint

   hf_index can either be a FT_BYTES or an FT_STRING
*/
uint32_t dpi_per_octet_string(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, int min_len, int max_len, uint8_t has_extension,
        struct dpi_pkt_op_st *pkt_dst, int dst_max_len)
{
    int val_start = 0, val_length;
    uint32_t length = 0, fragmented_length = 0;;
    uint8_t is_fragmented = 0;
    uint8_t flag = 0;

    if (has_extension) {  /* 16.3 an extension marker is present */
        uint8_t extension_present;
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
        if (extension_present) 
            max_len = NO_BOUND;    /* skip to 16.8 */
    }

    if (min_len == NO_BOUND) {
        min_len = 0;
    }
    if (max_len == 0) {  /* 16.5 if the length is 0 bytes there will be no encoding */
        val_start = offset >> 3;
        val_length = 0;

    } else if ((min_len == max_len) && (max_len <= 2)) {
        /* 16.6 if length is fixed and less than or equal to two bytes*/
        val_start = offset >> 3;
        val_length = min_len;
        dpi_append_octet_aligned(pkt, offset, val_length * 8, pkt_dst, dst_max_len);
        flag = 1;
        /* Add new data source if the offet was unaligned */
        if ((offset & 7) != 0) {
            //add_new_data_source(actx->pinfo, out_tvb, "Unaligned OCTET STRING");
        }
        offset += min_len * 8;
    } else if ((min_len == max_len) && (min_len < 65536)) {
        /* 16.7 if length is fixed and less than to 64k*/

        /* align to byte */
        if (ctx->aligned){
            BYTE_ALIGN_OFFSET(offset);
        }
        val_start = offset>>3;
        val_length = min_len;        
        dpi_append_octet_aligned(pkt, offset, val_length * 8, pkt_dst, dst_max_len);
        flag = 1;
        if ((offset & 7) != 0) {
            //add_new_data_source(actx->pinfo, out_tvb, "Unaligned OCTET STRING");
        }
        offset += min_len * 8;

    } else {  /* 16.8 */
        if (max_len > 0) {
            offset = dpi_per_constrained_integer(pkt, offset, ctx, min_len, max_len, &length, 0);
        } else {
        next_fragment:
            offset = dpi_per_length_determinant(pkt, offset, ctx, &length, &is_fragmented);
        }

        if (length || fragmented_length) {
            /* align to byte */
            if (ctx->aligned) {
                BYTE_ALIGN_OFFSET(offset);
            }
            if (is_fragmented) {
                //if (fragmented_length == 0)
                //    out_tvb = tvb_new_composite();
                
                dpi_append_octet_aligned(pkt, offset, length * 8, pkt_dst, dst_max_len);
                flag = 1;
                offset += length * 8;
                fragmented_length += length;
                goto next_fragment;
            }
            if (fragmented_length) {
                if (length) {
                    dpi_append_octet_aligned(pkt, offset, length * 8, pkt_dst, dst_max_len);
                    flag = 1;
                    fragmented_length += length;
                }
            } else {
            
                dpi_append_octet_aligned(pkt, offset, length * 8, pkt_dst, dst_max_len);
                flag = 1;
                if ((offset & 7) != 0) {
                    //add_new_data_source(actx->pinfo, out_tvb, "Unaligned OCTET STRING");
                }
            }
        } else {
            val_start = offset>>3;
        }
        val_length = fragmented_length ? fragmented_length : length;
        offset += length * 8;
    }

    if (!flag) {
        uint32_t copy_len;
        copy_len = (uint32_t)val_length < (dst_max_len - pkt_dst->payload_len) ? (uint32_t)val_length : (dst_max_len - pkt_dst->payload_len);
        memcpy((void *)(pkt_dst->payload + pkt_dst->payload_len), pkt->payload + val_start, copy_len);
        pkt_dst->payload_len += copy_len;
    }
    
    return offset;
}


uint32_t dpi_per_bit_string(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, 
        int min_len, int max_len, uint8_t has_extension, struct dpi_pkt_op_st *pkt_dst, int dst_max_len)
{
    /*gint val_start, val_length;*/
    uint32_t length, fragmented_length = 0;
    uint8_t is_fragmented = 0;

    /* 15.8 if the length is 0 bytes there will be no encoding */
    if(max_len==0) {
        if (pkt_dst)
            pkt_dst->payload_len = 0;
        return offset;
    }

    if (min_len == NO_BOUND) {
        min_len = 0;
    }
    /* 15.6 If an extension marker is present in the size constraint specification of the bitstring type,
     * a single bit shall be added to the field-list in a bit-field of length one.
     * The bit shall be set to 1 if the length of this encoding is not within the range of the extension root,
     * and zero otherwise.
     */
     if (has_extension) {
         uint8_t extension_present;
         offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
        if (extension_present) {
        next_fragment1:
            offset = dpi_per_length_determinant(pkt, offset, ctx, &length, &is_fragmented);
            if (length || fragmented_length) {
                /* align to byte */
                if (ctx->aligned) {
                    BYTE_ALIGN_OFFSET(offset);
                }
                if (is_fragmented) {
                    //if(fragmented_length==0)
                    //    fragmented_tvb = tvb_new_composite();
                    
                    dpi_append_octet_aligned(pkt, offset, length, pkt_dst, dst_max_len);
                    offset += length;
                    fragmented_length += length;
                    goto next_fragment1;
                }
                if (fragmented_length) {
                    if (length) {
                        dpi_append_octet_aligned(pkt, offset, length, pkt_dst, dst_max_len);
                        fragmented_length += length;
                    }
                    //out_tvb = dissect_per_bit_string_display(fragmented_tvb, 0, actx, tree, hf_index, hfi,
                    //                     fragmented_length);
                } else
                    dpi_append_octet_aligned(pkt, offset, length, pkt_dst, dst_max_len);
            }
            /* XXX: ?? */
            /*val_start = offset>>3;*/
            /*val_length = (length+7)/8;*/
            offset+=length;
            return offset;
         }
     }

    /* 15.9 if length is fixed and less than or equal to sixteen bits*/
    if ((min_len == max_len) && (max_len <= 16)) {
        dpi_append_octet_aligned(pkt, offset, min_len, pkt_dst, dst_max_len);
        offset+=min_len;
        return offset;
    }


    /* 15.10 if length is fixed and less than to 64kbits*/
    if((min_len == max_len) && (min_len < 65536)){
        /* (octet-aligned in the ALIGNED variant)
         * align to byte
         */
        if (ctx->aligned){
            BYTE_ALIGN_OFFSET(offset);
        }
        
        dpi_append_octet_aligned(pkt, offset, min_len, pkt_dst, dst_max_len);
        offset+=min_len;
        return offset;
    }

    /* 15.11 */
    if (max_len != NO_BOUND) {
        offset = dpi_per_constrained_integer(pkt, offset, ctx, min_len, max_len, &length, 0);
    } else {
    next_fragment2:
        offset = dpi_per_length_determinant(pkt, offset, ctx, &length, &is_fragmented);
    }
    if (length || fragmented_length) {
        /* align to byte */
        if (ctx->aligned){
            BYTE_ALIGN_OFFSET(offset);
        }
        if(is_fragmented){
            //if(fragmented_length==0)
            //    fragmented_tvb = tvb_new_composite();
            
            dpi_append_octet_aligned(pkt, offset, length, pkt_dst, dst_max_len);
            offset += length;
            fragmented_length += length;
            goto next_fragment2;
        }
        if(fragmented_length){
            if(length){
                dpi_append_octet_aligned(pkt, offset, length, pkt_dst, dst_max_len);
                fragmented_length += length;
            }

        } else
            dpi_append_octet_aligned(pkt, offset, length, pkt_dst, dst_max_len);
    }
    /* XXX: ?? */
    /*val_start = offset>>3;*/
    /*val_length = (length+7)/8;*/
    offset+=length;
    return offset;
}

/* 23 Encoding the object identifier type */
static uint32_t dissect_per_any_oid(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx)
{
    uint32_t length = 0;
    const char *str;

    offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);
    if (ctx->aligned) BYTE_ALIGN_OFFSET(offset);
    /* Add new data source if the offet was unaligned */

    //offset += 8 * length;

    return offset;
}

static uint32_t dpi_per_any_oid_str(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx)
{
    offset = dissect_per_any_oid(pkt, offset, ctx);

    return offset;
}

uint32_t dpi_per_object_identifier_str(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx)
{
    return dpi_per_any_oid_str(pkt, offset, ctx);
}

/* XXX we don't do >64k length strings   yet */
static uint32_t dissect_per_restricted_character_string_sorted(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, int min_len, int max_len, uint8_t has_extension, uint16_t lb, uint16_t ub)
{
    uint32_t length;
    uint8_t byte_aligned;
    uint8_t char_pos;
    uint32_t old_offset;

    /* xx.x if the length is 0 bytes there will be no encoding */
    if (max_len == 0) {
        return offset;
    }

    if (min_len == NO_BOUND) {
        min_len = 0;
    }

    if (has_extension) {
        uint8_t extension_present;
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
        //if (!display_internal_per_fields) PROTO_ITEM_SET_HIDDEN(actx->created_item);
        if (extension_present) {
            min_len = NO_BOUND;
            max_len = NO_BOUND;
        }
    }

    byte_aligned = 1;
    if ((min_len == max_len) && (max_len <= 2)) {
        byte_aligned = 0;
    }
    if ((max_len != NO_BOUND) && (max_len < 2)) {
        byte_aligned = 0;
    }

    /* xx.x */
    length = max_len;
    if (max_len == NO_BOUND) {
        offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);
        /* the unconstrained strings are always byte aligned (27.6.3)*/
        byte_aligned = 1;
    }
    else if (min_len != max_len) {
        offset = dpi_per_constrained_integer(pkt, offset, ctx, min_len, max_len, &length, 0);
        //if (!display_internal_per_fields) PROTO_ITEM_SET_HIDDEN(actx->created_item);
    }

    if (!length) {
        /* there is no string at all, so don't do any byte alignment */
        /* byte_aligned=FALSE; */
        /* Advance offset to next 'element' */
        offset = offset + 1;
    }

    if ((byte_aligned) && (ctx->aligned)) {
        BYTE_ALIGN_OFFSET(offset);
    }

    old_offset = offset;
    
    return offset;
}

uint32_t dpi_per_restricted_character_string(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, int min_len, int max_len, uint8_t has_extension)
{
    /* Not a known-multiplier character string: enforce lb and ub to max values */
    return dissect_per_restricted_character_string_sorted(pkt, offset, ctx, min_len, max_len, has_extension, 0, 65535);
}

uint32_t dpi_per_BMPString(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, int min_len, int max_len, uint8_t has_extension)
{
    uint32_t length;

    /* xx.x if the length is 0 bytes there will be no encoding */
    if (max_len == 0) {
        return offset;
    }

    if (min_len == NO_BOUND) {
        min_len = 0;
    }

    /* xx.x */
    length = max_len;
    if (min_len != max_len) {
        offset = dpi_per_constrained_integer(pkt, offset, ctx, min_len, max_len, &length, 0);
        //if (!display_internal_per_fields) PROTO_ITEM_SET_HIDDEN(actx->created_item);
    }

    /* align to byte boundary */
    BYTE_ALIGN_OFFSET(offset);

    if (length >= 1024) {
        //dissect_per_not_decoded_yet(tree, actx->pinfo, tvb, "BMPString too long");
        length = 1024;
    }

    offset += (length << 3) * 2;

    return offset;
}

uint32_t dpi_per_sequence_of(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, const per_sequence_t *seq)
{
    uint32_t old_offset = offset;
    uint32_t length;

    /* semi-constrained whole number for number of elements */
    /* each element encoded as 10.9 */

    offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);

    offset = dpi_per_sequence_of_helper(pkt, offset, ctx, seq->func, length);

    return offset;
}

uint32_t dpi_per_set_of(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, const per_sequence_t *seq)
{
    /* for basic-per  a set-of is encoded in the same way as a sequence-of */
    offset = dpi_per_sequence_of(pkt, offset, ctx, seq);
    return offset;
}

/* 17 Encoding the null type */
uint32_t dpi_per_null(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx)
{
    return offset;
}

uint32_t dpi_per_size_constrained_type(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, per_type_fn type_cb)
{
    offset = type_cb(pkt, offset, ctx);

    return offset;
}

/* 19 this function dissects a sequence of */
static uint32_t dissect_per_sequence_of_helper(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *actx, t124_per_type_fn func, uint32_t length, struct t124_info* local_info, rdp_info* info)
{
    uint32_t i;

    for (i = 0; i < length; i++) {
        //uint32_t lold_offset = offset;
        offset = (*func)(pkt, offset, actx, local_info, info);
    }

    return offset;
}

static uint32_t dissect_per_sequence_of(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, const t124_per_sequence_t *seq, struct t124_info* local_info, rdp_info* info)
{
    uint32_t old_offset = offset;
    uint32_t length;

    /* semi-constrained whole number for number of elements */
    /* each element encoded as 10.9 */

    offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);

    offset = dissect_per_sequence_of_helper(pkt, offset, ctx, seq->func, length, local_info, info);

    return offset;
}

uint32_t dissect_per_set_of(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, const t124_per_sequence_t *seq, struct t124_info* local_info, rdp_info* info)
{
    /* for basic-per  a set-of is encoded in the same way as a sequence-of */
    offset = dissect_per_sequence_of(pkt, offset, ctx, seq, local_info, info);
    return offset;
}

/* 23 Encoding the object identifier type */
static uint32_t dissect_per_any_oid_rdp(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, struct t124_info* local_info, rdp_info* info)
{
    uint32_t length = 0;
    const char *str;

    offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);
    if (ctx->aligned) BYTE_ALIGN_OFFSET(offset);

    offset += 8 * length;

    return offset;
}

static uint32_t dissect_per_any_oid_str(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, struct t124_info* local_info, rdp_info* info)
{
    offset = dissect_per_any_oid_rdp(pkt, offset, ctx, local_info, info);
    return offset;
}

uint32_t dissect_per_object_identifier_str(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, struct t124_info* local_info, rdp_info* info)
{
    return dissect_per_any_oid_str(pkt, offset, ctx, local_info, info);
}

uint32_t dissect_per_size_constrained_type(struct dpi_pkt_st * pkt, uint32_t offset, per_ctx_t *ctx, t124_per_type_fn type_cb, struct t124_info* local_info, rdp_info* info)
{
    offset = type_cb(pkt, offset, ctx, local_info, info);

    return offset;
}

/* this function dissects a constrained sequence of */
uint32_t dissect_per_constrained_sequence_of(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx,
    const t124_per_sequence_t *seq, int min_len, int max_len, uint8_t has_extension, struct t124_info* local_info, rdp_info* info)
{
    uint32_t old_offset = offset;
    uint32_t length;

    /* 19.4    If there is a PER-visible constraint and an extension marker is present in it,
     * a single bit shall be added to the field-list in a bit-field of length one
     */
    if (has_extension) {
        uint8_t extension_present;
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_present);
    }

    /* 19.5 if min==max and min,max<64k ==> no length determinant */
    if ((min_len == max_len) && (min_len < 65536)) {
        length = min_len;
        goto call_sohelper;
    }

    /* 19.6 ub>=64k or unset */
    if ((max_len >= 65536) || (max_len == NO_BOUND)) {
        /* no constraint, see 10.9.4.2 */
        offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);
        goto call_sohelper;
    }

    /* constrained whole number for number of elements */
    offset = dpi_per_constrained_integer(pkt, offset, ctx, min_len, max_len, &length, 0);

call_sohelper:

    old_offset = offset;
    offset = dissect_per_sequence_of_helper(pkt, offset, ctx, seq->func, length, local_info, info);

    if (offset == old_offset)
        length = 0;
    else if (offset >> 3 == old_offset >> 3)
        length = 1;
    else
        length = (offset >> 3) - (old_offset >> 3);

    return offset;
}

uint32_t dissect_per_sequence(struct dpi_pkt_st *pkt, uint32_t offset, per_ctx_t *ctx, const t124_per_sequence_t *sequence, struct t124_info* local_info, rdp_info* info)
{
    uint8_t /*extension_present,*/ extension_flag, optional_field_flag;
    uint32_t old_offset = offset;
    uint32_t i, j, num_opts;
    uint32_t optional_mask[SEQ_MAX_COMPONENTS >> 5];

    extension_flag = 0;
    if (sequence[0].extension == ASN1_NO_EXTENSIONS) {
        /*extension_present=0;  ?? */
    }
    else {
        /*extension_present=1; ?? */
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_flag);
    }
    /* 18.2 */
    num_opts = 0;
    for (i = 0; sequence[i].p_id; i++) {
        if ((sequence[i].extension != ASN1_NOT_EXTENSION_ROOT) && (sequence[i].optional == ASN1_OPTIONAL)) {
            num_opts++;
        }
    }
    if (num_opts > SEQ_MAX_COMPONENTS) {
        DPI_LOG(DPI_LOG_DEBUG, "too many optional/default components");
    }

    memset(optional_mask, 0, sizeof(optional_mask));
    for (i = 0; i < num_opts; i++) {
        offset = dpi_per_boolean(pkt, offset, ctx, &optional_field_flag);
        if (optional_field_flag) {
            optional_mask[i >> 5] |= 0x80000000 >> (i & 0x1f);
        }
    }


    /* 18.4 */
    for (i = 0, j = 0; sequence[i].p_id; i++) {
        if ((sequence[i].extension == ASN1_NO_EXTENSIONS)
            || (sequence[i].extension == ASN1_EXTENSION_ROOT)) {
            if (sequence[i].optional == ASN1_OPTIONAL) {
                uint8_t is_present;
                if (num_opts == 0) {
                    continue;
                }
                is_present = (0x80000000 >> (j & 0x1f)) & optional_mask[j >> 5];
                num_opts--;
                j++;
                if (!is_present) {
                    continue;
                }
            }
            if (sequence[i].func) {
                offset = sequence[i].func(pkt, offset, ctx, local_info, info);
            }
            else {
                DPI_LOG(DPI_LOG_DEBUG, "per sequence func is null");
            }
        }
    }


    if (extension_flag) {
        uint8_t extension_bit;
        uint32_t num_known_extensions;
        uint32_t num_extensions;
        uint32_t extension_mask;

        offset = dpi_per_normally_small_nonnegative_whole_number(pkt, offset, ctx, &num_extensions);

        num_extensions += 1;
        if (num_extensions > 32) {
            DPI_LOG(DPI_LOG_DEBUG, "too many extensions");
        }

        extension_mask = 0;
        for (i = 0; i < num_extensions; i++) {
            offset = dpi_per_boolean(pkt, offset, ctx, &extension_bit);
            extension_mask = (extension_mask << 1) | extension_bit;
        }

        /* find how many extensions we know about */
        num_known_extensions = 0;
        for (i = 0; sequence[i].p_id; i++) {
            if (sequence[i].extension == ASN1_NOT_EXTENSION_ROOT) {
                num_known_extensions++;
            }
        }

        /* decode the extensions one by one */
        for (i = 0; i < num_extensions; i++) {
            uint32_t length;
            uint32_t new_offset;
            uint32_t difference;
            uint32_t extension_index;
            uint32_t k;

            if (!((1U << (num_extensions - 1 - i)) & extension_mask)) {
                /* this extension is not encoded in this PDU */
                continue;
            }

            offset = dpi_per_length_determinant(pkt, offset, ctx, &length, NULL);

            if (i >= num_known_extensions) {
                /* we don't know how to decode this extension */
                offset += length * 8;
                continue;
            }

            extension_index = 0;
            for (j = 0, k = 0; sequence[j].p_id; j++) {
                if (sequence[j].extension == ASN1_NOT_EXTENSION_ROOT) {
                    if (k == i) {
                        extension_index = j;
                        break;
                    }
                    k++;
                }
            }

            if (sequence[extension_index].func) {
                new_offset = sequence[extension_index].func(pkt, offset, ctx, local_info, info);
                offset += length * 8;
                difference = offset - new_offset;
                /* A difference of 7 or less might be byte aligning */
                /* Difference could be 8 if open type has no bits and the length is 1 */
                if ((length > 1) && (difference > 7)) {

                }
            }
            else {
                offset += length * 8;
            }
        }
    }
    return offset;
}

uint32_t dissect_per_choice(struct dpi_pkt_st *pkt, uint32_t offset, const t124_per_choice_t *choice, per_ctx_t *ctx, int *value, struct t124_info* local_info, rdp_info* info)
{
    uint8_t extension_flag;
    int extension_root_entries;
    int extension_addition_entries;
    uint32_t choice_index;
    int i, idx, cidx;
    uint32_t ext_length;
    uint32_t old_offset = offset;

    if (value) *value = -1;

    /* 22.5 */
    if (choice[0].extension == ASN1_NO_EXTENSIONS) {
        extension_flag = 0;
    }
    else {
        offset = dpi_per_boolean(pkt, offset, ctx, &extension_flag);
    }

    /* count the number of entries in the extension root and extension addition */
    extension_root_entries = 0;
    extension_addition_entries = 0;
    for (i = 0; choice[i].p_id; i++) {
        switch (choice[i].extension) {
        case ASN1_NO_EXTENSIONS:
        case ASN1_EXTENSION_ROOT:
            extension_root_entries++;
            break;
        case ASN1_NOT_EXTENSION_ROOT:
            extension_addition_entries++;
            break;
        }
    }

    if (!extension_flag) {  /* 22.6, 22.7 */
        if (extension_root_entries == 1) {  /* 22.5 */
            choice_index = 0;
        }
        else {
            offset = dpi_per_constrained_integer(pkt, offset, ctx, 0, extension_root_entries - 1, &choice_index, 0);
        }

        idx = -1; cidx = choice_index;
        for (i = 0; choice[i].p_id; i++) {
            if (choice[i].extension != ASN1_NOT_EXTENSION_ROOT) {
                if (!cidx) { idx = i; break; }
                cidx--;
            }
        }
    }
    else {  /* 22.8 */
        offset = dpi_per_normally_small_nonnegative_whole_number(pkt, offset, ctx, &choice_index);
        offset = dpi_per_length_determinant(pkt, offset, ctx, &ext_length, NULL);

        idx = -1; cidx = choice_index;
        for (i = 0; choice[i].p_id; i++) {
            if (choice[i].extension == ASN1_NOT_EXTENSION_ROOT) {
                if (!cidx) { idx = i; break; }
                cidx--;
            }
        }
    }

    if (idx != -1) {
        if (!extension_flag) {
            if (choice[idx].func)
                offset = choice[idx].func(pkt, offset, ctx, local_info, info);
        }
        else {
            if (choice[idx].func)
                choice[idx].func(pkt, offset, ctx, local_info, info);
            offset += ext_length * 8;
        }
    }
    else {
        if (!extension_flag) {
            DPI_LOG(DPI_LOG_DEBUG, "unknown extension root index in choice");
        }
        else {
            offset += ext_length * 8;
        }
    }

    if (value && (idx != -1))
        *value = choice[idx].value;
    return offset;
}
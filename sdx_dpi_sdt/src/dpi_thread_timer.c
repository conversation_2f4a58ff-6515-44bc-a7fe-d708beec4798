/****************************************************************************************
 * 文 件 名 : dpi_thread_timer.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_lcore.h>
#include <rte_cycles.h>
#include <rte_timer.h>
#include <rte_ethdev.h>

#include "dpi_detect.h"
#include "dpi_tbl_log.h"
#include "dpi_conversation.h"

uint64_t g_inc_flow[TRAFFIC_NUM];
struct traffic_stats stat_dpdk[DEV_MAX_NUM][TRAFFIC_NUM];
uint64_t log_total[TBL_LOG_MAX][TRAFFIC_NUM];

static struct rte_timer thread_timer;

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;

void update_global_time(void);
void init_thread_timer(void);

static void _stat_inc_flow(void)
{
    unsigned int i;
    for (i = 0; i < TRAFFIC_NUM - 1; i++) {
        g_inc_flow[TRAFFIC_NUM - i - 1] = g_inc_flow[TRAFFIC_NUM - i - 2];
    }
    g_inc_flow[0] = 0;

    for (i = 0; i < g_config.dissector_thread_num; i++) {
        g_inc_flow[0] += flow_thread_info[i].stats.inc_flow_num;
    }
}

/*
*目前timer只用在每秒统计收包速率，和处理速率, tbl速率
*/
static void thread_timer_cb(__attribute__((unused)) struct rte_timer *tim,
      __attribute__((unused)) void *arg)
{
    unsigned int i;
    unsigned int j;
    uint16_t idx_port;
    struct rte_eth_stats stat_info;
#ifdef _DPI_DPDK_17
    unsigned nb_ports = rte_eth_dev_count();
#else
    unsigned nb_ports = rte_eth_dev_count_avail();
#endif
    int stat;
    //update_global_time();
    _stat_inc_flow();

    for (i = 0; i < TRAFFIC_NUM - 1; i++) {
        for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ibytes  = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].ibytes;
            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ipkts   = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].ipkts;
            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].imissed = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].imissed;
            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ierrors = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].ierrors;

            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].obytes  = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].obytes;
            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].opkts   = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].opkts;
            stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].oerrors = 
                                  stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].oerrors;
        }

        for (j = 0; j < g_config.dissector_thread_num; j++) {
            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 1].ibytes =
                            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 2].ibytes;
            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 1].ipkts = 
                            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 2].ipkts;

            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 1].obytes =
                            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 2].obytes;
            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 1].opkts = 
                            flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 2].opkts;
        }
    }
    
    for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
        stat_dpdk[idx_port][0].ibytes  = 0;
        stat_dpdk[idx_port][0].ipkts   = 0;
        stat_dpdk[idx_port][0].imissed = 0;
        stat_dpdk[idx_port][0].ierrors = 0;

        stat_dpdk[idx_port][0].obytes  = 0;
        stat_dpdk[idx_port][0].opkts   = 0;
        stat_dpdk[idx_port][0].oerrors = 0;
    }
    
    for (idx_port = 0; idx_port < nb_ports; idx_port++) {
        stat = rte_eth_stats_get(idx_port, &stat_info);
        if (stat == 0) {
            stat_dpdk[idx_port][0].ibytes  += stat_info.ibytes;
            stat_dpdk[idx_port][0].ipkts   += stat_info.ipackets;
            stat_dpdk[idx_port][0].imissed += stat_info.imissed;
            stat_dpdk[idx_port][0].ierrors += stat_info.ierrors;

            stat_dpdk[idx_port][0].obytes  += stat_info.obytes;
            stat_dpdk[idx_port][0].opkts   += stat_info.opackets;
            stat_dpdk[idx_port][0].oerrors += stat_info.oerrors;
            //printf("stat port port:%d, out pkt:%lu \n",idx_port,stat_dpdk[idx_port][0].obytes);
        }
    }
    
    for (j = 0; j < g_config.dissector_thread_num; j++) {
        flow_thread_info[j].stats.traffic[0].ibytes = 
                      flow_thread_info[j].stats.total_wire_bytes;
        flow_thread_info[j].stats.traffic[0].ipkts  = 
                      flow_thread_info[j].stats.raw_packet_count;
    }

    for (i = 0; i < TRAFFIC_NUM - 1; i++) {
        for (j = 0; j < TBL_LOG_MAX; j++)
            log_total[j][TRAFFIC_NUM - i - 1] = log_total[j][TRAFFIC_NUM - i - 2];
    }

    timeout_conversation_hash();

    uint64_t hz       = rte_get_timer_hz();
    unsigned lcore_id = rte_lcore_id();
    
    rte_timer_reset_sync(tim, hz, SINGLE, lcore_id,thread_timer_cb, tim);

}


void init_thread_timer(void)
{
    uint64_t hz;
    unsigned lcore_id;

    rte_timer_subsystem_init();

    /* init timer structures */
    rte_timer_init(&thread_timer);

    /* load timer0, every second, on master lcore, reloaded automatically */
    hz = rte_get_timer_hz();
    lcore_id = rte_lcore_id();
    rte_timer_reset(&thread_timer, hz, SINGLE, lcore_id, thread_timer_cb, NULL);

}

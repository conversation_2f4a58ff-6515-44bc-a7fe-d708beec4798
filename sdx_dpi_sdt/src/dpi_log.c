/****************************************************************************************
 * 文 件 名 : dpi_log.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdarg.h>
#include "dpi_common.h"
#include "dpi_log.h"

struct value_string{
    int value;
    const char* string;
};
//from include/asm-generic/errno.h errno-base.h, not strict
static struct value_string syscall_errno_str[] = {
    { 0,        "Unknown reason"},
    { EPERM,    "Operation not permitted,only by superuser"},
    { ENOENT,       "No such file or directory"},
    { ESRCH,    "No such process or thread with the given ID"},
    { EINTR,      "This procedure is interrupted"},
    { EIO,        "Iuput/Output error"},
    { ENXIO,    "No such device or address"},
    { E2BIG,     "Argument list too long"},
    { ENOEXEC,    "Exec format error"},
    { EBADF,    "File already closed or read a descriptor only for writing(vice verse)"},
    { ECHILD,    "No child process"},
    { EAGAIN,    "Resource temporarily unavailable,you can try again"},
    { ENOMEM,    "No virtual memory can be allocated"},
    { EACCES,    "File permession don't allow attempted operation"},
    { EFAULT,    "cpuset don't point a valid object"},
    { EDEADLK,     "Recourses sinked in deadlock"},
    { EBUSY,    "Device or resource is busy"},
    { EEXIST,    "File exist"},
    { EXDEV,    "Invalid cross-device link"},
    { ENODEV,    "No such device"},
    { ENOTDIR,    "NOt a directory"},
    { EISDIR,    "Is a directory"},
    { EINVAL,    "Invalid argument about cpu or thread or mutex"},
    { EMFILE,    "Too many open files"},
    { ENFILE,    "Too many open files in system"},
    { ENOTTY,    "Inappropriate ioctl for device"},
    { EFBIG,    "File too big"},
    { ETIME,    "Timer expired"},
    { ENOSPC,    "No space left for the file"},
    { ESPIPE,     "Illegal seek"},
    { EROFS,    "File system is read only"},
    { EMLINK,    "Too many links for the file"},
    { EPIPE,     "Broken pipe"},
    { ENOTSOCK,    "Not a socket"},
    { EDESTADDRREQ, "Destination address required"},
    { EADDRINUSE,    "Address already in use"},
    { ENETDOWN,    "Network ids down"},
    { EDOM,     "Numerical argument out of domain"},
    { ERANGE,    "Numerical result out of range"},
    { EINPROGRESS,    "Operation now in progress"},
    { EMSGSIZE,    "Message too long"},
    { ENOLCK,    "No locks available"},
    { ENOTEMPTY,    "Directory not empty"},
    { ENOSYS,    "Function not implemented"},
    { ENOTSUP,    "Not supported"},
    { EILSEQ,    "Invalid or imcomplete multibyte"},
    { ECANCELED,    "Operation canceled"},
    { ETIMEDOUT,    "Connection timed out"},
};


#ifdef LOG_USE_COLOR
static const char *level_colors[] = {
  "\x1b[94m", "\x1b[36m", "\x1b[32m", "\x1b[33m", "\x1b[31m", "\x1b[35m"
};
#endif

void dpi_log_set_level_str(const char * level_str)
{
    g_config.log_output_level = DPI_LOG_INFO;
    if (level_str == NULL) return;

    for (int i = 0; i < DPI_LOG_MAX; ++i) {
        if (strcmp(level_str, level_strings[i]) == 0) {
            g_config.log_output_level = i;
            break;
        }
    }
    return;
}

static const char* errdesc(void){
    int i, num = sizeof(syscall_errno_str) / sizeof(struct value_string);
    for(i=1; i < num; i++)
        if(errno == syscall_errno_str[i].value)
            return syscall_errno_str[i].string;
    return syscall_errno_str[0].string;
}

time_t log_last_time;
void dpi_debug_printf(struct file_line_info *info, enum error_type type, dpi_log_level_t log_level, const char *format, ...)
{
    va_list args;
    const char *level;
    char str[512];
    //char time_now[64] = {0};
    char *time_now = g_config.time_str;
    char log[1024];

    if(g_config.fp_log == NULL)
        return;

    //禁止刷屏
    // if(log_last_time == g_config.g_now_time)
    // {
    //     return;
    // }
    log_last_time = g_config.g_now_time;
    va_start(args, format);
    vsprintf(str, format, args);
    va_end(args);

    if(type == ERROR_ORDINARY){
        if(log_level == DPI_LOG_DEBUG){
#ifdef LOG_USE_COLOR
          snprintf(log, sizeof(log), "[%s] %s%-5s\x1b[0m \x1b[90m%s:%s:%u\x1b[0m %s\n",time_now, level_colors[log_level],level_strings[log_level],
                   info->debug_print_file, info->debug_print_function, info->debug_print_line, str);
#else
          snprintf(log, sizeof(log), "[%s] %-5s %s:%s:%u - %s\n",time_now, level_strings[log_level],
                   info->debug_print_file, info->debug_print_function, info->debug_print_line, str);
#endif
        } else {
#ifdef LOG_USE_COLOR
        snprintf(log, sizeof(log), "[%s] %s%-5s\x1b[0m %s\n",time_now, level_colors[log_level], level_strings[log_level],  str);
#else
            snprintf(log, sizeof(log), "[%s]%-5s - %s\n",time_now, level_strings[log_level], str);
#endif
        }
    } else if(type == ERROR_SYSCALL) {
        if(log_level == DPI_LOG_DEBUG){
            snprintf(log, sizeof(log), "[SYSTEM CALL %s][%s]%s:%s:%u - %s--%s\n", level_strings[log_level], time_now, info->debug_print_file,
                 info->debug_print_function, info->debug_print_line, str, errdesc());
        } else {
            snprintf(log, sizeof(log), "[SYSTEM CALL %s][%s] - %s -- %s\n", level_strings[log_level], time_now, str, errdesc());
        }
    }


    fwrite(log, strlen(log), 1, g_config.fp_log);
    fwrite(log, strlen(log), 1, stdout);
    fflush(g_config.fp_log);
    return;
}


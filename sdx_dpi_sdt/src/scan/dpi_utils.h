/****************************************************************************************
 * 文 件 名 : ya_utils.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 1次
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-12
* 编    码 : zhengsw      '2018-05-12
* 修    改 : zhangsx      '2018-11-13
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _YA_UTILS_H_
#define _YA_UTILS_H_

#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>
#include <dirent.h>

#include <memory>
#include <ctime>
#include <string>
#include <typeinfo>

#include <functional>

/* macros */
#define dimen_of(x) (sizeof(x) / sizeof((x)[0]))

// check_nor_exit
#define CHECK_NOR_EXIT(cond, retCode, ...) if ((cond)) \
    {                                                  \
        fprintf(stderr, __VA_ARGS__);                  \
        return (retCode);                              \
    }

#define EXIT_IF_ERROR(cond, retCode, ...) if ((cond)) \
    {                                                 \
        fprintf(stderr, __VA_ARGS__);                 \
        perror(" ");                                  \
        return (retCode);                             \
    }

/* finaly do ... */
#define defer(fun) std::shared_ptr<void> _(nullptr, [&](void *){fun})

// easy tools
// unique_ptr 自动管理 malloc/new 出来的内存
typedef std::unique_ptr<char, void (*)(void *)> alloced_uptr;
/****************************************************************************************
  * 函 数 名 : be2le
  * 功    能 : 大端序转换为小端序数值
  * 输入参数 : num: 大端序数值
  * 输出参数 : 无
  * 返 回 值 : 小端序数值
  ***************************************************************************************/
 template <class T>
 static inline T be2le(const T &num)
 {
     T         ret = 0;
     uint8_t * src = (uint8_t *)&num;
     uint8_t*  dst = (uint8_t *)&ret;

     for(uint i = 0; i < sizeof(T); i++)
     {
         dst[sizeof(T) - i - 1] = src[i];
     }

     return ret;
 }

/****************************************************************************************
 * 函 数 名 : hundredThousandthSecond
 * 功    能 : 10 万分之一秒
 * 输出参数 : 无
 * 返 回 值 : YV_SUCCSS: 成功; YV_FAIL: 失败
 ***************************************************************************************/
static inline uint hundredThousandthSecond()
{
    struct timespec ts;

    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_nsec / 10000;
}

static inline
int forDirEntry(const char *dir_name, std::function <int(const char *, bool)> func, int depth = 1)
{
    if( NULL == dir_name )
     {
        return -EINVAL;
    }

    /* check dir type */
    struct stat s;
    lstat( dir_name , &s );
    CHECK_NOR_EXIT(!S_ISDIR( s.st_mode ), -EINVAL, "%s not a dir.\n", dir_name);

    /* open dir */
    dirent *pDirEntry = NULL;
    DIR    *pDir      = NULL;

    pDir = opendir( dir_name );
    EXIT_IF_ERROR(NULL == pDir, -1, "opendir %s", dir_name);

    char fileEntryPathBuf[PATH_MAX] = { 0 };

    /* loop all  */
    while( ( pDirEntry = readdir(pDir) ) != NULL )
    {
        if( strcmp( pDirEntry->d_name , "." ) == 0
            || strcmp( pDirEntry->d_name , "..") == 0)
        {   // 忽略它俩
            continue;
        }

        /* 生成 fileEntry path */
        sprintf(fileEntryPathBuf, "%s/%s", dir_name, pDirEntry->d_name);

        // recurse sub dir
        struct stat st;
        lstat( fileEntryPathBuf , &st );

        // 注意 readdir 返回的 pDirEntry 中无法通过 pDirEntry->d_type == DT_DIR 区分目录与文件
        // 比如在挂载 ftp 路径的时候
        func(fileEntryPathBuf, S_ISDIR(st.st_mode));

        if (depth > 1
            && S_ISDIR(st.st_mode))
        {
            forDirEntry(fileEntryPathBuf, func, depth - 1);
        }
    }

    if (pDir)
    {
        closedir(pDir);
    }
    return 0;
}

static inline
int makeDir(const char* path)
{
    int lSts = 0;
    int beginCmpPath;
    int endCmpPath;
    int pathLen = strlen(path);
    char currentPath[PATH_MAX] = {0};

    //相对路径
    if('/' != path[0])
    {
        //获取当前路径
        getcwd(currentPath, sizeof(currentPath));
        strcat(currentPath, "/");
        beginCmpPath = strlen(currentPath);
        strcat(currentPath, path);
        if(path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        endCmpPath = strlen(currentPath);
    }
    else
    {
        //绝对路径
        int pathLen = strlen(path);
        strcpy(currentPath, path);
        if(path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        beginCmpPath = 1;
        endCmpPath = strlen(currentPath);
    }
    //创建各级目录
    for(int i = beginCmpPath; i < endCmpPath ; i++ )
    {
        if('/' == currentPath[i])
        {
            currentPath[i] = '\0';
            if(access(currentPath, 0) != 0)
            {
                lSts = mkdir(currentPath, 0755);
                EXIT_IF_ERROR(lSts < 0 && errno != EEXIST, -1, "mkdir %s error", currentPath);
            }
            currentPath[i] = '/';
        }
    }
    return 0;
}

static inline
int ensureDirExist(const char *dirPath)
{
    int lSts = 0;

    if (access(dirPath, R_OK) != 0)
    {
        lSts = makeDir(dirPath);
        EXIT_IF_ERROR(lSts < 0, lSts, dirPath);
    }

    return 0;
}

static inline
std::string unixTime2Str(time_t timeValue)
{
    char strTimeBuf[30] = { 0 };
    struct tm *tm = localtime((time_t *)&timeValue);

    strftime(strTimeBuf, sizeof strTimeBuf, "%Y-%m-%d %H:%M:%S", tm);
    return strTimeBuf;
}

static inline
std::string nanosecond2ms(int time)
{
    char strMsTimeBuf[10] = { 0 };

    sprintf(strMsTimeBuf, ".%d ms", int(time/(1000000.0)));
    return strMsTimeBuf;
}

/* return dir string with final backslash */
static inline
std::string getAppDir()
{
    std::string strAppPath;
    if (!strAppPath.empty())
    {
        return strAppPath;
    }

    char pathBuff[PATH_MAX];
    uint cnt = readlink("/proc/self/exe", pathBuff, sizeof(pathBuff));
    if (cnt >= sizeof(pathBuff))
    {
        return "";
    }

    // terminate the path
    pathBuff[cnt] = '\0';

    strAppPath = pathBuff;
    strAppPath = strAppPath.substr(0, strAppPath.find_last_of('/') + 1);
    return strAppPath ;
}

static inline
void formatDirString(std::string &strDir)
{
    if (*strDir.rbegin() == '/')
    {
        strDir.pop_back();
    }
}

static inline
bool strBeginwith(const std::string &str, const std::string &start)
{
    return str.find(start) == 0;
}

static inline
bool strEndwith(const std::string &str, const std::string &end)
{
    // add by zhangsx 2018.11.13
    if (str.size() < end.size())
    {
        return false;
    }
    // add end
    return str.compare(str.size() - end.size(), end.size(), end) == 0;      //这里参数为负数时函数会崩溃
}

static inline
void strReplace(std::string &str, const std::string &from, const std::string &to)
{
    std::string::size_type fIndex =  str.find(from);

    if (fIndex == std::string::npos)
    {
        return ;
    }

    str.replace(fIndex, from.length(), to);
}

#endif /* _YA_UTILS_H_ */

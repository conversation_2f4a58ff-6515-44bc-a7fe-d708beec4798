#include "dpi_data_input.h"
#include "dpi_pcap_scanner.h"
#include "dpi_trailer.h"
#include <arpa/inet.h>

extern "C" {
extern struct global_config g_config;
}

#include <vector>
#include <string>
#include <map>
#include <stdint.h>
#include <string.h>
#include <unistd.h>
#include <libgen.h>
#include <pcap/pcap.h>

#define PCAP_SCANNING_LOOP_INTERVAL (500000)         /* 单位为 us */
#define PCAP_SCANNING_FINISH_SUFFIX  ".finish"
#define PCAP_SCANNING_ERROR_SUFFIX   ".error"


// pcap 名示例:
// XI_20230221.090757_145253182611566352_[XX-09]XXXXX-7-8-E-T-ODU2#1-47@1-8ODU0_10G_GFP.cap
struct yn470_line_info
{
    std::string ruleType;                  // XI：表示规则类型
    std::string date;                      // 20230221.090757：表示时间；
    std::string ruleNO;                    // 145253182611566352：表示规则编号；
    std::string dataCenter;                // [XX-09]：数据中心名称；
    std::string lineName_optical_layer;    // 电路名称-光层: XXXXX-7-8-E-T-ODU2#1-47
    std::string lineName_electrical_layer; // 电路名称-电层: 1-8ODU0
    std::string speed;                     // 10G：表示速率；
    std::string lineType;                  // GFP：表示电路类型
    std::string filePath;
public:
    int parsing_from_pcap_file_name(const char *pcapFileName)
    {
        char pcapFileNameBuf[1024] = { 0 };
        strcpy(pcapFileNameBuf, basename(const_cast<char *>(pcapFileName)));
        char *token = NULL;

        // 文件名示例:
        // XI_20230221.090757_145253182611566352_[XX-09]XXXXX-7-8-E-T-ODU2#1-47@1-8ODU0_10G_GFP.cap
        // BBB_20230324.145258_936748722493128022_[JX-13]KMCT-3-53-E-T-U2$1@10GE_10G_ETH_NSP.cap

        // 文件名中是否有 '_'
        char *first_underscore_mark = strchr(pcapFileNameBuf, '_');
        if (NULL == first_underscore_mark)
        {   // 文件名中没有出现 '_'
            return -1;
        }

        // 检测第一个 '_' 之后，第二个 '_' 之前是否都是由数字与 '.' 组成
        char *pCheck = first_underscore_mark + 1;
        while (*pCheck && *pCheck != '_')
        {
            if (!isdigit(*pCheck) && *pCheck != '.')
            {
                return -1;
            }

            pCheck++;
        }

#define STRTOK(field, buff, delim)              \
        token = strtok(buff, delim);            \
        if (NULL == token)                      \
        {                                       \
            return -1;                          \
        }                                       \
        field = token;

        STRTOK(this->ruleType, pcapFileNameBuf, "_");
        STRTOK(this->date,     NULL, "_");
        STRTOK(this->ruleNO,   NULL, "_");

        // eg: [XX-09]XXXXX-7-8-E-T-ODU2#1-47@1-8ODU0
        std::vector<char> dataCenter_lineName_3(40);
        token = strtok(NULL, "_");
        if (NULL == token)
        {
            return -1;
        }
        strcpy(dataCenter_lineName_3.data(), token);

        STRTOK(this->speed, NULL, "_");
        STRTOK(this->lineType, NULL, "_.");

        STRTOK(this->dataCenter, &dataCenter_lineName_3[1], "]@.");
        STRTOK(this->lineName_optical_layer, NULL, "]@.");
        STRTOK(this->lineName_electrical_layer, NULL, "]@.");

        return 0;
    }

public:
    enum
    {
        EM_YN470_LINE_INFO_FIELD_CNT = 8,
    };

public:
    static int write_linfo_field_header(FILE *fp)
    {
        fprintf(fp, "YN_LINFO_RULETYPE\n");
        fprintf(fp, "YN_LINFO_DATE\n");
        fprintf(fp, "YN_LINFO_RULENO\n");
        fprintf(fp, "YN_LINFO_DATACENTER\n");
        fprintf(fp, "YN_LINFO_OPTICAL_LAYER\n");
        fprintf(fp, "YN_LINFO_ELECTRICAL_LAYER\n");
        fprintf(fp, "YN_LINFO_SPEED\n");
        fprintf(fp, "YN_LINFO_LINETYPE\n");
        return 0;
    }

    int write_linfo_field_value(char *log_content, int *idx) const
    {
#define WRITE_LINFO_F(field) dpi_input_write_coupler_log_string(log_content, idx, (uint8_t *)field.c_str(), field.length())

        WRITE_LINFO_F(ruleType);
        WRITE_LINFO_F(date);
        WRITE_LINFO_F(ruleNO);
        WRITE_LINFO_F(dataCenter);
        WRITE_LINFO_F(lineName_optical_layer);
        WRITE_LINFO_F(lineName_electrical_layer);
        WRITE_LINFO_F(speed);
        WRITE_LINFO_F(lineType);

        return 0;
    }
};

struct yn470_line_info_entry
{
    yn470_line_info info;
    time_t          last_check; // 上次查询时间
};

class yn470_line_info_keeper
{
private:
    yn470_line_info_keeper() = default;

public:
    static yn470_line_info_keeper* getInstance()
    {
        static yn470_line_info_keeper instance;

        return &instance;
    }

public:
    int add_scanned_pcap(const char *pcapFileName, uint64_t scanning_NO)
    {
        yn470_line_info_entry entry;
        entry.last_check = time(NULL);
        yn470_line_info *info = &entry.info;

        //info->parsing_from_pcap_file_name(pcapFileName);
        info->filePath = pcapFileName;
        mapping_scanningNO2LineInfo_.emplace(scanning_NO, entry);

        /* printf("scanning: 添加 linfo 映射: %d \n", scanning_NO); */
        return 0;
    }

    const yn470_line_info* get_line_info_by_scanning_NO(uint64_t scanning_NO)
    {
        auto findIter = mapping_scanningNO2LineInfo_.find(scanning_NO);
        if (findIter == mapping_scanningNO2LineInfo_.end())
        {
            /* printf("scanning: 查询 linfo 映射失败: %d \n", scanning_NO); */
            return NULL;
        }

        /* printf("scanning: 查询 linfo 映射成功: %d \n", scanning_NO); */
        findIter->second.last_check = time(NULL);
        return &findIter->second.info;
    }

    int check_timeouted_entry()
    {
        static const uint64_t line_entry_timeout = 300;            // 300s 未被访问的 entry 将会被移除

        // TODO: 进行超时检测扫描，移除已经超时的 entry;
        return 0;
    }

    int write_linfo_field_header(FILE *fp)
    {
        return yn470_line_info::write_linfo_field_header(fp);
    }

 int write_linfo_field_value(char *log_content, int *idx, const yn470_line_info *linfo)
    {
        if (linfo == NULL)
        {
            /* 写入 n 个空白字段进行占位 */
            dpi_input_write_coupler_log_empty(log_content, idx, yn470_line_info::EM_YN470_LINE_INFO_FIELD_CNT);
            return 0;
        }

        return linfo->write_linfo_field_value(log_content, idx);
    }

private:
    std::map<uint64_t, yn470_line_info_entry> mapping_scanningNO2LineInfo_; // TODO: 需要进行加锁保护
};

#define YN470_LINFO_KPER yn470_line_info_keeper::getInstance()

const struct yn470_line_info* dpi_input_get_yn470_linfo_by_scanning_NO(uint64_t scanning_NO)
{
    return YN470_LINFO_KPER->get_line_info_by_scanning_NO(scanning_NO);
}


#if 1
int enqueue_packet_from_pcap_to_process_thread(const char *pPcapFileName, const char *pPcapNewFileName, uint64_t pcap_scanning_NO)
{
    bpf_program    fp;
    char           errbuf[PCAP_ERRBUF_SIZE] = { 0 };
    char           filter_exp[]             = "";
    pcap_t        *pcapHandle               = NULL;
    pcap_pkthdr   *pkt_header               = NULL;
    const uint8_t *pkt_data                 = NULL;
    int            lSts                     = 0;

#define CHECK_NOR_EXIT(cond, errMsg, retCode) if ((cond)) \
    {                                                     \
        fprintf(stderr, "%s\n", errMsg);                  \
        return (retCode);                                 \
    }

    // 打开 pcap 文件
    pcapHandle = pcap_open_offline(pPcapNewFileName, errbuf);
    CHECK_NOR_EXIT(NULL == pcapHandle, errbuf, -1);

    /* pcap 过滤条件 */
    lSts = pcap_compile(pcapHandle, &fp, filter_exp, 0, 0);
    CHECK_NOR_EXIT(-1 == lSts, "Couldn't parse filter", -1);
    //lSts = pcap_setfilter(pcapHandle, &fp);
    //CHECK_NOR_EXIT(-1 == lSts, "Counldn't install filter", -1);

    fflush(stdout);

    //Trailer用于携带文件名
    yv_trailer_info   trailer;
    memset(&trailer, 0, sizeof(yv_trailer_info));
    trailer.magic_code[0] = 'X';
    trailer.magic_code[1] = 'A';
    trailer.ext_data_len = strlen(pPcapFileName);
    memcpy(trailer.ext_data, pPcapFileName, trailer.ext_data_len);

    uint16_t trailer_len = sizeof(yv_trailer_info)  - sizeof(trailer.ext_data) + trailer.ext_data_len;
    int  mbuf_size = 0;
    mbuf_size  = dpi_input_get_config_data_mbuf_size();
    uint8_t buff[mbuf_size];
    int buff_len;
    // process pkts
    for (int i = 1; ;i++)
    {
        lSts = pcap_next_ex(pcapHandle, &pkt_header, &pkt_data);
        if (0 == lSts || 1 == lSts)
        { // OK, got a new packet
            memset(buff, 0, sizeof(buff));
            uint16_t  new_trailer_len = trailer_len + 2;
            if(pkt_header->caplen + new_trailer_len <= sizeof(buff)){
                memcpy(buff, pkt_data, pkt_header->caplen);
                memcpy(buff+pkt_header->caplen, (uint8_t*)&trailer, trailer_len);
                //在数据尾部补充整个Trailer长度，用来区分其他Trailer
                memcpy(buff + pkt_header->caplen + trailer_len, (uint8_t*)&new_trailer_len, 2);
                buff_len = pkt_header->caplen  + new_trailer_len;
            } else {
                //printf("mbuf_size too small\n");
                continue;
            }
            dpi_input_enqueue_packet((const uint8_t*)buff, buff_len, (uint32_t)pkt_header->ts.tv_sec*(uint64_t)1000000, (void *)pcap_scanning_NO);
        }
        else if (-1 == lSts)
        {  // error
            fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
            break;
        }
        else if (-2 == lSts)
        { // no more pkt to read from offline file
            break;
        }
    }

    pcap_freecode(&fp);
    pcap_close(pcapHandle);
    return 0;
}
#endif

int process_pcap_file(const char *pcapFileName, uint64_t pcap_scanning_NO, int scanning_do_rename)
{
    printf("scanning pcap file[%5lu]: %s\n", pcap_scanning_NO, pcapFileName);

    int lsts = 0;
    std::string strNewFileName;

    if (scanning_do_rename)
    {
        strNewFileName = std::string(pcapFileName) + (lsts < 0 ? PCAP_SCANNING_ERROR_SUFFIX : dpi_input_get_config_data_finish_suffix());
        rename(pcapFileName, strNewFileName.c_str());
    } else
        strNewFileName = pcapFileName;
    /* 将 pcap 的 packet 根据 pcap_scanning_NO 加入到线程队列 */
    lsts = enqueue_packet_from_pcap_to_process_thread(pcapFileName, strNewFileName.c_str(), pcap_scanning_NO);

    if(dpi_input_get_config_del_file_flag())
        remove(strNewFileName.c_str());

    return 0;
}

int dpi_input_loop_scanning_pcap_in_dir(const char *scan_dir, int scanning_infinite, int scanning_do_rename, const char *scanning_ignore)
{
    std::vector<std::string> scanning_dir_list;
    scanning_dir_list.push_back(scan_dir);

    // 处理过的 pcap 个数统计，为每个 pcap 进行编号;
    // 此编号初始值不能从 0 开始，因为会将其作为 mbuf 的 userdata 保存，
    // 其会通过与 0 比较来判断 userdata 是否有效，如果从 0 开始编号会导致第一个 pcap 被视作没有编号信息;
    uint64_t pcap_process_number = 1;

    // 执行扫描
    PcapScanner scanner(scanning_dir_list, ".writing",                          /* 正在写入的文件，跳过 */
                                           scanning_ignore,                     /* 需要忽略的文件, TODO: 此处需要与 PCAP_SCANNING_FINISH 合并 */
                                           ".finish");                          /* 处理完成的文件 */

    scanner.scan();//第一次扫描获取文件大小记录
    do
    {
        //检查是否存在有效规则
        //327上线规则时暂停扫描新的pcap
        if(!dpi_input_is_existed_sdtEngine_active_rules() || dpi_input_get_config_pause_scan_pcap()){
            sleep(1);
            continue;
        }

        scanner.scan();

        if (scanner.getPcapFileCnt() == 0)
        {
            // 检查是否有超时不活跃的 yn470 line info entry;
            YN470_LINFO_KPER->check_timeouted_entry();

            usleep(PCAP_SCANNING_LOOP_INTERVAL );
            continue;
        }

        // 发现 pcap 文件则产生新的任务
        scanner.foreachPcapFile([&](const char *pszPcapFileName)
                                {
                                    process_pcap_file(pszPcapFileName, pcap_process_number++, scanning_do_rename);
                                    return 0;
                                });

    /* 如果 scanning_infinite 为 0 将仅将目录下现有文件处理一遍即退出
     * 否则将一直执行“扫描”
     */
    } while (scanning_infinite);

    return 0;
}

int dpi_input_write_yn470_linfo_field_header(FILE *fp)
{
    return YN470_LINFO_KPER->write_linfo_field_header(fp);
}

int dpi_input_write_yn470_linfo_field_value(char *log_content, int *idx, const yn470_line_info *linfo)
{
    return YN470_LINFO_KPER->write_linfo_field_value(log_content, idx, linfo);
}

const char *dpi_input_get_yn470_linfo_filepath_value(const struct yn470_line_info *linfo)
{
    if(linfo==NULL)
        return NULL;

    return linfo->filePath.c_str();
}

int dpi_input_get_scan_pcap_num(void)
{
    if(strlen(dpi_input_get_config_data_scan_dir()) <= 0)
        return 0;

    std::vector<std::string> scanning_dir_list;
    scanning_dir_list.push_back(dpi_input_get_config_data_scan_dir());
    // 执行扫描
    PcapScanner scanner(scanning_dir_list, ".writing",                                  /* 正在写入的文件，跳过 */
                                           dpi_input_get_config_data_scanning_ignore(), /* 需要忽略的文件, TODO: 此处需要与 PCAP_SCANNING_FINISH 合并 */
                                           ".finish");                          /* 处理完成的文件 */

    scanner.scan();//第一次扫描获取文件大小记录
    scanner.scan();

    return scanner.getPcapFileCnt() + scanner.getPcapMapFileCnt();
}


/****************************************************************************************
 * 文 件 名 : yaEty_pcap_scanner.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-06-24
* 编    码 : root      '2018-06-24
* 修    改 : zhangsx   '2018-11-12
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "dpi_pcap_scanner.h"
#include "dpi_utils.h"

std::string& ltrim(std::string &s)
{
    return s.erase(0, s.find_first_not_of(" "));
}

std::string& rtrim(std::string& s)
{
    return s.erase(s.find_last_not_of(" ") + 1);
}

std::string& trim(std::string &s)
{
    return ltrim(rtrim(s));
}

PcapScanner::PcapScanner(const std::vector<std::string> &dirList,
                         const std::string &strWritingSuffix,
                         const std::string &strIgnoreSuffixList,
                         const std::string &strProcMarkSuffix)
    : dirList_(dirList)
    , strWritingSuffix_(strWritingSuffix)
    , uTotalWritingFileSize_(0)
    , strProcMarkSuffix_(strProcMarkSuffix)
{
    for (std::string::size_type start = 0, pos = 0;
         pos != std::string::npos;
         start = pos + 1)
    {
        pos = strIgnoreSuffixList.find(',', start);
        std::string ignoreSuffix = strIgnoreSuffixList.substr(start,
                                                              std::string::npos == pos
                                                              ? std::string::npos : pos - start);
        ignoreSuffix = trim(ignoreSuffix);
        vecIgnoreSuffixs_.push_back(ignoreSuffix);
    }
}

PcapScanner::~PcapScanner()
{

}

//判断文件大小是否变化
int PcapScanner::isFileSizeInc(std::string fileName)
{
    if(fileName.size() == 0)
        return 0;
    
    int lSts = 0;
    struct stat s;

    lSts = lstat(fileName.c_str(), &s);
    if(lSts < 0)
        return 0;
    auto iter = pcapMap_.find(fileName);
    if(iter == pcapMap_.end()){
        pcapMap_.emplace(fileName, s.st_size);
    }else{
        if(iter->second == (unsigned int)s.st_size){
            pcapMap_.erase(fileName);
            return 0;
        }else{
            iter->second = s.st_size;
        }
    }

    return 1;
}

//获取存在增长的pcap文件数量
int PcapScanner::getPcapMapFileCnt()
{
    return pcapMap_.size();
}

int PcapScanner::shouldProcessFile(const char *fileName, bool bIsDir)
{
    if (bIsDir)
    {   // 不处理目录
        return 0;
    }

    std::string strFileName = fileName;
    /* is a writing file ? */
    if (strEndwith(strFileName, strWritingSuffix_))
    {   // 累计 writing file size
        int lSts = 0;
        struct stat s;

        lSts = lstat(fileName, &s);
        CHECK_NOR_EXIT(lSts < 0, lSts, "stat file %s error\n", fileName);

        uTotalWritingFileSize_ += s.st_size;
        return 0;
    }
    /* ignore the file */
    for (const auto &ignore : vecIgnoreSuffixs_)
    {
        if (strEndwith(strFileName, ignore))
        {
            return 0;
        }
    }

    if(isFileSizeInc(strFileName) == 0)
    {
        /* push to queue */
        pcapQueue_.push(strFileName);
    }
    return 0;
}

int PcapScanner::scan()
{
    if (getPcapFileCnt() > 0)
    {   // 还有内容，不进行实际扫描
        return 0;
    }

    uTotalWritingFileSize_ = 0;
    for (const auto &dir : dirList_)
    {
        forDirEntry(dir.c_str(),
                    std::bind(&PcapScanner::shouldProcessFile,
                              this, std::placeholders::_1, std::placeholders::_2),
                    5);             /* 最多扫描 5 层 */
    }

    return 0;
}

int PcapScanner::getPcapFileCnt()
{
    return pcapQueue_.size();
}

uint64_t PcapScanner::getTotalWritingFileSize()
{
    return uTotalWritingFileSize_;
}

int  PcapScanner::foreachPcapFile(std::function<int (const char *)> funProc)
{
    while (!pcapQueue_.empty())
    {
        funProc(pcapQueue_.front().c_str());
        pcapQueue_.pop();
    }

    return 0;
}

int PcapScanner::getNextPcapFile(std::string &strFileName)
{
    if (pcapQueue_.empty())
    {
        return -1;
    }

    int         lSts                = 0;
    std::string strOriginalFileName = pcapQueue_.front();
    pcapQueue_.pop();

    // 重命名，防止下次 scan 又扫到它，重复分配
    strFileName = strOriginalFileName + strProcMarkSuffix_;
    lSts        = rename(strOriginalFileName.c_str(), strFileName.c_str());
    CHECK_NOR_EXIT(lSts < 0, lSts, "rename error:%s -> %s\n", strOriginalFileName.c_str(), strFileName.c_str());

    return 0;
}

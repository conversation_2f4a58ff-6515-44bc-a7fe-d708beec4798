#ifndef _DPI_FLOW_TIMER_H
#define _DPI_FLOW_TIMER_H

#include <rte_timer.h>

#include "dpi_detect.h"

int dpi_flow_timer_init(struct work_process_data *workflow, struct flow_info *flow);
int dpi_flow_timer_reset(struct work_process_data *workflow, struct flow_info *flow, rte_timer_cb_t fct, void *arg);
void dpi_flow_timer_reset_sync(struct work_process_data *workflow, struct flow_info *flow, rte_timer_cb_t fct, void *arg);
int dpi_flow_timer_reset_tick(struct work_process_data *workflow, struct flow_info *flow, int tick ,rte_timer_cb_t fct, void *arg);
int dpi_flow_timer_stop();

#endif
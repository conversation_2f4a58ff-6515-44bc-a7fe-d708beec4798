#ifndef _DPI_SDT_DATA_HEADER_
#define _DPI_SDT_DATA_HEADER_



enum mac_pheader_index_em{
    EM_MAC_PHEADER_GLOBAL_LINE_NO,
    EM_MAC_PHEADER_LINE_TYPE,
    EM_MAC_PHEADER_POS_TYPE,
    EM_MAC_PHEADER_SYSNO_DEVICE_TYPE,
    EM_MAC_PHEADER_DEVICE_SEQ,
    EM_MAC_PHEADER_BOARD_TYPE_SEQ,
    EM_MAC_PHEADER_SENDPORT_RESERVE,

    EM_MAC_PHEADER_DSTMAC,
    EM_MAC_PHEADER_SRCMAC,
    EM_MAC_PHEADER_WPROTOCOL,
    EM_MAC_PHEADER_MAC_SEQ,
    EM_MAC_PHEADER_DATATYPE,

    EM_MAC_PHEADER_WDATALEN,


    EM_MAC_PHEADER_TIMESTAMP,
    EM_MAC_PHEADER_BLINKTYPE,
    EM_MAC_PHEADER_BLINKLEN,
    
    
    EM_MAC_PHEADER_MAX
};


#endif

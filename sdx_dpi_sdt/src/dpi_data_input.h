#ifndef DPI_DATA_INPUT_H
#define DPI_DATA_INPUT_H

#include <stdint.h>
#include <time.h>
#include <stdio.h>

struct yn470_line_info;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

int dpi_input_loop_scanning_pcap_in_dir(const char *scan_dir, int scanning_infinite, int scanning_do_rename, const char *scanning_ignore);

int dpi_input_enqueue_packet(const uint8_t *data, int pkt_len, uint64_t usec, void* userdata);

const struct yn470_line_info* dpi_input_get_yn470_linfo_by_scanning_NO(uint64_t scanning_NO);

int dpi_input_write_yn470_linfo_field_header(FILE *fp);

int dpi_input_write_yn470_linfo_field_value(char *log_content, int *idx, const struct yn470_line_info *linfo);

int dpi_input_write_coupler_log_string(char *log, int *idx, const uint8_t *data,uint64_t int_data);

int dpi_input_write_coupler_log_empty(char *log, int *idx, int empty_cnt);

int dpi_input_get_config_data_input_with_yn470_line_info(void );

uint16_t dpi_input_get_config_data_mbuf_size(void);

const char *dpi_input_get_config_data_finish_suffix(void);

const char *dpi_input_get_yn470_linfo_filepath_value(const struct yn470_line_info *linfo);

uint8_t dpi_input_get_config_del_file_flag(void);

int dpi_input_is_existed_sdtEngine_active_rules(void);

int dpi_existed_data_not_del(void);

int dpi_input_get_scan_pcap_num(void);

const char *dpi_input_get_config_data_scan_dir(void);

const char *dpi_input_get_config_data_scanning_ignore(void);

uint8_t dpi_input_get_config_pause_scan_pcap(void);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DPI_DATA_INPUT_H */

/****************************************************************************************
 * 文 件 名 : dpi_forward_data_collect.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: hongll           2022/08/12
编码: hongll           2022/08/12
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_FORWARD_COLLECT_H_
#define _DPI_FORWARD_COLLECT_H_


#include "dpi_forward_datatype.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_detect.h"


/* 数据采集文件： 转发 */
void sdt_out_forward_sdx(struct packet_stream *pkt_stream);

void forward_collect_send_kafka(sdt_out_status  *out_elem, struct packet_stream  *pkt_elm, const char *filename);


#endif

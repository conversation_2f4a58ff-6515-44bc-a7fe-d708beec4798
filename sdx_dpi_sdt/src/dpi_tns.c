/****************************************************************************************
 * 文 件 名 : dpi_tns.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liyj         2021/07/17
编码: liyj         2021/07/17
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>

#include "dpi_pint.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "charsets.h"

extern struct rte_mempool *tbl_log_mempool;
extern dpi_field_table dbbasic_field_array[];

enum tns_index_em{
    EM_TNS_DATA_TYPE,
    EM_TNS_DIRECTION,
    EM_TNS_HEAD_LENGTH,
    EM_TNS_HEAD_PACKET_CHECKSUM,
    EM_TNS_HEAD_TYPE,
    EM_TNS_HEAD_RESERVED,
    EM_TNS_HEAD_HEADER_CHECKSUM,

    EM_TNS_CONNECT_VERSION,
    EM_TNS_CONNECT_COMPAT_VERSION,
    EM_TNS_CONNECT_SERVICE_OPTIONS,
    EM_TNS_CONNECT_SDU_SIZE,
    EM_TNS_CONNECT_MAX_TDU_SIZE,
    EM_TNS_CONNECT_NT_PROTO_CHARACTERISTICS,
    EM_TNS_CONNECT_LINE_TURNAROUND,
    EM_TNS_CONNECT_VALUE_OF_ONE,
    EM_TNS_CONNECT_DATA_LENGTH,
    EM_TNS_CONNECT_DATA_OFFSET,
    EM_TNS_CONNECT_DATA_MAX,
    EM_TNS_CONNECT_FLAGS0,
    EM_TNS_CONNECT_FALGS1,
    EM_TNS_CONNECT_TRACE_CF1,
    EM_TNS_CONNECT_TRACE_CF2,
    EM_TNS_CONNECT_TRACE_CID,
    EM_TNS_CONNECT_DATA,
    EM_TNS_CONNECT_DATA_SID,
    EM_TNS_CONNECT_DATA_PROGRAM,
    EM_TNS_CONNECT_DATA_USER,
    EM_TNS_CONNECT_DATA_PROTOCOL,
    EM_TNS_CONNECT_DATA_HOST1,
    EM_TNS_CONNECT_DATA_HOST2,
    EM_TNS_CONNECT_DATA_PORT,
    EM_TNS_CONNECT_DATA_SERVER,

    EM_TNS_DATA_FLAGS,
    EM_TNS_DATA_ID,
    EM_TNS_DATA_CALL_ID,
    EM_TNS_DATA_CONTENT,
    EM_TNS_DATA_SQL,
    EM_TNS_DATA_VERSION,
    EM_TNS_DATA_CLI_PLAT,
    EM_TNS_DATA_BANNER,

    EM_TNS_AUTH_PASSWORD,
    EM_TNS_MAX
};

static dpi_field_table  tns_field_array[] = {
    DPI_FIELD_D(EM_TNS_DATA_TYPE,                            EM_F_TYPE_STRING,                 "type"),
    DPI_FIELD_D(EM_TNS_DIRECTION,                            EM_F_TYPE_STRING,                 "direction"),
    DPI_FIELD_D(EM_TNS_HEAD_LENGTH,                          EM_F_TYPE_UINT32,                 "head_length"),
    DPI_FIELD_D(EM_TNS_HEAD_PACKET_CHECKSUM,                 EM_F_TYPE_UINT16,                 "head_packet_checksum"),
    DPI_FIELD_D(EM_TNS_HEAD_TYPE,                            EM_F_TYPE_UINT8,                  "head_type"),
    DPI_FIELD_D(EM_TNS_HEAD_RESERVED,                        EM_F_TYPE_UINT8,                  "head_reserved"),
    DPI_FIELD_D(EM_TNS_HEAD_HEADER_CHECKSUM,                 EM_F_TYPE_UINT16,                 "head_header_checksum"),

    DPI_FIELD_D(EM_TNS_CONNECT_VERSION,                      EM_F_TYPE_UINT16,                 "connect_version"),
    DPI_FIELD_D(EM_TNS_CONNECT_COMPAT_VERSION,               EM_F_TYPE_UINT16,                 "connect_compat_version"),
    DPI_FIELD_D(EM_TNS_CONNECT_SERVICE_OPTIONS,              EM_F_TYPE_UINT16,                 "connect_service_options"),
    DPI_FIELD_D(EM_TNS_CONNECT_SDU_SIZE,                     EM_F_TYPE_UINT16,                 "connect_sdu_size"),
    DPI_FIELD_D(EM_TNS_CONNECT_MAX_TDU_SIZE,                 EM_F_TYPE_UINT16,                 "connect_max_tdu_size"),
    DPI_FIELD_D(EM_TNS_CONNECT_NT_PROTO_CHARACTERISTICS,     EM_F_TYPE_UINT16,                 "connect_nt_proto_characteristics"),
    DPI_FIELD_D(EM_TNS_CONNECT_LINE_TURNAROUND,              EM_F_TYPE_UINT16,                 "connect_line_turnaround"),
    DPI_FIELD_D(EM_TNS_CONNECT_VALUE_OF_ONE,                 EM_F_TYPE_UINT16,                 "connect_value_of_one"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_LENGTH,                  EM_F_TYPE_UINT16,                 "connect_data_length"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_OFFSET,                  EM_F_TYPE_UINT16,                 "connect_data_offset"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_MAX,                     EM_F_TYPE_UINT32,                 "connect_data_max"),
    DPI_FIELD_D(EM_TNS_CONNECT_FLAGS0,                       EM_F_TYPE_UINT8,                  "connect_flags0"),
    DPI_FIELD_D(EM_TNS_CONNECT_FALGS1,                       EM_F_TYPE_UINT8,                  "connect_flags1"),
    DPI_FIELD_D(EM_TNS_CONNECT_TRACE_CF1,                    EM_F_TYPE_UINT32,                 "connect_trace_cf1"),
    DPI_FIELD_D(EM_TNS_CONNECT_TRACE_CF2,                    EM_F_TYPE_UINT32,                 "connect_trace_cf2"),
    DPI_FIELD_D(EM_TNS_CONNECT_TRACE_CID,                    EM_F_TYPE_HEX,                    "connect_trace_cid"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA,                         EM_F_TYPE_STRING,                 "connect_data"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_SID,                     EM_F_TYPE_STRING,                 "connect_data_sid"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_PROGRAM,                 EM_F_TYPE_STRING,                 "connect_data_program"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_USER,                    EM_F_TYPE_STRING,                 "connect_data_user"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_PROTOCOL,                EM_F_TYPE_STRING,                 "connect_data_protocol"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_HOST1,                   EM_F_TYPE_STRING,                 "connect_data_host1"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_HOST2,                   EM_F_TYPE_STRING,                 "connect_data_host2"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_PORT,                    EM_F_TYPE_STRING,                 "connect_data_port"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_SERVER,                  EM_F_TYPE_STRING,                 "connect_data_server"),

    DPI_FIELD_D(EM_TNS_DATA_FLAGS,                           EM_F_TYPE_UINT16,                 "data_flags"),
    DPI_FIELD_D(EM_TNS_DATA_ID,                              EM_F_TYPE_UINT32,                 "data_id"),
    DPI_FIELD_D(EM_TNS_DATA_CALL_ID,                         EM_F_TYPE_UINT8,                  "data_call_id"),
    DPI_FIELD_D(EM_TNS_DATA_CONTENT,                         EM_F_TYPE_STRING,                 "data_content"),
    DPI_FIELD_D(EM_TNS_DATA_SQL,                             EM_F_TYPE_STRING,                 "data_sql"),
    DPI_FIELD_D(EM_TNS_DATA_VERSION,                         EM_F_TYPE_STRING,                 "data_version"),
    DPI_FIELD_D(EM_TNS_DATA_CLI_PLAT,                        EM_F_TYPE_STRING,                 "data_cli_plat"),
    DPI_FIELD_D(EM_TNS_DATA_BANNER,                          EM_F_TYPE_STRING,                 "data_banner"),

    DPI_FIELD_D(EM_TNS_AUTH_PASSWORD,                        EM_F_TYPE_STRING,                 "auth_word"),
};

static dpi_field_table  tns_field_array_sdt[] = {
    DPI_FIELD_D(EM_TNS_DATA_TYPE,                            YA_FT_STRING,                 "type"),
    DPI_FIELD_D(EM_TNS_DIRECTION,                            YA_FT_STRING,                 "direction"),
    DPI_FIELD_D(EM_TNS_HEAD_LENGTH,                          YA_FT_UINT32,                 "head_length"),
    DPI_FIELD_D(EM_TNS_HEAD_PACKET_CHECKSUM,                 YA_FT_UINT16,                 "head_packet_checksum"),
    DPI_FIELD_D(EM_TNS_HEAD_TYPE,                            YA_FT_UINT8,                  "head_type"),
    DPI_FIELD_D(EM_TNS_HEAD_RESERVED,                        YA_FT_UINT8,                  "head_reserved"),
    DPI_FIELD_D(EM_TNS_HEAD_HEADER_CHECKSUM,                 YA_FT_UINT16,                 "head_header_checksum"),

    DPI_FIELD_D(EM_TNS_CONNECT_VERSION,                      YA_FT_UINT16,                 "connect_version"),
    DPI_FIELD_D(EM_TNS_CONNECT_COMPAT_VERSION,               YA_FT_UINT16,                 "connect_compat_version"),
    DPI_FIELD_D(EM_TNS_CONNECT_SERVICE_OPTIONS,              YA_FT_UINT16,                 "connect_service_options"),
    DPI_FIELD_D(EM_TNS_CONNECT_SDU_SIZE,                     YA_FT_UINT16,                 "connect_sdu_size"),
    DPI_FIELD_D(EM_TNS_CONNECT_MAX_TDU_SIZE,                 YA_FT_UINT16,                 "connect_max_tdu_size"),
    DPI_FIELD_D(EM_TNS_CONNECT_NT_PROTO_CHARACTERISTICS,     YA_FT_UINT16,                 "connect_nt_proto_characteristics"),
    DPI_FIELD_D(EM_TNS_CONNECT_LINE_TURNAROUND,              YA_FT_UINT16,                 "connect_line_turnaround"),
    DPI_FIELD_D(EM_TNS_CONNECT_VALUE_OF_ONE,                 YA_FT_UINT16,                 "connect_value_of_one"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_LENGTH,                  YA_FT_UINT16,                 "connect_data_length"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_OFFSET,                  YA_FT_UINT16,                 "connect_data_offset"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_MAX,                     YA_FT_UINT32,                 "connect_data_max"),
    DPI_FIELD_D(EM_TNS_CONNECT_FLAGS0,                       YA_FT_UINT8,                  "connect_flags0"),
    DPI_FIELD_D(EM_TNS_CONNECT_FALGS1,                       YA_FT_UINT8,                  "connect_flags1"),
    DPI_FIELD_D(EM_TNS_CONNECT_TRACE_CF1,                    YA_FT_UINT32,                 "connect_trace_cf1"),
    DPI_FIELD_D(EM_TNS_CONNECT_TRACE_CF2,                    YA_FT_UINT32,                 "connect_trace_cf2"),
    DPI_FIELD_D(EM_TNS_CONNECT_TRACE_CID,                    YA_FT_STRING,                 "connect_trace_cid"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA,                         YA_FT_STRING,                 "connect_data"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_SID,                     YA_FT_STRING,                 "connect_data_sid"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_PROGRAM,                 YA_FT_STRING,                 "connect_data_program"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_USER,                    YA_FT_STRING,                 "connect_data_user"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_PROTOCOL,                YA_FT_STRING,                 "connect_data_protocol"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_HOST1,                   YA_FT_STRING,                 "connect_data_host1"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_HOST2,                   YA_FT_STRING,                 "connect_data_host2"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_PORT,                    YA_FT_STRING,                 "connect_data_port"),
    DPI_FIELD_D(EM_TNS_CONNECT_DATA_SERVER,                  YA_FT_STRING,                 "connect_data_server"),

    DPI_FIELD_D(EM_TNS_DATA_FLAGS,                           YA_FT_UINT16,                 "data_flags"),
    DPI_FIELD_D(EM_TNS_DATA_ID,                              YA_FT_UINT32,                 "data_id"),
    DPI_FIELD_D(EM_TNS_DATA_CALL_ID,                         YA_FT_UINT8,                  "data_call_id"),
    DPI_FIELD_D(EM_TNS_DATA_CONTENT,                         YA_FT_STRING,                 "data_content"),
    DPI_FIELD_D(EM_TNS_DATA_SQL,                             YA_FT_STRING,                 "data_sql"),
    DPI_FIELD_D(EM_TNS_DATA_VERSION,                         YA_FT_STRING,                 "data_version"),
    DPI_FIELD_D(EM_TNS_DATA_CLI_PLAT,                        YA_FT_STRING,                 "data_cli_plat"),
    DPI_FIELD_D(EM_TNS_DATA_BANNER,                          YA_FT_STRING,                 "data_banner"),
    DPI_FIELD_D(EM_TNS_AUTH_PASSWORD,                        YA_FT_STRING,                 "auth_word"),
};



typedef struct value_string {
    uint32_t value;
    const char *string;
}value_string;

/* Packet Types */
#define TNS_TYPE_CONNECT        1
#define TNS_TYPE_ACCEPT         2
#define TNS_TYPE_ACK            3
#define TNS_TYPE_REFUSE         4
#define TNS_TYPE_REDIRECT       5
#define TNS_TYPE_DATA           6
#define TNS_TYPE_NULL           7
#define TNS_TYPE_ABORT          9
#define TNS_TYPE_RESEND         11
#define TNS_TYPE_MARKER         12
#define TNS_TYPE_ATTENTION      13
#define TNS_TYPE_CONTROL        14
#define TNS_TYPE_MAX            19

#define is_valid_tns_type(x) (((x) >= TNS_TYPE_CONNECT && (x) < TNS_TYPE_MAX))

/* Data Packet Functions */
#define SQLNET_SET_PROTOCOL     1
#define SQLNET_SET_DATATYPES    2
#define SQLNET_USER_OCI_FUNC    3
#define SQLNET_RETURN_STATUS    4
#define SQLNET_ACCESS_USR_ADDR  5
#define SQLNET_ROW_TRANSF_HDR   6
#define SQLNET_ROW_TRANSF_DATA  7
#define SQLNET_RETURN_OPI_PARAM 8
#define SQLNET_FUNCCOMPLETE     9
#define SQLNET_NERROR_RET_DEF   10
#define SQLNET_IOVEC_4FAST_UPI  11
#define SQLNET_LONG_4FAST_UPI   12
#define SQLNET_INVOKE_USER_CB   13
#define SQLNET_LOB_FILE_DF      14
#define SQLNET_WARNING          15
#define SQLNET_DESCRIBE_INFO    16
#define SQLNET_PIGGYBACK_FUNC   17
#define SQLNET_SIG_4UCS         18
#define SQLNET_FLUSH_BIND_DATA  19
#define SQLNET_SNS              0xdeadbeef
#define SQLNET_XTRN_PROCSERV_R1 32
#define SQLNET_XTRN_PROCSERV_R2 68

static const value_string tns_data_funcs[] = {
    {SQLNET_SET_PROTOCOL,     "Set Protocol"},
    {SQLNET_SET_DATATYPES,    "Set Datatypes"},
    {SQLNET_USER_OCI_FUNC,    "User OCI Functions"},
    {SQLNET_RETURN_STATUS,    "Return Status"},
    {SQLNET_ACCESS_USR_ADDR,  "Access User Address Space"},
    {SQLNET_ROW_TRANSF_HDR,   "Row Transfer Header"},
    {SQLNET_ROW_TRANSF_DATA,  "Row Transfer Data"},
    {SQLNET_RETURN_OPI_PARAM, "Return OPI Parameter"},
    {SQLNET_FUNCCOMPLETE,     "Function Complete"},
    {SQLNET_NERROR_RET_DEF,   "N Error return definitions follow"},
    {SQLNET_IOVEC_4FAST_UPI,  "Sending I/O Vec only for fast UPI"},
    {SQLNET_LONG_4FAST_UPI,   "Sending long for fast UPI"},
    {SQLNET_INVOKE_USER_CB,   "Invoke user callback"},
    {SQLNET_LOB_FILE_DF,      "LOB/FILE data follows"},
    {SQLNET_WARNING,          "Warning messages - may be a set of them"},
    {SQLNET_DESCRIBE_INFO,    "Describe Information"},
    {SQLNET_PIGGYBACK_FUNC,   "Piggy back function follow"},
    {SQLNET_SIG_4UCS,         "Signals special action for untrusted callout support"},
    {SQLNET_FLUSH_BIND_DATA,  "Flush Out Bind data in DML/w RETURN when error"},
    {SQLNET_XTRN_PROCSERV_R1, "External Procedures and Services Registrations"},
    {SQLNET_XTRN_PROCSERV_R2, "External Procedures and Services Registrations"},
    {SQLNET_SNS,              "Secure Network Services"},
    {0, NULL}
};

#define TCP_PORT_TNS            1521 /* Not IANA registered */

struct tns_info {
    uint8_t     direction[8];
    uint8_t     type;
    uint8_t     flag;
    uint16_t    packet_chksum;
    uint16_t    head_chksum;
    uint32_t    length;

    uint16_t    conn_version;
    uint16_t    conn_compat_ver;
    uint16_t    conn_service_opt;
    uint16_t    conn_sdu_size;
    uint16_t    conn_max_tdu_size;
    uint16_t    conn_nt_proto_charac;
    uint16_t    conn_line_turnaround;
    uint16_t    conn_value_of_one;
    uint16_t    conn_data_length;
    uint16_t    conn_data_offset;
    uint32_t    conn_data_max;
    uint8_t     conn_flag0;
    uint8_t     conn_flag1;
    uint32_t    conn_trace_cf1;
    uint32_t    conn_trace_cf2;
    uint8_t     conn_trace_cid[8];
    uint8_t     conn_data[256];
    uint8_t     conn_data_sid[8];
    uint8_t     conn_data_program[64];
    uint8_t     conn_data_user[32];
    uint8_t     conn_data_protocol[16];
    uint8_t     conn_data_host1[32];
    uint8_t     conn_data_host2[32];
    uint8_t     conn_data_server[32];
    uint8_t     conn_data_port[8];

    uint16_t    data_flag;
    uint32_t    data_id;
    uint8_t     data_callid;
    uint8_t     data_content[1024];
    uint8_t     data_sql[1024];
    uint8_t     data_cli_plat[64];
    uint8_t     data_version[32];
    uint8_t     data_banner[32];

    uint8_t     auth_password[256];
};


static const char *
try_val_to_str(uint32_t val, const value_string *vs)
{
    int i=0;
    if(vs){
        while(vs[i].string){
            if(vs[i].value==val){
                return vs[i].string;
            }
            i++;
        }
    }
    return NULL;
}

static int tns_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct tns_info *info, int *idx, int i)
{
    int local_idx=*idx;
    switch(i){
    case EM_TNS_DATA_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)"sqlserver", strlen("sqlserver"));
        break;
    case EM_TNS_DIRECTION:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->direction, strlen((const char*)info->direction));
        break;
    case EM_TNS_HEAD_LENGTH:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->length);
        break;
    case EM_TNS_HEAD_PACKET_CHECKSUM:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->packet_chksum);
        break;
    case EM_TNS_HEAD_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->type);
        break;
    case EM_TNS_HEAD_RESERVED:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->flag);
        break;
    case EM_TNS_HEAD_HEADER_CHECKSUM:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->head_chksum);
        break;

    case EM_TNS_CONNECT_VERSION:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_version);
        break;
    case EM_TNS_CONNECT_COMPAT_VERSION:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_compat_ver);
        break;
    case EM_TNS_CONNECT_SERVICE_OPTIONS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_service_opt);
        break;
    case EM_TNS_CONNECT_SDU_SIZE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_sdu_size);
        break;
    case EM_TNS_CONNECT_MAX_TDU_SIZE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_max_tdu_size);
        break;
    case EM_TNS_CONNECT_NT_PROTO_CHARACTERISTICS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_nt_proto_charac);
        break;
    case EM_TNS_CONNECT_LINE_TURNAROUND:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_line_turnaround);
        break;
    case EM_TNS_CONNECT_VALUE_OF_ONE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_value_of_one);
        break;
    case EM_TNS_CONNECT_DATA_LENGTH:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_data_length);
        break;
    case EM_TNS_CONNECT_DATA_OFFSET:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_data_offset);
        break;
    case EM_TNS_CONNECT_DATA_MAX:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_data_max);
        break;
    case EM_TNS_CONNECT_FLAGS0:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_flag0);
        break;
    case EM_TNS_CONNECT_FALGS1:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_flag1);
        break;
    case EM_TNS_CONNECT_TRACE_CF1:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_trace_cf1);
        break;
    case EM_TNS_CONNECT_TRACE_CF2:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->conn_trace_cf2);
        break;
    case EM_TNS_CONNECT_TRACE_CID:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_trace_cid, strlen((const char*)info->conn_trace_cid));
        break;

    case EM_TNS_CONNECT_DATA:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data, strlen((const char*)info->conn_data));
        break;
    case EM_TNS_CONNECT_DATA_SID:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_sid, strlen((const char*)info->conn_data_sid));
        break;
    case EM_TNS_CONNECT_DATA_PROGRAM:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_program, strlen((const char*)info->conn_data_program));
        break;
    case EM_TNS_CONNECT_DATA_USER:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_user, strlen((const char*)info->conn_data_user));
        break;
    case EM_TNS_CONNECT_DATA_PROTOCOL:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_protocol, strlen((const char*)info->conn_data_protocol));
        break;
    case EM_TNS_CONNECT_DATA_HOST1:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_host1, strlen((const char*)info->conn_data_host1));
        break;
    case EM_TNS_CONNECT_DATA_HOST2:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_host2, strlen((const char*)info->conn_data_host2));
        break;
    case EM_TNS_CONNECT_DATA_PORT:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t*)info->conn_data_port, strlen((const char*)info->conn_data_port));
        break;
    case EM_TNS_CONNECT_DATA_SERVER:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->conn_data_server, strlen((const char*)info->conn_data_server));
        break;

    case EM_TNS_DATA_FLAGS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->data_flag);
        break;
    case EM_TNS_DATA_ID:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->data_id);
        break;
    case EM_TNS_DATA_CALL_ID:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, NULL, info->data_callid);
        break;
    case EM_TNS_DATA_CONTENT:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->data_content, strlen((const char *)info->data_content));
        break;
    case EM_TNS_DATA_SQL:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->data_sql, strlen((const char *)info->data_sql));
        break;
    case EM_TNS_DATA_VERSION:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->data_version, strlen((const char *)info->data_version));
        break;
    case EM_TNS_DATA_CLI_PLAT:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->data_cli_plat, strlen((const char *)info->data_cli_plat));
        break;
    case EM_TNS_DATA_BANNER:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->data_banner, strlen((const char *)info->data_banner));
        break;
    case EM_TNS_AUTH_PASSWORD:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tns_field_array[i].type, (const uint8_t *)info->auth_password, strlen((const char *)info->auth_password));
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = tns_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif

    return 0;
}


static int write_tns_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    struct tns_info *info=(struct tns_info *)field_info;
    if(!info){
        return 0;
    }

    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "tns");

    for(i=0;i<EM_TNS_MAX;i++){
        tns_field_element(log_ptr, flow, direction, info, &idx, i);
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_TNS;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}

static void write_dbbasic_log(struct flow_info *flow, int direction, struct tns_info *info, SdtMatchResult *match_result)
{
    char __str[64] = {0};
    int idx = 0,i;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }

    init_log_ptr_data(log_ptr, flow,PROTOCOL_DBBASIC);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "dbbasic");

    for(i=0;i<EM_DBBASIC_MAX;i++){
        switch(dbbasic_field_array[i].index){
        case EM_DBBASIC_DBTYPE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "oracle", 6);
            break;
        case EM_DBBASIC_USERNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->conn_data_user, strlen((const char*)info->conn_data_user));
            break;
        case EM_DBBASIC_PASSWORD:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)info->auth_password, strlen((const char *)info->auth_password));
            break;
        case EM_DBBASIC_DBNAME:
            //客户端SID和server_name模式
            if(strlen((const char*)info->conn_data_sid) > 0)
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->conn_data_sid, strlen((const char*)info->conn_data_sid));
            else
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->conn_data_server, strlen((const char*)info->conn_data_server));
            break;
        case EM_DBBASIC_DBSQL:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)info->data_sql, strlen((const char *)info->data_sql));
            break;
        case EM_DBBASIC_DBIP:
            if(strlen((const char*)info->conn_data_host1)) {
                memset(__str, 0, sizeof(__str));
                get_ipstring(flow->ip_version, (char *)flow->tuple.inner.ip_dst,  __str, sizeof(__str));
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)__str, strlen(__str));
            } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_DBBASIC_DBPORT:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->conn_data_port, (uint64_t)strlen((const char*)info->conn_data_port));
            break;
        case EM_DBBASIC_ORACLE_HOST:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->conn_data_host1, strlen((const char*)info->conn_data_host1));
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    log_ptr->proto_id = PROTOCOL_DBBASIC;
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_DBBASIC;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return;
}


static void identify_tns(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_TNS] == 0)
        return;

    uint16_t chksum;
    uint32_t length;
    uint8_t type;
    uint8_t offset;

    // port==TCP_PORT_TNS 4bytes类型在范围内

    if (ntohs(flow->tuple.inner.port_dst) != TCP_PORT_TNS
       && ntohs(flow->tuple.inner.port_src) != TCP_PORT_TNS) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TNS);
        return;
    }

    type = get_uint8_t(payload, 4);
    if (type < TNS_TYPE_CONNECT || type > TNS_TYPE_CONTROL){
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TNS);
        return;
    }

    chksum = get_uint16_ntohs(payload, 2);

    offset = (chksum == 0 || chksum == 4) ? 2 : 4;
    length = offset==2?get_uint16_ntohs(payload, 0):get_uint32_ntohl(payload, 0);
    if(length<payload_len){
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TNS);
        return;
    }

    flow->real_protocol_id = PROTOCOL_TNS;
    return;
}

static void
dissect_tns_connect(struct flow_info *flow, const uint8_t *payload, struct tns_info* info){

    uint16_t offset = 8;
    info->conn_version = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_compat_ver = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_service_opt = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_sdu_size = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_max_tdu_size = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_nt_proto_charac = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_line_turnaround = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_value_of_one = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_data_length = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_data_offset = get_uint16_ntohs(payload, offset);
    offset += 2;

    info->conn_data_max = get_uint32_ntohl(payload, offset);
    offset += 4;

    info->conn_flag0 = get_uint8_t(payload, offset);
    offset += 1;

    info->conn_flag1 = get_uint8_t(payload, offset);
    offset += 1;

    if (offset+16 < info->conn_data_offset) {
        info->conn_trace_cf1 = get_uint32_ntohl(payload, offset);
        offset += 4;

        info->conn_trace_cf2 = get_uint32_ntohl(payload, offset);
        offset += 4;

        memcpy((char*)info->conn_trace_cid, (const char*)payload+offset, 8);
        offset += 8;

    }

    if (info->length != info->conn_data_length + info->conn_data_offset){
        // 长度不正确
        return;
    }

    memcpy((char*)info->conn_data, (const char*)payload+info->conn_data_offset, info->conn_data_length);
    if (strlen((const char *)info->conn_data) == info->conn_data_length){
        //解析data中的数据
        unsigned int i = 0;
        int count_left = 0;
        char* tmp1 = 0;
        char* tmp2 = 0;
        char key[16];
        char tmp[256];
        char address[256];
        char connect_data[256];

        tmp1 = strstr((const char *)(info->conn_data), "(DESCRIPTION=");
        tmp1 += 13;
        for(i = 0;i<strlen((const char *)info->conn_data);i++){
            if(tmp1[i] == '('){
                count_left++;
            } else if (tmp1[i] == ')'){
                count_left--;
            }
            if(count_left == 0){
                break;
            }
        }

        memset(tmp, 0, sizeof(tmp));
        strncpy((char *)tmp, tmp1, i+1);
        if(strstr(tmp, "CONNECT_DATA") != NULL){
            strncpy((char *)connect_data,tmp1,i+1);
            strcpy(address,(const char *)(info->conn_data+i+13+1));
        } else {
            strncpy((char *)address,tmp1,i+1);
            strcpy(connect_data,(const char *)(info->conn_data+i+13+1));
        }

        // address
        tmp1 = strstr(address, "PROTOCOL");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_protocol, tmp1+9, tmp2-tmp1-9);
        }

        tmp1 = strstr(address, "HOST");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_host1, tmp1+5, tmp2-tmp1-5);
        }

        tmp1 = strstr(address, "PORT");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_port, tmp1+5, tmp2-tmp1-5);
        }

        // connect_data
        tmp1 = strstr(connect_data, "SID");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_sid, tmp1+4, tmp2-tmp1-4);
        }

        tmp1 = strstr(connect_data, "SERVER");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_server, tmp1+7, tmp2-tmp1-7);

        }

        tmp1 = strstr(connect_data, "PROGRAM");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_program, tmp1+8, tmp2-tmp1-8);
        }

        tmp1 = strstr(connect_data, "HOST");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_host2, tmp1+5, tmp2-tmp1-5);
        }

        tmp1 = strstr(connect_data, "USER");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_user, tmp1+5, tmp2-tmp1-5);
        }

        tmp1 = strstr(connect_data, "SERVICE_NAME");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
                strncpy((char *)info->conn_data_server, tmp1+13, tmp2-tmp1-13);
        }
    }

    return;
}

static void
dissect_tns_redirect(struct flow_info *flow, const uint8_t *payload, struct tns_info* info){
    uint16_t offset = 8;

    info->conn_data_length = get_uint16_ntohs(payload, offset);
    offset += 2;

    if (info->conn_data_length + offset == info->length){
        memcpy((char*)info->conn_data, (const char*)payload+offset, info->conn_data_length);
        //解析data中的数据
        unsigned int i = 0;
        int count_left = 0;
        char* tmp1 = 0;
        char* tmp2 = 0;

        if(strstr((char*)info->conn_data, "ADDRESS") == NULL){
            return;
        }

        // address
        tmp1 = strstr((char*)info->conn_data, "PROTOCOL");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_protocol, tmp1+9, tmp2-tmp1-9);
        }

        tmp1 = strstr((char*)info->conn_data, "HOST");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_host1, tmp1+5, tmp2-tmp1-5);
        }

        tmp1 = strstr((char*)info->conn_data, "PORT");
        if(tmp1) {
            tmp2 = strchr(tmp1, ')');
            if(tmp2)
            strncpy((char *)info->conn_data_port, tmp1+5, tmp2-tmp1-5);
        }

    }

    return;
}

static void
dissect_tns_data_sql(struct flow_info *flow, const uint8_t *payload, uint32_t data_len, struct tns_info* info){
    uint32_t i = 0,j = 0,count = 0,length = 0;
    for(i = 0;i<data_len-1;i++){
        if(isalpha(payload[i]) > 0){
            if (count == 0) {
                j = i;
            }
            count++;
        } else if (count < 5) {
            count = 0;
            j = 0;
        } else if (count > 5 && payload[i] == 0x01 && payload[i] == 0x01) {
            break;
        }
    }

    if (i && j && i-j > 5){
        memcpy(info->data_sql, payload+j, i-j);
    }

    length = i - j;
    for(i = length-1;i>0;i--){
        if(info->data_sql[i] != 0x0d && info->data_sql[i] != 0x20
        && info->data_sql[i] != 0x01 && info->data_sql[i] != 0x09){
            info->data_sql[i] = '\0';
            break;
        }
    }

}

static uint32_t get_data_func_id(const uint8_t *payload, uint16_t offset)
{
    uint8_t first_byte;

    first_byte = get_uint8_t(payload, offset);

    if ( first_byte == 0xDE &&
        (get_uint32_ntohl(payload, offset+1) & 0xffffff00) == 0xADBEEF00 )
    {
        return SQLNET_SNS;
    }
    else
    {
        return (uint32_t)first_byte;
    }
}

static void
dissect_tns_data(struct flow_info *flow, int direction, const uint8_t *payload, struct tns_info* info){
    uint16_t offset = 8;
    uint32_t data_func_id = 0;
    info->data_flag = get_uint16_ntohs(payload, offset);
    offset += 2;

    data_func_id = get_data_func_id(payload, offset);
    info->data_id = data_func_id;

    if ( (data_func_id != SQLNET_SNS) && (try_val_to_str(data_func_id, tns_data_funcs) != NULL) ) {
        info->data_id = get_uint8_t(payload, offset);
        offset += 1;
    }

    switch(info->data_id){
    case SQLNET_PIGGYBACK_FUNC:
    {
        info->data_callid = get_uint8_t(payload, offset);
        offset += 1;
        uint32_t data_len = info->length - 8 - 4;

        dissect_tns_data_sql(flow, payload+12, data_len, info);
        //write_tns_log(flow, direction, info, NULL);
        write_dbbasic_log(flow, direction, info, NULL);
        break;
    }
    case SQLNET_SET_PROTOCOL:
    {
        char sep;
        uint32_t i = 0;
        if ( strcmp((const char *)info->direction, "Request") == 0 )
        {
            sep = ',';
            for (;;) {
                /*
                 * Add each accepted version as a
                 * separate item.
                 */
                uint8_t vers;

                vers = get_uint8_t(payload, offset);
                if (vers == 0) {
                    /*
                     * A version of 0 terminates
                     * the list.
                     */
                    i = i>2?i-1:0;
                    info->data_version[i] = '\0';
                    break;
                }
                info->data_version[i] = vers+48;
                info->data_version[i+1] = sep;
                offset += 1;
                i+=2;
            }
            offset += 1; /* skip the 0 terminator */
            memcpy(info->data_cli_plat, payload+offset, info->length-offset);

            return; /* skip call_data_dissector */
        }
        else
        {
            sep = ',';
            for (;;) {
                /*
                 * Add each accepted version as a
                 * separate item.
                 */
                uint8_t vers;

                vers = get_uint8_t(payload, offset);
                if (vers == 0) {
                    /*
                     * A version of 0 terminates
                     * the list.
                     */
                    i = i>=2?i-1:0;
                    info->data_version[i] = '\0';
                    break;
                }
                info->data_version[i] = vers+48;
                info->data_version[i+1] = sep;
                offset += 1;
                i+=2;
            }
            offset += 1; /* skip the 0 terminator */
            for(i=offset;i<info->length;i++){
                if(payload[i] == 0x00){
                    break;
                }
            }
            if(i<info->length && i-offset > 0){
                memcpy(info->data_banner, payload+offset, i-offset);
            }

        }
        //write_tns_log(flow, direction, info, NULL);
        write_dbbasic_log(flow, direction, info, NULL);
        break;
    }
    case SQLNET_USER_OCI_FUNC:
    {
        info->data_callid = get_uint8_t(payload, offset);
        offset += 1;
        uint32_t data_len = info->length - 8 - 4;
        uint8_t data_tmp[2048];
        uint8_t tmp[16]={0};
        uint8_t i,length;
        if (info->length > 1000) {
            memcpy(data_tmp, payload+12, data_len);
            for(i = 0; i < data_len-20;i++) {
                memcpy(tmp, data_tmp+i, 13);
                if(strcmp((const char *)tmp, "AUTH_PASSWORD") == 0 || strcmp((const char *)tmp, "auth_password") == 0){
                    if (get_uint8_t(data_tmp, i+13) == 0x01 && get_uint8_t(data_tmp, i+14) == get_uint8_t(data_tmp, i+15)) {
                        length = get_uint8_t(data_tmp, i+14);
                        strncpy((char*)info->auth_password, (const char *)data_tmp+i+16, length);
                        //write_tns_log(flow, direction, info, NULL);
                        write_dbbasic_log(flow, direction, info, NULL);
                        break;
                    }
                }
            }

        }
        break;
    }
    default:
        break;
    }
}

static int dissect_tns_payload(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len)
{
    struct tns_info info;
    memset(&info, 0, sizeof(info));

    uint8_t *reassemble_result = NULL;
    uint8_t *tmp_result = NULL;
    uint8_t  length_len = 0;
    uint32_t index=0;
    uint32_t actual_len = 0;
    uint32_t expect_len = 0;
    uint32_t remain_len = 0;
    uint32_t reassemble_result_len=0;
    struct list_head *reassemble;

    if(direction==FLOW_DIR_DST2SRC) {
        reassemble_result_len = flow->reassemble_pkt_dst2src_num * TCP_PAYLOAD_MAX_LEN;
        reassemble = &flow->reassemble_dst2src_head;
    } else {
        reassemble_result_len = flow->reassemble_pkt_src2dst_num * TCP_PAYLOAD_MAX_LEN;
        reassemble = &flow->reassemble_src2dst_head;
    }
    if(reassemble_result_len<=0){return 0;}
    tcp_reassemble_do_guesslen(reassemble,  &actual_len, &expect_len);
    if(expect_len>100*1024*1024){
        return 0;
    }
    reassemble_result = (uint8_t *)dpi_malloc(expect_len*sizeof(uint8_t)+1500);
    tmp_result = reassemble_result;
    if (tmp_result) {
        tcp_reassemble_do(reassemble, tmp_result, &actual_len);
        remain_len = actual_len;
        while(remain_len > 8 && remain_len <= actual_len){
            memset(&info, 0, sizeof(info));
            info.packet_chksum = get_uint16_ntohs(tmp_result, 2);
            length_len = (info.packet_chksum == 0 || info.packet_chksum == 4) ? 2 : 4;
            info.length = length_len==2?get_uint16_ntohs(tmp_result, 0):get_uint32_ntohl(tmp_result, 0);
            info.type = get_uint8_t(tmp_result, 4);
            info.flag = get_uint8_t(tmp_result, 5);
            info.head_chksum = get_uint16_ntohs(tmp_result, 6);
            if(direction == FLOW_DIR_SRC2DST) {
                strcpy((char*)info.direction, "Request");
            } else {
                strcpy((char*)info.direction, "Reponse");
            }

            if(info.type < TNS_TYPE_CONNECT || info.type > TNS_TYPE_CONTROL || info.length > remain_len){
                return 0;
            }

            flow->match_data_len = info.length;

            switch (info.type) {
                case TNS_TYPE_CONNECT:
                    dissect_tns_connect(flow, tmp_result, &info);
                    //write_tns_log(flow, direction, &info, NULL);
                    write_dbbasic_log(flow, direction, &info, NULL);
                    break;
                case TNS_TYPE_REDIRECT:
                    dissect_tns_redirect(flow, tmp_result, &info);
                    //write_tns_log(flow, direction, &info, NULL);
                    write_dbbasic_log(flow, direction, &info, NULL);
                    break;
                case TNS_TYPE_DATA:
                    dissect_tns_data(flow, direction, tmp_result, &info);
                    break;

                default:
                    break;
            }
            remain_len -= info.length;
            tmp_result += info.length;

        }
    }

    if (reassemble_result) {
        dpi_free(reassemble_result);
    }

    return 0;
}

static int dissect_tns(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    // direction重新判断
    // dst port == 1521 request 内容基本都在request里
    // else reponse
    direction = flow->port_dst == TCP_PORT_TNS ? FLOW_DIR_SRC2DST : FLOW_DIR_DST2SRC;

    if (flag == DISSECT_PKT_ORIGINAL) {
        if (direction == FLOW_DIR_SRC2DST && flow->reassemble_pkt_src2dst_num < g_config.tcp_resseamble_max_num) {
            tcp_reassemble_add_item(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
            flow->reassemble_pkt_src2dst_num++;
        } else if (direction == FLOW_DIR_DST2SRC && flow->reassemble_pkt_dst2src_num < g_config.tcp_resseamble_max_num) {
            tcp_reassemble_add_item(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len, seq, payload, payload_len);
            flow->reassemble_pkt_dst2src_num++;
        } else {
            return PKT_DROP;
        }
    }

    if (flag == DISSECT_PKT_FIANL) {
        dissect_tns_payload(flow, FLOW_DIR_SRC2DST, payload, payload_len);
        dissect_tns_payload(flow, FLOW_DIR_DST2SRC, payload, payload_len);

        tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
        flow->reassemble_pkt_src2dst_num=0;
        tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);
        flow->reassemble_pkt_dst2src_num=0;
        return PKT_OK;
    }
    return PKT_OK;

}

static void init_tns_dissector(void)
{
    dpi_register_proto_schema(tns_field_array,EM_TNS_MAX,"tns");
    port_add_proto_head(IPPROTO_TCP, TCP_PORT_TNS, PROTOCOL_TNS);

    tcp_detection_array[PROTOCOL_TNS].proto = PROTOCOL_TNS;
    tcp_detection_array[PROTOCOL_TNS].identify_func = identify_tns;
    tcp_detection_array[PROTOCOL_TNS].dissect_func = dissect_tns;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TNS].excluded_protocol_bitmask, PROTOCOL_TNS);



    map_fields_info_register(tns_field_array_sdt,PROTOCOL_TNS, EM_TNS_MAX,"tns");

    return;
}


static __attribute((constructor)) void    before_init_tns(void){
    register_tbl_array(TBL_LOG_TNS, 0, "tns", init_tns_dissector);
}

static void init_dbbasic_dissector(void)
{
    dpi_register_proto_schema(dbbasic_field_array,EM_DBBASIC_MAX,"dbbasic");
    map_fields_info_register(dbbasic_field_array,PROTOCOL_DBBASIC, EM_DBBASIC_MAX,"dbbasic");
    return;
}
static __attribute((constructor)) void    before_init_dbbasic(void){
    register_tbl_array(TBL_LOG_DBBASIC, 0, "dbbasic", init_dbbasic_dissector);
}


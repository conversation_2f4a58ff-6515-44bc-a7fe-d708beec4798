/****************************************************************************************
 * 文 件 名 : dpi_notify.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 : dpi 输出模块
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chenzq      2022/11/11
编码: chenzq      2022/11/11
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <unistd.h>
#include "dpi_notify.h"

#include <pthread.h>
#include <glib.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>


int g_inotify_fd;
GHashTable *g_conf_hash;

#define EVENTS_BUF_SIZE 4096
#define MAX_LINE 1024

static
int find_config(const char * file_name)
{
  FILE *fp = NULL;
  char buff[MAX_LINE] = { 0 };
  char prefix[64] = { 0 };
  fp = fopen(file_name, "r");
  GSList * find = NULL;

  while (fgets(buff, MAX_LINE, fp) != NULL) {
    if (buff[strlen(buff) - 1] == '\n'){
      buff[strlen(buff) - 1] = '\0';
    }
    if (buff[0] == '[') {
      snprintf(prefix, sizeof(prefix), "%s", buff);
      continue;
    }

    char key[MAX_LINE] = { 0 };
    char value[MAX_LINE] = { 0 };
    char *beg = NULL;
    beg = strstr(buff, "=");
    if (!beg) {
        continue;
    }
    snprintf(key, sizeof(key), "%s", prefix);
    snprintf(key + strlen(key), beg - buff + 1, "%s", buff);
    snprintf(value, sizeof(value), "%s", beg + 1);

    char * find = (char *)g_hash_table_lookup(g_conf_hash, (void *)key);
    if (NULL == find || strcmp(value, find) != 0)
      return -1; 
  }

  return 0;
}


static
void display_event(const char *base, struct inotify_event *event)
{
    const char *operate;
    FILE *fp;
    int mask = event->mask;
    if (mask & IN_ACCESS)        operate = "ACCESS";
    if (mask & IN_ATTRIB)        operate = "ATTRIB";
    if (mask & IN_CLOSE_WRITE)   operate = "CLOSE_WRITE";
    if (mask & IN_CLOSE_NOWRITE) operate = "CLOSE_NOWRITE";
    if (mask & IN_CREATE)        operate = "CREATE";
    if (mask & IN_DELETE_SELF)   operate = "DELETE_SELF";
    if (mask & IN_MODIFY)        operate = "MODIFY";
    if (mask & IN_MOVE_SELF)     operate = "MOVE_SELF";
    if (mask & IN_MOVED_FROM)    operate = "MOVED_FROM";
    if (mask & IN_MOVED_TO)      operate = "MOVED_TO";
    if (mask & IN_OPEN)          operate = "OPEN";
    if (mask & IN_IGNORED)       operate = "IGNORED";
    if (mask & IN_DELETE)        operate = "DELETE";
    if (mask & IN_UNMOUNT)       operate = "UNMOUNT";
    printf("%s/%s: %s\n", base, event->name, operate);
    if ((mask & IN_MODIFY) && find_config(base) == -1)
      exit(1);
}

// GHFunc print_callback(gpointer key, gpointer value, gpointer user_data)
// {
//   printf("key = %s, value = %s\n",key,  (char *)value);
// }

static
void config_data_init(const char * file_name)
{
  FILE *fp = NULL;
  char buff[MAX_LINE] = { 0 };
  char prefix[64] = { 0 };
  fp = fopen(file_name, "r");

  while (fgets(buff, MAX_LINE, fp) != NULL) {
    if (buff[strlen(buff) - 1] == '\n'){
      buff[strlen(buff) - 1] = '\0';
    }
    if (buff[0] == '[') {
      snprintf(prefix, sizeof(prefix), "%s", buff);
      continue;
    }

    char key[MAX_LINE] = { 0 };
    char value[MAX_LINE] = { 0 };
    char *beg = NULL;
    beg = strstr(buff, "=");
    if (!beg) {
        continue;
    }
    snprintf(key, sizeof(key), "%s", prefix);
    snprintf(key + strlen(key), beg - buff + 1, "%s", buff);
    snprintf(value, sizeof(value), "%s", beg + 1);
    g_hash_table_insert(g_conf_hash, g_strdup(key), g_strdup(value));
    // printf("key = %s, value = %s\n", key, value);
  }

  // g_hash_table_foreach(g_conf_hash, print_callback, NULL);
}


static
void * inotify_thread(void * args)
{
  char events[EVENTS_BUF_SIZE];
  int nbytes, offset;
  struct inotify_event *event;
  const char * config_name = "./web_config.ini";
  config_data_init(config_name);
  inotify_add_watch(g_inotify_fd, config_name, IN_MODIFY);
  while (1)
  {
    memset(events, 0, sizeof(events));
    nbytes = read(g_inotify_fd, events, sizeof(events));
    if (nbytes <= 0)
      continue;

    // 开始打印发生的事件
    for (offset = 0; offset < nbytes; ) {
        event = (struct inotify_event *)&events[offset]; // 获取变动事件的指针

        display_event(config_name, event);
        offset += sizeof(struct inotify_event) + event->len; // 获取下一个变动事件的偏移量
    }
  }
    return args;
}


// int  dpi_notify_add_watch(const char * file_name, uint32_t mask)
// {
//   // inotify_add_watch(g_inotify_fd, file_name, mask);
// }

void dpi_notify_init(void)
{
  pthread_t th;
  g_inotify_fd = inotify_init();
  if (g_inotify_fd < 0) {
    printf("inotify 监控模块初始化失败!!!");
  }
  g_conf_hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);

  pthread_create(&th, NULL, inotify_thread , NULL);

}

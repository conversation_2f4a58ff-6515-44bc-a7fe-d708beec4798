
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <stdio.h>
#include <unistd.h>

#include "dpi_pint.h"
#include "dpi_common.h"
#include "dpi_ber_ori.h"
#include "charsets.h"

static uint8_t    last_class;
static uint8_t    last_pc;
static uint32_t   last_tag;
static uint32_t   last_length;
static uint8_t    last_ind;

/* 8.1 General rules for encoding */

/*  8.1.2 Identifier octets */
int dpi_ber_identifier(struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *ber_class, uint8_t *pc, uint32_t *tag) 
{
    uint8_t    id, t;
    uint8_t    tmp_class;
    uint8_t    tmp_pc;
    uint32_t   tmp_tag;

    if (dpi_get_uint8(pkt, offset, &id) == -1)
        return offset;
    offset += 1;

    /* 8.1.2.2 */
    tmp_class = (id >> 6) & 0x03;
    tmp_pc = (id >> 5) & 0x01;
    tmp_tag = id & 0x1F;

    /* 8.1.2.4 */
    if (tmp_tag == 0x1F) {
        tmp_tag = 0;
        while (offset < pkt->payload_len) {
            t = get_uint8_t(pkt->payload, offset);

            offset += 1;
            if (offset >= pkt->payload_len)
                return offset;
            tmp_tag <<= 7;
            tmp_tag |= t & 0x7F;
            if (!(t & 0x80))
                break;
        }
    }

    if (ber_class)
        *ber_class = tmp_class;
    if (pc)
        *pc=tmp_pc;
    if (tag)
        *tag = tmp_tag;

    last_class = tmp_class;
    last_pc  = tmp_pc;
    last_tag = tmp_tag;

    return offset;
}

static void dpi_last_ber_identifier(uint8_t *ber_class, uint8_t *pc, uint32_t *tag)
{
    if (ber_class)
        *ber_class = last_class;
    if (pc)
        *pc  = last_pc;
    if (tag)
        *tag = last_tag;
}

/** Try to get the length octets of the BER TLV.
 * Only (TAGs and) LENGTHs that fit inside 32 bit integers are supported.
 *
 * @return TRUE if we have the entire length, FALSE if we're in the middle of
 * an indefinite length and haven't reached EOC.
 */
/* 8.1.3 Length octets */

static int try_dpi_ber_length(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *length, uint8_t *ind)
{
    uint8_t        oct, len;
    uint32_t    indef_len=0;
    uint32_t    tmp_length=0;
    uint8_t        tmp_ind=0;
    uint32_t    tmp_offset, s_offset;
    uint8_t        tclass;
    uint8_t        tpc;
    uint32_t    ttag;

    tmp_length = 0;
    tmp_ind    = 0;

    if (dpi_get_uint8(pkt, offset, &oct) == -1)
        return offset;

    offset += 1;

    if (!(oct & 0x80)) {
        /* 8.1.3.4 */
        tmp_length = oct;
    } else {
        len = oct & 0x7F;
        if (len) {
            /* 8.1.3.5 */
            while (len--) {
                if (offset > pkt->payload_len)
                    return 0;
                oct = get_uint8_t(pkt->payload, offset);
                offset++;
                tmp_length = (tmp_length << 8) + oct;
            }
        } else {
            /* 8.1.3.6 */
            tmp_offset = offset;

            /* ok in here we can traverse the BER to find the length, this will fix most indefinite length issues */
            /* Assumption here is that indefinite length is always used on constructed types*/
            /* check for EOC */
            if(offset + 1 >= pkt->payload_len)
                return 0;
            while (get_uint8_t(pkt->payload, offset) || get_uint8_t(pkt->payload, offset + 1)) {
                /* not an EOC at offset */
                s_offset = offset;
                offset = dpi_ber_identifier(pkt, offset, &tclass, &tpc, &ttag);
                offset = try_dpi_ber_length(pkt, offset, &indef_len, NULL);
                tmp_length += indef_len + (offset - s_offset); /* length + tag and length */
                offset += indef_len;

                /* Make sure we've moved forward in the packet */
                if (offset <= s_offset || offset >= pkt->payload_len){
                    return pkt->payload_len;
                }
            }
            tmp_length += 2;
            tmp_ind = 1;
            offset = tmp_offset;
        }
    }

    /* Several users treat the length as signed value, clamp the value to avoid
    * an overflow to negative values. */
    // if (tmp_length > (guint32)G_MAXINT32)
    //     tmp_length = (guint32)G_MAXINT32;

    if (length)
        *length = tmp_length;
    if (ind)
        *ind = tmp_ind;

    if(offset >= pkt->payload_len)
        return pkt->payload_len;
    return offset;
}

int dpi_ber_length(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *length, uint8_t *ind)
{
    uint32_t tmp_length;
    uint8_t tmp_ind;

    offset = try_dpi_ber_length(pkt, offset, &tmp_length, &tmp_ind);

    if (length)
        *length = tmp_length;
    if (ind)
        *ind = tmp_ind;

    last_length = tmp_length;
    last_ind = tmp_ind;
    
    return offset;
}

static void dpi_last_ber_length(uint32_t *length, uint8_t *ind)
{
    if (length)
        *length = last_length;
    if (ind)
        *ind = last_ind;
}

int dpi_ber_integer64(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, int64_t *value)
{
    uint8_t    ber_class;
    uint8_t    pc;
    uint32_t   tag;
    uint32_t  len;
    int64_t   val;
    uint32_t  i;
    uint8_t   used_too_many_bytes = 0;
    uint8_t   first;

    if (!implicit_tag) {
        offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset = dpi_ber_length(pkt, offset, &len, NULL);
    } else {
        len = pkt->payload_len > offset ? pkt->payload_len - offset : 0;
    }

    if (dpi_get_uint8(pkt, offset, &first) == -1)
        return offset;
    /* we can't handle integers > 64 bits */
    /* take into account the use case of a 64bits unsigned integer: you will have a 9th byte set to 0 */
    if ((len > 9) || ((len == 9) && (first != 0))) {
        offset += len;
        return offset;
    }

    val = 0;
    if (len > 0) {
        /* extend sign bit for signed fields */
        enum ftenum type  = BER_FT_INT32; /* Default to signed, is this correct? */
        if (first & 0x80 && IS_BER_FT_INT(type)) {
            val = -1;
        }
        if (len > 1) {
            uint8_t second;
            if (dpi_get_uint8(pkt, offset + 1, &second) == -1)
                return offset;
            if (((first == 0x00) && ((second & 0x80) == 0)) 
                    || ((first == 0xff) && ((second & 0x80) != 0))) {
                used_too_many_bytes = 1;
            }
        }
        for (i = 0; i < len; i++) {
            if (offset > pkt->payload_len)
                return offset;
            val = ((uint64_t)val << 8) | get_uint8_t(pkt->payload, offset);
            offset++;
        }
    }


    if (value) {
        *value = val;
    }

    return offset;
}

int dpi_ber_integer(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *value)
{
    int64_t val;

    offset = dpi_ber_integer64(implicit_tag, pkt, offset, &val);
    if (value) {
        *value = (uint32_t)val;
    }

    return offset;
}

/* this function dissects a BER sequence */
int dpi_ber_sequence(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, const ber_sequence_t *seq, ber_ctx_t *ctx) 
{
    uint8_t                classx;
    uint8_t                pcx;
    uint8_t                ind = 0;
    uint8_t                ind_field;
    uint8_t                imp_tag = 0;
    uint32_t            tagx;
    int                    identifier_offset;
    int                    identifier_len;
    uint32_t            lenx;
    uint32_t            end_offset = 0;
    uint32_t            hoffset;
    struct dpi_pkt_st    pkt_seq;

    hoffset = offset;
    
    if (!implicit_tag) {
        offset = dpi_ber_identifier(pkt, offset, NULL, NULL, NULL);
        offset = dpi_ber_length(pkt, offset, &lenx, NULL);
    } else {
        lenx = pkt->payload_len - offset;
        end_offset = offset + lenx;
    }

    offset = hoffset;
    
    if (!implicit_tag) {
        offset = dpi_ber_identifier(pkt, offset, &classx, &pcx, &tagx);
        offset = dpi_ber_length(pkt, offset, &lenx, &ind);
        if (ind) {
            end_offset = offset + lenx -2;
        } else {
            end_offset = offset + lenx;
        }

        if (classx != BER_CLASS_APP && classx != BER_CLASS_PRI) {
            if (!pcx || classx != BER_CLASS_UNI || tagx != BER_UNI_TAG_SEQUENCE) {
                return end_offset;
            }
        }
    }
    
    if (offset == end_offset) {
        return end_offset;
    }

    while (offset < end_offset) {

        uint8_t        ber_class;
        uint8_t        pc;
        uint32_t    tag;
        uint32_t    len;
        uint32_t    eoffset, count;

        if ((get_uint8_t(pkt->payload, offset) == 0) && (get_uint8_t(pkt->payload, offset + 1) == 0)) {
            offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
            dpi_ber_length(pkt, offset, &len, &ind);
            return end_offset;
        }

        hoffset = offset;

        offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset = dpi_ber_length(pkt, offset, &len, &ind_field);

        eoffset = offset + len;
        
        if (eoffset > pkt->payload_len || eoffset <= hoffset)
            return pkt->payload_len;

ber_sequence_try_again:

        if (!seq->func) {
            offset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
            offset = dpi_ber_length(pkt, offset, NULL, NULL);
            offset = eoffset;
            continue;
        }
        
        if ((seq->ber_class == BER_CLASS_CON || seq->ber_class == BER_CLASS_APP || seq->ber_class == BER_CLASS_PRI)
                && (!(seq->flags & BER_FLAGS_NOOWNTAG))) {
            if ((seq->ber_class != BER_CLASS_ANY)
                    && (seq->tag != 0xffff)
                    && ((seq->ber_class != ber_class) || (seq->tag != tag))) {
                /* it was not,  move to the next one and try again */
                if (seq->flags & BER_FLAGS_OPTIONAL) {
                    /* well this one was optional so just skip to the next one and try again. */
                    seq++;
                    goto ber_sequence_try_again;
                }
                offset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
                offset = dpi_ber_length(pkt, offset, NULL, NULL);
                seq++;
                offset = eoffset;
                continue;
            }
        } else if (!(seq->flags & BER_FLAGS_NOTCHKTAG)) {
            if (seq->ber_class != BER_CLASS_ANY
                    && seq->tag != 0xffff
                    && (seq->ber_class != ber_class || seq->tag != tag)) {
                /* it was not,  move to the next one and try again */
                if (seq->flags & BER_FLAGS_OPTIONAL) {
                    /* well this one was optional so just skip to the next one and try again. */
                    seq++;
                    goto ber_sequence_try_again;
                }
                offset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
                offset = dpi_ber_length(pkt, offset, NULL, NULL);
                seq++;
                offset = eoffset;
                continue;
            }
        }

        if (!(seq->flags & BER_FLAGS_NOOWNTAG)) {
            if (ind_field && (len == 2)) {
                pkt_seq.payload = pkt->payload + offset;
                pkt_seq.payload_len = len;
                //next_tvb = ber_tvb_new_subset_length(tvb, offset, len);
                hoffset = eoffset;
            } else {
                hoffset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
                hoffset = dpi_ber_length(pkt, hoffset, NULL, NULL);
                pkt_seq.payload = pkt->payload + hoffset;
                pkt_seq.payload_len = eoffset - hoffset - (2 * ind_field);
                //next_tvb = ber_tvb_new_subset_length(tvb, hoffset, eoffset - hoffset - (2 * ind_field));
            }
        } else {
                pkt_seq.payload = pkt->payload + hoffset;
                pkt_seq.payload_len = eoffset - hoffset;
                //next_tvb = ber_tvb_new_subset_length(tvb, hoffset, eoffset - hoffset);
        }

        imp_tag = 0;
        if (seq->flags & BER_FLAGS_IMPLTAG) {
            imp_tag = 1;
        }
        if (offset + len > pkt->payload_len)
            return 0;

        count = seq->func(imp_tag, &pkt_seq, 0, ctx);

        if ((len != 0) && (count == 0) && (seq->flags & BER_FLAGS_OPTIONAL)) {
            seq++;
            goto ber_sequence_try_again;
            /* move the offset to the beginning of the next sequenced item */
        }
        offset = eoffset;
        if (!(seq->flags & BER_FLAGS_NOOWNTAG)) {
            // ...
        }
        seq++;
    }
    
    if (offset != end_offset) {
        // ...
    }
    if (ind) {
        end_offset += 2;
    }
    return end_offset;
}

int dpi_ber_choice(struct dpi_pkt_st *pkt, uint32_t offset, const ber_choice_t *choice, ber_ctx_t *ctx, int *branch_taken)
{
    uint8_t       ber_class;
    uint8_t    pc, ind, imp_tag = 0;
    uint32_t      tag;
    uint32_t     len;
    int         end_offset, start_offset, count;
    int         hoffset = offset;
    int        length;
    uint8_t    first_pass;
    const ber_choice_t *ch;

    start_offset = offset;
    
    if (branch_taken) {
        *branch_taken = -1;
    }

    if (offset >= pkt->payload_len) {
        return offset;
    }

    /* read header and len for choice field */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &len, &ind);
    end_offset = offset + len ;

    /* loop over all entries until we find the right choice or
    run out of entries */
    ch = choice;
    first_pass = 1;
    while (ch->func || first_pass) {
        if (branch_taken) {
            (*branch_taken)++;
        }
        /* we reset for a second pass when we will look for choices */
        if (!ch->func) {
            first_pass = 0;
            ch = choice; /* reset to the beginning */
            if (branch_taken) {
                *branch_taken = -1;
            }
            continue;
        }

        if ((first_pass && (((ch->ber_class == ber_class) && (ch->tag == tag))    || ((ch->ber_class == ber_class) && (ch->tag == 0xffff) && (ch->flags & BER_FLAGS_NOOWNTAG))))
                || (!first_pass && (((ch->ber_class == BER_CLASS_ANY) && (ch->tag == 0xffff)))) /* we failed on the first pass so now try any choices */) {
            if (!(ch->flags & BER_FLAGS_NOOWNTAG)) {
                /* dissect header and len for field */
                hoffset = dpi_ber_identifier(pkt, start_offset, NULL, NULL, NULL);
                hoffset = dpi_ber_length(pkt, hoffset, NULL, NULL);
                start_offset = hoffset;
                if (ind) {
                    length = len - 2;
                } else {
                    length = len;
                }
            } else {
                length = end_offset- hoffset;
            }


            imp_tag = 0;
            if ((ch->flags & BER_FLAGS_IMPLTAG))
                imp_tag = 1;
            count = ch->func(imp_tag, pkt, hoffset, ctx);

            if ((count == 0) && (((ch->ber_class == ber_class) && (ch->tag == 0xffff) && (ch->flags & BER_FLAGS_NOOWNTAG)) || !first_pass)) {
                /* wrong one, break and try again */
                ch++;
                continue;
            }
            return end_offset;
        }
        ch++;
    }
    if (branch_taken) {
        /* none of the branches were taken so set the param
            back to -1 */
        *branch_taken = -1;
    }

    return start_offset;
}

int dpi_ber_tagged_type(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, uint8_t tag_impl, ber_ctx_t *ctx, ber_type_fn type)
{
    uint8_t                tmp_cls;
    uint32_t            tmp_tag;
    uint32_t            tmp_len;
    uint32_t            tmp_pkt_len;
    struct dpi_pkt_st    next_pkt;

    next_pkt.payload = NULL;
    next_pkt.payload_len = 0;
    
    if (implicit_tag) {
        offset = type(tag_impl, pkt, offset, ctx);
        return offset;
    }

    offset = dpi_ber_identifier(pkt, offset, &tmp_cls, NULL, &tmp_tag);
    offset = dpi_ber_length(pkt, offset, &tmp_len, NULL);

    if (tag_impl) {
        if (tmp_len + offset > pkt->payload_len) {
            tmp_len = pkt->payload_len - offset;
        }

        //tmp_pkt_len = pkt->payload_len;
        //pkt->payload_len = tmp_len + offset;
        
        type(tag_impl, pkt, offset, ctx);
        //pkt->payload_len = tmp_pkt_len;
        offset += tmp_len;
    } else {
        offset = type(tag_impl, pkt, offset, ctx);
    }

    return offset;
}

int dpi_ber_octet_string_pkt(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, struct dpi_pkt_st *pkt_string) 
{
    uint8_t       ber_class;
    uint8_t      pc, ind;
    uint32_t      tag;
    uint32_t     len;
    uint32_t       encoding;
    int         hoffset;
    int         end_offset;
    uint32_t     len_remain;

    if (!implicit_tag) {
        hoffset = offset;
        /* read header and len for the octet string */
        offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset = dpi_ber_length(pkt, offset, &len, &ind);
        end_offset = offset + len;

        /* sanity check: we only handle Constructed Universal Sequences */
        if ((ber_class != BER_CLASS_APP) && (ber_class != BER_CLASS_PRI)) {
            if ((ber_class != BER_CLASS_UNI)
                    || ((tag < BER_UNI_TAG_NumericString) && (tag != BER_UNI_TAG_OCTETSTRING) && (tag != BER_UNI_TAG_UTF8String))) {
                return end_offset;
            }
        }
    } else {
        /* implicit tag so get from last tag/length */
        dpi_last_ber_identifier(&ber_class, &pc, &tag);
        dpi_last_ber_length(&len, &ind);

        end_offset = offset+len;

        /* caller may have created new buffer for indefinite length data Verify via length */
        len_remain = pkt->payload_len - offset;
        if (ind && (len_remain == (len - 2))) {
            /* new buffer received so adjust length and indefinite flag */
            len -= 2;
            end_offset -= 2;
            ind = 0;
        } else if (len_remain < len) {
            /*
            * error - short frame, or this item runs past the
            * end of the item containing it
            */
            //ber_add_large_length_error(actx->pinfo, tree, tvb, offset, len);
            return end_offset;
        }
    }

    if (pc) {
        /* constructed */
        //end_offset = reassemble_octet_string(actx, tree, hf_id, tvb, offset, len, ind, out_tvb, nest_level);
    } else {
        /* primitive */
        uint32_t length_remaining;
        length_remaining = pkt->payload_len - offset;
        
        if (len <= (uint32_t)length_remaining) {
            length_remaining = len;
        }

        //if (length_remaining >= (uint32_t)max_len)
        //    length_remaining = max_len - 1;
        
        //strncpy(val, (const char *)pkt->payload + offset, length_remaining);
        //val[length_remaining] = 0;
        pkt_string->payload = pkt->payload + offset;
        pkt_string->payload_len = length_remaining;
    }
    return end_offset;
}


static int dpi_ber_constrained_octet_string_impl(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len) 
{
    uint8_t            ber_class;
    uint8_t            pc, ind;
    uint32_t        tag;
    uint32_t        len;
    uint32_t        encoding;
    int                hoffset;
    int                end_offset;
    uint32_t        len_remain;

    if (!implicit_tag) {
        hoffset = offset;
        /* read header and len for the octet string */
        offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset = dpi_ber_length(pkt, offset, &len, &ind);
        end_offset = offset + len;

        if ((ber_class != BER_CLASS_APP) && (ber_class != BER_CLASS_PRI)) {
            if ((ber_class != BER_CLASS_UNI)
                    || ((tag < BER_UNI_TAG_NumericString) && (tag != BER_UNI_TAG_OCTETSTRING) && (tag != BER_UNI_TAG_UTF8String))) {
                return end_offset;
            }
        }
    } else {
        /* implicit tag so get from last tag/length */
        dpi_last_ber_identifier(&ber_class, &pc, &tag);
        dpi_last_ber_length(&len, &ind);

        end_offset = offset + len;

        /* caller may have created new buffer for indefinite length data Verify via length */
        len_remain = pkt->payload_len - offset;
        if (ind && (len_remain == (len - 2))) {
            /* new buffer received so adjust length and indefinite flag */
            len -= 2;
            end_offset -= 2;
            ind = 0;
        } else if (len_remain < len) {
            return end_offset;
        }
    }

    if (pc) {
        /* constructed */
        //end_offset = reassemble_octet_string(actx, tree, hf_id, tvb, offset, len, ind, out_tvb, nest_level);
    } else {
        uint32_t length_remaining;
        length_remaining = pkt->payload_len - offset;
        
        if (len <= (uint32_t)length_remaining) {
            length_remaining = len;
        }

        //if (length_remaining >= (uint32_t)max_len)
        //    length_remaining = max_len - 1;
        
        //strncpy(val, (const char *)pkt->payload + offset, length_remaining);
        //val[length_remaining] = 0;

        switch (tag) {

        case BER_UNI_TAG_UTF8String:
            encoding = ENC_UTF_8|ENC_NA;
            break;

        case BER_UNI_TAG_NumericString:
        case BER_UNI_TAG_PrintableString:
        case BER_UNI_TAG_VisibleString:
        case BER_UNI_TAG_IA5String:
            encoding = ENC_ASCII|ENC_NA;
            break;

        case BER_UNI_TAG_TeletexString:
            encoding = ENC_T61|ENC_NA;
            break;

        case BER_UNI_TAG_VideotexString:
            encoding = ENC_T61|ENC_NA;
            break;

        case BER_UNI_TAG_GraphicString:
        case BER_UNI_TAG_GeneralString:
            encoding = ENC_ASCII|ENC_NA;
            break;

        case BER_UNI_TAG_UniversalString:
            encoding = ENC_UCS_4|ENC_BIG_ENDIAN;
            break;

        case BER_UNI_TAG_CHARACTERSTRING:
            encoding = ENC_ASCII|ENC_NA;
            break;

        case BER_UNI_TAG_BMPString:
            encoding = ENC_UCS_2|ENC_BIG_ENDIAN;
            break;

        default:
             encoding = ENC_BIG_ENDIAN;
             break;
        }

        dpi_get_string_enc(pkt->payload + offset, length_remaining, encoding, val, max_len);

    }
    return end_offset;
}

/* 8.7 Encoding of an octetstring value */
int dpi_ber_constrained_octet_string(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len) {
    return dpi_ber_constrained_octet_string_impl(implicit_tag, pkt, offset, val, max_len);
}

int dpi_ber_octet_string(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len) {
    return dpi_ber_constrained_octet_string(implicit_tag, pkt, offset, val, max_len);
}


/* 8.8 Encoding of a null value */
int dpi_ber_null(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset) {
    uint8_t       ber_class;
    uint8_t      pc;
    uint32_t     tag;
    uint32_t     len;
    uint32_t     offset_old;

    if (!implicit_tag) {
    offset_old = offset;
        offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset_old = offset;
        offset = dpi_ber_length(pkt, offset, &len, NULL);
        if (len) {
            offset += len;
        }
    }

    return offset;
}

int dpi_ber_boolean(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *value)
{
    uint8_t    ber_class;
    uint8_t    pc;
    uint32_t   tag;
    uint32_t   len;
    uint8_t    val;

    if (!implicit_tag) {
        offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset = dpi_ber_length(pkt, offset, &len, NULL);
        /*if (ber_class != BER_CLASS_UNI)*/
    } else {
        /* nothing to do here, yet */
    }

    if (dpi_get_uint8(pkt, offset, &val) == -1)
        return offset;
    offset += 1;

    if (value) {
        *value = (val ? 1 : 0);
    }

    return offset;
}

static int dpi_ber_sq_of(uint8_t implicit_tag, uint32_t type, struct dpi_pkt_st *pkt, uint32_t offset, const ber_sequence_t *seq, ber_ctx_t *ctx) 
{
    uint8_t           classx;
    uint8_t           pcx, ind = 0, ind_field;
    uint32_t          tagx;
    uint32_t          lenx;
    uint32_t          cnt, hoffsetx, end_offset;
    uint8_t           have_cnt;

    if (!implicit_tag) {
        hoffsetx = offset;
        offset = dpi_ber_identifier(pkt, offset, &classx, &pcx, &tagx);
        offset = dpi_ber_length(pkt, offset, &lenx, &ind);
        end_offset = offset + lenx;

        if ((classx != BER_CLASS_APP) && (classx != BER_CLASS_PRI)) {
            if (!pcx || (!implicit_tag && ((classx != BER_CLASS_UNI) || (tagx != type)))) {
                return end_offset;
            }
        }
    } else {
        lenx = pkt->payload_len - offset;
        end_offset = offset + lenx;
    }

    cnt = 0;
    
    /* loop over all entries until we reach the end of the sequence */
    while (offset < end_offset) {

        uint8_t                ber_class;
        uint8_t                pc;
        uint32_t            tag;
        uint32_t            len;
        int                    eoffset;
        int                    hoffset;
        uint8_t                imp_tag;
        struct dpi_pkt_st    next_pkt;

        hoffset = offset;

        if (offset + 2 < pkt->payload_len && (get_uint8_t(pkt->payload, offset) == 0) && (get_uint8_t(pkt->payload, offset + 1) == 0)) {
            return offset + 2;
        }

        offset  = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
        offset  = dpi_ber_length(pkt, offset, &len, &ind_field);
        eoffset = offset + len;

        /* Make sure we move forward */
        if (eoffset <= hoffset)
            return offset;

        if ((ber_class == BER_CLASS_UNI) && (tag == BER_UNI_TAG_EOC)) {
            /* This is a zero length sequence of*/
            hoffset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
            dpi_ber_length(pkt, hoffset, NULL, NULL);
            return eoffset;
        }
    
        if (seq->ber_class != BER_CLASS_ANY) {
            if ((seq->ber_class != ber_class) || (seq->tag != tag) ) {
                if (!(seq->flags & BER_FLAGS_NOTCHKTAG)) {
                offset = eoffset;
                continue;
                }
            }
        }

        if (!(seq->flags & BER_FLAGS_NOOWNTAG) && !(seq->flags & BER_FLAGS_IMPLTAG)) {
            /* dissect header and len for field */
            hoffset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
            hoffset = dpi_ber_length(pkt, hoffset, NULL, NULL);
        }
        if ((seq->flags == BER_FLAGS_IMPLTAG) && (seq->ber_class == BER_CLASS_CON)) {
            /* Constructed sequence of with a tag */
            /* dissect header and len for field */
            hoffset = dpi_ber_identifier(pkt, hoffset, NULL, NULL, NULL);
            hoffset = dpi_ber_length(pkt, hoffset, NULL, NULL);
            /* Function has IMPLICIT TAG */
        }
        
        next_pkt.payload = pkt->payload + hoffset;
        next_pkt.payload_len = eoffset - hoffset;

        imp_tag = 0;
        if (seq->flags == BER_FLAGS_IMPLTAG)
            imp_tag = 1;
        /* call the dissector for this field */
        seq->func(imp_tag, &next_pkt, 0, ctx);
        cnt++; 
        offset = eoffset;
    }

    return end_offset;
}

int dpi_ber_constrained_sequence_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx) {
    return dpi_ber_sq_of(implicit_tag, BER_UNI_TAG_SEQUENCE, pkt, offset, seq, ctx);
}

int dpi_ber_sequence_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx) {
    return dpi_ber_sq_of(implicit_tag, BER_UNI_TAG_SEQUENCE, pkt, offset, seq, ctx);
}

int dpi_ber_constrained_set_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx) {
    return dpi_ber_sq_of(implicit_tag, BER_UNI_TAG_SET, pkt, offset, seq, ctx);
}

int dpi_ber_set_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx) {
    return dpi_ber_sq_of(implicit_tag, BER_UNI_TAG_SET, pkt, offset, seq, ctx);
}

int dpi_ber_object_identifier_str(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len) {
	
	uint8_t ber_class;
	uint8_t pc;
	uint32_t tag;
	int identifier_offset;
	int identifier_len;
	uint32_t len;
	int eoffset;
	int hoffset;
	const char *str, *name;

	if (!implicit_tag) {
		/* sanity check */
		identifier_offset = (uint32_t)offset;
		offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
		identifier_len = (int)offset - identifier_offset;
		offset = dpi_ber_length(pkt, offset, &len, NULL);
		eoffset = (int)(offset + len);
	}
	else {
		len = pkt->payload_len - offset;
		eoffset = (int)(offset + len);
	}

	len = (max_len > (int)len ? len : (uint32_t)max_len);

	if (val)
		strncpy(val, (const char*)pkt->payload + offset, len);

	return eoffset;
}

int dpi_ber_byte_string(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len, int *val_len) {

	uint8_t		ber_class;
	uint8_t		pc, ind;
	uint32_t	tag;
	uint32_t	len;
	int			end_offset;

	/* read header and len for the octet string */
	offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
	offset = dpi_ber_length(pkt, offset, &len, &ind);
	end_offset = offset + len;

	int min = ((int)len > (max_len - 1) ? (max_len - 1) : (int)len);

	if (val && min > 0) {
		*val_len = min;
		memcpy(val, pkt->payload + offset, min);
	}
	else
		*val_len = 0;

	return end_offset;
}

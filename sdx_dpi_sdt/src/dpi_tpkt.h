/****************************************************************************************
 * 文 件 名 : dpi_tpkt.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/11/22
编码: wangy                2018/11/22
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_TPKT_H_
#define _DPI_TPKT_H_

// 目前tptk只用于解析RDP协议

#include <stdint.h>
#include "dpi_detect.h"

#define RDP_CR 10001
#define RDP_CC 10002
#define RDP_DATA 10003

typedef struct _rdp_info {
    char type[64];
    uint16_t length;
    uint16_t versionmajor;
    uint16_t versionminor;
    uint16_t desktopwidth;
    uint16_t desktopheight;
    uint16_t colordepth;
    uint16_t sassequence;
    uint32_t keyboardlayout;
    uint32_t clientbuild;
    char clientname[64];
    uint32_t keyboardtype;
    uint32_t keyboardsubtype;
    uint32_t keyboardfunctionkey;
    char imefilename[1024];
    char encryptionmethods[64];
    char extencryptionmethods[64];
    uint32_t cluster_flags;
    uint32_t redirectedsessionid;
    uint32_t msgchannelflags;
    uint32_t monitorcount;
    uint32_t monitorexflags;
    uint32_t monitorattributesize;
    uint32_t multitransportflags;
    uint32_t encryptionlevel;
    uint32_t serverrandomlen;
    uint32_t servercertlen;
    char servercertificate[64];
    uint16_t mcschannelid;
    uint16_t channelcount;
    uint16_t pad;
    uint16_t msgchannelid;
}rdp_info;

// Check whether this could be a TPKT - encapsulated PDU
int is_tpkt(const uint8_t *payload, const uint32_t payload_len, int min_len);

int dissect_tpkt(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag, rdp_info* info, int only_check);

#endif // !_DPI_TPKT_H_
#include "dpi_327_common.h"
#include "dpi_typedefs.h"
#include "dpi_tbl_log.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"

#include "sdt_ip_protocols.h"
extern struct global_config g_config;
static dpi_field_table common_p327_field_array[] = {
    DPI_FIELD_D(ENUM_P327_RULESYSID,          EM_F_TYPE_UINT32,               "P327_ruleSysID"),
    DPI_FIELD_D(ENUM_P327_PORTGROUPID,        EM_F_TYPE_UINT8,                "P327_portGroupID"),
    DPI_FIELD_D(ENUM_P327_LINKDIRECTION,      EM_F_TYPE_UINT8,                "P327_linkDirection"),
    DPI_FIELD_D(ENUM_P327_OPRATOR,            EM_F_TYPE_UINT8,                "P327_oprator"),
    DPI_FIELD_D(ENUM_P327_DATATYPE,           EM_F_TYPE_UINT8,                "P327_dataType"),
    DPI_FIELD_D(ENUM_P327_HASH,               EM_F_TYPE_UINT16,               "P327_hash"),
    DPI_FIELD_D(ENUM_P327_216RULEID,          EM_F_TYPE_UINT8,                "P327_216ruleID"),
    DPI_FIELD_D(ENUM_P327_RULEID,             EM_F_TYPE_UINT16,               "P327_ruleID"),
    DPI_FIELD_D(ENUM_P327_FROMPORT,           EM_F_TYPE_UINT8,                "P327_fromPort"),
    DPI_FIELD_D(ENUM_P327_FROMDEV,            EM_F_TYPE_UINT16,               "P327_fromDev"),
};

int dissect_mac_to_p327_header(struct p327_header *p327_header, const uint8_t *payload, int payload_len)
{
    if (!p327_header || !payload || (unsigned)payload_len < sizeof(struct dpi_ethhdr))
        return -1;

    struct dpi_ethhdr  *ethhdr = (struct dpi_ethhdr  *)payload;
    char __str[4];
    memset(__str, 0, sizeof(__str));
    memcpy(__str, (char*)ethhdr->h_dest,3);
    p327_header->P327_ruleSysID = get_uint32_ntohl((const uint8_t*)__str, 0) >> 8;
    p327_header->P327_portGroupID = get_uint8_t((const uint8_t*)ethhdr->h_dest,3);
    p327_header->P327_linkDirection = (ethhdr->h_dest[4] & 0x80) >> 7;
    p327_header->P327_oprator = (ethhdr->h_dest[4] & 0x60) >> 5;
    p327_header->P327_dataType = (ethhdr->h_dest[4] & 0x1C) >> 2;
    p327_header->P327_hash = get_uint16_ntohs((const uint8_t*)ethhdr->h_dest, 4) & 0x3ff;
    p327_header->P327_216ruleID = ethhdr->h_source[0] & 0x80;
    p327_header->P327_ruleID = get_uint16_ntohs((const uint8_t*)ethhdr->h_source,1);
    p327_header->P327_fromPort = get_uint16_ntohs((const uint8_t*)ethhdr->h_source,3);
    p327_header->P327_fromDev = ethhdr->h_source[5];
    return 0;
}

int write_tbl_log_p327_header(precord_t* log_content, int *idx, int log_len_max, struct p327_header *p327_header)
{
    if (!log_content || !idx || !p327_header)
        return -1;

    char __str[sizeof(struct p327_header)] = {0};
    if(memcmp(p327_header, __str, sizeof(struct p327_header)) == 0) {//不存在以太层
        write_n_empty_reconds(log_content, idx, log_len_max, ENUM_P327_MAX);
        return 0;
    }
    for(int i=0; i<ENUM_P327_MAX; i++){
        switch (i) {
        case ENUM_P327_RULESYSID:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_ruleSysID);
            break;
        case ENUM_P327_PORTGROUPID:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_portGroupID);
            break;
        case ENUM_P327_LINKDIRECTION:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_linkDirection);
            break;
        case ENUM_P327_OPRATOR:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_oprator);
            break;
        case ENUM_P327_DATATYPE:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_dataType);
            break;
        case ENUM_P327_HASH:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_hash);
            break;
        case ENUM_P327_216RULEID:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_216ruleID);
            break;
        case ENUM_P327_RULEID:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_ruleID);
            break;
        case ENUM_P327_FROMPORT:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_fromPort);
            break;
        case ENUM_P327_FROMDEV:
            write_one_num_reconds(log_content, idx, log_len_max, (uint32_t)p327_header->P327_fromDev);
            break;
        default:
            write_n_empty_reconds(log_content, idx, log_len_max, 1);
            break;
        }
    }
    return 0;
}

void init_327_common_map_fields(void)
{
    if (g_config._327_common_switch) {
        map_fields_info_register(common_p327_field_array, PROTOCOL_P327_COMMON, ENUM_P327_MAX,"327_common");
        dpi_register_proto_schema(common_p327_field_array, ENUM_P327_MAX, "327_common");
    }
    return;
}

static __attribute((constructor))
void before_init_link(void){
    register_tbl_array(TBL_LOG_P327_COMMON, 0, "327_common", init_327_common_map_fields);
}

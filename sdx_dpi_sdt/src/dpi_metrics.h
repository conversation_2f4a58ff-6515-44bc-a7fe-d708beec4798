#ifndef _DPI_METRICS_H
#define _DPI_METRICS_H

#include <rte_atomic.h>

#define DPI_METRICS_ELEM_MAX    16

typedef struct _dpi_metrics_output_h
{
  rte_atomic64_t total;       // 自程序启动起，所有入队数量
  uint64_t       last_total;  // 周期入队计数
  uint32_t       qps;         // 吞吐量

  rte_atomic64_t enqueue_total;       // 入队总数
  rte_atomic64_t enqueue_failed;      // 入队失败数量
  uint64_t       last_enqueue_total;  // 时间节点总数，周期更新
  uint32_t       enqueue_qps;         // 入队吞吐量
}DpiMetricsOutput;

typedef struct _dpi_metrics_flow_h
{
  uint64_t       last_total;  // 每次 tick 更新
  uint64_t       last_del;    // 每次 tick 更新
  uint64_t       qps;         // 吞吐量, 每次 tick 更新
  uint64_t       del_qps;     // 删除吞吐量, 每次 tick 更新
  uint64_t       total;       // 自启动流总数
  uint64_t       curr;        // 当前流总数
  uint64_t       del;         // 删除总数量
}DpiMetricsFlow;

typedef struct _dpi_metrics_mbuf
{
  uint64_t  last_total;
  uint64_t  total;
  uint64_t  qps;
}DpiMetricsMbuf;

typedef struct _dpi_metrics_record
{
  rte_atomic64_t  total;
  rte_atomic64_t  del_total;
}DpiMetricsRecord;

typedef struct _dpi_metrics_h
{
  int tick;                                     // 采集频率，默认 5s
  DpiMetricsFlow flow[DPI_METRICS_ELEM_MAX];
  DpiMetricsMbuf mbuf[DPI_METRICS_ELEM_MAX];
  DpiMetricsRecord record;
}DpiMetrics;

DpiMetricsOutput g_metrics_output;
DpiMetricsFlow   g_metrics_flow;

void dpi_metrics_init();
void dpi_metrics_output_statics(int period);
void dpi_metrics_output_statics_print();

void dpi_metrics_flow();
void dpi_metrics_mbuf();

#endif
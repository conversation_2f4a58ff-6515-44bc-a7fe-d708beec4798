#include "dpi_high_app_protos.h"

#include <stddef.h>
#include <assert.h>


static struct HighProtoInfo_ high_proto_infos[HIGH_PROTO_MAX] = {
    {HIGH_PROTO_UNKNOWN,    "Unknown",      "unknown"},
    {HIGH_PROTO_WEIXIN,     "Weixin",       "Tencent Chat App"},
    {HIGH_PROTO_SKYPE,      "Skype",        ""}
};


const HighProtoInfo* dpi_high_proto_find_by_id(int id)
{
    assert(id < HIGH_PROTO_MAX);

    return &high_proto_infos[id];
}

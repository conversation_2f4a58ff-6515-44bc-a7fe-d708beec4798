#ifndef DPI_HTTP_H
#define DPI_HTTP_H

#include <netinet/in.h>
#include <glib.h>
#include <string.h>
#include <ctype.h>
#include <sys/time.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"



#define HTTP_CONTENT_LEN_MAX        655350
#define HTTP_UNKNOWN_LINE_NUM_MAX   50
#define HTTP_MAX_URI_LEN            1024

#define CHUNK_SIZE_MAX              32

#define HTTP_NEED_MORE  0
#define HTTP_ERROR     -1


#define CPU_LEN     64
#define OS_LEN      64
#define TYPE_LEN    20
#define LOGIN_LEN   128
#define APP_LEN     128

#define URI_KEY_MAX        (50)
#define BODY_MAX        (8192)


enum _http_index_em{

    EM_HTTP_METHOD,
    EM_HTTP_URI,
    EM_HTTP_URI_COUNT,
    EM_HTTP_URI_KEYS,
    EM_HTTP_URI_KEYS_COUNT,
    EM_HTTP_URI_PATH,
    EM_HTTP_URI_PATH_COUNT,
    EM_HTTP_VERSION,
    EM_HTTP_STATUS,
    EM_HTTP_RESPONSESTATUS,
    EM_HTTP_CACHE_CONTROL,
    EM_HTTP_CONNECTION,
    EM_HTTP_COOKIE,
    EM_HTTP_COOKIE2,
    EM_HTTP_COOKIE_KEYS,
    EM_HTTP_DATE,
    EM_HTTP_PRAGMA,
    EM_HTTP_TRAILER,
    EM_HTTP_TRANSFER_ENCODING,
    EM_HTTP_UPGRADE,
    EM_HTTP_VIA,
    EM_HTTP_VIA_COUNT,
    EM_HTTP_WARNING,
    EM_HTTP_ACCEPT,
    EM_HTTP_ACCEPT_CHARSET,
    EM_HTTP_ACCEPT_ENCODING,
    EM_HTTP_ACCEPT_LANGUAGE,
    EM_HTTP_AUTHORIZATION,
    EM_HTTP_AUTH_USERNAME,
    EM_HTTP_EXPECT,
    EM_HTTP_FROM,
    EM_HTTP_HOST,
    EM_HTTP_HOST_COUNT,
    EM_HTTP_IF_MATCH,
    EM_HTTP_IF_MODIFIED_SINCE,
    EM_HTTP_IF_NONE_MATCH,
    EM_HTTP_IF_RANGE,
    EM_HTTP_IF_UNMODIFIED_SINCE,
    EM_HTTP_MAX_FORWARDS,
    EM_HTTP_PROXY_AUTHORIZATION,
    EM_HTTP_PROXY_TYPE,
    EM_HTTP_PROXY_LOGIN,
    EM_HTTP_PROXY_AUTHORIZATION_INFO,
    EM_HTTP_RANGE,
    EM_HTTP_REFERER,
    EM_HTTP_TE,
    EM_HTTP_USER_AGENT,
    EM_HTTP_USER_AGENT_NUM,
    EM_HTTP_PROXY_PROXYAGENT,
    EM_HTTP_USER_CPU,
    EM_HTTP_USER_OS,
    EM_HTTP_ACCEPT_RANGES,
    EM_HTTP_AGE,
    EM_HTTP_ETAG,
    EM_HTTP_LOCATION,
    EM_HTTP_PROXY_AUTHENTICATE,
    EM_HTTP_INQUIRY_TYPE,
    EM_HTTP_PROXY_CONNECT_HOST,
    EM_HTTP_PROXY_CONNECT_PORT,
    EM_HTTP_RETRY_AFTER,
    EM_HTTP_SERVER,
    EM_HTTP_VARY,
    EM_HTTP_WWW_AUTHENTICATE,
    EM_HTTP_ALLOW,
    EM_HTTP_CONTENT_ENCODING_C,
    EM_HTTP_CONTENT_ENCODING_S,
    EM_HTTP_CONTENT_LANGUAGE,
    EM_HTTP_CONTENT_LENGTH,
    EM_HTTP_CONTENT_LENGTH_REQ,
    EM_HTTP_CONTENT_LENGTH_RSP,
    EM_HTTP_CONTENT_LOCATION,
    EM_HTTP_CONTENT_MD5,
    EM_HTTP_CONTENT_RANGE,
    EM_HTTP_CONTENT_TYPE,
    EM_HTTP_EXPIRES,
    EM_HTTP_REFRESH,
    EM_HTTP_LAST_MODIFIED,
    EM_HTTP_X_FORWARDED_FOR,
    EM_HTTP_SET_COOKIE,
    EM_HTTP_SET_COOKIE2,
    EM_HTTP_DNT,
    EM_HTTP_X_POWERED_BY,
    EM_HTTP_P3P,
    EM_HTTP_CLIENT_HEAD,
    EM_HTTP_CLIENT_BODY,
    EM_HTTP_GET_SVR_REQ_URL,    //客户端获取服务器请求的 URL
    EM_HTTP_ENTITY_TITLE_HEAD,    //实体标题首部
    EM_HTTP_H_A_NUMBER,
    EM_HTTP_H_X_NUMBER,
    EM_HTTP_APPTYPE,

    EM_HTTP_X_HTTP_TOKEN,
    EM_HTTP_X_COOKIE_TOKEN,
    EM_HTTP_X_SINKHOLE,
    EM_HTTP_REQ_HEAD_FIELDS,
    EM_HTTP_REQ_HEAD_FIELDS_MD5,
    EM_HTTP_REQ_HEAD_FIELDS_NUM,
    EM_HTTP_REQ_METHOD_NUM,
    EM_HTTP_REQ_VERSION_NUM,
    EM_HTTP_REQ_BODY,
    EM_HTTP_RSP_HEAD_FIELDS,
    EM_HTTP_RSP_HEAD_FIELDS_MD5,
    EM_HTTP_RSP_CACHE_CONTROL,
    EM_HTTP_RSP_CONNECTION,
    EM_HTTP_RSP_PRAGMA,
    EM_HTTP_RSP_ACCEPT_RANGES,
    EM_HTTP_RSP_ACCEPT_CHARSET,
    EM_HTTP_RSP_CONTENT_TYPE,
    EM_HTTP_RSP_VERSION,
    EM_HTTP_RSP_BODY,
    EM_HTTP_RSP_FULLTEXT_LEN,
    EM_HTTP_URL,
    EM_HTTP_CONDISPUP,
    EM_HTTP_CONDISPDOWN,
    EM_HTTP_URISEARCH,
    EM_HTTP_IMSI,
    EM_HTTP_IMEI,
#ifdef DPI_SDT_ZDY
    EM_HTTP_V51,
    EM_HTTP_LOCAL_FILENAME,
    EM_HTTP_CONDISP,
    EM_HTTP_REMAINDERLINE,
#endif
    EM_HTTP_MAX
};



struct http_unknown_line {
    uint32_t key_len;
    uint32_t val_len;
    const uint8_t *key_ptr;
    const uint8_t *val_ptr;
};


struct http_info {
    GHashTable *table;
    uint16_t status;
    uint32_t   header_num;
    uint32_t   empty_line_position;
    PROTOCOL_HEAD_DEF(head_line)
    PROTOCOL_HEAD_DEF(version)
    PROTOCOL_HEAD_DEF(code)
    PROTOCOL_HEAD_DEF(response_code)
    PROTOCOL_HEAD_DEF(uri)
    PROTOCOL_HEAD_DEF(method)
    PROTOCOL_HEAD_DEF(content)
    PROTOCOL_HEAD_DEF(rsp_version)  // 响应版本
    PROTOCOL_HEAD_DEF(username)  // 认证的用户名

    char       uaCPU[CPU_LEN];
    char       uaOS[OS_LEN];
    char       proxy_login[LOGIN_LEN];
    char       proxy_type[TYPE_LEN];
    char       inquiry_type[TYPE_LEN];
    char       filename[COMMON_FILE_PATH];
    char       app_type[APP_LEN];
    uint8_t    uri_num;  // uri的数量
    uint8_t       host_num; // host的数量 包含多个host的请求
    uint8_t    via_num;  // via的个数， 以逗号间隔
    char       req_heads[2048]; // 多个请求头字段的key组合，分号隔开
    char       req_heads_md5[100]; // 多个请求头字段的md5
    int           req_heads_num;  // 请求头字段的数量
    char       rsp_heads[2048]; // 同req
    char       rsp_heads_md5[100]; // 同 req
    int           rsp_heads_num;  // 响应头字段的数量
    uint8_t       is_request; // 请求，响应标识，用来区分共有的字段，比如 Cache-Control, Connection, Pragma等。

    uint8_t       req_method_num;  // 请求方法的数量
    uint8_t    req_version_num; // 请求版本的数量

    uint16_t   rsp_fulltext_len;  // 图片，视频等文本的大小
    char       url[256];

    char       cookie_keys[2048]; // cookie的key集合
    int           cookie_keys_num;

    uint8_t    action;
    char       req_body[2048];
    uint32_t   req_len;

    char       rsp_body[2048];
    uint32_t   rsp_len;

    int        user_agent_num;

    uint8_t    chunk_flag;

    char       uriSearch[512];
    int        is_search;        //-1:非搜索引擎host  0:未知需要探测   1:是  2:已经有过一次搜索动作
    char       conDisp[512];
    char       imei[32];
    char       imsi[32];

    char       boundary[256];
    int        boundary_key_len;

};

struct http_cache
{
    char     *addr;
    int       cache_size;
    int       cache_hold;
};


struct http_file{
    FILE    *fp;
    char    file_name[COMMON_FILE_PATH];
};

struct http_session
{
    int    direction[2];
    char   host[256];
    char   auth[1024];
    char   *uri;
    char    uri_path[256];        // uri path
    int        uri_path_num;        // uri path 的数量
    char    uri_keys[URI_KEY_MAX][1024];    // uri key 的组合， 分号隔开
    int        uri_keys_num;        // uri key 的数量
    struct http_cache  cache[2];
    struct http_info   http_value[2];
};





#endif


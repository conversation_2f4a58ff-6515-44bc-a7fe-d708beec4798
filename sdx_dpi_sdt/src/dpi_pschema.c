/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-08-01 06:15:52
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-08-14 03:07:00
 * @FilePath: /01_sdx_dpi_sdt/src/dpi_pschema.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <stdio.h>
#include "dpi_pschema.h"
#include "dpi_common.h"
#include "dpi_tbl_log.h"
#include "dpi_lua_adapt.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#define PSCHEMA_COMMON "pschema_common"

extern struct global_config g_config;
static pschema_db_t* fs_pschema_db;
extern __thread ya_allocator_t *th_alloc;

static ya_ftenum_t dpi_ftype_to_ya_ftype(int dpi_ftype)
{
    return YA_FT_STRING;
}

int dpi_pschema_register_common_proto_field(const char *proto_name, dpi_field_table *field_table, int field_count)
{
    pschema_t* schema = pschema_register_proto_template(fs_pschema_db,
                                               proto_name,
                                               "no protocol desc.");

    for (int i = 0; i < field_count; i++)
    {
        pfield_desc_t *p = pschema_register_field(schema, field_table[i].field_name, field_table[i].type, "");
        field_table[i].field_id = pfdesc_get_index(p);
        field_table[i].proto_id = pschema_get_index(schema);
    }
    return 0;
}

int dpi_pschema_register_proto_field(const char *proto_name, dpi_field_table *field_table, int field_count, char *common_field_schema)
{
    if (pschema_get_proto(fs_pschema_db, proto_name))
    {
        printf("重复注册 proto schema: <%s>\n", proto_name);
        return 0;
    }

    pschema_t* schema = pschema_register_proto(fs_pschema_db,
                                               common_field_schema,
                                               proto_name,
                                               "no protocol desc.");

    for (int i = 0; i < field_count; i++)
    {
        pschema_register_field(schema, field_table[i].field_name, field_table[i].type, "");
    }

    return 0;
}

int dpi_pschema_register_proto(const char *proto_name,
                               const char *proto_full_name,
                               char *common_field_schema) {
    pschema_register_proto(fs_pschema_db, common_field_schema, proto_name,
                           proto_full_name);

    return 0;
}


pschema_t * dpi_pschema_get_proto(const char * proto_name)
{
    if (proto_name == NULL) {
        return NULL;
    }

    return pschema_get_proto(fs_pschema_db, proto_name);
}

pschema_t * dpi_pschema_get_first()
{
    return pschema_get_first(fs_pschema_db);
}

pschema_t * dpi_pschema_get_next(pschema_t *schema)
{
    if (schema == NULL) {
        return NULL;
    }
    return pschema_get_next(fs_pschema_db, schema);
}

static int dpi_pschema_dump_proto_schema_of(pschema_t *schema, const char *pschema_output_dir)
{
    const char *proto_name = pschema_get_proto_name(schema);
    char pschema_file_path[PATH_MAX] = { 0 };

    snprintf(pschema_file_path, sizeof pschema_file_path, "%s/%s_f.txt", pschema_output_dir, proto_name);
    FILE *pschema_file = fopen(pschema_file_path, "w");
    if (NULL == pschema_file)
    {
        return -1;
    }
    pschema_t  *common_schema = pschema_get_proto(fs_pschema_db, "common");
    for (pfield_desc_t *fdesc = pschema_fdesc_get_first(common_schema); fdesc != NULL;
         fdesc                = pschema_fdesc_get_next(common_schema, fdesc)) {
        const char *field_name = pfdesc_get_name(fdesc);

        fprintf(pschema_file, "%s\n", field_name);
    }
    if (g_config._327_common_switch) {
        pschema_t *p327_common_schema = pschema_get_proto(fs_pschema_db, "327_common");
        for (pfield_desc_t *fdesc = pschema_fdesc_get_first(p327_common_schema); fdesc != NULL;
             fdesc                = pschema_fdesc_get_next(p327_common_schema, fdesc)) {
            const char *field_name = pfdesc_get_name(fdesc);

            fprintf(pschema_file, "%s\n", field_name);
        }
    }
#ifndef DPI_SDT_ZDY
    pschema_t *link_schema = pschema_get_proto(fs_pschema_db, "link");
    for (pfield_desc_t *fdesc = pschema_fdesc_get_first(link_schema); fdesc != NULL;
         fdesc                = pschema_fdesc_get_next(link_schema, fdesc)) {
        const char *field_name = pfdesc_get_name(fdesc);

        fprintf(pschema_file, "%s\n", field_name);
    }
#endif

    for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema);
         fdesc != NULL;
         fdesc = pschema_fdesc_get_next(schema, fdesc))
    {
        const char *field_name = pfdesc_get_name(fdesc);

        fprintf(pschema_file, "%s\n", field_name);
    }

    fclose(pschema_file);
    return 0;
}

int dpi_pschema_dump_proto_schemas(const char *pschema_output_dir)
{
    if (!is_file_exist(pschema_output_dir))
    {
        mkdirs(pschema_output_dir);
    }

    for (pschema_t *schema = pschema_get_first(fs_pschema_db);
         schema != NULL;
         schema = pschema_get_next(fs_pschema_db, schema))
    {
        dpi_pschema_dump_proto_schema_of(schema, pschema_output_dir);
    }

    return 0;
}

int dpi_pschema_module_init()
{
    ya_ftypes_initialize();
    fs_pschema_db = pschema_db_create();

    // SDT 的 SHARE_HEADR 和 LINK 都是 公共部分, 且都会用于协议号,字段号的匹配
    // 模板再这种场景里, 会受到限制,

    dpi_padapt_init(fs_pschema_db);
    dpi_padapt_tll_init(fs_pschema_db);
    return 0;
}

precord_t* dpi_pschema_new_record(const char *proto_name)
{
    return precord_fast_create(th_alloc, fs_pschema_db, PRECORD_FLAG_ARENA_ALLOC);
}

precord_t * dpi_precord_create(const char * proto_name)
{
     return precord_fast_create(th_alloc, fs_pschema_db, PRECORD_FLAG_ARENA_ALLOC);
}

void dpi_precord_destroy(precord_t ** record)
{
    if (record && *record) {
        precord_destroy(*record);
        *record = NULL;
    }
}

/* 声明 libyaProtoRecord 中的隐藏接口 */
pfield_t* precord_fvalue_put_by_index(precord_t *record, int fieldIndex, ya_fvalue_t *field_value);

pfield_t* dpi_precord_fvalue_put_by_index(precord_t *record, int fieldIndex, ya_fvalue_t *field_value)
{
    return precord_fvalue_put_by_index(record, fieldIndex, field_value);
}

void dpi_precord_print_layers(precord_t *record, const char * prefix)
{
    log_debug("----%s, record %p:", prefix, record);
    for (player_t * layer = precord_layer_get_first(record); layer; layer = precord_layer_get_next(record, layer)) {
        pschema_t *schema = precord_layer_get_schema(layer);
        log_debug("layer: %s", pschema_get_proto_name(schema));
    }
}

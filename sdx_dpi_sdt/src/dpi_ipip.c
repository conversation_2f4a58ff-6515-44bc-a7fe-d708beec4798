/****************************************************************************************
 * 文 件 名 : dpi_ipip.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 设计:      chenzq    2021/11/22
 编码:      chenzq    2021/11/22
 修改:
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2018 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

 *****************************************************************************************/


#include <netinet/in.h>
#include <stdio.h>
#include <glib.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_dissector.h"

#define SET_IPV4_FLOW_TUPLE(tuple, protocol, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                            \
                tuple.proto = protocol;                                                  \
                tuple.ip_version = 4;                                                    \
                memcpy(tuple.ip_src, srcip, 4);                                          \
                memcpy(tuple.ip_dst, dstip, 4);                                          \
                tuple.port_src = srcport;                                                \
                tuple.port_dst = dstport;                                                \
                tuple.sctp_id = _sctp_id;                                                \
            }

#define SET_IP_FLOW_TUPLE(tuple, protocol, version, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                                   \
                tuple.proto = protocol;                                                         \
                tuple.ip_version = version;                                                     \
                memcpy(tuple.ip_src, srcip, (version - 4) * 7 + 4);                             \
                memcpy(tuple.ip_dst, dstip, (version - 4) * 7 + 4);                             \
                tuple.port_src = srcport;                                                       \
                tuple.port_dst = dstport;                                                       \
                tuple.sctp_id  = _sctp_id;                                                      \
            }

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern __thread uint16_t g_proto_layer[32];
extern __thread uint8_t  g_proto_layer_cnt;

enum ipip_index_em{
    EM_IPIP_TYPE,
    EM_IPIP_OUTERIPSRC,
    EM_IPIP_OUTERIPDST,
    EM_IPIP_OUTERIPPROTO,
    EM_IPIP_OUTERSRCPORT,
    EM_IPIP_OUTERDSTPORT,
    EM_IPIP_INNERIPSRC,
    EM_IPIP_INNERIPDST,
    EM_IPIP_INNERIPPROTO,
    EM_IPIP_INNERSRCPORT,
    EM_IPIP_INNERDSTPORT,
    EM_IPIP_MAX
};


static dpi_field_table  ipip_field_array[] = {
    DPI_FIELD_D(EM_IPIP_TYPE,                   YA_FT_STRING,           "ProtoType"),
    DPI_FIELD_D(EM_IPIP_OUTERIPSRC,             YA_FT_STRING,           "OuterIPSrc"),
    DPI_FIELD_D(EM_IPIP_OUTERIPDST,             YA_FT_STRING,           "OuterIPDst"),
    DPI_FIELD_D(EM_IPIP_OUTERIPPROTO,           YA_FT_UINT8,            "OuterIPProto"),
    DPI_FIELD_D(EM_IPIP_OUTERSRCPORT,           YA_FT_UINT16,           "OuterSrcPort"),
    DPI_FIELD_D(EM_IPIP_OUTERDSTPORT,           YA_FT_UINT16,           "OuterDstPort"),
    DPI_FIELD_D(EM_IPIP_INNERIPSRC,             YA_FT_STRING,           "InnerIPSrc"),
    DPI_FIELD_D(EM_IPIP_INNERIPDST,             YA_FT_STRING,           "InnerIPDst"),
    DPI_FIELD_D(EM_IPIP_INNERIPPROTO,           YA_FT_UINT8,            "InnerIPProto"),
    DPI_FIELD_D(EM_IPIP_INNERSRCPORT,           YA_FT_UINT16,           "InnerSrcPort"),
    DPI_FIELD_D(EM_IPIP_INNERDSTPORT,           YA_FT_UINT16,           "InnerDstPort"),
};

typedef struct dpi_ipiphd {
    // struct five_tuple outer;
    char    tunel_type[16];
    uint8_t ip_version;
    struct five_tuple inner;
}IpIpInfo;



static int ipip_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, IpIpInfo *info, int *idx, int i)
{
    char __str[64] = {0};
    //int local_idx=*idx;
    switch(i){
    case EM_IPIP_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, (const uint8_t *)info->tunel_type, strlen(info->tunel_type));
        break;
    case EM_IPIP_OUTERIPSRC:
        write_one_ip_reconds(log_ptr->record, idx, flow->ip_version, flow->tuple.inner.ip_src);
        break;
    case EM_IPIP_OUTERIPDST:
        write_one_ip_reconds(log_ptr->record, idx, flow->ip_version, flow->tuple.inner.ip_dst);
        break;
    case EM_IPIP_OUTERIPPROTO:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, NULL, flow->tuple.inner.proto);
        break;
    case EM_IPIP_OUTERSRCPORT:
        if(flow->tuple.inner.port_src == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, NULL, ntohs(flow->tuple.inner.port_src));
        }
        break;
    case EM_IPIP_OUTERDSTPORT:
        if(flow->tuple.inner.port_dst == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, NULL, ntohs(flow->tuple.inner.port_dst));
        }
        break;
    case EM_IPIP_INNERIPSRC:
        write_one_ip_reconds(log_ptr->record, idx, info->ip_version, info->inner.ip_src);
        break;
    case EM_IPIP_INNERIPDST:
        write_one_ip_reconds(log_ptr->record, idx, info->ip_version, info->inner.ip_dst);
        break;
    case EM_IPIP_INNERIPPROTO:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, NULL, info->inner.proto);
        break;
    case EM_IPIP_INNERSRCPORT:
        if(info->inner.port_src == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, NULL, info->inner.port_src);
        }
        break;
    case EM_IPIP_INNERDSTPORT:
        if(info->inner.port_dst == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipip_field_array[i].type, NULL, info->inner.port_dst);
        }
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    return 0;

    UNUSED(direction);
    UNUSED(__str);
}


static int write_ipip_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    if (g_config.protocol_switch[PROTOCOL_IPIP] == 0)
        return 0;

    IpIpInfo *info=(IpIpInfo *)field_info;
    if(!info){
        return 0;
    }

    int i;
    int idx = 0;
    struct tbl_log *log_ptr;


    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, 0, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ipip");

    #if 0
    int *ipip_reflect_array=map_fields_get_array(PROTOCOL_IPIP);
    for(i=0; i<map_fields_get_num(PROTOCOL_IPIP);i++){
        ipip_field_element(log_ptr,flow, direction, info, &idx, ipip_reflect_array[i]);
    }
    #else
    for(i=0; i<EM_IPIP_MAX;i++){
        ipip_field_element(log_ptr,flow, direction, info, &idx, i);
    }
    #endif


    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_IPIP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    log_ptr->flow        = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

int dpi_dissect_ipip(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(!g_config.protocol_switch[PROTOCOL_IPIP] || payload_len < 4)
        return PKT_OK;

    uint32_t offset = 0;
    uint32_t inner_l4_offset = 0;
    const uint8_t *src_ip = NULL;
    const uint8_t *dst_ip = NULL;
    uint16_t sport,dport;
    struct pkt_info  pkt_data;
    IpIpInfo  info;
    memset(&pkt_data, 0, sizeof(struct pkt_info));
    memset(&info, 0, sizeof(IpIpInfo));
    const uint8_t *chunck_data = payload;
    uint16_t chunck_total_len = payload_len;
    const uint8_t *inner_l3 = &chunck_data[0];
    uint16_t raw = chunck_total_len;

    pkt_data.ipversion = *((const uint8_t*)inner_l3) >> 4;
    info.ip_version = pkt_data.ipversion;
    if (info.ip_version == 4 && payload_len >= 20) {
        append_hardlink_proto_info(ETH_P_IP);
        const struct dpi_iphdr *inner_iph = (const struct dpi_iphdr *)inner_l3;
        inner_l4_offset = inner_iph->ihl * 4;
        pkt_data.iph4=inner_iph;
        pkt_data.proto = inner_iph->protocol;
        src_ip = inner_iph->saddr;
        dst_ip = inner_iph->daddr;
        g_proto_layer[g_proto_layer_cnt++] = ETH_P_IP + PROTOCOL_MAX;
    }
    else
    if (info.ip_version == 6 && payload_len >= 40) {
        append_hardlink_proto_info(ETH_P_IPV6);
        const struct dpi_ipv6hdr *iph6 = (const struct dpi_ipv6hdr *)inner_l3;
        inner_l4_offset = 40;
        pkt_data.iph6=iph6;
        pkt_data.proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        src_ip = iph6->ip6_src;
        dst_ip = iph6->ip6_dst;
        g_proto_layer[g_proto_layer_cnt++] = ETH_P_IPV6 + PROTOCOL_MAX;
    }
    else
    {
        //2024.11.25 牡丹江 src_ip,dst_ip 出现值为 0x00
        return PKT_OK; //这不是IP4，IP6
    }

    if (flow->ip_version == 4 && info.ip_version == 6) {
        snprintf(info.tunel_type, sizeof(info.tunel_type), "%s", "IPv6inIPv4");
    } else if (flow->ip_version == 4 && info.ip_version == 4) {
        snprintf(info.tunel_type, sizeof(info.tunel_type), "%s", "IPv4inIPv4");
    } else if (flow->ip_version == 6 && info.ip_version == 6) {
        snprintf(info.tunel_type, sizeof(info.tunel_type), "%s", "IPv6inIPv6");
    } else {
        snprintf(info.tunel_type, sizeof(info.tunel_type), "%s", "IPv4inIPv6");
    }

    chunck_total_len = chunck_total_len - inner_l4_offset;

    const uint8_t *inner_l4  = ((const uint8_t *)inner_l3 + inner_l4_offset);

    if (pkt_data.proto == IPPROTO_TCP && chunck_total_len >= 20) {
        //u_int tcp_len;
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)inner_l4;
        sport = ntohs(tcph->source);
        dport = ntohs(tcph->dest);
        //tcp_len = DPI_MIN(4 * (*tcph)->doff, l4_packet_len);
        //*payload = &l4[tcp_len];
        //*payload_len = DPI_MAX(0, l4_packet_len - 4 * (*tcph)->doff);
        pkt_data.tcph=tcph;
        append_hardlink_proto_info(pkt_data.proto);
    } else if(pkt_data.proto == IPPROTO_UDP && chunck_total_len >= 8) {
        const struct dpi_udphdr *udph = (const struct dpi_udphdr *)inner_l4;
        sport = ntohs(udph->source);
        dport = ntohs(udph->dest);
        //*payload = &l4[sizeof(struct dpi_udphdr)];
        //*payload_len = l4_packet_len > sizeof(struct dpi_udphdr) ? l4_packet_len - sizeof(struct dpi_udphdr) : 0;
        pkt_data.udph=udph;
        append_hardlink_proto_info(pkt_data.proto);
    } else {
        //*payload_len = 0;
        sport = dport = 0;
        //return NULL;
    }

    SET_IP_FLOW_TUPLE(info.inner,  pkt_data.proto, pkt_data.ipversion, src_ip, dst_ip, sport, dport, 0);

    struct five_tuple   *outer_tuple;
    outer_tuple = direction == FLOW_DIR_SRC2DST ? &flow->tuple.inner : &flow->tuple_reverse.inner;

    SdtAclMatchedRuleInfo acl = {0};
    dpi_dissect_inherit_out_layer(flow, &pkt_data, &acl);

    dpi_packet_processing_ip_layer(&flow_thread_info[flow->thread_id],
                                    flow->timestamp,
                                    outer_tuple,
                                    &pkt_data,
                                    (const struct dpi_iphdr*)inner_l3,
                                    raw, raw, &acl);

    return PKT_OK;

    UNUSED(offset);
    UNUSED(flag);
}

static void init_ipip_dissector(void)
{
    dpi_register_proto_schema(ipip_field_array,EM_IPIP_MAX,"ipip");
    map_fields_info_register(ipip_field_array,PROTOCOL_IPIP, EM_IPIP_MAX,"ipip");
    return;
}

static __attribute((constructor)) void    before_init_ipip(void){
    register_tbl_array(TBL_LOG_IPIP, 0, "ipip", init_ipip_dissector);
}

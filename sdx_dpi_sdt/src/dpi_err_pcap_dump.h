/****************************************************************************************
 * 文 件 名 : dpi_err_pcap_dump.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chunli              2018/06/06
编码: chunli              2020/06/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_ERR_PCAP_DUMP_H_
#define _DPI_ERR_PCAP_DUMP_H_


#ifdef __cplusplus
extern "C" {
#endif

int dpi_err_pcap_copy(struct flow_info *f, const char *p, int l);
int dpi_err_pcap_dump(struct flow_info* f, const char *dir);

#ifdef __cplusplus
}
#endif /* __cplusplus */


#endif

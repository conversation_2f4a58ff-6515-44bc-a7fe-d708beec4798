#ifndef YA_DPI_HIGH_APP_PROTOS_H_
#define YA_DPI_HIGH_APP_PROTOS_H_

/**
 *  高层应用层协议, 如: Wechat, Skype等 
 */

enum HighProtoId_ {
    HIGH_PROTO_UNKNOWN,
    HIGH_PROTO_WEIXIN,
    HIGH_PROTO_SKYPE,

    HIGH_PROTO_MAX,
};

struct HighProtoInfo_ {
    enum HighProtoId_   id;
    const char         *name;
    const char         *desc;
};

typedef enum HighProtoId_       HighProtoId;
typedef struct HighProtoInfo_   HighProtoInfo;

const HighProtoInfo* dpi_high_proto_find_by_id(int id);



/****** http high app protocol *******/
struct flow_info;

int high_app_proto_http_init(void);
void high_app_proto_http_destory(void);
int high_app_proto_http_identify(struct flow_info *flow, void *line_info);



#endif // YA_DPI_HIGH_APP_PROTOS_H_
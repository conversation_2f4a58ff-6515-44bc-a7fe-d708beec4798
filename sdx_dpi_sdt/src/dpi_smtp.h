/* 
 * File:   dpi_smtp.h
 * Author: xu
 *
 * Created on 2019年3月7日, 下午7:06
 */

#ifndef DPI_SMTP_H
#define DPI_SMTP_H
#include "dpi_conversation.h"

#include <yaemail/email.h>

#include "dpi_email.h"

#define SMTP_PORT 25
#define SMTP_ATTACHMENT_NUM_MAX 10

#define O_RDONLY         00
#define O_WRONLY         01
#define O_RDWR             02
#ifndef O_CREAT
# define O_CREAT       0100    /* Not fcntl.  */
#endif

typedef enum 
{
    SMTP_STATE_READING_CMDS,              // reading commands
    SMTP_STATE_READING_AUTHNAME,
    SMTP_STATE_READING_AUTHPASSWD,
    SMTP_STATE_READING_DATA,              // reading message data
    SMTP_STATE_AWAITING_STARTTLS_RESPONSE // sent STARTTLS, awaiting response
} smtp_state_t;

enum smtp_index_field
{

    EM_SMTP_AUTH_NAME,
    EM_SMTP_AUTH_PASSWD,
    EM_SMTP_MAIL_FILENAME,
    E<PERSON>_SMTP_MAIL_SERVER_NAME,
    EM_SMTP_MAIL_SERVER_SOFT,
    EM_SMTP_SEND_HOST_IP,
    EM_SMTP_RECV_HOST_IP,
    
    EM_SMTP_DATE,
    EM_SMTP_FROM,
    EM_SMTP_SENDER,
    EM_SMTP_SENDER_DOMAIN,
    EM_SMTP_REPLY_TO,
    EM_SMTP_TO,
    EM_SMTP_RECV,
    EM_SMTP_CC,
    EM_SMTP_BCC,
    EM_SMTP_REFERENCES,
    EM_SMTP_SUBJECT,
    EM_SMTP_X_PRIORITY,
    EM_SMTP_X_GUID,
    EM_SMTP_X_HAS_ATTACH,
    EM_SMTP_X_MAILER,
    EM_SMTP_MIME_VERSION,
    EM_SMTP_MESSAGE_ID,
    EM_SMTP_CONTENT_TYPE,
    EM_SMTP_CONTENT_ID,
    EM_SMTP_USER_AGENT,    //用户代理
    EM_SMTP_COMMENTS,
    EM_SMTP_KEYWORDS,
    EM_SMTP_RESENT_DATE,
    EM_SMTP_RESENT_FROM,
    EM_SMTP_RESENT_SENDER,
    EM_SMTP_RESENT_TO,
    EM_SMTP_RESENT_CC,
    EM_SMTP_RESENT_BCC,
    EM_SMTP_RESENT_MESSAGE_ID,
    EM_SMTP_RETURN_PATH,
    EM_SMTP_RECEIVED,
    EM_SMTP_IN_REPLY_TO,
    EM_SMTP_CONTENT_TRANSFER_ENCODING,
    EM_SMTP_DELIVERY_DATE,
    EM_SMTP_LATEST_DELIVERY_TIME,
    EM_SMTP_CONTENT_DESCRIPTION,
    
    EM_SMTP_AUTOFORWARDED,
    EM_SMTP_AUTOSUBMITTED,
    EM_SMTP_X400_CONTENT_IDENTIFIER,
    EM_SMTP_CONTENT_LANGUAGE,
    EM_SMTP_CONVERSION,
    EM_SMTP_CONVERSION_WITH_LOSS,
    EM_SMTP_DISCARDED_X400_IPMS_EXTENSIONS,
    EM_SMTP_DISCARDED_X400_MTS_EXTENSIONS,
    EM_SMTP_DL_EXPANSION_HISTORY,
    EM_SMTP_DEFERRED_DELIVERY,
    EM_SMTP_EXPIRES,
    EM_SMTP_IMPORTANCE,
    EM_SMTP_INCOMPLETE_COPY,
    EM_SMTP_MESSAGE_TYPE,
    EM_SMTP_ORIGINAL_ENCODED_INFORMATION_TYPES,
    EM_SMTP_ORIGINATOR_RETURN_ADDRESS,
    EM_SMTP_PRIORITY,
    EM_SMTP_REPLY_BY,
    EM_SMTP_SENSITIVITY,
    EM_SMTP_SUPERSEDES,
    EM_SMTP_X400_CONTENT_TYPE,
    EM_SMTP_X400_MTS_IDENTIFIER,
    EM_SMTP_X400_ORIGINATOR,
    EM_SMTP_X400_RECEIVED,
    EM_SMTP_X400_RECIPIENTS,
    EM_SMTP_DELIVERED_TO,
    EM_SMTP_THREAD_INDEX,
    EM_SMTP_X_MIMEOLE,
    EM_SMTP_EXPIRY_DATE,
    EM_SMTP_X_MS_TNEF_CORRELATOR,
    EM_SMTP_X_UIDL,
    EM_SMTP_X_AUTHENTICATION_WARNING,
    EM_SMTP_X_VIRUS_SCANNED,
    EM_SMTP_SIO_LABEL,
    EM_SMTP_HELO,

    EM_SMTP_RECV_HOST,        //接收主机
    EM_SMTP_SEND_SOFT,        //发送软件
    EM_SMTP_PROXY_RESEND,    //转发代理
    //EM_SMTP_PROXY_USER,        //用户代理
    EM_SMTP_USER_OP_TYPE,    //用户操作类型
    EM_SMTP_USER_OP_TIME,    //用户操作时间
    EM_SMTP_USER_OP_RES,    //用户操作结果
    EM_SMTP_RECEIVER_TYPE,    //收件人类型
    EM_SMTP_SEND_DURATION,    //发送持续时间
    
    EM_SMTP_ATTACHMENT_FILENAME0,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE0,
    EM_SMTP_ATTACHMENT_ENCODING0,
    EM_SMTP_ATTACHMENT_DISPOSITION0,
    EM_SMTP_ATTACHMENT_LEN0,
    
    EM_SMTP_ATTACHMENT_FILENAME1,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE1,
    EM_SMTP_ATTACHMENT_ENCODING1,
    EM_SMTP_ATTACHMENT_DISPOSITION1,
    EM_SMTP_ATTACHMENT_LEN1,
    
    EM_SMTP_ATTACHMENT_FILENAME2,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE2,
    EM_SMTP_ATTACHMENT_ENCODING2,
    EM_SMTP_ATTACHMENT_DISPOSITION2,
    EM_SMTP_ATTACHMENT_LEN2,
    
    EM_SMTP_ATTACHMENT_FILENAME3,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE3,
    EM_SMTP_ATTACHMENT_ENCODING3,
    EM_SMTP_ATTACHMENT_DISPOSITION3,
    EM_SMTP_ATTACHMENT_LEN3,
    
    EM_SMTP_ATTACHMENT_FILENAME4,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE4,
    EM_SMTP_ATTACHMENT_ENCODING4,
    EM_SMTP_ATTACHMENT_DISPOSITION4,
    EM_SMTP_ATTACHMENT_LEN4,
    
    EM_SMTP_ATTACHMENT_FILENAME5,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE5,
    EM_SMTP_ATTACHMENT_ENCODING5,
    EM_SMTP_ATTACHMENT_DISPOSITION5,
    EM_SMTP_ATTACHMENT_LEN5,
    
    EM_SMTP_ATTACHMENT_FILENAME6,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE6,
    EM_SMTP_ATTACHMENT_ENCODING6,
    EM_SMTP_ATTACHMENT_DISPOSITION6,
    EM_SMTP_ATTACHMENT_LEN6,
    
    EM_SMTP_ATTACHMENT_FILENAME7,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE7,
    EM_SMTP_ATTACHMENT_ENCODING7,
    EM_SMTP_ATTACHMENT_DISPOSITION7,
    EM_SMTP_ATTACHMENT_LEN7,
    
    EM_SMTP_ATTACHMENT_FILENAME8,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE8,
    EM_SMTP_ATTACHMENT_ENCODING8,
    EM_SMTP_ATTACHMENT_DISPOSITION8,
    EM_SMTP_ATTACHMENT_LEN8,
    
    EM_SMTP_ATTACHMENT_FILENAME9,
    EM_SMTP_ATTACHMENT_CONTENT_TYPE9,
    EM_SMTP_ATTACHMENT_ENCODING9,
    EM_SMTP_ATTACHMENT_DISPOSITION9,
    EM_SMTP_ATTACHMENT_LEN9,


    EM_SMTP_MAIL_CODE_FORMAT,
    EM_SMTP_MAIL_CODE_BASE,
    EM_SMTP_MAIL_CONTENT_DATA,

    
    EM_SMTP_PROTO_TYPE,

    EM_SMTP_EML_FILESIZE,
    EM_SMTP_LOGIN_STATUS,

    EM_SMTP_MAX
};

struct smtp_unknown_line 
{
    uint16_t key_len;
    uint16_t val_len;
    const uint8_t *key_ptr;
    const uint8_t *val_ptr;
};

/*!
    @brief 用于区分smtp和esmtp
    @detail 若解析到SMTP_CMD_AUTH_AFTER命令时，而上次的命令确是SMTP_CMD_AUTH_BEFORE，
            则认为该协议为smtp，反之esmtp(默认)
 */
typedef enum 
{
    SMTP_CMD_AUTH_BEFORE,
    SMTP_CMD_AUTH,
    SMTP_CMD_AUTH_AFTER,
} smtp_cmd_t;



#define ENCODING_LEN        32
#define FILE_NAME_LEN       64
#define CONTENT_TYPE_LEN    128
#define DISPOSITION_LEN     128
#define CONTENT_MD5_LEN     (64)

typedef struct __attachment_info__
{
    //附件文件名
    uint8_t attachment_filename[FILE_NAME_LEN];
    //附件内容类型
    uint8_t attachment_content_type[CONTENT_TYPE_LEN];
    //附件内容编码格式
    uint8_t attachment_content_transfer_encoding[ENCODING_LEN];
    //附件类型，附件文件名
    uint8_t attachment_content_disposition[DISPOSITION_LEN];
    //附件的内容MD5
    uint8_t attachment_content_md5[CONTENT_MD5_LEN];
    //附件内容长度
    long long attachment_len;
} Attachment;

struct smtp_session
{
    dpi_email_t *email;
    session_type_t session_type;
    uint8_t     has_ensure_type;
    smtp_cmd_t cur_cmd;
    smtp_state_t state;

    uint32_t mail_begin_seq;
    uint32_t user_seq;
    uint32_t passwd_seq;
    
    char auth_name[64];
    char auth_passwd[64];
    char mail_filename[128];

    uint64_t mail_file_size;
    char mail_file_type[64];
    uint32_t mail_send_size;
	//信封
    const uint8_t *mail_mail_from_ptr;
    uint16_t mail_mail_from_len;
    const uint8_t *mail_mail_from_domain_ptr;
    uint16_t mail_mail_from_domain_len;
    const uint8_t *mail_rcpt_to_ptr;
    uint16_t mail_rcpt_to_len;
    const uint8_t *mail_rcpt_to_domain_ptr;
    uint16_t mail_rcpt_to_domain_len;

    const uint8_t *mail_date_ptr;
    uint16_t mail_date_len;
    const uint8_t *mail_from_ptr;
    uint16_t mail_from_len;
    const uint8_t *sender_email_ptr;
    uint16_t sender_email_len;
    //发送人
    const uint8_t *mail_sender_name_ptr;
    uint16_t mail_sender_name_len;
    
    //发件域 senderDomain
    const uint8_t *mail_sender_domain_ptr;
    uint16_t mail_sender_domain_len;
    
    const uint8_t *mail_to_ptr;
    uint16_t mail_to_len;
    const uint8_t *receiver_email_ptr;
    uint16_t receiver_email_len;
    //接收人
    const uint8_t *mail_receiver_name_ptr;
    uint16_t mail_receiver_name_len;
    //发件域 receiverDomain
    const uint8_t *mail_receiver_domain_ptr;
    uint16_t mail_receiver_domain_len;
    
    const uint8_t *mail_cc_ptr;
    uint16_t mail_cc_len;
    const uint8_t *mail_bcc_ptr;
    uint16_t mail_bcc_len;
    const uint8_t *mail_subject_ptr;
    uint16_t mail_subject_len;
    
    const uint8_t *mail_x_priority_ptr;
    uint16_t mail_x_priority_len;
    const uint8_t *mail_x_guid_ptr;
    uint16_t mail_x_guid_len;
    const uint8_t *mail_x_has_attach_ptr;
    uint16_t mail_x_has_attach_len;
    const uint8_t *mail_x_mailer_ptr;
    uint16_t mail_x_mailer_len;
    const uint8_t *mail_mime_version_ptr;
    uint16_t mail_mime_version_len;
    const uint8_t *mail_message_id_ptr;
    uint16_t mail_message_id_len;
    const uint8_t *mail_content_type_ptr;
    uint16_t mail_content_type_len;
    const uint8_t *mail_content_id_ptr;
    uint16_t mail_content_id_len;
    const uint8_t *mail_user_agent_ptr;
    uint16_t mail_user_agent_len;
    const uint8_t *mail_comments_ptr;
    uint16_t mail_comments_len;
    const uint8_t *mail_keywords_ptr;
    uint16_t mail_keywords_len;
    const uint8_t *mail_resent_date_ptr;
    uint16_t mail_resent_date_len;
    const uint8_t *mail_resent_from_ptr;
    uint16_t mail_resent_from_len;
    const uint8_t *mail_resent_sender_ptr;
    uint16_t mail_resent_sender_len;
    const uint8_t *mail_resent_to_ptr;
    uint16_t mail_resent_to_len;
    const uint8_t *mail_resent_cc_ptr;
    uint16_t mail_resent_cc_len;
    const uint8_t *mail_resent_bcc_ptr;
    uint16_t mail_resent_bcc_len;
    const uint8_t *mail_resent_message_id_ptr;
    uint16_t mail_resent_message_id_len;
    const uint8_t *mail_return_path_ptr;
    uint16_t mail_return_path_len;
    const uint8_t *mail_received_ptr;
    uint16_t mail_received_len;
    const uint8_t *mail_in_reply_to_ptr;
    uint16_t mail_in_reply_to_len;
    const uint8_t *mail_content_transfer_encoding_ptr;
    uint16_t mail_content_transfer_encoding_len;
    const uint8_t *mail_delivery_date_ptr;
    uint16_t mail_delivery_date_len;
    const uint8_t *mail_latest_delivery_time_ptr;
    uint16_t mail_latest_delivery_time_len;
    const uint8_t *mail_content_description_ptr;
    uint16_t mail_content_description_len;  
    const uint8_t *mail_reply_to_ptr;
    uint16_t mail_reply_to_len;
    const uint8_t *mail_references_ptr;
    uint16_t mail_references_len;
    const uint8_t *mail_autoforwarded_ptr;
    uint16_t mail_autoforwarded_len;
    const uint8_t *mail_autosubmitted_ptr;
    uint16_t mail_autosubmitted_len;
    const uint8_t *mail_x400_content_identifier_ptr;
    uint16_t mail_x400_content_identifier_len;
    const uint8_t *mail_content_language_ptr;
    uint16_t mail_content_language_len;
    const uint8_t *mail_conversion_ptr;
    uint16_t mail_conversion_len;
    const uint8_t *mail_conversion_with_loss_ptr;
    uint16_t mail_conversion_with_loss_len;
    const uint8_t *mail_discarded_x400_ipms_extensions_ptr;
    uint16_t mail_discarded_x400_ipms_extensions_len;
    const uint8_t *mail_discarded_x400_mts_extensions_ptr;
    uint16_t mail_discarded_x400_mts_extensions_len;
    const uint8_t *mail_dl_expansion_history_ptr;
    uint16_t mail_dl_expansion_history_len;
    const uint8_t *mail_deferred_delivery_ptr;
    uint16_t mail_deferred_delivery_len;
    const uint8_t *mail_expires_ptr;
    uint16_t mail_expires_len;
    const uint8_t *mail_importance_ptr;
    uint16_t mail_importance_len;
    const uint8_t *mail_incomplete_copy_ptr;
    uint16_t mail_incomplete_copy_len;
    const uint8_t *mail_message_type_ptr;
    uint16_t mail_message_type_len;
    const uint8_t *mail_original_encoded_information_types_ptr;
    uint16_t mail_original_encoded_information_types_len;
    const uint8_t *mail_originator_return_address_ptr;
    uint16_t mail_originator_return_address_len;
    const uint8_t *mail_priority_ptr;
    uint16_t mail_priority_len;
    const uint8_t *mail_reply_by_ptr;
    uint16_t mail_reply_by_len;
    const uint8_t *mail_sensitivity_ptr;
    uint16_t mail_sensitivity_len;
    const uint8_t *mail_supersedes_ptr;
    uint16_t mail_supersedes_len;
    const uint8_t *mail_x400_content_type_ptr;
    uint16_t mail_x400_content_type_len;
    const uint8_t *mail_x400_mts_identifier_ptr;
    uint16_t mail_x400_mts_identifier_len;
    const uint8_t *mail_x400_originator_ptr;
    uint16_t mail_x400_originator_len;
    const uint8_t *mail_x400_received_ptr;
    uint16_t mail_x400_received_len;
    const uint8_t *mail_x400_recipients_ptr;
    uint16_t mail_x400_recipients_len;
    const uint8_t *mail_delivered_to_ptr;
    uint16_t mail_delivered_to_len;
    const uint8_t *mail_thread_index_ptr;
    uint16_t mail_thread_index_len;
    const uint8_t *mail_x_mimeole_ptr;
    uint16_t mail_x_mimeole_len;
    const uint8_t *mail_expiry_date_ptr;
    uint16_t mail_expiry_date_len;
    const uint8_t *mail_x_ms_tnef_correlator_ptr;
    uint16_t mail_x_ms_tnef_correlator_len;
    const uint8_t *mail_x_uidl_ptr;
    uint16_t mail_x_uidl_len;
    const uint8_t *mail_x_authentication_warning_ptr;
    uint16_t mail_x_authentication_warning_len;
    const uint8_t *mail_x_virus_scanned_ptr;
    uint16_t mail_x_virus_scanned_len;
    const uint8_t *mail_sio_label_ptr;
    uint16_t mail_sio_label_len;
    //暂定为最多支持10个附件
    Attachment  attachment_list[SMTP_ATTACHMENT_NUM_MAX+1];
    uint8_t     attachment_filename_num;
    uint8_t     attachment_conttype_num;
    uint8_t     attachent_md5_num;
    
    char mail_helo_ptr[16];
    char mail_recv_host_ip_ptr[16];
    char mail_server_name_ptr[64];
    char mail_server_soft_ptr[64];
 
    uint8_t send_host_ip4[64];
    uint8_t send_host_ip6[64];
    
    uint8_t   main_content_print[32];
    uint8_t   main_content_code[50];
    const uint8_t   *main_content_data;
    uint16_t  main_content_len;

    uint32_t eml_filesize;
    uint8_t  drop_data[16];
    char     login_status[COMMON_STATUS_LEN];

    // from ip -- Received字段中的IP，如果有多个，逗号隔开， 可能还有多个Received字段，都解析
    char    received_from_ips[1024];
    uint8_t received_from_ips_num;  // from ip数量

    char    receiveds[2048];
    uint8_t receiveds_num;

    // from domain  -- Received字段中的from 域名， 用逗号分开
    char    received_from_doms[1024];
    uint8_t received_from_doms_num;

    // by ip  -- Received 字段中的by IP， 逗号分开
    char    received_by_ips[1024];
    uint8_t received_by_ips_num;

    // by dom -- Received字段中的by 域名， 用逗号分开
    char    received_by_doms[1024];
    uint8_t received_by_doms_num;

    // from asn  IP解析出来的自治域
    char    from_asns[2048];
    uint8_t from_asns_num;

    // from country IP解析出来的国家
    char    from_countries[1024];
    uint8_t from_countries_num;

    char    cc_addrs[2048];  // Cc 抄送的地址
    char    cc_alias[2048];  // Cc 抄送的名字
    uint8_t cc_addrs_num;
    uint8_t cc_alias_num;

    // content-type 逗号分开
    char    cont_types[2048];
    uint8_t cont_types_num;

    // text charset  content-type=text是的charset
    char    text_charsets[1024];
    uint8_t text_charsets_num;

    // body type 正文的content-type
    char    body_type[1024];
    uint8_t body_type_num;

    // 邮件的头部设置字段，逗号分开  如 Content-type、X- clm-senderinfo 等
    char    head_sets[2048];
    uint8_t head_sets_num;

    // Message-ID 头
    char    msg_ids[1024];
    uint8_t msg_ids_num;

    // mime-version 头
    char    mime_vers[256];
    uint8_t mime_vers_num;

    // 包含html标签的正文
    const uint8_t *content_has_html_ptr;
    uint16_t content_has_html_len;

    // 正文的charset
    const uint8_t *content_charset_ptr;
    uint16_t content_charset_len;

    // 命令集合
    char    commands[2048];
    uint8_t commands_num;

    const uint8_t *x_ori_ip_ptr;
    uint16_t x_ori_ip_len;

    uint8_t mail_num;  // 邮件份数

    // 发件人邮箱域
    char    mail_from_doms[2048];
    uint8_t mail_from_doms_num;

    // 收件人邮箱域
    char    mail_rcpt_to_doms[2048];
    uint8_t mail_rcpt_to_doms_num;

    // 内容传输编码
    char    body_tra_encs[1024];
    uint8_t body_tra_encs_num;

    // 收件人邮箱， 逗号分号
    char    rcv_mails[2048];
    uint8_t rcv_mails_num;

    // 收件人别名,逗号分开
    char    rcv_aliases[2048];
    uint8_t rcv_aliases_num;

    // 收件人域，逗号分开
    char    rcv_doms[2048];
    uint16_t rcv_doms_num;

    // Subject主题， 逗号分开
    char    subjects[2048];
    uint8_t subjects_num;

    // X-Mailer， 逗号分开
    char    x_mailers[2048];
    uint8_t x_mailers_num;

    // starttls  tls加密标识
    uint8_t starttls_f;

    // 以下是针对imap协议的客户端的信息
    char mail_id_name[128];
    char mail_id_version[128];
    char mail_id_os[128];
    char mail_id_os_version[128];
    char mail_id_vendor[128];

    // login 登录的邮件服务器
    const uint8_t   *login_svr_ptr;
    uint16_t        login_svr_len;

    // host 发件人身份标识
    const uint8_t   *host_ptr;
    uint16_t        host_len;

    // index 请求索引
    const uint8_t   *req_index_ptr;
    uint16_t        req_index_len;

};
#endif /* DPI_SMTP_H */

#ifndef SDT_ACTION_OUT_H
#define SDT_ACTION_OUT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "dpi_detect.h"
#include "dpi_tbl_log.h"
#include "libsdt/libsdt_interface.h"

#if 0
#define SDT_MAX_RULE_NUM 10000
#define SDT_MAX_OUT_RING 4

struct stu_rule_pcap
{
    FILE *pcap_fp;

    uint32_t  pcap_time;
    uint32_t  pkt_bytes;   /* 到目前为止输出总字节数 */
    uint32_t  pkt_cnt;     /* 到目前为止输出总报文数 */
    uint32_t  max_pcap_size;
    uint32_t  max_time;

    uint64_t  pkts_hit_cnt;
    uint64_t  bytes_hit_cnt;
    uint64_t  flow_hit_cnt;
    char pcap_name[COMMON_FILE_PATH];
};

struct stu_rule_event
{
    FILE *event_fp;
    uint32_t  event_time;
    uint32_t  event_cnt;   /* 到目前为止输出event条数 */
    uint32_t  max_event_num;
    uint32_t  max_time;

    char event_name[COMMON_FILE_PATH];
};

struct stu_rule_statistics{
    time_t    rule_first_match_time;
    time_t    rule_last_match_time;
    uint64_t  rule_match_cnt;       /* 规则命中计数 */
    uint64_t  rule_report_cnt;      /* 规则命中上报计数 */

    uint64_t  rule_match_pkts;      /* 规则命中报文数 */
    uint64_t  rule_match_bytes;     /* 规则命中字节数 */
    uint64_t  rule_match_flows;     /* 规则命中流计数 */
};


struct stu_statistics_result{
    time_t    rule_first_match_time;
    time_t    rule_last_match_time;

    uint32_t  match_pps;
    uint32_t  match_fps;
    uint32_t  match_bps;

    uint32_t  match_cnt;
};


typedef struct _sdt_out_status
{
    uint8_t   flag;
    int       rule_id;
    uint32_t  rule_hash_code;

    struct stu_rule_pcap  pcap_status;
    struct stu_rule_event event_status;

    struct stu_statistics_result statistics_data;
    struct stu_rule_statistics statistics_before;    /* 上报前一次统计记录 */
    struct stu_rule_statistics statistics_current;   /* 上报当前统计记录，上报统计为增量 */
    struct stu_rule_statistics thread_statistics[MAX_FLOW_THREAD_NUM]; /* 每个线程命中规则统计 */

    uint32_t  next;
}sdt_out_status;


typedef struct _sdt_out_db
{
    pthread_mutex_t mutex;
    uint32_t        conflict_index;
    sdt_out_status  rule_db[SDT_MAX_RULE_NUM*2];
}sdt_out_db;
#endif

typedef struct SdxRecIPRule_ {
  FILE    *fp;
  time_t   create_time;
  uint64_t write_bytes;
  char     filename_writing[FILENAME_MAX];
  char     filename_finished[FILENAME_MAX];
} SdxRecIPRule;

typedef struct SdxOutThreadCtx_ {
    int          ring_id;
    SdxRecIPRule sdx_rec_rule;
    FILE         *test_out_fp;
} SdxOutThreadCtx;


extern int g_sdt_hash_db_clear_flag;
extern struct rte_mempool *pktstream_pool;


SdxOutThreadCtx *SdxOutThreadCtx_new();
void             SdxOutThreadCtx_free(SdxOutThreadCtx *);

static uint32_t sdt_rule_hash(uint32_t rule_hash_code);

static int sdt_rule_hash_init(sdt_out_status *npq, uint32_t rule_hash_code, uint32_t rule_id);

static sdt_out_status *sdt_rule_hash_insert(uint32_t rule_hash_code, uint32_t rule_id);

sdt_out_status *sdt_rule_hash_lookup(uint32_t rule_hash_code);

sdt_out_status *sdt_rule_hash_lookup_insert(uint32_t rule_hash_code, uint32_t rule_id);

int sdt_init_rules_to_hash_db(SdtMatchResult *rules_result, sdt_out_status *rule_elem);

int sdt_rule_hash_db_insert(SdtMatchResult *rules_result);

int sdt_rule_hash_db_clean(void);

sdt_out_status *sdt_rule_hash_db_lookup(SdtMatchResult *match_result);

sdt_out_status *sdt_rule_hash_lookup_key(const char *uintid, const char *taskid, const char *groupid, uint32_t rule_id);

int sdt_clean_rule_data_status(void);

static int sdt_in_init(void);

int sdt_in_pcap(SdtMatchResult *sdt_act, const struct pkt_info *pkt);

int sdt_in_syslog(struct flow_info *flow, SdtMatchResult *sdt_act);

int sdt_in_event(struct flow_info *flow, SdtMatchResult *sdt_act, int direction);

int sdt_event_handle(struct flow_info *flow, SdtMatchResult *get_action, sdt_out_status *get_rule_status,
                     int direction);

int sdt_out_packet_data_out(struct flow_info *flow, struct packet_stream *pos, SdtMatchResult  *sdt_act, sdt_out_status  *flow_rule_status);

/*************************************************sdt 统计相关
 * 操作*************************************************************/

int sdt_rule_perthread_pkt_statistics(struct flow_info *flow, SdtMatchResult *match_result,
                                      sdt_out_status *flow_rule_status, int thread_id, uint8_t flag);

int sdt_rule_perthread_flow_statistics(SdtMatchResult *sdt_action, sdt_out_status *flow_rule_status, int thread_id,
                                       uint16_t pkt_len);

int sdt_rule_collect_statistics(void);

int sdt_statistics_keep_before(void);

void *sdt_statistics_thread(void *arg);

rule_web_stat *sdt_rule_get_increase_statistics(uint32_t rule_hash_code);

void *sdt_out_thfunc(void *arg);

static void sdt_out_thread(void);

int sdt_io_init(void);
void sdt_out_threads_stop(void);

int sdt_check_rule_exist_report(struct flow_info *flow);

/* 文件序号操作方法 */
#define FILE_SEQ_TYPE_GLOBAL 0
uint32_t dpi_safe_get_filename_seq(int file_seq_type);

#endif

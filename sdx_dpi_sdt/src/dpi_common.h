#ifndef _DPI_COMMON_H_
#define _DPI_COMMON_H_
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <arpa/inet.h>
#include <sys/stat.h>

#ifdef DPDK_MEMPOOL
#include <rte_hash.h>
#define DpiHash struct rte_hash
#else
#include <glib.h>
#define DpiHash GHashTable
#endif

#define COMMON_LONG_STRING       1024
#define COMMON_FILE_PATH         256
#define COMMON_NAME_PASSWD_LEN   128
#define COMMON_FILE_NAME         128
#define COMMON_SOME_TYPE         32
#define COMMON_STATUS_LEN        5
#define COMMON_FIELD_LEN         64
#define COMMON_IP_MAX_SIZE       32


#define STD_TIME_LEN             19
#define COMMON_FILTER_NUM        10

struct header_value
{
    uint8_t need_free;
    uint16_t len;
    const uint8_t *ptr;
};

struct header_tmp_value
{
    uint8_t need_free;
    uint16_t len;
        uint8_t *ptr;
};

struct int_to_string
{
    int value;
    const char *strptr;
};

/*for safe get int/string value from payload*/
struct dpi_pkt_st
{
    const uint8_t *payload;
    uint32_t payload_len;
};

struct dpi_pkt_op_st
{
    uint8_t *payload;
    uint32_t payload_len;
};
/* "libpcap" record header. */
struct pcappkt_hdr {
    unsigned int tv_sec;      /* timestamp seconds */
    unsigned int tv_usec;     /* timestamp microseconds */
    unsigned int caplen;      /* number of octets of packet saved in file */
    unsigned int len;          /* actual length of packet */
};

typedef struct dpi_thread_args
{
  uint8_t ring_id;
  uint8_t core_id;
  pthread_t  thread_id;
}DpiThreadArgs;

#define PROTOCOL_HEAD_DEF(head_name) const uint8_t *head_name##_val_ptr; uint32_t head_name##_val_len;
#define PROTOCOL_VAL_PTR(head_name) head_name##_val_ptr
#define PROTOCOL_VAL_LEN(head_name) head_name##_val_len

#define _U_       __attribute__((unused))
#define UNUSED(arg) do {(void)(arg); } while(0);
#define array_length(x)    (sizeof x / sizeof x[0])

#define get_uint8_t(X,O)  (*(const uint8_t *)(((const uint8_t *)X) + O))
#define get_uint16_t(X,O)  (*(const uint16_t *)(((const uint8_t *)X) + O))
#define get_uint32_t(X,O)  (*(const uint32_t *)(((const uint8_t *)X) + O))
#define get_uint64_t(X,O)  (*(const uint64_t *)(((const uint8_t *)X) + O))

#define get_uint16_ntohs(X,O)  (ntohs(*(const uint16_t *)(((const uint8_t *)X) + O)))
#define get_uint32_ntohl(X,O)  (ntohl(*(const uint32_t *)(((const uint8_t *)X) + O)))

#define dpi_match_strprefix(payload, payload_len, str) match_prefix_str((payload), (payload_len), (str), (sizeof(str)-1))

#define ATOMIC_FETCH_ADD(a)    __sync_fetch_and_add(a, 1)
#define ATOMIC_FETCH_SUB(a)    __sync_fetch_and_sub(a, 1)
#define ATOMIC_ADD_FETCH(a)    __sync_add_and_fetch(a, 1)
#define ATOMIC_SUB_FETCH(a)    __sync_sub_and_fetch(a, 1)
#define ATOMIC_ADD_NUM(a,n)    __sync_add_and_fetch(a, n)
#define ATOMIC_SUB_NUM(a,n)    __sync_sub_and_fetch(a, n)
#define ATOMIC_COM_SWAP(a,o,n) __sync_val_compare_and_swap(a,o,n)
#define ATOMIC_SET(a,n)        __sync_lock_test_and_set(a,n)
#define ATOMIC_ZERO(a)         __sync_lock_release(a)

#include <limits.h>    /* for CHAR_BIT */
#include <stdint.h>    /* for uint32_t */

#define WORD  uint64_t
enum { BITS_PER_WORD = sizeof(WORD) * CHAR_BIT };
#define WORD_OFFSET(b)     ((b) / BITS_PER_WORD)
#define BIT_OFFSET(b)      ((b) % BITS_PER_WORD)
#define bit_set(words,n)   (words[WORD_OFFSET(n)] |=  (1UL<<BIT_OFFSET(n)))
#define bit_clear(words,n) (words[WORD_OFFSET(n)] &= ~(1UL<<BIT_OFFSET(n)))
#define bit_get(words,n)   (!!(words[WORD_OFFSET(n)] &(1UL<<BIT_OFFSET(n))))

//元素个数 占用几个字
#define bit_cost(length)   (WORD_OFFSET(BITS_PER_WORD-1+(length)))

//输入字位和数位, 组合为实际数
#define bit_num(i,k)       ((i)*BITS_PER_WORD+(k))

//返回 x 的二进制中 1 的个数
#define bit_popcount(x) __builtin_popcountll(x)

//返回 x 的二进制末尾连续 0 的个数
#define bit_ctz(x)      __builtin_ctzll(x)

//返回 x 的二进制的前导 0 的个数
#define bit_clz(x)      __builtin_clzll(x)

//返回 x 的二进制末尾最后一个 1 的位置，位置的编号从 1 开始
#define bit_ffs(x)      __builtin_ffsll(x)


static inline int is_ip4(const uint8_t *begin, int len)
{
    if(len < 20 || begin[0] != 0x45 || get_uint16_ntohs(begin, 2) > len)
        return 0;
    return 1;
}

static inline int is_ip6(const uint8_t *begin, int len)
{
    if(len < 40 || begin[0] >> 4 != 6 || get_uint16_ntohs(begin, 4) + 40 > len)
        return 0;
    return 1;
}

int find_packet_line_end(const uint8_t *data, uint16_t len);
int match_prefix_str(const uint8_t *payload, size_t payload_len, const char *str, size_t str_len);

uint32_t dpi_get_token_len(const uint8_t *linep, const uint8_t *lineend, const uint8_t **next_token);
int find_blank_space(const uint8_t *start, int max_len);
int find_special_char(const uint8_t *start, int max_len, char c);

int find_special_colon_space(const uint8_t *start, int max_len);
int _find_imap_fetch_end_line(const uint8_t *payload, uint32_t payload_len);
int _find_email_end_line(const uint8_t *payload, uint32_t payload_len);

// 处理parameters格式的字符串
typedef void (parameters_keys_values)(void* user,char* key,int key_len,char* value,int val_len);

void get_parameters_keys_values(char *payload, int payload_len, void *user, parameters_keys_values func);

int _find_empty_line(const uint8_t *payload, uint32_t payload_len);
int _find_single_line_end(const uint8_t *payload, uint32_t payload_len);
char *strdown_inplace(char *str);
int find_str_end_len(const uint8_t *start, uint32_t max_len);
const char *val_to_string(const int val, const struct int_to_string *vs);
const char *val_to_string_ext(const int val, const struct int_to_string *vs, int *idx);
int timet_to_datetime(time_t t, char *time_str, int len);
int timet_to_datetime_gm(time_t t, char *time_str, int len);
int timet_to_datetime_local(time_t t, char *time_str, int len);
char *get_owner_path(void);

void *dpi_malloc(size_t size);
void dpi_free(void *ptr);



int dpi_get_uint8(struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *val);
int dpi_get_be16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val);
int dpi_get_be24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_be32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_be64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val);

int dpi_get_le16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val);
int dpi_get_le24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_le32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val);
int dpi_get_le64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val);
int dpi_get_string_endwith_null(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len);
int dpi_get_string_ascii(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len);
int dpi_get_hex_string(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len);
int dpi_get_guid(struct dpi_pkt_st *pkt, uint32_t offset, char *val, uint32_t max_len);
int dpi_strneql(struct dpi_pkt_st *pkt, uint32_t offset, const char *str, const size_t size);

void dpi_append_octet_aligned(struct dpi_pkt_st *pkt_src, uint32_t bit_offset, int no_of_bits, struct dpi_pkt_op_st *pkt_dst, uint32_t dst_max_len);

int dpi_is_gbk(const char *pData, int len);
int dpi_is_utf8(const char *pData, int len);
const uint8_t* memstr(const uint8_t* long_str, const char* short_str, int length);
const char* mail_memstr(const char* a,const char* b,int len);

const uint8_t* memstr_reverse(const uint8_t* long_str, const char* short_str, int length);

unsigned long get_file_size(const char *path);

int dpi_reported_length_left(int        payload_len,  int offset);

uint64_t  get_uint64_ntohll(uint64_t val);

void mkdirs(const char *dir);
int is_file_exist(const char *filepath);

int discern_filetype(const uint8_t* payload,int payload_len,char* filename,int max_filename_len);

const char *filetype(const char*p, int l);

const char *time_to_datetime(time_t t);
const char *time_to_date(time_t t);

void split(char *src, const char *separator, char **dest, int *num);

int bintoMAC(const unsigned char *i, size_t l, char *o, size_t s);
int bintohex(const unsigned char *i, size_t l, char *o, size_t s);
int bintohexlow(const char *i, size_t l, char *o, size_t s);

int get_data_key_value(const char *ptr, int len, const char *key, char *end,char **value_ptr, int *value_len);


char * yv_strupr(char *str);    // 字符串转大写
char * yv_strlowr(char *str);   // 字符串转小写
int hex2bin(const unsigned char *i, size_t l, unsigned char *o, size_t s);

/**
* 线程绑定核心
* @param th  pthread_self()
* @param core_id cpu core id
**/
bool dpi_pthread_setaffinity(pthread_t th, uint8_t core_id);
#endif

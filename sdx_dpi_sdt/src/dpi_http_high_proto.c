#include "dpi_high_app_protos.h"

#include <glib.h>
#include <assert.h>

#include "dpi_http.h"

#define value_and_length(__str) __str, strlen(__str)

typedef struct http_info http_request_info;

static GRegex *uri_pattern;
static GRegex *UA_pattern;
static GRegex *host_pattern;


static int8_t
init_regex_patterns(void)
{
    GError  *err = NULL;

do {
    uri_pattern = g_regex_new("^/(?:mmtls)|(?:mmcrhead)", 0, 0, &err);
    if (uri_pattern == NULL)
        break;

    UA_pattern = g_regex_new("^(?:WeChat/)|(?:MicroMessenger Client)", 0, 0, &err);
    if (UA_pattern == NULL)
        break;

    host_pattern = g_regex_new("(?:^wx.*\\.cn)|(?:weixin\\.qq\\.com$)", 0, 0, &err);
    if (host_pattern == NULL)
        break;

    return 0;
} while(0);

    printf("Error: %s::%d %s\n", __func__, __LINE__, err->message);
    g_error_free(err);
    return -1;
}

static void
destory_regex_patterns(void)
{
    if (uri_pattern)
        g_regex_unref(uri_pattern);
    if (UA_pattern)
        g_regex_unref(UA_pattern);
    if (host_pattern)
        g_regex_unref(host_pattern);
}


static int8_t
http_is_wexin(http_request_info *line_info)
{
    struct header_value *value;

    if (line_info->uri_val_ptr && line_info->uri_val_len > 0)
    {
        if (g_regex_match_full(uri_pattern,
                (const gchar*)line_info->uri_val_ptr, line_info->uri_val_len,
                0, 0, NULL, NULL))
            return 1;
    }

    value = (struct header_value*)g_hash_table_lookup(line_info->table, "user-agent");
    if (value)
    {
        if (g_regex_match_full(UA_pattern,
                (const gchar*)value->ptr, value->len, 0, 0, NULL, NULL))
            return 1;
    }

    value = (struct header_value*)g_hash_table_lookup(line_info->table, "host");
    if (value)
    {
        if (g_regex_match_full(host_pattern,
                (const gchar*)value->ptr, value->len, 0, 0, NULL, NULL))
            return 1;
    }

    return 0;
}

static int8_t
http_is_skype(http_request_info *line_info)
{
    UNUSED(line_info);
    return 0;
}



int high_app_proto_http_init(void)
{
    return init_regex_patterns();
}

void high_app_proto_http_destory(void)
{
    destory_regex_patterns();
}



int high_app_proto_http_identify(struct flow_info *flow, void *value_info)
{
    http_request_info* line_info;

    assert(UA_pattern && uri_pattern && host_pattern);

    if (flow->high_app_proto_id != HIGH_PROTO_UNKNOWN)
        return flow->high_app_proto_id;

    line_info = (http_request_info*)value_info;

    if (http_is_wexin(line_info))
    {
        flow->high_app_proto_id = HIGH_PROTO_WEIXIN;
    }
    else
    if (http_is_skype(line_info))
    {
        flow->high_app_proto_id = HIGH_PROTO_SKYPE;
    }

    return flow->high_app_proto_id;
}

/****************************************************************************************
 * 文 件 名 : dpi_gtp_control.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh              2018/08/27
编码: liugh            2018/08/27
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>


#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"

#include "dpi_gtp_control.h"

static uint8_t gtp_version = 0;

/* bcd decode */
static const dgt_set_t Dgt1_9_bcd = {
  {
      /*  0   1   2   3   4   5   6   7   8   9   a   b   c   d   e  f*/
      '0','1','2','3','4','5','6','7','8','9','?','?','?','?','?','?'
  }
};


void 
payload_to_ipv6(const uint8_t *payload, uint32_t offset, char *ipv6)
{
    int i;
    char forwardip6[32];
    for (i = 0; i < 16; i+=2)
    {
        sprintf(forwardip6, "%02x", get_uint16_t(payload, offset + i));
        snprintf(ipv6 + strlen(ipv6), sizeof(forwardip6),"%s:", forwardip6);
    }

    /*去掉最后一个冒号*/
    int len = strlen(ipv6);
    if (ipv6[len - 1] == ':')
        ipv6[len - 1] = '\0';
}


/* Cause */
/* GPRS:        9.60 v7.6.0, chapter
 * UMTS:        29.060 v4.0, chapter
 * 7.7.1 Cause
 */
static struct int_to_string gtp_cause[] = {
    { 128, "Request accepted"},
    { 0,   NULL},    
};
static int
decode_gtp_cause(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length,  gtp_info_t *gtp_info)
{
    const char* tmp = val_to_string(payload[offset + 1], gtp_cause); 
    gtp_info->ie_array[EM_GTP_CONTROL_CAUSE].data   = tmp ? (const uint8_t*)tmp : NULL;
    gtp_info->ie_array[EM_GTP_CONTROL_CAUSE].length = tmp ? strlen(tmp) : 0;

    return 2;
}

/* imsi */
/* GPRS:        9.60 v7.6.0, chapter 7.9.2
 * UMTS:        29.060 v4.0, chapter 7.7.2
 */
static int
decode_gtp_imsi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    /* const gchar *imsi_str; */

    /* Octets 2 - 9 IMSI */
    /* imsi_str = */ 
    gtp_info->dev_flag = 1;

    length = 8;
    dissect_gtpv2_imsi(payload, payload_len,offset+1,length,gtp_info);

    return 9;
}

static int
decode_gtp_rai(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    uint16_t lac, rac;
    uint16_t tmp_len = 0;
    if(offset + 7 > payload_len)
        return -1;

    dissect_mcc_mnc(payload, offset+ 1, 3, gtp_info);
    lac = get_uint16_ntohs(payload, offset+4);
    rac = payload[offset+6];
    snprintf((char *)gtp_info->rai,MR_FIELD_LITTLE_LEN,"LAC 0x%x RAC 0x%x",lac,rac);
    gtp_info->ie_array[EM_GTP_CONTROL_RAI].data=gtp_info->rai;
    gtp_info->ie_array[EM_GTP_CONTROL_RAI].length=strlen((char *)gtp_info->rai);
    return 7;
#if 0
    length = 6;
    
    int i=0;
    uint8_t octet;
    uint32_t    t_length;
    const dgt_set_t *dgt;

    dgt=&Dgt1_9_bcd;

    if(length>MR_FIELD_VALUE_LEN){
        length=MR_FIELD_VALUE_LEN;
    }
    
    t_length=offset+length;
    if(t_length>payload_len){return -1;}
    
    while(offset < t_length){
        
        octet=get_uint8_t(payload, offset);
        gtp_info->rai[i] = dgt->out[octet & 0x0f];
        i++;

        octet=(octet >> 4);
        if(offset == t_length-1 && octet == 0x0f){
            break;
        }   

        gtp_info->rai[i] = dgt->out[octet & 0x0f];
        i++;
        offset++;
    }

    gtp_info->rai[i]= '\0';
    
    gtp_info->ie_array[EM_GTP_CONTROL_RAI].data = gtp_info->rai;
    if(length*2-1>MR_FIELD_VALUE_LEN){
        gtp_info->ie_array[EM_GTP_CONTROL_RAI].length=MR_FIELD_VALUE_LEN-1;
    }else{
        gtp_info->ie_array[EM_GTP_CONTROL_RAI].length=length*2-1;
    }
#endif
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.4, page 39
 * UMTS:        29.060 v4.0, chapter 7.7.4 Temporary Logical Link Identity (TLLI)
 */
static int
decode_gtp_tlli(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 5;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.5, page 39
 * UMTS:        29.060 v4.0, chapter 7.7.5 Packet TMSI (P-TMSI)
 */
static int
decode_gtp_ptmsi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 5;
}

/*
 * adjust - how many bytes before offset should be highlighted
 */
static int
decode_qos_gprs(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 3;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.6, page 39
 *              4.08
 *              3.60
 * UMTS:        not present
 * TODO:        check if length is included: ETSI 4.08 vs 9.60
 */
static int
decode_gtp_qos_gprs(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    //return (1 + decode_qos_gprs(tvb, offset + 1, tree, "Quality of Service", 1));
    return 4;

}

/* GPRS:        9.60 v7.6.0, chapter 7.9.7, page 39
 * UMTS:        29.060 v4.0, chapter 7.7.6 Reordering Required
 */
static int
decode_gtp_reorder(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 2;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.8, page 40
 *              4.08 v7.1.2, chapter 10.5.3.1+
 * UMTS:        29.060 v4.0, chapter 7.7.7
 * TODO: Add blurb support by registering items in the protocol registration
 */
static int
decode_gtp_auth_tri(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 1 + 16 + 4 + 8;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.9, page 40
 *              9.02 v7.7.0, page 1090
 * UMTS:        29.060 v4.0, chapter 7.7.8, page 48
 *              29.002 v4.2.1, chapter 17.5, page 268
 */
static int
decode_gtp_map_cause(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 2;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.10, page 41
 * UMTS:        29.060 v4.0, chapter 7.7.9, page 48
 */
static int
decode_gtp_ptmsi_sig(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 4;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.11, page 41
 * UMTS:        29.060 v4.0, chapter 7.7.10, page 49
 */
static int
decode_gtp_ms_valid(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{



    return 2;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.12, page 41
 * UMTS:        29.060 v4.0, chapter 7.7.11 Recovery
 */
static int
decode_gtp_recovery(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    return 2;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.13, page 42
 * UMTS:        29.060 v4.0, chapter 7.7.12 Selection Mode
 */
static int
decode_gtp_sel_mode(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    return 2;
}


static int
decode_gtp_16(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    uint32_t teid_data;
    

    switch (gtp_version)
    {
    case 0:
        
        return 3;
    case 1:
        teid_data =  get_uint32_ntohl(payload, offset + 1);
        gtp_info->ie_array[EM_GTP_CONTROL_TEIDDATAI].length=teid_data;
        return 5;
    default:
        return 3;
    }

    //return 3;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.15, page 42
 * UMTS:        29.060 v4.0, chapter 7.7.14, page 42
 */
static int
decode_gtp_17(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    switch (gtp_version) {
    case 0:
        return 3;
    case 1:
        return 5;
    default:
        return 3;
    }
}


static int
decode_gtp_18(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    uint32_t teid_dataii;

    switch (gtp_version)
    {
    case 0:
        
        return 4;
    case 1:
        teid_dataii =  get_uint32_ntohl(payload, offset + 2);
        gtp_info->ie_array[EM_GTP_CONTROL_TEIDDATAI].length=teid_dataii;
        return 6;
    default:
        return 4;
    }

    //return 3;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.16A, page 43
 * UMTS:        29.060 v4.0, chapter 7.7.16, page 51
 * Check if all ms_reason types are included
 */
static int
decode_gtp_19(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    return 2;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.17, page 51
 */
static int
decode_gtp_nsapi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    gtp_info->ie_array[EM_GTP_CONTROL_NSAPI].length = get_uint8_t(payload, offset+1) & 0x0F;
    return 2;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.18, page 52
 */
static int
decode_gtp_ranap_cause(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 2;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.19, page 52
 */
static int
decode_gtp_rab_cntxt(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    return 10;
}


/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.20, page 53
 */
static int
decode_gtp_rp_sms(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 2;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.21, page 53
 */
static int
decode_gtp_rp(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 2;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.22, page 53
 */
static int
decode_gtp_pkt_flow_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    return 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.23, page 53
 * TODO: Differenciate these uints?
 */
static int
decode_gtp_chrg_char(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    return 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.24, page
 */
static int
decode_gtp_trace_ref(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.25, page
 */
static int
decode_gtp_trace_type(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 3;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.16A
 * UMTS:        29.060 v4.0, chapter 7.7.25A, page
 */
static int
decode_gtp_ms_reason(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 2;
}

/* GPRS:        12.15 v7.6.0, chapter 7.3.3, page 45
 * UMTS:        33.015
 */
static int
decode_gtp_tr_comm(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{


    return 2;
}

/* ChargingID  */
/* GPRS:        9.60 v7.6.0, chapter 7.9.17, page 43
 * UMTS:        29.060 v4.0, chapter 7.7.26, page 55
 */
static int
decode_gtp_chrg_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length,  gtp_info_t *gtp_info)
{
    if(offset+length>payload_len || length <4){return 0;}
    length = 8;

    //uint32_t chrg_id;

    gtp_info->chrg_id = ntohl(get_uint32_t(payload, offset));
    //gtp_info->ie_array[EM_GTP_CONTROL_CHARGINGID].data=&payload[offset];
    //gtp_info->ie_array[EM_GTP_CONTROL_CHARGINGID].data = &gtp_info->chrg_id;
    gtp_info->ie_array[EM_GTP_CONTROL_CHARGINGID].length= gtp_info->chrg_id;

    return 5;
}

/* EndUserAddressIPv4 EndUserAddressIPv6 */
/* GPRS:        9.60 v7.6.0, chapter 7.9.18, page 43
 * UMTS:        29.060 v4.0, chapter 7.7.27, page 55
 */
static int
decode_gtp_user_addr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len || length<2){return 0;}
    uint8_t           pdp_typ, pdp_org;

    pdp_org = get_uint8_t(payload, offset) & 0x0F;
    offset++;
    
    pdp_typ = get_uint8_t(payload, offset);
    offset++;

     if (length > 2) {
        switch (pdp_typ) {
        case 0x21:
            if(length<6){return 0;}
            gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV4].data = &payload[offset];
            gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV4].length = 4;
            break;
        case 0x57:
            if(length<18){return 0;}
            gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV6].data = &payload[offset];
            gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV6].length = 16;
            break;
        case 0x8d:
            if (length == 6) {
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV4].data = &payload[offset];
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV4].length = 4;
            } else if (length == 18) {
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV6].data = &payload[offset];
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV6].length = 16;
            } else if (length == 22) {
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV4].data = &payload[offset];
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV4].length = 4;
                offset+=4;
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV6].data = &payload[offset];
                gtp_info->ie_array[EM_GTP_CONTROL_ENDUSERADDRESSIPV6].length = 16;
            }
            break;
        }
    }

    return length + 3;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.19 page
 * UMTS:        29.060 v4.0, chapter 7.7.28 page 57
 * TODO:        - check if for quintuplets first 2 bytes are length, according to AuthQuint
 *              - finish displaying last 3 parameters
 */
static int
decode_gtp_mm_cntxt(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return length + 3;
}


static int
decode_gtp_pdp_cntxt(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    uint8_t pdb_typ_num;
    uint8_t pdb_addr_len;
    uint8_t ggsn_addr_len;
    uint8_t retlength = 0;
    char controlip4[32];
    char controlip6[64];
    char userip4[32];
    char userip6[64];
    
    length = get_uint16_ntohs(payload, offset + 1);

    switch (gtp_version)
    {
    case 0:
        offset = offset + 14;
        break;
    case 1:
        offset = offset + 5;
        //qos subscribed
        offset = offset + get_uint8_t(payload, offset) + 1;
        //qos requested
        offset = offset + get_uint8_t(payload, offset) + 1;
        //qos negotiated
        offset = offset + get_uint8_t(payload, offset) + 1;
        break;
    default:
        break;  
    }

    switch (gtp_version)
    {
    case 0:
        offset = offset + 8;
        break;
    case 1:
        offset = offset + 15;
        break;
    default:
        break;
    }

    pdb_typ_num = get_uint8_t(payload, offset + 1);
    pdb_addr_len = get_uint8_t(payload, offset + 2);

    if (pdb_addr_len > 0)
    {
        switch (pdb_typ_num)
        {
        case 0x21:
            break;
        case 0x57:
            break;
        }
    }

    offset = offset + 3 + pdb_addr_len;

    ggsn_addr_len = get_uint8_t(payload, offset);

    switch (ggsn_addr_len)
    {
    case 4:
        sprintf(gtp_info->controlip4, "%u.%u.%u.%u", payload[offset], payload[offset + 1], payload[offset + 2], payload[offset + 3] );
        gtp_info->ie_array[EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV4].data=(const uint8_t *)gtp_info->controlip4;
        gtp_info->ie_array[EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV4].length=strlen(gtp_info->controlip4);
        break;
    case 16:
        payload_to_ipv6(payload, offset, gtp_info->controlip6);
        gtp_info->ie_array[EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV6].data=(const uint8_t *)gtp_info->controlip6;
        gtp_info->ie_array[EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV6].length=strlen(gtp_info->controlip6);
        break;
    default:
        break;
    }

    offset = offset + 1 + ggsn_addr_len;

    if (gtp_version == 1)
    {
        ggsn_addr_len = get_uint8_t(payload, offset);

        switch (ggsn_addr_len)
        {
        case 4:
            sprintf(gtp_info->userip4, "%u.%u.%u.%u", payload[offset], payload[offset + 1], payload[offset + 2], payload[offset + 3] );
            gtp_info->ie_array[EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV4].data=(const uint8_t *)gtp_info->userip4;
            gtp_info->ie_array[EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV4].length=strlen(gtp_info->userip4);
            break;
        case 16:
            payload_to_ipv6(payload, offset, gtp_info->userip6);
            gtp_info->ie_array[EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV6].data=(const uint8_t *)gtp_info->userip6;
            gtp_info->ie_array[EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV6].length=strlen(gtp_info->userip6);
            break;
        default:
            break;
        }
    }

    return length + 3;
}

/*  apn */
/* GPRS:        9.60, v7.6.0, chapter 7.9.21
 * UMTS:        29.060, v4.0, chapter 7.7.30
 */
static int
decode_gtp_apn(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    char apn[32];
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len || length < 2 )
        return 0;
    offset += 3;
    uint8_t    curr_len;
    int i = 0;
    curr_len = get_uint8_t(payload,offset);
    offset++;
//    snprintf((char *)gtp_info->apn,length,"%s",&payload[offset]);
    while(curr_len && curr_len + 1 <= length)
        {
        memcpy(gtp_info->apn + i, payload+offset, i + curr_len > MR_FIELD_LITTLE_LEN ? 0 : curr_len);
        offset += curr_len;
        i += curr_len;
        if(i + 1 < MR_FIELD_LITTLE_LEN){
            gtp_info->apn[i++] = '.';
        }
        else break;
        curr_len = get_uint8_t(payload,offset);
        offset += 1;
    }
    gtp_info->ie_array[EM_GTP_CONTROL_APN].data   = gtp_info->apn;
    gtp_info->ie_array[EM_GTP_CONTROL_APN].length = i > 1 ? i - 1 : 0;

    return length + 3;
}

/* GPRS:        9.60 v7.6.0, chapter 7.9.22
 *              4.08 v. 7.1.2, chapter ******** (p.580)
 * UMTS:        29.060 v4.0, chapter 7.7.31 Protocol Configuration Options
 *              24.008, v4.2, chapter ********
 */
static int
decode_gtp_proto_conf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}


/* GSNaddressIPv4 */
/* GPRS:        9.60 v7.6.0, chapter 7.9.23
 * UMTS:        29.060 v4.0, chapter 7.7.32
 */
static int
decode_gtp_gsn_addr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len){return 0;}

    uint8_t               addr_type, addr_len;
    char ipv6[32];

    offset += 3;
    switch (length) {
    case 4:
        sprintf(gtp_info->gsnaddress, "%u.%u.%u.%u", payload[offset], payload[offset + 1], payload[offset + 2], payload[offset + 3]);
        //gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV4].data=&payload[offset];
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV4].data =(const uint8_t *)gtp_info->gsnaddress;
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV4].length=strlen(gtp_info->gsnaddress);
        break;
    case 5:
        sprintf(gtp_info->gsnaddress, "%u.%u.%u.%u", payload[offset + 1], payload[offset + 2], payload[offset + 3], payload[offset + 4]);
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV4].data=(const uint8_t *)gtp_info->gsnaddress;
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV4].length=strlen(gtp_info->gsnaddress);
        break;
    case 16:
        payload_to_ipv6(payload, offset, gtp_info->gsnaddress);
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV6].data=(const uint8_t *)gtp_info->gsnaddress;
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV6].length=strlen(gtp_info->gsnaddress);
        break;
    case 17:
        payload_to_ipv6(payload, offset + 1, gtp_info->gsnaddress);
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV6].data=(const uint8_t *)gtp_info->gsnaddress;
        gtp_info->ie_array[EM_GTP_CONTROL_GSNADDRESSIPV6].length=strlen(gtp_info->gsnaddress);
        break;
    #if 0
    case 5:
        proto_tree_add_item(ext_tree_gsn_addr, hf_gtp_gsn_address_information_element_length, tvb, offset + 1, 2, ENC_BIG_ENDIAN);
        addr_type = tvb_get_guint8(tvb, offset + 3) & 0xC0;
        addr_type = get_uint8_t(payload,offset)
        proto_tree_add_uint(ext_tree_gsn_addr, hf_gtp_gsn_addr_type, tvb, offset + 3, 1, addr_type);
        addr_len = tvb_get_guint8(tvb, offset + 3) & 0x3F;
        proto_tree_add_uint(ext_tree_gsn_addr, hf_gtp_gsn_addr_len, tvb, offset + 3, 1, addr_len);
        proto_tree_add_item(ext_tree_gsn_addr, hf_gtp_gsn_ipv4, tvb, offset + 4, 4, ENC_BIG_ENDIAN);
        proto_item_append_text(te, "%s", tvb_ip_to_str(tvb, offset + 4));
        set_address_tvb(gsn_address, AT_IPv6, 16, tvb, offset + 4);
        break;
    case 16:
        proto_tree_add_item(ext_tree_gsn_addr, hf_gtp_gsn_address_length, tvb, offset + 1, 2, ENC_BIG_ENDIAN);
        proto_tree_add_item(ext_tree_gsn_addr, hf_gtp_gsn_ipv6, tvb, offset + 3, 16, ENC_NA);
        proto_item_append_text(te, "%s", tvb_ip6_to_str(tvb, offset + 3));
        set_address_tvb(gsn_address, AT_IPv4, 4, tvb, offset + 3);
        break;
    case 17:
        proto_tree_add_item(ext_tree_gsn_addr, hf_gtp_gsn_address_information_element_length, tvb, offset + 1, 2, ENC_BIG_ENDIAN);
        addr_type = tvb_get_guint8(tvb, offset + 3) & 0xC0;
        proto_tree_add_uint(ext_tree_gsn_addr, hf_gtp_gsn_addr_type, tvb, offset + 3, 1, addr_type);
        addr_len = tvb_get_guint8(tvb, offset + 3) & 0x3F;
        proto_tree_add_uint(ext_tree_gsn_addr, hf_gtp_gsn_addr_len, tvb, offset + 3, 1, addr_len);
        proto_item_append_text(te, "%s", tvb_ip6_to_str(tvb, offset + 4));
        proto_tree_add_item(ext_tree_gsn_addr, hf_gtp_gsn_ipv6, tvb, offset + 4, 16, ENC_NA);
        set_address_tvb(gsn_address, AT_IPv6, 16, tvb, offset + 4);
        break;
    #endif
    default:
        break;
    }


    return length + 3;
}

static int
decode_gtp_msisdn(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    gtp_info->dev_flag = 1;
    length = get_uint16_ntohs(payload, offset + 1);
    dissect_gtpv2_msisdn(payload, payload_len,offset + 4,length-1,gtp_info);
    return length + 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.34
 *              24.008 v4.2, chapter ********
 */
static int
decode_gtp_qos_umts(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);


    return length + 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.35
 */
static int
decode_gtp_auth_qui(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);


    return (3 + length);

}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.36
 *              24.008 v4.2, chapter 10.5.6.12
 */
static int
decode_gtp_tft(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}



/* MCC MNC  LAC  RAC */
/* GPRS:        not present
 * UMTS:        3GPP TS 29.060 version 10.4.0 Release 10, chapter 7.7.37
 * Type = 138 (Decimal)
 *              25.413(RANAP) TargetID
 * There are several CRs to to this IE make sure to check with a recent spec if dissection is questioned.
 */
static int
decode_gtp_target_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    if (length+offset>payload_len || length < 1){
        return 0;
    }

    /* Quote from specification:
     * The Target Identification information element contains the identification of a target RNC. Octets 4-n shall contain a
     * non-transparent copy of the corresponding IEs (see subclause 7.7.2) and be encoded as specified in Figure 51 below.
     * The "Target RNC-ID" part of the "Target ID" parameter is specified in 3GPP TS 25.413 [7].
     * NOTE 1: The ASN.1 parameter "Target ID" is forwarded non-transparently in order to maintain backward compatibility.
     * NOTE 2: The preamble of the "Target RNC-ID" (numerical value of e.g. 0x20) however shall not be included in
     *         octets 4-n. Also the optional "iE-Extensions" parameter shall not be included into the GTP IE.
     */
    /* Octet 1-3 MCC + MNC */
    if (length == 9) {
        /* Patch for systems still not following NOTE 2 */
        
        offset+=1;
        dissect_mcc_mnc(payload, offset, length,gtp_info);
    } else {
        /* Following Standards */
        dissect_mcc_mnc(payload, offset, length,gtp_info);
    }
    offset+=3;

    /* Octet 4-5 LAC */
    gtp_info->lac=ntohl(get_uint16_t(payload,offset));
    offset+=2;
    
    /* Octet 6 RAC */
    gtp_info->rac=get_uint8_t(payload,offset);

    /* Octet 7-8 RNC-ID*/


    return length + 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.38
 */
static int
decode_gtp_utran_cont(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* RNCaddressIPv4 */
/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.39
 */
static int
decode_gtp_rab_setup(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len){return 0;}
    uint32_t            teid;

    if (length > 1) {
        if(length<4){return 0;}
        teid = ntohl(get_uint32_t(payload, offset));
        offset+=4;
        switch (length) {
        case 9:
            gtp_info->ie_array[EM_GTP_CONTROL_RNCADDRESSIPV4].data=&payload[offset];
            gtp_info->ie_array[EM_GTP_CONTROL_RNCADDRESSIPV4].length=4;
            break;
        /*
        case 21:
            gtp_info->ie_array[EM_RNC_ADDRESS_IPV6].data=&payload[offset];
            gtp_info->ie_array[EM_RNC_ADDRESS_IPV6].length=16;
            break;
        */
        default:
            break;
        }
    }

    return length + 3;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.40
 */
static int
decode_gtp_hdr_list(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    uint8_t len;

    len = get_uint8_t(payload, offset + 1);


    return 2 + len;
}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.41
 * TODO:        find TriggerID description
 */
static int
decode_gtp_trigger_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        not present
 * UMTS:        29.060 v4.0, chapter 7.7.42
 * TODO:        find OMC-ID description
 */
static int
decode_gtp_omc_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 V9.4.0, chapter 7.7.43 RAN Transparent Container
 * The information in the value part of the RAN Transparent Container IE contains all information elements (starting with
 * and including the BSSGP "PDU Type") in either of the RAN INFORMATION, RAN INFORMATION REQUEST,
 * RAN INFORMATION ACK or RAN INFORMATION ERROR messages respectively as specified in 3GPP TS 48.018
 */
static int
decode_gtp_ran_tr_cont(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.45 PDP Context Prioritization
 */
static int
decode_gtp_pdp_cont_prio(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.45A Additional RAB Setup Information
 */
static int
decode_gtp_add_rab_setup_inf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}


 /* GPRS:       ?
  * UMTS:       29.060 v6.11.0, chapter 7.7.47 SGSN Number
  */
static int
decode_gtp_ssgn_no(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        3GPP TS 29.060 version 7.8.0 Release 7, chapter 7.7.48 Common Flags
 */
static int
decode_gtp_common_flgs(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.49
 */
static int
decode_gtp_apn_res(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    return 3 + length;
}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.25B
 * Radio Priority LCS
 */
static int
decode_gtp_ra_prio_lcs(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}


/* RAT type */
/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.50 RAT Type
 * RAT Type
 * Type = 151 (Decimal)
 */
static int
decode_gtp_rat_type(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len || length <1){return 0;}

    gtp_info->rat_type=get_uint8_t(payload,offset);

    return length + 3;
}

static int
decode_gtp_usr_loc_inf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    //offset++;
    //proto_tree_add_item(ext_tree, hf_gtp_ext_length, tvb, offset, 2, ENC_BIG_ENDIAN);
    //offset = offset + 2;

    //dissect_gtp_uli(tvb, offset, pinfo, ext_tree, args);

    return 3 + length;
}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.52
 * MS Time Zone
 * Type = 153 (Decimal)
 * The ' MS Time Zone' IE is used to indicate the offset between universal time and local time
 * in steps of 15 minutes of where the MS currently resides. The 'Time Zone' field uses the same
 * format as the 'Time Zone' IE in 3GPP TS 24.008 (10.5.3.8)
 * its value shall be set as defined in 3GPP TS 22.042
 */
static int
decode_gtp_ms_time_zone(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/* cgi sai rai */
static int
dissect_gtp_uli(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    gtp_info->uli_flag = 1;
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len || length<8){return -1;}
    uint8_t      geo_loc_type;

    uint16_t    lac,ci,sac,rac;
    /* Geographic Location Type */
    offset += 3;
    geo_loc_type = get_uint8_t(payload, offset);
    offset++;

    switch(geo_loc_type) {
        case 0:
            /* Geographic Location field included and it holds the Cell Global
             * Identification (CGI) of where the user currently is registered.
             * CGI is defined in sub-clause 4.3.1 of 3GPP TS 23.003 [2].
             */
            /* mcc mnc */
            dissect_mcc_mnc(payload, offset, length,gtp_info);
            offset+=3;
            lac=get_uint16_ntohs(payload,offset);
            offset+=2;
            ci=get_uint16_ntohs(payload,offset);
            offset+=2;
            
            gtp_info->cell_lac=lac;
            gtp_info->cell_ci=ci;
            gtp_info->ie_array[EM_GTP_CONTROL_CGI_MCC].length = gtp_info->mcc;
           // gtp_info->ie_array[EM_GTP_CONTROL_CGI_MNC].length = gtp_info->mnc;
            snprintf(gtp_info->cgi_mnc, 4, "%u%u", gtp_info->mnc & 0x02, gtp_info->mnc & 0x01);
            gtp_info->ie_array[EM_GTP_CONTROL_CGI_LAC].length = lac; 
            gtp_info->ie_array[EM_GTP_CONTROL_CGI_CI].length  = ci;

            snprintf((char *)gtp_info->cgi,MR_FIELD_LITTLE_LEN,"LAC 0x%x CI 0x%x",lac,ci);
            gtp_info->ie_array[EM_GTP_CONTROL_CGI].data=gtp_info->cgi;
            gtp_info->ie_array[EM_GTP_CONTROL_CGI].length=strlen((char *)gtp_info->cgi);
            break;
        case 1:
            /* Geographic Location field included and it holds the Service
             * Area Identity (SAI) of where the user currently is registered.
             * SAI is defined in sub-clause 9.2.3.9 of 3GPP TS 25.413 [7].
             */
            dissect_mcc_mnc(payload, offset, length,gtp_info);
            offset+=3;
            lac=get_uint16_ntohs(payload,offset);
            offset+=2;
            sac=get_uint16_ntohs(payload,offset);
            offset+=2;        
              
            snprintf((char *)gtp_info->sai,MR_FIELD_LITTLE_LEN,"LAC 0x%x SAC 0x%x",lac,sac);
            gtp_info->ie_array[EM_GTP_CONTROL_SAI_MCC].length = gtp_info->mcc;
            gtp_info->ie_array[EM_GTP_CONTROL_SAI_MNC].length = gtp_info->mnc;
            gtp_info->ie_array[EM_GTP_CONTROL_SAI_LAC].length = lac;
            gtp_info->ie_array[EM_GTP_CONTROL_SAC].length     = sac;

            gtp_info->ie_array[EM_GTP_CONTROL_SAI].data=gtp_info->sai;
            gtp_info->ie_array[EM_GTP_CONTROL_SAI].length=strlen((char *)gtp_info->sai);
            break;
        case 2:
            /* Geographic Location field included and it holds the Routing
             * Area Identification (RAI) of where the user currently is
             * registered. RAI is defined in sub-clause 4.2 of 3GPP TS 23.003
             * [2].
             */
            dissect_mcc_mnc(payload, offset, length,gtp_info);
            offset+=3;
            lac=ntohl(get_uint16_t(payload,offset));
            offset+=2;
            rac=ntohl(get_uint16_t(payload,offset));
            offset+=2;    
            snprintf((char *)gtp_info->rai,MR_FIELD_LITTLE_LEN,"LAC 0x%x RAC 0x%x",lac,rac);
            gtp_info->ie_array[EM_GTP_CONTROL_RAI].data=gtp_info->rai;
            gtp_info->ie_array[EM_GTP_CONTROL_RAI].length=strlen((char *)gtp_info->rai);
            break;
        default:
            break;
    }

    return length + 3;
}



/*  imei  */
/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.53
 * International Mobile Equipment Identity (and Software Version) (IMEI(SV))
 * Type = 154 (Decimal)
 */
static int decode_gtp_imeisv(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    gtp_info->dev_flag = 1;
    length = get_uint16_ntohs(payload, offset + 1);
    dissect_gtpv2_mei(payload, payload_len,offset+3,length,gtp_info);

    return length + 3;
}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.54
 * CAMEL Charging Information Container
 * Type = 155 (Decimal)
 */
static int
decode_gtp_camel_chg_inf_con(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.55
 * MBMS UE Context
 */
static int
decode_gtp_mbms_ue_ctx(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        3GPP TS 29.060 version 7.8.0 Release 7, chapter 7.7.56
 * Temporary Mobile Group Identity (TMGI)
 * The Temporary Mobile Group Identity (TMGI) information element contains
 * a TMGI allocated by the BM-SC. It is coded as in the value part defined
 * in 3GPP T S 24.008 [5] (i.e. the IEI and octet length indicator are not included).
 */

static int
decode_gtp_tmgi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.57
 * RIM Routing Address
 */
static int
decode_gtp_rim_ra(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.58
 * MBMS Protocol Configuration Options
 */
static int
decode_gtp_mbms_prot_conf_opt(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

static int
decode_gtp_mbms_sa(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.61
 * Source RNC PDCP context info
 */
static int
decode_gtp_src_rnc_pdp_ctx_inf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.62
 * Additional Trace Info
 */
static int
decode_gtp_add_trs_inf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.63
 * Hop Counter
 */
static int
decode_gtp_hop_count(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.64
 * Selected PLMN ID
 */
static int
decode_gtp_sel_plmn_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.65
 * MBMS Session Identifier
 */
static int
decode_gtp_mbms_ses_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.66
 * MBMS 2G/3G Indicator
 */
 /*
static const value_string gtp_mbs_2g_3g_ind_vals[] = {
    {0, "2G only"},
    {1, "3G only"},
    {2, "Both 2G and 3G"},
    {0, NULL}
};
    */

static int
decode_gtp_mbms_2g_3g_ind(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.67
 * Enhanced NSAPI
 */
static int
decode_gtp_enh_nsapi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

static int
decode_gtp_mbms_ses_dur(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    /* The MBMS Session Duration is defined in 3GPP TS 23.246 [26].
     * The MBMS Session Duration information element indicates the estimated
     * session duration of the MBMS service data transmission if available.
     * The payload shall be encoded as per the MBMS-Session-Duration AVP defined
     * in 3GPP TS 29.061 [27], excluding the AVP Header fields
     * (as defined in IETF RFC 3588 [36], section 4.1).
     */
    /* The MBMS-Session-Duration AVP (AVP code 904) is of type OctetString
     * with a length of three octets and indicates the estimated session duration
     * (MBMS Service data transmission). Bits 0 to 16 (17 bits) express seconds, for which the
     * maximum allowed value is 86400 seconds. Bits 17 to 23 (7 bits) express days,
     * for which the maximum allowed value is 18 days. For the whole session duration the seconds
     * and days are added together and the maximum session duration is 19 days.
     */

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.68
 * Additional MBMS Trace Info
 */
static int
decode_gtp_add_mbms_trs_inf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.69
 * MBMS Session Identity Repetition Number
 */
static int
decode_gtp_mbms_ses_id_rep_no(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        3GPP TS 29.060 version 7.8.0 Release 7
 * MBMS Time To Data Transfer
 */
/* Used for Diameter */

static int
decode_gtp_mbms_time_to_data_tr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    /* TODO add decoding of data
     * The MBMS Time To Data Transfer is defined in 3GPP TS 23.246 [26].
     * The MBMS Time To Data Transfer information element contains a
     * MBMS Time To Data Transfer allocated by the BM-SC.
     * The payload shall be encoded as per the MBMS-Time-To-Data-Transfer AVP
     * defined in 3GPP TS 29.061 [27], excluding the AVP Header fields
     * (as defined in IETF RFC 3588 [36], section 4.1).
     */
    /* The coding is specified as per the Time to MBMS Data Transfer Value Part Coding
     * of the Time to MBMS Data Transfer IE in 3GPP TS 48.018
     * Bits
     * 8 7 6 5 4 3 2 1
     * 0 0 0 0 0 0 0 0 1s
     * 0 0 0 0 0 0 0 1 2s
     * 0 0 0 0 0 0 1 0 3s
     * :
     * 1 1 1 1 1 1 1 1 256s
     */


    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.71
 * PS Handover Request Context
 */
static int
decode_gtp_ps_ho_req_ctx(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.72
 * BSS Container
 */
static int
decode_gtp_bss_cont(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* CellID  */
/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.73
 * Cell Identification
 */
static int
decode_gtp_cell_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    /*
     * for PS handover from A/Gb mode, the identification of a target cell (Cell ID 1) and the identification of the
     * source cell (Cell ID 2) as defined in 3GPP TS 48.018 [20].
     *
     * for PS handover from Iu mode, the identification of a target cell (Cell ID 1)) and the identification of the
     * source RNC (RNC-ID) as defined in 3GPP TS 48.018
     */
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len || length <1){return 0;}
    gtp_info->ie_array[EM_GTP_CONTROL_CELLID].data=&payload[offset];
    gtp_info->ie_array[EM_GTP_CONTROL_CELLID].length=length;
    
    return length + 3;
}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.74
 * PDU Numbers
 */
static int
decode_gtp_pdu_no(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        ?
 * UMTS:        29.060 v6.11.0, chapter 7.7.75
 * BSSGP Cause
 */
static int
decode_gtp_bssgp_cause(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/*
 * Required MBMS bearer capabilities    7.7.76
 */
static int
decode_gtp_mbms_bearer_cap(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * RIM Routing Address Discriminator    7.7.77
 */
/*
static const value_string gtp_bssgp_ra_discriminator_vals[] = {
    { 0, "A Cell Identifier is used to identify a GERAN cell" },
    { 1, "A Global RNC-ID is used to identify a UTRAN RNC" },
    { 2, "An eNB identifier is used to identify an E-UTRAN eNodeB or HeNB" },
    { 0, NULL }
};
*/
static int
decode_gtp_rim_ra_disc(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}
/*
 * List of set-up PFCs  7.7.78
 */
static int
decode_gtp_lst_set_up_pfc(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}
/*
 * PS Handover XID Parameters   7.7.79
 */
static int
decode_gtp_ps_handover_xid(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 4 + length;

}

/*
 * MS Info Change Reporting Action      7.7.80
 */
static int
decode_gtp_ms_inf_chg_rep_act(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/*
 * Direct Tunnel Flags  7.7.81
 */
static int
decode_gtp_direct_tnl_flg(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}
/*
 * Correlation-ID       7.7.82
 */
static int
decode_gtp_corrl_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

static int
decode_gtp_bearer_cntrl_mod(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/*
 * 7.7.84 MBMS Flow Identifier
 */
static int
decode_gtp_mbms_flow_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);


    return 3 + length;
}

/*
 * 7.7.85 MBMS IP Multicast Distribution
 */

static int
decode_gtp_mbms_ip_mcast_dist(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}


static int
decode_gtp_mbms_dist_ack(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.87 Reliable INTER RAT HANDOVER INFO
 */
static int
decode_gtp_reliable_irat_ho_inf(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.88 RFSP Index
 */
static int
decode_gtp_rfsp_index(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.89 PDP Type
 */
/*
 * 7.7.90 Fully Qualified Domain Name (FQDN)
 */
static int
decode_gtp_fqdn(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}


/*
 * 7.7.91 Evolved Allocation/Retention Priority I
 */
static int
decode_gtp_evolved_allc_rtn_p1(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;


}

/*
 * 7.7.92 Evolved Allocation/Retention Priority II
 */
static int
decode_gtp_evolved_allc_rtn_p2(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;


}

/*
 * 7.7.93 Extended Common Flags
 */
static int
decode_gtp_extended_common_flgs(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.94 User CSG Information (UCI)
 */
static int
decode_gtp_uci(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.95 CSG Information Reporting Action
 */

static int
decode_gtp_csg_inf_rep_act(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.96 CSG ID
 */

static int
decode_gtp_csg_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.97 CSG Membership Indication (CMI)
 */
static int
decode_gtp_cmi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.98 APN Aggregate Maximum Bit Rate (APN-AMBR)
 */
static int
decode_gtp_apn_ambr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.99 UE Network Capability
 */
static int
decode_gtp_ue_network_cap(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.100 UE-AMBR
 */

static int
decode_gtp_ue_ambr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.101 APN-AMBR with NSAPI
 */
static int
decode_gtp_apn_ambr_with_nsapi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.102 GGSN Back-Off Time
 */
/* Table *********: GGSN Back-Off Time information element */ /*
static const value_string gtp_ggsn_back_off_time_units_vals[] = {
    {0, "value is incremented in multiples of 2 seconds"},
    {1, "value is incremented in multiples of 1 minute"},
    {2, "value is incremented in multiples of 10 minutes"},
    {3, "value is incremented in multiples of 1 hour"},
    {4, "value is incremented in multiples of 10 hours"},
    {5, "value indicates that the timer is infinite"},
    {0, NULL}
};*/
static int
decode_gtp_ggsn_back_off_time(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.103 Signalling Priority Indication
 */

static int
decode_gtp_sig_pri_ind(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.104 Signalling Priority Indication with NSAPI
 */

static int
decode_gtp_sig_pri_ind_w_nsapi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.105 Higher bitrates than 16 Mbps flag
 */ /*
static const value_string gtp_higher_br_16mb_flg_vals[] = {
    {0, "Not allowed"},
    {1, "Allowed"},
    {0, NULL}
};*/

static int
decode_gtp_higher_br_16mb_flg(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.106 Max MBR/APN-AMBR
 */

static int
decode_gtp_max_mbr_apn_ambr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.107 Additional MM context for SRVCC
 */

static int
decode_gtp_add_mm_ctx_srvcc(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.108 Additional flags for SRVCC
 */

static int
decode_gtp_add_flgs_srvcc(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.109 STN-SR
 */
static int
decode_gtp_stn_sr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}


/* msisdn */
/*
 * 7.7.110 C-MSISDN
 */
static int
decode_gtp_c_msisdn(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    //dissect_gtpv2_msisdn(payload, payload_len,offset,length,gtp_info);
    return length + 3;
}

/*
 * 7.7.111 Extended RANAP Cause
 */
static int
decode_gtp_ext_ranap_cause(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/* ENODEBID */
static int
decode_gtp_ext_enodeb_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len || length <4 ){return 0;}
    uint32_t enb_type;

    /* eNodeB Type */
    enb_type=get_uint8_t(payload, offset);
    offset++;

    /*mcc mnc*/
    dissect_mcc_mnc(payload, offset, length,gtp_info);
    offset += 3;

    switch (enb_type){
    case 0:
        if(length<7){return 0;}
        /* Macro eNodeB ID */
        break;
    case 1:
        /* Home eNodeB ID */
        if(length<8){return 0;}
        gtp_info->enobe_id=ntohl(get_uint32_t(payload,offset)) & 0x0fffffff;
        offset+=4;

        break;
    default:
        break;
    }

    return length + 3;

}


/*
 * 7.7.113 Selection Mode with NSAPI
 */
static int
decode_gtp_ext_sel_mode_w_nsapi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.114 ULI Timestamp
 */
static int
decode_gtp_ext_uli_timestamp(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.115 Local Home Network ID (LHN-ID) with NSAPI
 */
static int
decode_gtp_ext_lhn_id_w_sapi(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}
/*
 * 7.7.116 CN Operator Selection Entity
 */
static int
decode_gtp_ext_cn_op_sel_entity(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/*
 * 7.7.118 Extended Common Flags II
 */
static int
decode_gtp_extended_common_flgs_II(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}


static int
decode_gtp_rel_pack(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/* GPRS:        12.15
 * UMTS:        33.015
 */
static int
decode_gtp_can_pack(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}


/* CGaddressIPv4 */
/* GPRS:        9.60 v7.6.0, chapter 7.9.25
 * UMTS:        29.060 v6.11.0, chapter 7.7.44 Charging Gateway Address
 */
static int
decode_gtp_chrg_addr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    length = get_uint16_ntohs(payload, offset + 1);
    if(offset+length>payload_len){return 0;}
    switch (length) {
    case 4:
        gtp_info->ie_array[EM_GTP_CONTROL_CGADDRESSIPV4].data=&payload[offset];
        gtp_info->ie_array[EM_GTP_CONTROL_CGADDRESSIPV4].length=4;
        break;
    /*
    case 16:
        gtp_info->ie_array[EM_CG_ADDRESS_IPV6].data=&payload[offset];
        gtp_info->ie_array[EM_CG_ADDRESS_IPV6].length=16;
        break;
    */
    default:
        break;
    }

    return length + 3;;
}

static int
decode_gtp_data_req(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;
}

/* GPRS:        12.15
 * UMTS:        33.015
 */
static int
decode_gtp_data_resp(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        12.15
 * UMTS:        33.015
 */
static int
decode_gtp_node_addr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);

    return 3 + length;

}

/* GPRS:        9.60 v7.6.0, chapter 7.9.26
 * UMTS:        29.060 v4.0, chapter 7.7.46 Private Extension
 *
 */

static int
decode_gtp_priv_ext(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{

    length = get_uint16_ntohs(payload, offset + 1);
    return 3 + length;
}

static int
decode_gtp_unknown(const uint8_t *payload, const uint16_t payload_len, uint32_t offset, uint16_t length, gtp_info_t *gtp_info)
{
    uint32_t offset_ptr;

    //proto_tree_add_expert(tree, pinfo, &ei_gtp_unknown_extention_header, tvb, offset, 1);

    //return tvb_reported_length_remaining(tvb, offset);

    if (payload_len >= offset)
        return payload_len - offset;
    else 
        return 0;

}


typedef struct _gtp_opt {
    int optcode;
    int (*decode) ( const uint8_t *, const uint16_t ,uint32_t, uint16_t ,gtp_info_t *);
} gtp_opt_t;



static const gtp_opt_t gtp_opt[] = {
/* 0x01 */  {GTP_EXT_CAUSE, decode_gtp_cause},
/* 0x02 */  {GTP_EXT_IMSI, decode_gtp_imsi},
/* 0x03 */  {GTP_EXT_RAI, decode_gtp_rai},
/* 0x04 */  {GTP_EXT_TLLI, decode_gtp_tlli},
/* 0x05 */  {GTP_EXT_PTMSI, decode_gtp_ptmsi},
/* 0x06 */  {GTP_EXT_QOS_GPRS, decode_gtp_qos_gprs},
/* 0x07 */
/* 0x08 */  {GTP_EXT_REORDER, decode_gtp_reorder},
/* 0x09 */  {GTP_EXT_AUTH_TRI, decode_gtp_auth_tri},
/* 0x0a */
/* 0x0b */  {GTP_EXT_MAP_CAUSE, decode_gtp_map_cause},
/* 0x0c */  {GTP_EXT_PTMSI_SIG, decode_gtp_ptmsi_sig},
/* 0x0d */  {GTP_EXT_MS_VALID, decode_gtp_ms_valid},
/* 0x0e */  {GTP_EXT_RECOVER, decode_gtp_recovery},
/* 0x0f */  {GTP_EXT_SEL_MODE, decode_gtp_sel_mode},
/* 0x10 */  {GTP_EXT_16, decode_gtp_16},
/* 0x11 */  {GTP_EXT_17, decode_gtp_17},
/* 0x12 */  {GTP_EXT_18, decode_gtp_18},
/* 0x13 */  {GTP_EXT_19, decode_gtp_19},
/* 0x14 */  {GTP_EXT_NSAPI, decode_gtp_nsapi},
/* 0x15 */  {GTP_EXT_RANAP_CAUSE, decode_gtp_ranap_cause},
/* 0x16 */  {GTP_EXT_RAB_CNTXT, decode_gtp_rab_cntxt},
/* 0x17 */  {GTP_EXT_RP_SMS, decode_gtp_rp_sms},
/* 0x18 */  {GTP_EXT_RP, decode_gtp_rp},
/* 0x19 */  {GTP_EXT_PKT_FLOW_ID, decode_gtp_pkt_flow_id},
/* 0x1a */  {GTP_EXT_CHRG_CHAR, decode_gtp_chrg_char},
/* 0x1b */  {GTP_EXT_TRACE_REF, decode_gtp_trace_ref},
/* 0x1c */  {GTP_EXT_TRACE_TYPE, decode_gtp_trace_type},
/* 0x1d */  {GTPv1_EXT_MS_REASON, decode_gtp_ms_reason},

/* 0x7e */  {GTP_EXT_TR_COMM, decode_gtp_tr_comm},
/* 0x7f */  {GTP_EXT_CHRG_ID, decode_gtp_chrg_id},
/* 0x80 */  {GTP_EXT_USER_ADDR, decode_gtp_user_addr},
/* 0x81 */  {GTP_EXT_MM_CNTXT, decode_gtp_mm_cntxt},
/* 0x82 */  {GTP_EXT_PDP_CNTXT, decode_gtp_pdp_cntxt},
/* 0x83 */  {GTP_EXT_APN, decode_gtp_apn},
/* 0x84 */  {GTP_EXT_PROTO_CONF, decode_gtp_proto_conf},
/* 0x85 */  {GTP_EXT_GSN_ADDR, decode_gtp_gsn_addr},
/* 0x86 */  {GTP_EXT_MSISDN, decode_gtp_msisdn},
/* 0x87 */  {GTP_EXT_QOS_UMTS, decode_gtp_qos_umts},                            /* 3G */
/* 0x88 */  {GTP_EXT_AUTH_QUI, decode_gtp_auth_qui},                            /* 3G */
/* 0x89 */  {GTP_EXT_TFT, decode_gtp_tft},                                      /* 3G */
/* 0x8a */  {GTP_EXT_TARGET_ID, decode_gtp_target_id},                          /* 3G */
/* 0x8b */  {GTP_EXT_UTRAN_CONT, decode_gtp_utran_cont},                        /* 3G */
/* 0x8c */  {GTP_EXT_RAB_SETUP, decode_gtp_rab_setup},                          /* 3G */
/* 0x8d */  {GTP_EXT_HDR_LIST, decode_gtp_hdr_list},                            /* 3G */
/* 0x8e */  {GTP_EXT_TRIGGER_ID, decode_gtp_trigger_id},                        /* 3G */
/* 0x8f */  {GTP_EXT_OMC_ID, decode_gtp_omc_id},                                /* 3G */
    /* TS 29 060 V6.11.0 */
/* 0x90 */  {GTP_EXT_RAN_TR_CONT, decode_gtp_ran_tr_cont},                      /* 7.7.43 */
/* 0x91 */  {GTP_EXT_PDP_CONT_PRIO, decode_gtp_pdp_cont_prio},                  /* 7.7.45 */
/* 0x92 */  {GTP_EXT_ADD_RAB_SETUP_INF, decode_gtp_add_rab_setup_inf},          /* 7.7.45A */
/* 0x93 */  {GTP_EXT_SSGN_NO, decode_gtp_ssgn_no},                              /* 7.7.47 */
/* 0x94 */  {GTP_EXT_COMMON_FLGS, decode_gtp_common_flgs},                      /* 7.7.48 */
/* 0x95 */  {GTP_EXT_APN_RES, decode_gtp_apn_res},                              /* 3G */
/* 0x96 */  {GTP_EXT_RA_PRIO_LCS, decode_gtp_ra_prio_lcs},                      /* 7.7.25B */
/* 0x97 */  {GTP_EXT_RAT_TYPE, decode_gtp_rat_type},                            /* 3G */
/* 0x98 */  {GTP_EXT_USR_LOC_INF, dissect_gtp_uli},                      /* 7.7.51 */
/* 0x99 */  {GTP_EXT_MS_TIME_ZONE, decode_gtp_ms_time_zone},                    /* 7.7.52 */
/* 0x9a */  {GTP_EXT_IMEISV, decode_gtp_imeisv},                                /* 3G 7.7.53 */
/* 0x9b */  {GTP_EXT_CAMEL_CHG_INF_CON, decode_gtp_camel_chg_inf_con},          /* 7.7.54 */
/* 0x9c */  {GTP_EXT_MBMS_UE_CTX, decode_gtp_mbms_ue_ctx},                      /* 7.7.55 */
/* 0x9d */  {GTP_EXT_TMGI, decode_gtp_tmgi},                                    /* 7.7.56 */
/* 0x9e */  {GTP_EXT_RIM_RA, decode_gtp_rim_ra},                                /* 7.7.57 */
/* 0x9f */  {GTP_EXT_MBMS_PROT_CONF_OPT, decode_gtp_mbms_prot_conf_opt},        /* 7.7.58 */
/* 0xa0 */  {GTP_EXT_MBMS_SA, decode_gtp_mbms_sa},                              /* 7.7.60 */
/* 0xa1 */  {GTP_EXT_SRC_RNC_PDP_CTX_INF, decode_gtp_src_rnc_pdp_ctx_inf},      /* 7.7.61 */
/* 0xa2 */  {GTP_EXT_ADD_TRS_INF, decode_gtp_add_trs_inf},                      /* 7.7.62 */
/* 0xa3 */  {GTP_EXT_HOP_COUNT, decode_gtp_hop_count},                          /* 7.7.63 */
/* 0xa4 */  {GTP_EXT_SEL_PLMN_ID, decode_gtp_sel_plmn_id},                      /* 7.7.64 */
/* 0xa5 */  {GTP_EXT_MBMS_SES_ID, decode_gtp_mbms_ses_id},                      /* 7.7.65 */
/* 0xa6 */  {GTP_EXT_MBMS_2G_3G_IND, decode_gtp_mbms_2g_3g_ind},                /* 7.7.66 */
/* 0xa7 */  {GTP_EXT_ENH_NSAPI, decode_gtp_enh_nsapi},                          /* 7.7.67 */
/* 0xa8 */  {GTP_EXT_MBMS_SES_DUR, decode_gtp_mbms_ses_dur},                    /* 7.7.59 */
/* 0xa9 */  {GTP_EXT_ADD_MBMS_TRS_INF, decode_gtp_add_mbms_trs_inf},            /* 7.7.68 */
/* 0xaa */  {GTP_EXT_MBMS_SES_ID_REP_NO, decode_gtp_mbms_ses_id_rep_no},        /* 7.7.69 */
/* 0xab */  {GTP_EXT_MBMS_TIME_TO_DATA_TR, decode_gtp_mbms_time_to_data_tr},    /* 7.7.70 */
/* 0xac */  {GTP_EXT_PS_HO_REQ_CTX, decode_gtp_ps_ho_req_ctx},                  /* 7.7.71 */
/* 0xad */  {GTP_EXT_BSS_CONT, decode_gtp_bss_cont},                            /* 7.7.72 */
/* 0xae */  {GTP_EXT_CELL_ID, decode_gtp_cell_id},                              /* 7.7.73 */
/* 0xaf */  {GTP_EXT_PDU_NO, decode_gtp_pdu_no},                                /* 7.7.74 */
/* 0xb0 */  {GTP_EXT_BSSGP_CAUSE, decode_gtp_bssgp_cause},                      /* 7.7.75 */
/* 0xb1 */  {GTP_EXT_REQ_MBMS_BEARER_CAP, decode_gtp_mbms_bearer_cap},          /* 7.7.76 */
/* 0xb2 */  {GTP_EXT_RIM_ROUTING_ADDR_DISC, decode_gtp_rim_ra_disc},            /* 7.7.77 */
/* 0xb3 */  {GTP_EXT_LIST_OF_SETUP_PFCS, decode_gtp_lst_set_up_pfc},            /* 7.7.78 */
/* 0xb4 */  {GTP_EXT_PS_HANDOVER_XIP_PAR, decode_gtp_ps_handover_xid},          /* 7.7.79 */
/* 0xb5 */  {GTP_EXT_MS_INF_CHG_REP_ACT, decode_gtp_ms_inf_chg_rep_act},        /* 7.7.80 */
/* 0xb6 */  {GTP_EXT_DIRECT_TUNNEL_FLGS, decode_gtp_direct_tnl_flg},            /* 7.7.81 */
/* 0xb7 */  {GTP_EXT_CORRELATION_ID, decode_gtp_corrl_id},                      /* 7.7.82 */
/* 0xb8 */  {GTP_EXT_BEARER_CONTROL_MODE, decode_gtp_bearer_cntrl_mod},         /* 7.7.83 */
/* 0xb9 */  {GTP_EXT_MBMS_FLOW_ID, decode_gtp_mbms_flow_id},                    /* 7.7.84 */
/* 0xba */  {GTP_EXT_MBMS_IP_MCAST_DIST, decode_gtp_mbms_ip_mcast_dist},        /* 7.7.85 */
/* 0xba */  {GTP_EXT_MBMS_DIST_ACK, decode_gtp_mbms_dist_ack},                  /* 7.7.86 */
/* 0xbc */  {GTP_EXT_RELIABLE_IRAT_HO_INF, decode_gtp_reliable_irat_ho_inf},    /* 7.7.87 */
/* 0xbd */  {GTP_EXT_RFSP_INDEX, decode_gtp_rfsp_index},                        /* 7.7.87 */

/* 0xbe */  {GTP_EXT_FQDN, decode_gtp_fqdn},                                    /* 7.7.90 */
/* 0xbf */  {GTP_EXT_EVO_ALLO_RETE_P1, decode_gtp_evolved_allc_rtn_p1},         /* 7.7.91 */
/* 0xc0 */  {GTP_EXT_EVO_ALLO_RETE_P2, decode_gtp_evolved_allc_rtn_p2},         /* 7.7.92 */
/* 0xc1 */  {GTP_EXT_EXTENDED_COMMON_FLGS, decode_gtp_extended_common_flgs},    /* 7.7.93 */
/* 0xc2 */  {GTP_EXT_UCI, decode_gtp_uci},                                      /* 7.7.94 */
/* 0xc3 */  {GTP_EXT_CSG_INF_REP_ACT, decode_gtp_csg_inf_rep_act},              /* 7.7.95 */
/* 0xc4 */  {GTP_EXT_CSG_ID, decode_gtp_csg_id},                                /* 7.7.96 */
/* 0xc5 */  {GTP_EXT_CMI, decode_gtp_cmi},                                      /* 7.7.97 */
/* 0xc6 */  {GTP_EXT_AMBR, decode_gtp_apn_ambr},                                /* 7.7.98 */
/* 0xc7 */  {GTP_EXT_UE_NETWORK_CAP, decode_gtp_ue_network_cap},                /* 7.7.99 */
/* 0xc8 */  {GTP_EXT_UE_AMBR, decode_gtp_ue_ambr},                              /* 7.7.100 */
/* 0xc9 */  {GTP_EXT_APN_AMBR_WITH_NSAPI, decode_gtp_apn_ambr_with_nsapi},      /* 7.7.101 */
/* 0xCA */  {GTP_EXT_GGSN_BACK_OFF_TIME, decode_gtp_ggsn_back_off_time},        /* 7.7.102 */
/* 0xCB */  {GTP_EXT_SIG_PRI_IND, decode_gtp_sig_pri_ind},                      /* 7.7.103 */
/* 0xCC */  {GTP_EXT_SIG_PRI_IND_W_NSAPI, decode_gtp_sig_pri_ind_w_nsapi},      /* 7.7.104 */
/* 0xCD */  {GTP_EXT_HIGHER_BR_16MB_FLG, decode_gtp_higher_br_16mb_flg},        /* 7.7.105 */
/* 0xCE */  {GTP_EXT_MAX_MBR_APN_AMBR, decode_gtp_max_mbr_apn_ambr},            /* 7.7.106 */
/* 0xCF */  {GTP_EXT_ADD_MM_CTX_SRVCC, decode_gtp_add_mm_ctx_srvcc},            /* 7.7.107 */
/* 0xD0 */  {GTP_EXT_ADD_FLGS_SRVCC, decode_gtp_add_flgs_srvcc},                /* 7.7.108 */
/* 0xD1 */  {GTP_EXT_STN_SR, decode_gtp_stn_sr},                                /* 7.7.109 */
/* 0xD2 */  {GTP_EXT_C_MSISDN, decode_gtp_c_msisdn},                            /* 7.7.110 */
/* 0xD3 */  {GTP_EXT_EXT_RANAP_CAUSE, decode_gtp_ext_ranap_cause},              /* 7.7.111 */
/* 0xD4 */  {GTP_EXT_ENODEB_ID, decode_gtp_ext_enodeb_id },                     /* 7.7.112 */
/* 0xD5 */  {GTP_EXT_SEL_MODE_W_NSAPI, decode_gtp_ext_sel_mode_w_nsapi },       /* 7.7.113 */
/* 0xD6 */  {GTP_EXT_ULI_TIMESTAMP, decode_gtp_ext_uli_timestamp },             /* 7.7.114 */
/* 0xD7 */  {GTP_EXT_LHN_ID_W_SAPI, decode_gtp_ext_lhn_id_w_sapi },             /* 7.7.115 */
/* 0xD8 */  {GTP_EXT_CN_OP_SEL_ENTITY, decode_gtp_ext_cn_op_sel_entity },       /* 7.7.116 */

/* 0xDA */    {GTP_EXT_EXT_COMMON_FLGS_II, decode_gtp_extended_common_flgs_II},    /* 7.7.118 */

/* 0xf9 */  {GTP_EXT_REL_PACK, decode_gtp_rel_pack },                           /* charging */
/* 0xfa */  {GTP_EXT_CAN_PACK, decode_gtp_can_pack},                            /* charging */
/* 0xfb */  {GTP_EXT_CHRG_ADDR, decode_gtp_chrg_addr},

/* 0xfc */  {GTP_EXT_DATA_REQ, decode_gtp_data_req},                           /* charging */
/* 0xfd */  {GTP_EXT_DATA_RESP, decode_gtp_data_resp},                         /* charging */
/* 0xfe */  {GTP_EXT_NODE_ADDR, decode_gtp_node_addr},
/* 0xff */  {GTP_EXT_PRIV_EXT, decode_gtp_priv_ext},

    {0, decode_gtp_unknown}
};


static int dissect_gtp_common(struct flow_info *flow, int direction,const uint8_t *payload, const uint32_t payload_len)
{
    gtp_info_t  gtp_info;
    uint32_t    offset=0;
    uint8_t     octet;
    uint8_t     opt_val;
    int         opt_num,i;
    uint8_t     gtp_prime;
    uint8_t     flags;
    uint16_t    length;
    uint8_t     version = 0;

    uint8_t     next_hdr = 0;
    uint32_t    ext_hdr_length;

    memset(&gtp_info, 0 ,sizeof(gtp_info_t));
    opt_num=sizeof(gtp_opt)/sizeof(gtp_opt[0]);
    //printf("opt num:%d\n",opt_num);
    if(payload==NULL || payload_len <20){
        return 0;
    }

    version = (get_uint8_t(payload, offset)>>5) & 0x07;
    gtp_info.version = version;

    switch (version)
    {
    case 0:
        gtp_version = 0;
        break;
    case 1:
        gtp_version = 1;
        break;
    default:
        gtp_version = 1;
        break;
    }
    
    if(version>=2 ){
        return 0;
    }
    
    

    octet=get_uint8_t(payload,1);
    if(octet ==  0){
        return 0;
    }

    flags=get_uint8_t(payload, offset);
    if(!(flags&0x10)){
        gtp_prime=1;
    }else{
        gtp_prime=0;
    }
    offset++;

    gtp_info.message_type=get_uint8_t(payload,offset);
    offset++;

    gtp_info.length = ntohs(get_uint16_t(payload,offset));
    offset+=2;

    if((uint32_t)(gtp_info.length + 8) > payload_len)
        return 0;

    if(1==gtp_prime){
        gtp_info.seq_num=ntohs(get_uint16_t(payload,offset));
        offset+=2;
        if(0==version && 0==(flags & 0x01)){
            offset+=14;
        }
    }else{
        switch (version){
        case 0:
            gtp_info.seq_num=ntohs(get_uint16_t(payload,offset));
            offset+=2;

            /* flow label */
            //ntohs(get_uint16_t(payload,offset));
            offset += 2;

            /* dpu number */
            //get_uint8_t(payload,offset);
            offset += 4;

            /* tid str */
            //get_uint8_t(payload,offset);
            offset += 8;
            break;
        case 1:
            gtp_info.teid=ntohl(get_uint32_t(payload, offset));
            offset+=4;

            if(flags & (GTP_E_MASK|GTP_S_MASK|GTP_PN_MASK)){
                if(flags & GTP_S_MASK){
                    /* seq number */
                    gtp_info.seq_num=ntohs(get_uint16_t(payload,offset));
                }
                offset+=2;

                if (flags & GTP_PN_MASK) {
                    /* pdu number */
                }
                offset++;
                if (flags & GTP_E_MASK) {
                    next_hdr = get_uint8_t(payload,offset);
                    offset++;
                    while(next_hdr!=0){
                        if(offset>=payload_len){
                            return 0;
                        }
                        ext_hdr_length=get_uint8_t(payload,offset);
                        if(ext_hdr_length==0){
                            return payload_len-offset;
                        }
                        offset ++;
                        /* some data*/

                        offset += ext_hdr_length*4-2;
                        if(offset>=payload_len){
                            return 0;
                        }
                        next_hdr = get_uint8_t(payload,offset);
                        offset++;
                        
                    }
                }else{
                    offset++;
                }

            }
        
            break;
        default:
            break;
        }

    }


    if(gtp_info.message_type !=GTP_MSG_TPDU){
        while(offset<payload_len){
            opt_val=get_uint8_t(payload, offset);
                    length = 0;
                    /*
            offset++;

            length=ntohl(get_uint16_t(payload, offset));
            if(length<1){return -1;}
                    offset ++;;

            for(i=0;i<opt_num;i++){
                if (gtp_opt[i].optcode == opt_val)
                    (*gtp_opt[i].decode)(payload, payload_len, offset,length ,&gtp_info); 
            }*/    
            
            i = -1;
                    while (gtp_opt[++i].optcode)
                    {
                        if (gtp_opt[i].optcode == opt_val)
                                break;
                if(i>=opt_num-1){return 0;}
                    }
                   length =  (*gtp_opt[i].decode) (payload, payload_len, offset,length ,&gtp_info);
            if(length<=0 || i>=opt_num-1){
                return 0;
            }
            offset+=length;
        }
    }

    write_gtp_control_log(flow, direction, &gtp_info);
    return 0;
}

int dpi_gtp_skip_pdu_head(const uint8_t *payload, const uint16_t payload_len, uint16_t *offset)
{
    uint8_t     flags, message_type;
    uint16_t    length;
    uint8_t     version = 0;

    uint8_t     next_hdr = 0;
    uint32_t    ext_hdr_length;
    uint16_t    _offset = 0;

    if (payload == NULL || payload_len < 8){
        return -1;
    }

    flags = get_uint8_t(payload, 0);

    version = (flags >> 5) & 0x07;
    if (version >= 2)
        return -1;

    if ((flags & 0x10) == 0)
        return -1;

    message_type = get_uint8_t(payload, 1);
    if (message_type == 0)
        return -1;

    length = ntohs(get_uint16_t(payload, 2));
    if (length + 8u > payload_len)
        return -1;

    _offset = 4;

    switch (version){
        case 0:
            // seq num
            _offset += 2;

            /* flow label */
            //ntohs(get_uint16_t(payload,_offset));
            _offset += 2;

            /* dpu number */
            //get_uint8_t(payload,_offset);
            _offset += 4;

            /* tid str */
            //get_uint8_t(payload,_offset);
            _offset += 8;
            break;
        case 1:
            // teid
            _offset += 4;

            if (flags & (GTP_E_MASK|GTP_S_MASK|GTP_PN_MASK)) {
                // if GTP_S_MASK: seq number
                _offset += 2;

                // if GTP_PN_MASK: pdu number
                _offset++;

                if (flags & GTP_E_MASK) {
                    next_hdr = get_uint8_t(payload, _offset);
                    _offset++;
                    while (next_hdr != 0) {
                        if (_offset >= payload_len){
                            break;
                        }
                        ext_hdr_length = get_uint8_t(payload, _offset);
                        if (ext_hdr_length == 0) {
                            return -1;
                        }
                        _offset++;
                        /* some data*/

                        _offset += ext_hdr_length*4 - 2;
                        if(_offset >= payload_len){
                            break;
                        }
                        next_hdr = get_uint8_t(payload,_offset);
                        _offset++;
                    }
                } else {
                    _offset++;
                }
            }
            break;
        default:
            break;
    }

    *offset = _offset;
    return 0;
}


static void identify_gtp_control(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{

    UNUSED(payload);
    UNUSED(payload_len);
    uint16_t s_port = 0, d_port = 0;
    
    s_port = ntohs(flow->tuple.inner.port_src);
    d_port = ntohs(flow->tuple.inner.port_dst);
    
    if(d_port != GTP_C_PORT_0 && s_port != GTP_C_PORT_0
    && d_port != GTP_C_PORT_1 && s_port != GTP_C_PORT_1){
        return;
    }
    
    uint8_t version = payload[0] >> 5;
    if(version > 2)
        return;
    if(version == 2){
        if(get_uint16_ntohs(payload, 2) + 4 != payload_len)
            return;
    }
    else{
        if(get_uint16_ntohs(payload, 2) + 8 != payload_len)
            return;
    }

    flow->real_protocol_id = PROTOCOL_GTP_CONTROL;
    return ;
}

/*
PKT_OK,
PKT_STOLEN,
PKT_REASSEMBLE,
PKT_DROP
*/
static int dissect_gtp_control(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    uint8_t     version;

    if(payload==NULL || payload_len <20){
        return PKT_DROP;
    }

    version = get_uint8_t(payload, 0)>>5;
    if(version > 2){
        return PKT_DROP;
    }
    else if(version == 2){
        if(payload[0] & 0x10)
            return PKT_DROP;
        
        dissect_gtpv2_control(flow, direction, seq, payload, payload_len, flag);
    }
    else {
        if(!((payload[0] >> 4) & 0x01))
            return PKT_DROP;

        if(payload[0] & 0x08)
            return PKT_DROP;

        dissect_gtp_common(flow, direction,payload,  payload_len);
    }
    return PKT_OK;
}


static void init_gtp_control_dissector(void)
{
    init_gtp_control_field();
    port_add_proto_head(IPPROTO_UDP, GTP_C_PORT_0, PROTOCOL_GTP_CONTROL);
    port_add_proto_head(IPPROTO_UDP, GTP_C_PORT_1, PROTOCOL_GTP_CONTROL);

    udp_detection_array[PROTOCOL_GTP_CONTROL].proto = PROTOCOL_GTP_CONTROL;
    udp_detection_array[PROTOCOL_GTP_CONTROL].identify_func = identify_gtp_control;
    udp_detection_array[PROTOCOL_GTP_CONTROL].dissect_func = dissect_gtp_control;
    
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_GTP_CONTROL].excluded_protocol_bitmask, PROTOCOL_GTP_CONTROL);


    port_add_proto_head(IPPROTO_TCP, GTP_C_PORT_0, PROTOCOL_GTP_CONTROL);
    port_add_proto_head(IPPROTO_TCP, GTP_C_PORT_1, PROTOCOL_GTP_CONTROL);

    tcp_detection_array[PROTOCOL_GTP_CONTROL].proto = PROTOCOL_GTP_CONTROL;
    tcp_detection_array[PROTOCOL_GTP_CONTROL].identify_func = identify_gtp_control;
    tcp_detection_array[PROTOCOL_GTP_CONTROL].dissect_func = dissect_gtp_control;
    
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_GTP_CONTROL].excluded_protocol_bitmask, PROTOCOL_GTP_CONTROL);

    
    return;
}

static __attribute((constructor)) void    before_init_gtp_control(void){
    register_tbl_array(TBL_LOG_GTP_CONTROL, 0, "gtp_control", init_gtp_control_dissector);
}



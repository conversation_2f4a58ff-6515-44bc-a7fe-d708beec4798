/****************************************************************************************
 * 文 件 名 : dpi_gre.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy     2018/07/06
编码: wangy     2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <glib.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_dissector.h"

extern __thread char    g_protoinfo[MAX_CONTENT_SIZE];
extern __thread uint16_t g_proto_layer[32];
extern __thread uint8_t  g_proto_layer_cnt;

#define SET_IPV4_FLOW_TUPLE(tuple, protocol, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                            \
                tuple.proto = protocol;                                                  \
                tuple.ip_version = 4;                                                    \
                memcpy(tuple.ip_src, srcip, 4);                                          \
                memcpy(tuple.ip_dst, dstip, 4);                                          \
                tuple.port_src = srcport;                                                \
                tuple.port_dst = dstport;                                                \
                tuple.sctp_id = _sctp_id;                                                \
            }

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];

enum gre_index_em{
    EM_GRE_FLAG_CHECKSUM,
    EM_GRE_FLAG_ROUTING,
    EM_GRE_FLAG_KEY,
    EM_GRE_FLAG_SEQ,
    EM_GRE_VERSION,
    EM_GRE_PROTO_TYPE,
    EM_GRE_CHECKSUM,
    EM_GRE_OFFSET,
    EM_GRE_KEY_PAYLENGTH,
    EM_GRE_KEY_CALLID,
    EM_GRE_KEY_KEY,
    EM_GRE_SEQ,
    EM_GRE_ACK,
    EM_GRE_TUNNELTRAFFICSTAT,
    EM_GRE_INNERIPSRC,
    EM_GRE_INNERIPDST,
    EM_GRE_INNERIPPROTO,
    EM_GRE_INNERSRCPORT,
    EM_GRE_INNERDSTPORT,
    EM_GRE_MAX
};


static dpi_field_table  gre_field_array[] = {
    DPI_FIELD_D(EM_GRE_FLAG_CHECKSUM,                YA_FT_UINT8,                 "Checksum_flag"),
    DPI_FIELD_D(EM_GRE_FLAG_ROUTING,                 YA_FT_UINT8,                 "Routing_flag"),
    DPI_FIELD_D(EM_GRE_FLAG_KEY,                     YA_FT_UINT8,                 "Key_flag"),
    DPI_FIELD_D(EM_GRE_FLAG_SEQ,                     YA_FT_UINT8,                 "Sequence_number_flag"),
    DPI_FIELD_D(EM_GRE_VERSION,                      YA_FT_UINT32,                "Version"),
    DPI_FIELD_D(EM_GRE_PROTO_TYPE,                   YA_FT_STRING,                "Protocol_type"),
    DPI_FIELD_D(EM_GRE_CHECKSUM,                     YA_FT_UINT16,                "checksum"),
    DPI_FIELD_D(EM_GRE_OFFSET,                       YA_FT_UINT16,                "offset"),
    DPI_FIELD_D(EM_GRE_KEY_PAYLENGTH,                YA_FT_UINT16,                "Key_payload_length"),
    DPI_FIELD_D(EM_GRE_KEY_CALLID,                   YA_FT_UINT16,                "Key_call_id"),
    DPI_FIELD_D(EM_GRE_KEY_KEY,                      YA_FT_BYTES,                 "Key_key"),
    DPI_FIELD_D(EM_GRE_SEQ,                          YA_FT_UINT32,                "Sequence_number"),
	DPI_FIELD_D(EM_GRE_ACK,                          YA_FT_UINT32,                "Acknowledgment_number"),
    DPI_FIELD_D(EM_GRE_TUNNELTRAFFICSTAT,            YA_FT_STRING,                "TunnelTrafficStat"),
    DPI_FIELD_D(EM_GRE_INNERIPSRC,                   YA_FT_STRING,                "InnerIPSrc"),
    DPI_FIELD_D(EM_GRE_INNERIPDST,                   YA_FT_STRING,                "InnerIPDst"),
    DPI_FIELD_D(EM_GRE_INNERIPPROTO,                 YA_FT_UINT8,                 "InnerIPProto"),
    DPI_FIELD_D(EM_GRE_INNERSRCPORT,                 YA_FT_UINT16,                "InnerSrcPort"),
    DPI_FIELD_D(EM_GRE_INNERDSTPORT,                 YA_FT_UINT16,                "InnerDstPort"),
};

struct dpi_grehd {
    uint8_t flags;
    uint8_t flag_checksum;
    uint8_t flag_routing;
    uint8_t flag_key;
    uint8_t flag_seq;
    uint8_t flag_ssr;
    uint8_t flag_ack;
    uint8_t control;
    uint16_t payload_length;
    uint16_t call_id;
    uint16_t proto_type;
    char cproto_type[16];
    uint16_t checksum;
    uint16_t offset;
    uint32_t gre_key;
    uint32_t seq;
	uint32_t ack;
    uint32_t router;
    uint32_t gre_version;
    struct five_tuple outer;
};


static int gre_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct dpi_grehd *ghread, int *idx, int i)
{
    char __str[64] = {0};
    //int local_idx=*idx;
    switch(i){
    case EM_GRE_FLAG_CHECKSUM:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->flag_checksum);
        break;
    case EM_GRE_FLAG_ROUTING:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->flag_routing);
        break;
    case EM_GRE_FLAG_KEY:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->flag_key);
        break;
    case EM_GRE_FLAG_SEQ:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->flag_seq);
        break;
    case EM_GRE_PROTO_TYPE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->cproto_type, strlen(ghread->cproto_type));
        break;
    case EM_GRE_CHECKSUM:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->checksum);
        break;
    case EM_GRE_OFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->offset);
        break;
    case EM_GRE_KEY_PAYLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->payload_length);
        break;
    case EM_GRE_KEY_CALLID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->call_id);
        break;
    case EM_GRE_KEY_KEY:
        write_one_hexnum_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->gre_key);
        break;
    case EM_GRE_SEQ:
        if (ghread->seq > 0)
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->seq);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_GRE_ACK:
        if (ghread->ack > 0)
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->ack);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_GRE_VERSION:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ghread->gre_version);
        break;
    case EM_GRE_INNERIPSRC:
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), ghread->outer.ip_src);
        else if (flow->ip_version == 6)
            get_ip6string(__str, sizeof(__str), ghread->outer.ip_src);
        else
            strncpy(__str, "0.0.0.0", sizeof("0.0.0.0"));
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
        break;
    case EM_GRE_INNERIPDST:
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), ghread->outer.ip_dst);
        else if (flow->ip_version == 6)
            get_ip6string(__str, sizeof(__str), ghread->outer.ip_dst);
        else
            strncpy(__str, "0.0.0.0", sizeof("0.0.0.0"));
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
        break;
    case EM_GRE_INNERIPPROTO:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gre_field_array[i].type, NULL, ghread->outer.proto);
        break;
    case EM_GRE_INNERSRCPORT:
        if(ghread->outer.port_src==0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gre_field_array[i].type, NULL, ntohs(ghread->outer.port_src));
        }
        break;
    case EM_GRE_INNERDSTPORT:
        if(ghread->outer.port_src==0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gre_field_array[i].type, NULL, ntohs(ghread->outer.port_dst));
        }
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = gre_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif

    return 0;

    UNUSED(direction);
}


static int write_gre_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    if (g_config.protocol_switch[PROTOCOL_GRE] == 0)
        return 0;

    struct dpi_grehd *ghread=(struct dpi_grehd *)field_info;
    if(!ghread){
        return 0;
    }

    int i;
    int idx = 0;
    struct tbl_log *log_ptr;


    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }


    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "gre");

    for(i=0;i<EM_GRE_MAX;i++){
        gre_field_element(log_ptr, flow, direction, ghread, &idx, i);
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_GRE;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1){
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

int dpi_dissect_gre(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(!g_config.protocol_switch[PROTOCOL_GRE] || payload_len < 4)
        return PKT_OK;

    uint32_t offset = 0;
    const uint8_t *chunck_data = payload;

    struct dpi_grehd  grehd;
    memset(&grehd, 0, sizeof(grehd));
    grehd.control    = get_uint8_t(chunck_data, offset);
    grehd.flags      = get_uint8_t(chunck_data, offset+1);
    grehd.proto_type = get_uint16_ntohs(chunck_data, offset+2);
    sprintf(grehd.cproto_type, "0x%04x", grehd.proto_type);
    grehd.flag_checksum = (grehd.control & 0x80) >> 7;
    grehd.flag_routing  = (grehd.control & 0x40) >> 6;
    grehd.flag_key      = (grehd.control & 0x20) >> 5;
    grehd.flag_seq      = (grehd.control & 0x10) >> 4;
    grehd.flag_ssr      = (grehd.control & 0x08) >> 3;
    grehd.flag_ack      = (grehd.flags   & 0x80) >> 7;
    grehd.gre_version   = (grehd.flags   & 0x07);
    offset += 4;

    uint8_t  is_ppp = 0;
    switch (grehd.proto_type)
    {
    case ETHERTYPE_PPP:
        if (grehd.gre_version)
            is_ppp = 1;
        break;
    case ETHERTYPE_3GPP2:
    case ETHERTYPE_CDMA2000_A10_UBS:
        is_ppp = 1; break;
    case GRE_WCCP:
        break;
    }

    if(grehd.flag_checksum || grehd.flag_routing){
        if(payload_len<8){return PKT_DROP;}
        grehd.checksum = get_uint16_ntohs(chunck_data, offset);
        grehd.offset   = get_uint16_ntohs(chunck_data, offset + 2);
        offset += 4;
    }

    if(grehd.flag_key){
        if(payload_len<16){return PKT_DROP;}
        if (is_ppp && grehd.proto_type != ETHERTYPE_CDMA2000_A10_UBS){
            grehd.payload_length = get_uint16_ntohs(chunck_data, offset);
            grehd.call_id        = get_uint16_ntohs(chunck_data, offset + 2);
            offset += 4;
        }
        else{
            grehd.gre_key=get_uint32_ntohl(chunck_data, offset);
            offset += 4;
        }
    }

    if(grehd.flag_seq){
        if(payload_len<20){return PKT_DROP;}
        grehd.seq=get_uint32_ntohl(chunck_data, offset); //sequence number
        offset+=4;
    }

	if (is_ppp && grehd.flag_ack) {
		grehd.ack = get_uint32_ntohl(chunck_data, offset);
		offset += 4;//gre ack number
	}

    if(grehd.flag_routing){
        while(offset + 4 < payload_len){
            uint16_t sre_af = get_uint16_ntohs(chunck_data, offset);
            offset += 2; //routing address family
            offset += 1; //routing sre offset
            uint8_t sre_length = get_uint8_t(chunck_data, offset);
            offset += 1; ////routing sre length
            offset += sre_length;
            if (sre_af == 0 && sre_length == 0)
                break;
        }
    }

    uint16_t chunck_total_len = payload_len - offset;
	uint16_t raw = chunck_total_len;
	uint16_t ppp_prototype;

    if(is_ppp){
        g_proto_layer[g_proto_layer_cnt++] = ETHERTYPE_PPP + PROTOCOL_MAX;
		dissect_ppp_family(flow, chunck_data + offset, chunck_total_len, PROTOCOL_GRE);
		ppp_prototype = get_uint16_ntohs(chunck_data + offset, 2);
    }

    write_gre_log(flow, direction, &grehd, NULL);

	if ((grehd.proto_type == 0x0800 && chunck_total_len >= 20)
		|| (ppp_prototype == 0x0021 || ppp_prototype == 0x0057)) {  // IPV4 IPV6

		if (0x0021 == ppp_prototype || 0x0057 == ppp_prototype) {
			offset += 4;
			chunck_total_len -= 4;
		}
    if(ppp_prototype){
        append_hardlink_proto_info(ppp_prototype);
    }

        struct pkt_info  pkt_data;
        memset(&pkt_data, 0, sizeof(struct pkt_info));

        uint16_t sport,dport;
        const uint8_t *inner_l3 = &chunck_data[offset];
        const struct dpi_iphdr *inner_iph = (const struct dpi_iphdr *)inner_l3;

        uint32_t inner_l4_offset = inner_iph->ihl * 4;
        const uint8_t *inner_l4  = ((const uint8_t *)inner_l3 + inner_l4_offset);
        chunck_total_len  = chunck_total_len - inner_l4_offset;

        pkt_data.ipversion=inner_iph->version;
        uint8_t  proto=0;
        const struct dpi_ipv6hdr *iph6 = NULL;
        if (inner_iph->version == 4) {
            proto = inner_iph->protocol;
            pkt_data.iph4=inner_iph;
        } else if (inner_iph->version == 6) {
            iph6 = (const struct dpi_ipv6hdr *)inner_l3;
            proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
            pkt_data.iph6=iph6;
        }
        pkt_data.proto=proto;

        if (inner_iph->protocol == IPPROTO_TCP && chunck_total_len >= 20) {
            //u_int tcp_len;
            const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)inner_l4;
            sport = ntohs(tcph->source);
            dport = ntohs(tcph->dest);
            //tcp_len = DPI_MIN(4 * (*tcph)->doff, l4_packet_len);
            //*payload = &l4[tcp_len];
            //*payload_len = DPI_MAX(0, l4_packet_len - 4 * (*tcph)->doff);
            pkt_data.tcph=tcph;
            append_hardlink_proto_info(inner_iph->protocol);
        } else if(inner_iph->protocol == IPPROTO_UDP && chunck_total_len >= 8) {
            const struct dpi_udphdr *udph = (const struct dpi_udphdr *)inner_l4;
            sport = ntohs(udph->source);
            dport = ntohs(udph->dest);
            //*payload = &l4[sizeof(struct dpi_udphdr)];
            //*payload_len = l4_packet_len > sizeof(struct dpi_udphdr) ? l4_packet_len - sizeof(struct dpi_udphdr) : 0;
            pkt_data.udph=udph;
            append_hardlink_proto_info(inner_iph->protocol);
        } else {
            //*payload_len = 0;
            sport = dport = 0;
            //return NULL;
        }

        SET_IPV4_FLOW_TUPLE(grehd.outer, inner_iph->protocol, inner_iph->saddr, inner_iph->daddr, ntohs(sport), ntohs(dport), 0);

        struct five_tuple   *outer_tuple;
        outer_tuple = direction == FLOW_DIR_SRC2DST ? &flow->tuple.inner : &flow->tuple_reverse.inner;

        SdtAclMatchedRuleInfo acl = {0};
        dpi_dissect_inherit_out_layer(flow, &pkt_data, &acl);
        if(pkt_data.proto){
            append_hardlink_proto_info(pkt_data.proto);
        }

        dpi_packet_processing_ip_layer(&flow_thread_info[flow->thread_id],
                                        flow->timestamp,
                                        outer_tuple,
                                        &pkt_data,
                                        (const struct dpi_iphdr*)inner_l3,
                                        raw, raw, &acl);
    }

    return PKT_OK;

    UNUSED(flag);
}

static void init_gre_dissector(void)
{
    dpi_register_proto_schema(gre_field_array, EM_GRE_MAX, "gre");
    map_fields_info_register(gre_field_array, PROTOCOL_GRE, EM_GRE_MAX,"gre");
    return;
}

static __attribute((constructor)) void    before_init_gre(void){
    register_tbl_array(TBL_LOG_GRE, 0, "gre", init_gre_dissector);
}


#ifndef __DPI_SOCKET_H__
#define __DPI_SOCKET_H__

#include <sys/types.h>
#include <stdint.h>

#include "list.h"

#define   MAX_REASSEABLE_LEN      1024*1024*1024
#define   MAX_READ_SIZE           512
#define   PACKET_LEN_BYTE         4
#define   PACKET_HEADER_SIZE      10

#define   SOCKET_GET_ELEMENT_LEN  2048

#define   ETHERTYPE_MIN_PKT_SIZE  1
#define   ETHERTYPE_MAX_PKT_SIZE  1518

struct socket_args{
    int conn_fd;
};

struct socket_reassemble
{ 
    uint16_t data_len;
    char     data[SOCKET_GET_ELEMENT_LEN];

    struct list_head node;
};



void *socket_receive_packet_thread(void *args);

#endif

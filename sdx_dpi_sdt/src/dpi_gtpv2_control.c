/****************************************************************************************
 * 文 件 名 : dpi_gtpv2_control.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh              2018/08/27
编码: liugh            2018/08/27
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <time.h>


#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_pint.h"

#include "dpi_gtp_control.h"

extern struct rte_mempool *tbl_log_mempool;


static dpi_field_table    gtp_control_field_array[] = {
    DPI_FIELD_D(EM_GTP_CONTROL_VERSION,                              EM_F_TYPE_UINT8,               "version"),
    DPI_FIELD_D(EM_GTP_CONTROL_MESSAGETYPE,                          EM_F_TYPE_UINT8,               "MessageType"),
    DPI_FIELD_D(EM_GTP_CONTROL_SEQUENCENUMBER,                    EM_F_TYPE_HEX,                     "SequenceNumber"),
    DPI_FIELD_D(EM_GTP_CONTROL_TEID,                               EM_F_TYPE_UINT32,               "TEID"),
    DPI_FIELD_D(EM_GTP_CONTROL_APN,                                  EM_F_TYPE_STRING,               "APN"),
    DPI_FIELD_D(EM_GTP_CONTROL_IMSI,                               EM_F_TYPE_STRING,               "IMSI"),
    DPI_FIELD_D(EM_GTP_CONTROL_MSISDN,                              EM_F_TYPE_STRING,               "MSISDN"),
    DPI_FIELD_D(EM_GTP_CONTROL_IMEI,                               EM_F_TYPE_STRING,               "IMEI"),
    DPI_FIELD_D(EM_GTP_CONTROL_CGI_MCC,                           EM_F_TYPE_UINT16,               "CGI_MCC"),
    DPI_FIELD_D(EM_GTP_CONTROL_CGI_MNC,                           EM_F_TYPE_UINT16,               "CGI_MNC"),
    DPI_FIELD_D(EM_GTP_CONTROL_CGI_LAC,                           EM_F_TYPE_UINT16,               "CGI_LAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_CGI_CI,                               EM_F_TYPE_UINT16,               "CGI_CI"),
    DPI_FIELD_D(EM_GTP_CONTROL_TAI_MCC,                           EM_F_TYPE_UINT16,               "TAI_MCC"),
    DPI_FIELD_D(EM_GTP_CONTROL_TAI_MNC,                           EM_F_TYPE_UINT16,               "TAI_MNC"),
    DPI_FIELD_D(EM_GTP_CONTROL_TAI_LAC,                           EM_F_TYPE_UINT16,               "TAI_LAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_SAI_MCC,                           EM_F_TYPE_UINT16,               "SAI_MCC"),
    DPI_FIELD_D(EM_GTP_CONTROL_SAI_MNC,                           EM_F_TYPE_UINT16,               "SAI_MNC"),
    DPI_FIELD_D(EM_GTP_CONTROL_SAI_LAC,                           EM_F_TYPE_UINT16,               "SAI_LAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_LAI_MCC,                           EM_F_TYPE_UINT16,               "LAI_MCC"),
    DPI_FIELD_D(EM_GTP_CONTROL_LAI_MNC,                           EM_F_TYPE_UINT16,               "LAI_MNC"),
    DPI_FIELD_D(EM_GTP_CONTROL_LAI_LAC,                           EM_F_TYPE_UINT16,               "LAI_LAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_ECGI_MCC,                           EM_F_TYPE_UINT16,               "ECGI_MCC"),
    DPI_FIELD_D(EM_GTP_CONTROL_ECGI_MNC,                           EM_F_TYPE_UINT16,               "ECGI_MNC"),
    DPI_FIELD_D(EM_GTP_CONTROL_ECGI_CI,                           EM_F_TYPE_UINT32,               "ECGI_CI"),
    DPI_FIELD_D(EM_GTP_CONTROL_STARTTIME,                           EM_F_TYPE_STRING,               "Start_time"),
    DPI_FIELD_D(EM_GTP_CONTROL_ENDTIME,                           EM_F_TYPE_STRING,               "End_time"),
    DPI_FIELD_D(EM_GTP_CONTROL_SESSION_DURATION,                   EM_F_TYPE_STRING,               "Session_duration"),
    DPI_FIELD_D(EM_GTP_CONTROL_CAUSE,                               EM_F_TYPE_STRING,               "Cause"),
    DPI_FIELD_D(EM_GTP_CONTROL_MCC,                                  EM_F_TYPE_UINT16,               "MCC"),
    DPI_FIELD_D(EM_GTP_CONTROL_MNC,                                  EM_F_TYPE_UINT16,               "MNC"),
    DPI_FIELD_D(EM_GTP_CONTROL_LAI,                                  EM_F_TYPE_STRING,               "LAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_CGI,                                  EM_F_TYPE_STRING,               "CGI"),
    DPI_FIELD_D(EM_GTP_CONTROL_GSNADDRESS,                           EM_F_TYPE_NULL,               "Gsn_address"),
    DPI_FIELD_D(EM_GTP_CONTROL_TEIDDATAI,                          EM_F_TYPE_UINT32,               "TEIDDataI"),
    DPI_FIELD_D(EM_GTP_CONTROL_TEIDDATAII,                          EM_F_TYPE_UINT32,               "TEIDDataII"),
    DPI_FIELD_D(EM_GTP_CONTROL_TEIDCONTROLPLANE,                   EM_F_TYPE_STRING,               "TEIDControlPlane"),
    DPI_FIELD_D(EM_GTP_CONTROL_NSAPI,                              EM_F_TYPE_UINT16,               "NSAPI"),
    DPI_FIELD_D(EM_GTP_CONTROL_CHARGINGID,                          EM_F_TYPE_UINT32,               "ChargingID"),
    DPI_FIELD_D(EM_GTP_CONTROL_LAC,                                  EM_F_TYPE_UINT16,               "LAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_RAC,                                  EM_F_TYPE_UINT16,               "RAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_CELLLAC,                              EM_F_TYPE_UINT16,               "CellLAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_CELLCI,                              EM_F_TYPE_UINT16,               "CellCI"),
    DPI_FIELD_D(EM_GTP_CONTROL_SAC,                                  EM_F_TYPE_UINT16,               "SAC"),
    DPI_FIELD_D(EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV4,        EM_F_TYPE_STRING,               "GGSN_address_control_IPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV6,        EM_F_TYPE_STRING,               "GGSN_address_control_IPv6"),
    DPI_FIELD_D(EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV4,            EM_F_TYPE_STRING,               "GGSN_address_user_IPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV6,            EM_F_TYPE_STRING,               "GGSN_address_user_IPv6"),
    DPI_FIELD_D(EM_GTP_CONTROL_GSNADDRESSIPV4,                    EM_F_TYPE_STRING,               "GSNaddressIPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_GSNADDRESSIPV6,                    EM_F_TYPE_STRING,               "GSNaddressIPv6"),
    DPI_FIELD_D(EM_GTP_CONTROL_RNCADDRESSIPV4,                    EM_F_TYPE_STRING,               "RNCaddressIPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_CGADDRESSIPV4,                      EM_F_TYPE_STRING,               "CGaddressIPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_ENDUSERADDRESSIPV4,                EM_F_TYPE_STRING,               "EndUserAddressIPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_ENDUSERADDRESSIPV6,                EM_F_TYPE_STRING,               "EndUserAddressIPv6"),
    DPI_FIELD_D(EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4,            EM_F_TYPE_STRING,               "PDNAddressAndPrefixIPv4"),
    DPI_FIELD_D(EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6,            EM_F_TYPE_STRING,               "PDNAddressAndPrefixIPv6"),
    DPI_FIELD_D(EM_GTP_CONTROL_TRACKINGAREACODE,                   EM_F_TYPE_UINT16,               "TrackingAreaCode"),
    DPI_FIELD_D(EM_GTP_CONTROL_ECGI_ECI,                           EM_F_TYPE_UINT32,               "ECGI_ECI"),
    DPI_FIELD_D(EM_GTP_CONTROL_RAI,                                  EM_F_TYPE_STRING,               "RAI"),
    DPI_FIELD_D(EM_GTP_CONTROL_SAI,                                  EM_F_TYPE_STRING,               "SAI"),
    DPI_FIELD_D(EM_GTP_CONTROL_ENODEBID,                           EM_F_TYPE_UINT32,               "ENODEBID"),
    DPI_FIELD_D(EM_GTP_CONTROL_CELLID,                              EM_F_TYPE_UINT32,               "CellID"),
    DPI_FIELD_D(EM_GTP_CONTROL_RAT,                                  EM_F_TYPE_UINT8,               "RAT"),
    DPI_FIELD_D(EM_GTP_CONTROL_PDNTYPE,                              EM_F_TYPE_UINT8,               "PDNType"),
    DPI_FIELD_D(EM_GTP_CONTROL_CHARGING_ID,                          EM_F_TYPE_STRING,               "Charging_id"),
    DPI_FIELD_D(EM_GTP_CONTROL_UETIMEZONE,                          EM_F_TYPE_STRING,               "UETimeZone"),
    DPI_FIELD_D(EM_GTP_CONTROL_IPCP_PRI_DNS_ADDRESS,             EM_F_TYPE_STRING,               "ipcp_pri_dns_address"),
    DPI_FIELD_D(EM_GTP_CONTROL_IPCP_SEC_DNS_ADDRESS,             EM_F_TYPE_STRING,               "ipcp_sec_dns_address"),
    DPI_FIELD_D(EM_GTP_CONTROL_DNSSERVERIPV4ADDRESS,             EM_F_TYPE_STRING,               "DNSServerIPv4Address"),
    DPI_FIELD_D(EM_GTP_CONTROL_S1_U_ENODEB_GTP_U_TEIDGREKEY,     EM_F_TYPE_HEX,                     "S1-U_eNodeB_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S1_U_SGW_GTP_U_TEIDGREKEY,        EM_F_TYPE_HEX,                   "S1-U_SGW_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S12_RNC_GTP_U_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S12_RNC_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S12_SGW_GTP_U_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S12_SGW_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_SGW_GTP_U_TEIDGREKEY,        EM_F_TYPE_HEX,                   "S5/S8_SGW_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_PGW_GTP_U_TEIDGREKEY,        EM_F_TYPE_HEX,                   "S5/S8_PGW_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_SGW_GTP_C_TEIDGREKEY,        EM_F_TYPE_HEX,                   "S5/S8_SGW_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_PGW_GTP_C_TEIDGREKEY,        EM_F_TYPE_HEX,                   "S5/S8_PGW_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S11_MME_GTP_C_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S11_MME_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S11_S4_SGW_GTP_C_TEIDGREKEY,        EM_F_TYPE_HEX,                   "S11/S4_SGW_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S10_MME_GTP_C_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S10_MME_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S3_MME_GTP_C_TEIDGREKEY,            EM_F_TYPE_HEX,                   "S3_MME_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S3_SGSN_GTP_C_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S3_SGSN_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGSN_GTP_U_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S4_SGSN_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGW_GTP_U_TEIDGREKEY,            EM_F_TYPE_HEX,                   "S4_SGW_GTP-U_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGSN_GTP_C_TEIDGREKEY,         EM_F_TYPE_HEX,                   "S4_SGSN_GTP-C_TeidGREKey"),
    DPI_FIELD_D(EM_GTP_CONTROL_S1_U_ENODEB_GTP_U_IPV4,            EM_F_TYPE_STRING,               "S1-U_eNodeB_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S1_U_SGW_GTP_U_IPV4,                EM_F_TYPE_STRING,               "S1-U_SGW_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S12_RNC_GTP_U_IPV4,                EM_F_TYPE_STRING,               "S12_RNC_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S12_SGW_GTP_U_IPV4,                EM_F_TYPE_STRING,               "S12_SGW_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_SGW_GTP_U_IPV4,             EM_F_TYPE_STRING,               "S5/S8_SGW_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_PGW_GTP_U_IPV4,             EM_F_TYPE_STRING,               "S5/S8_PGW_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_SGW_GTP_C_IPV4,             EM_F_TYPE_STRING,               "S5/S8_SGW_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_PGW_GTP_C_IPV4,             EM_F_TYPE_STRING,               "S5/S8_PGW_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S11_MME_GTP_C_IPV4,                EM_F_TYPE_STRING,               "S11_MME_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S11_S4_SGW_GTP_C_IPV4,            EM_F_TYPE_STRING,               "S11/S4_SGW_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S10_MME_GTP_C_IPV4,                EM_F_TYPE_STRING,               "S10_MME_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S3_MME_GTP_C_IPV4,                  EM_F_TYPE_STRING,               "S3_MME_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S3_SGSN_GTP_C_IPV4,                EM_F_TYPE_STRING,               "S3_SGSN_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGSN_GTP_U_IPV4,                EM_F_TYPE_STRING,               "S4_SGSN_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGW_GTP_U_IPV4,                  EM_F_TYPE_STRING,               "S4_SGW_GTP-U_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGSN_GTP_C_IPV4,                EM_F_TYPE_STRING,               "S4_SGSN_GTP-C_IPV4"),
    DPI_FIELD_D(EM_GTP_CONTROL_S1_U_ENODEB_GTP_U_IPV6,            EM_F_TYPE_STRING,               "S1-U_eNodeB_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S1_U_SGW_GTP_U_IPV6,                EM_F_TYPE_STRING,               "S1-U_SGW_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S12_RNC_GTP_U_IPV6,                EM_F_TYPE_STRING,               "S12_RNC_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S12_SGW_GTP_U_IPV6,                EM_F_TYPE_STRING,               "S12_SGW_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_SGW_GTP_U_IPV6,             EM_F_TYPE_STRING,               "S5/S8_SGW_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_PGW_GTP_U_IPV6,             EM_F_TYPE_STRING,               "S5/S8_PGW_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_SGW_GTP_C_IPV6,             EM_F_TYPE_STRING,               "S5/S8_SGW_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S5_S8_PGW_GTP_C_IPV6,             EM_F_TYPE_STRING,               "S5/S8_PGW_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S11_MME_GTP_C_IPV6,                EM_F_TYPE_STRING,               "S11_MME_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S11_S4_SGW_GTP_C_IPV6,            EM_F_TYPE_STRING,               "S11/S4_SGW_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S10_MME_GTP_C_IPV6,                EM_F_TYPE_STRING,               "S10_MME_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S3_MME_GTP_C_IPV6,                  EM_F_TYPE_STRING,               "S3_MME_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S3_SGSN_GTP_C_IPV6,                EM_F_TYPE_STRING,               "S3_SGSN_GTP-C_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGSN_GTP_U_IPV6,                EM_F_TYPE_STRING,               "S4_SGSN_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGW_GTP_U_IPV6,                  EM_F_TYPE_STRING,               "S4_SGW_GTP-U_IPV6"),
    DPI_FIELD_D(EM_GTP_CONTROL_S4_SGSN_GTP_C_IPV6,                EM_F_TYPE_STRING,               "S4_SGSN_GTP-C_IPV6"),

};


/* bcd decode */
static const dgt_set_t Dgt1_9_bcd = {
  {
      /*  0   1   2   3   4   5   6   7   8   9   a   b   c   d   e  f*/
      '0','1','2','3','4','5','6','7','8','9','?','?','?','?','?','?'
  }
};

static const char *mon_names[12] = {
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
};

static void
timestamp_to_utctime(char *timestr, uint32_t timestamp)
{
    time_t ttm;
    struct tm *p;
    ttm = (time_t)timestamp;
    p = gmtime(&ttm);
    snprintf(timestr, 128, "%s %2d, %d %02d:%02d:%02d UTC",
        mon_names[p->tm_mon],
        p->tm_mday,
        p->tm_year + 1900,
        p->tm_hour,
        p->tm_min,
        p->tm_sec);
}

void
dissect_mcc_mnc(const uint8_t *payload, uint32_t offset, const uint16_t length,gtp_info_t *gtp_info)
{
    if(length<3){return;}

    uint8_t      octet;
    uint8_t      mcc1, mcc2, mcc3, mnc1, mnc2, mnc3;
    uint16_t     mcc,mnc = 0;

    octet = get_uint8_t(payload, offset);
    mcc1 = octet & 0x0f;
    mcc2 = octet >> 4;
    offset++;

    octet = get_uint8_t(payload, offset);
    mcc3 = octet & 0x0f;
    /* MNC, Mobile network code (octet 3 bits 5 to 8, octet 4)  */
    mnc3 = octet >> 4;
    offset++;

    octet = get_uint8_t(payload, offset);
    mnc1 = octet & 0x0f;
    mnc2 = octet >> 4;

    mcc = 100 * mcc1 + 10 * mcc2 + mcc3;
    if(gtp_info->version == 1)
        mnc = 10 * mnc1 + mnc2;
    else if(gtp_info->version == 2)
        mnc = 100 * mnc1 + mnc2 * 10 + mnc3;

    gtp_info->mcc=mcc;
    gtp_info->mnc=mnc;

}

void
dissect_gtpv2_imsi(const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    int           i=0;
    uint8_t       octet;
    uint32_t      t_length;
    const dgt_set_t *dgt;

    gtp_info->dev_flag = 1;

    dgt=&Dgt1_9_bcd;

    if(length>MR_FIELD_VALUE_LEN){
        length=MR_FIELD_VALUE_LEN;
    }

    t_length=offset+length;
    if(t_length>payload_len){return;}

    while(offset < t_length){
        if(i>MR_FIELD_VALUE_LEN-1){break;}
        octet=get_uint8_t(payload, offset);
        gtp_info->imsi[i] = dgt->out[octet & 0x0f];
        i++;

        octet=(octet >> 4);
        if(offset == t_length-1 && octet == 0x0f){
            break;
        }

        gtp_info->imsi[i] = dgt->out[octet & 0x0f];
        i++;
        offset++;
    }

    gtp_info->imsi[i]= '\0';
    gtp_info->ie_array[EM_GTP_CONTROL_IMSI].data = gtp_info->imsi;
    if(i>MR_FIELD_VALUE_LEN){
        gtp_info->ie_array[EM_GTP_CONTROL_IMSI].length=MR_FIELD_VALUE_LEN-1;
    }else{
        gtp_info->ie_array[EM_GTP_CONTROL_IMSI].length=i;
    }

}

/*
 * 8.4 Cause
 */

/* Table 8.4-1: Cause values */
static const struct int_to_string gtpv2_cause_vals[] = {
    {0, "Reserved"},
    /* Request / Initial message */
    {  1, "Reserved"},
    {  2, "Local Detach"},
    {  3, "Complete Detach"},
    {  4, "RAT changed from 3GPP to Non-3GPP"},
    {  5, "ISR deactivation"},
    {  6, "Error Indication received from RNC/eNodeB/S4-SGSN"},
    {  7, "IMSI Detach Only"},
    {  8, "Reactivation Requested"},
    {  9, "PDN reconnection to this APN disallowed"},
    { 10, "Access changed from Non-3GPP to 3GPP"},
    { 11, "PDN connection inactivity timer expires"},
    { 12, "PGW not responding"},
    { 13, "Network Failure"},
    { 14, "QoS parameter mismatch"},
    /* 15 Spare. This value range is reserved for Cause values in a request message */
    { 15, "Spare"},
    /* Acceptance in a Response / triggered message */
    { 16, "Request accepted"},
    { 17, "Request accepted partially"},
    { 18, "New PDN type due to network preference"},
    { 19, "New PDN type due to single address bearer only"},
    /* 20-63 Spare. This value range shall be used by Cause values in an acceptance response/triggered message */
    { 20, "Spare"},
    { 21, "Spare"},
    { 22, "Spare"},
    { 23, "Spare"},
    { 24, "Spare"},
    { 25, "Spare"},
    { 26, "Spare"},
    { 27, "Spare"},
    { 28, "Spare"},
    { 29, "Spare"},
    { 30, "Spare"},
    { 31, "Spare"},
    { 32, "Spare"},
    { 33, "Spare"},
    { 34, "Spare"},
    { 35, "Spare"},
    { 36, "Spare"},
    { 37, "Spare"},
    { 38, "Spare"},
    { 39, "Spare"},
    { 40, "Spare"},
    { 41, "Spare"},
    { 42, "Spare"},
    { 43, "Spare"},
    { 44, "Spare"},
    { 45, "Spare"},
    { 46, "Spare"},
    { 47, "Spare"},
    { 48, "Spare"},
    { 49, "Spare"},
    { 50, "Spare"},
    { 51, "Spare"},
    { 52, "Spare"},
    { 53, "Spare"},
    { 54, "Spare"},
    { 55, "Spare"},
    { 56, "Spare"},
    { 57, "Spare"},
    { 58, "Spare"},
    { 59, "Spare"},
    { 60, "Spare"},
    { 61, "Spare"},
    { 62, "Spare"},
    { 63, "Spare"},
    /* Rejection in a Response / triggered message */
    { 64, "Context Not Found"},
    { 65, "Invalid Message Format"},
    { 66, "Version not supported by next peer"},
    { 67, "Invalid length"},
    { 68, "Service not supported"},
    { 69, "Mandatory IE incorrect"},
    { 70, "Mandatory IE missing"},
    { 71, "Shall not be used"},
    { 72, "System failure"},
    { 73, "No resources available"},
    { 74, "Semantic error in the TFT operation"},
    { 75, "Syntactic error in the TFT operation"},
    { 76, "Semantic errors in packet filter(s)"},
    { 77, "Syntactic errors in packet filter(s)"},
    { 78, "Missing or unknown APN"},
    { 79, "Shall not be used"},
    { 80, "GRE key not found"},
    { 81, "Relocation failure"},
    { 82, "Denied in RAT"},
    { 83, "Preferred PDN type not supported"},
    { 84, "All dynamic addresses are occupied"},
    { 85, "UE context without TFT already activated"},
    { 86, "Protocol type not supported"},
    { 87, "UE not responding"},
    { 88, "UE refuses"},
    { 89, "Service denied"},
    { 90, "Unable to page UE"},
    { 91, "No memory available"},
    { 92, "User authentication failed"},
    { 93, "APN access denied - no subscription"},
    { 94, "Request rejected(reason not specified)"},
    { 95, "P-TMSI Signature mismatch"},
    { 96, "IMSI/IMEI not known"},
    { 97, "Semantic error in the TAD operation"},
    { 98, "Syntactic error in the TAD operation"},
    { 99, "Shall not be used"},
    {100, "Remote peer not responding"},
    {101, "Collision with network initiated request"},
    {102, "Unable to page UE due to Suspension"},
    {103, "Conditional IE missing"},
    {104, "APN Restriction type Incompatible with currently active PDN connection"},
    {105, "Invalid overall length of the triggered response message and a piggybacked initial message"},
    {106, "Data forwarding not supported"},
    {107, "Invalid reply from remote peer"},
    {108, "Fallback to GTPv1"},
    {109, "Invalid peer"},
    {110, "Temporarily rejected due to handover procedure in progress"},
    {111, "Modifications not limited to S1-U bearers"},
    {112, "Request rejected for a PMIPv6 reason "},
    {113, "APN Congestion"},
    {114, "Bearer handling not supported"},
    {115, "UE already re-attached"},
    {116, "Multiple PDN connections for a given APN not allowed"},
    {117, "Target access restricted for the subscriber"},
    {118, "Shall not be used. See NOTE 2 and NOTE 3."},
    {119, "MME/SGSN refuses due to VPLMN Policy"},
    {120, "GTP-C Entity Congestion"},
    {121, "Late Overlapping Request"},
    {122, "Timed out Request"},
    {123, "UE is temporarily not reachable due to power saving"},
    {124, "Relocation failure due to NAS message redirection"},
    {125, "UE not authorised by OCS or external AAA Server"},
    {126, "Multiple accesses to a PDN connection not allowed"},
    {127, "Request rejected due to UE capability"},

    /* 128-239 Spare. For future use in a triggered/response message  */
    /* 240-255 Spare. For future use in an initial/request message */
    {0, NULL}
};


/*  2 */
 static void
 dissect_gtpv2_cause( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
 {
    uint8_t cause ;
    const char *casestr;
    if(offset+length>payload_len){return;}
    cause = get_uint8_t(payload, offset);
    casestr =  val_to_string(cause, gtpv2_cause_vals);
    snprintf(gtp_info->cause, 128, "%s (%d)", casestr, cause);

    gtp_info->ie_array[EM_GTP_CONTROL_CAUSE].data=(const uint8_t *)gtp_info->cause;
    gtp_info->ie_array[EM_GTP_CONTROL_CAUSE].length=strlen(gtp_info->cause);

    //gtp_info->ie_array[EM_GTP_CONTROL_CAUSE].data=&payload[offset];
    //gtp_info->ie_array[EM_GTP_CONTROL_CAUSE].length=length;

 }


/*  57 */
static void
dissect_gtpv2_tgt_rnc_id( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length, gtp_info_t *gtp_info)
{
    uint32_t lac=0;

    if(offset+length>payload_len || length <5){return;}

    /* skip mnc mcc */
    dissect_mcc_mnc(payload, offset, length, gtp_info);
    offset+=3;

    lac=ntohs(get_uint16_t(payload, offset));
    gtp_info->lac=lac;
}


/*  59 teid-c */
static void
dissect_gtpv2_teid_c(const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    if(offset+length > payload_len || length < 4){
        return;
    }
    gtp_info->ie_array[EM_GTP_CONTROL_TEIDCONTROLPLANE].data=&payload[offset];
    gtp_info->ie_array[EM_GTP_CONTROL_TEIDCONTROLPLANE].length=4;
}

/*  IE type 61   (LAC SAC)*/
static void dissect_gtpv2_sai( const uint8_t *payload,
                              const uint16_t payload_len,
                              uint32_t offset,
                              uint16_t length,
                              gtp_info_t *gtp_info)
{
    if(offset+length > payload_len || length < 5){
        return;
    }

    dissect_mcc_mnc(payload, offset, length, gtp_info);
    offset+=3;
    gtp_info->ie_array[EM_GTP_CONTROL_LAC].length = get_uint16_ntohs(payload, offset);

    offset+=2;
    gtp_info->ie_array[EM_GTP_CONTROL_SAC].length = get_uint16_ntohs(payload, offset);
}



/*  called by uli 86*/
static void
dissect_gtpv2_uli_cgi( const uint8_t *payload, const uint16_t payload_len,uint32_t *offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint16_t lac,ci;
    if(*offset > payload_len || payload_len-*offset<7){
        return;
    }

    dissect_mcc_mnc(payload, *offset, length, gtp_info);
    *offset+=3;
    lac=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;
    ci=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;


    gtp_info->cell_lac=lac;
    gtp_info->cell_ci=ci;
    snprintf((char *)gtp_info->cgi,MR_FIELD_LITTLE_LEN,"LAC 0x%x CI 0x%x",lac,ci);
    gtp_info->ie_array[EM_GTP_CONTROL_CGI_MCC].length = gtp_info->mcc;
    snprintf(gtp_info->cgi_mnc, 4, "%u%u", gtp_info->mnc & 0x02, gtp_info->mnc & 0x01);
//    gtp_info->ie_array[EM_GTP_CONTROL_CGI_MNC].length = gtp_info->mnc;
    gtp_info->ie_array[EM_GTP_CONTROL_CGI_LAC].length = gtp_info->lac;
    gtp_info->ie_array[EM_GTP_CONTROL_CGI_CI].length = ci;
    gtp_info->ie_array[EM_GTP_CONTROL_CGI].data=gtp_info->cgi;
    gtp_info->ie_array[EM_GTP_CONTROL_CGI].length=strlen((char *)gtp_info->cgi);

}


/*  called by uli 86*/
static void
dissect_gtpv2_uli_sai( const uint8_t *payload, const uint16_t payload_len,uint32_t *offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint16_t lac,sac;
    uint16_t tmp_len=0;
    if(*offset > payload_len || payload_len-*offset<7){
        return;
    }

    dissect_mcc_mnc(payload, *offset, length, gtp_info);
    *offset+=3;
    lac=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;
    sac=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;

    snprintf((char *)gtp_info->sai,MR_FIELD_LITTLE_LEN,"LAC 0x%x SAC 0x%x",lac,sac);
    gtp_info->ie_array[EM_GTP_CONTROL_SAI_MCC].length=gtp_info->mcc;
    gtp_info->ie_array[EM_GTP_CONTROL_SAI_MNC].length=gtp_info->mnc;
    gtp_info->ie_array[EM_GTP_CONTROL_SAI_LAC].length=lac;
    gtp_info->ie_array[EM_GTP_CONTROL_SAI].data=gtp_info->sai;
    gtp_info->ie_array[EM_GTP_CONTROL_SAI].length= strlen((char *)gtp_info->sai);

}

 /*  called by uli 86*/
static void
dissect_gtpv2_uli_rai( const uint8_t *payload, const uint16_t payload_len,uint32_t *offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint16_t lac,rac;
    uint16_t tmp_len=0;
    if(*offset > payload_len || payload_len-*offset<7){
        return;
    }

    dissect_mcc_mnc(payload, *offset, length, gtp_info);
    *offset+=3;
    lac=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;
    rac=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;
    snprintf((char *)gtp_info->rai,MR_FIELD_LITTLE_LEN,"LAC 0x%x RAC 0x%x",lac,rac);
    gtp_info->ie_array[EM_GTP_CONTROL_RAI].data=gtp_info->rai;
    gtp_info->ie_array[EM_GTP_CONTROL_RAI].length=strlen((char *)gtp_info->rai);

}


/*  called by uli 86*/
static void
dissect_gtpv2_uli_tai( const uint8_t *payload, const uint16_t payload_len,uint32_t *offset,uint16_t length,gtp_info_t *gtp_info)
{
    if(*offset > payload_len || payload_len-*offset<5){
        return;
    }

    dissect_mcc_mnc(payload, *offset, length, gtp_info);
    *offset+=3;
    gtp_info->ie_array[EM_GTP_CONTROL_TRACKINGAREACODE].length = ntohs(get_uint16_t(payload, *offset));

    *offset+=2;

    gtp_info->ie_array[EM_GTP_CONTROL_TAI_MCC].length=gtp_info->mcc;
    gtp_info->ie_array[EM_GTP_CONTROL_TAI_MNC].length=gtp_info->mnc;
    gtp_info->ie_array[EM_GTP_CONTROL_TAI_LAC].length=gtp_info->ie_array[EM_GTP_CONTROL_TRACKINGAREACODE].length;
}
/*  called by uli 86*/
static void
dissect_gtpv2_uli_ecgi( const uint8_t *payload, const uint16_t payload_len,uint32_t *offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint32_t ecgi;
    uint8_t  octet;
    uint32_t octet4;
    uint8_t  spare;

    if(*offset > payload_len || payload_len-*offset<7){
        return;
    }

    dissect_mcc_mnc(payload, *offset, length, gtp_info);
    *offset+=3;
    octet = get_uint8_t(payload, *offset);
    spare = octet & 0xF0;

    octet4 = ntohl(get_uint32_t(payload, *offset));
    ecgi = octet4 & 0x0FFFFFFF;
    *offset+=4;

    gtp_info->ie_array[EM_GTP_CONTROL_ENODEBID].length = ecgi>>8 & 0xFFFFF;
    gtp_info->ie_array[EM_GTP_CONTROL_CELLID].length   = ecgi & 0xFF;

    gtp_info->ie_array[EM_GTP_CONTROL_ECGI_MCC].length=gtp_info->mcc;
    gtp_info->ie_array[EM_GTP_CONTROL_ECGI_MNC].length=gtp_info->mnc;
    gtp_info->ie_array[EM_GTP_CONTROL_ECGI_CI].length=ecgi;

}


/*  called by uli 86*/
static void
dissect_gtpv2_uli_lai( const uint8_t *payload, const uint16_t payload_len,uint32_t *offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint16_t lac;
    uint16_t tmp_len=0;
    if(*offset > payload_len || payload_len-*offset<5){
        return;
    }

    dissect_mcc_mnc(payload, *offset, length, gtp_info);
    *offset+=3;
    lac=ntohs(get_uint16_t(payload, *offset));

    *offset+=2;

    snprintf((char *)gtp_info->lai,MR_FIELD_LITTLE_LEN,"LAC 0x%x",lac);
    gtp_info->ie_array[EM_GTP_CONTROL_LAI].data=gtp_info->lai;
    gtp_info->ie_array[EM_GTP_CONTROL_LAI].length=strlen((char *)gtp_info->lai);
    gtp_info->ie_array[EM_GTP_CONTROL_LAI_MCC].length=gtp_info->mcc;
    gtp_info->ie_array[EM_GTP_CONTROL_LAI_MNC].length=gtp_info->mnc;
    gtp_info->ie_array[EM_GTP_CONTROL_LAI_LAC].length=lac;

}

/*  IE type 86 */
static void
dissect_gtpv2_uli( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t flags;
    gtp_info->uli_flag = 1;

    if(offset+1>payload_len){return;}

    flags=get_uint8_t(payload, offset);
    offset+=1;

    /* CGI field */
    if(flags & GTPv2_ULI_CGI_MASK){
        dissect_gtpv2_uli_cgi(payload,payload_len,&offset,length, gtp_info);
    }

    /* SAI field */
    if (flags & GTPv2_ULI_SAI_MASK){
        dissect_gtpv2_uli_sai(payload,payload_len,&offset,length,gtp_info);
    }

    /* RAI field */
    if (flags & GTPv2_ULI_RAI_MASK){
        dissect_gtpv2_uli_rai(payload,payload_len,&offset,length,gtp_info);
    }

    /* TAI field */
    if (flags & GTPv2_ULI_TAI_MASK){
        dissect_gtpv2_uli_tai(payload,payload_len,&offset,length,gtp_info);
    }

    /* ECGI field*/
    if (flags & GTPv2_ULI_ECGI_MASK){
        dissect_gtpv2_uli_ecgi(payload,payload_len,&offset,length,gtp_info);
    }

    /* LAI field */
    if (flags & GTPv2_ULI_LAI_MASK){
        dissect_gtpv2_uli_lai(payload,payload_len,&offset,length,gtp_info);
    }


}


/* 58 , cell id*/
static void
dissect_gtpv2_tgt_global_cell_id( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    if(offset+6<payload_len){
        gtp_info->ie_array[EM_GTP_CONTROL_CELLID].data=payload+offset+5;
        gtp_info->ie_array[EM_GTP_CONTROL_CELLID].length=2;
    }
}


/*  71  APN */
static void
dissect_gtpv2_apn( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{

    uint8_t  octet;
    uint8_t  *apn;
    int      name_len, tmp;

    if(length+1>MR_FIELD_LITTLE_LEN){length=MR_FIELD_LITTLE_LEN-1;}

    if(offset+length>payload_len || length < 1){return;}

    name_len = get_uint8_t(payload, offset);
    if(name_len < 0x20){
        offset++;
        snprintf((char *)gtp_info->apn,length,"%s",&payload[offset]);
        for(;;){
            if(name_len>=length-1)
                break;
            tmp=name_len;
            name_len=name_len + gtp_info->apn[tmp] + 1;
            gtp_info->apn[tmp] = '.';
        }
        gtp_info->ie_array[EM_GTP_CONTROL_APN].data=gtp_info->apn;
        gtp_info->ie_array[EM_GTP_CONTROL_APN].length=length-1;
    }else{
        gtp_info->ie_array[EM_GTP_CONTROL_APN].data=&payload[offset];
        gtp_info->ie_array[EM_GTP_CONTROL_APN].length=length;
    }


}



/* 75, MEI */
void
dissect_gtpv2_mei( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    int i=0;
    uint8_t    octet;
    uint32_t   t_length;
    const dgt_set_t *dgt;

    gtp_info->dev_flag = 1;

    dgt=&Dgt1_9_bcd;

    if(length>MR_FIELD_VALUE_LEN){
        length=MR_FIELD_VALUE_LEN;
    }

    t_length=offset+length;
    if(t_length>payload_len){return;}

    while(offset < t_length){

        octet=get_uint8_t(payload, offset);
        gtp_info->imei[i] = dgt->out[octet & 0x0f];
        i++;

        octet=(octet >> 4);
        if(offset == t_length-1 && octet == 0x0f){
            break;
        }

        gtp_info->imei[i] = dgt->out[octet & 0x0f];
        i++;
        offset++;
    }

    gtp_info->imei[i]= '\0';
    gtp_info->ie_array[EM_GTP_CONTROL_IMEI].data = gtp_info->imei;
    if(i>MR_FIELD_VALUE_LEN){
        gtp_info->ie_array[EM_GTP_CONTROL_IMEI].length=MR_FIELD_VALUE_LEN-1;
    }else{
        gtp_info->ie_array[EM_GTP_CONTROL_IMEI].length=i;
    }

}


/* 76 MSISDN */
void
dissect_gtpv2_msisdn( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    int i=0;
    uint8_t octet;
    uint32_t    t_length;
    const dgt_set_t *dgt;

    gtp_info->dev_flag = 1;

    dgt=&Dgt1_9_bcd;

    if(length>MR_FIELD_VALUE_LEN){
        return;
    }

    t_length=offset+length;
    if(t_length>payload_len){return;}

    while(offset < t_length){

        octet=get_uint8_t(payload, offset);
        gtp_info->msisdn[i] = dgt->out[octet & 0x0f];
        i++;

        octet=(octet >> 4);
        if(offset == t_length-1 && octet == 0x0f){
            break;
        }

        gtp_info->msisdn[i] = dgt->out[octet & 0x0f];
        i++;
        offset++;
    }

    gtp_info->msisdn[i]= '\0';

    gtp_info->ie_array[EM_GTP_CONTROL_MSISDN].data = gtp_info->msisdn;
    if(i>MR_FIELD_VALUE_LEN){
        gtp_info->ie_array[EM_GTP_CONTROL_MSISDN].length=MR_FIELD_VALUE_LEN-1;
    }else{
        gtp_info->ie_array[EM_GTP_CONTROL_MSISDN].length=i;
    }

}


/*  79 PDN Address*/
static void
dissect_gtpv2_paa( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t pdn_type;
    uint8_t ip_len;
    if(offset + length > payload_len){
        return;
    }

    pdn_type=get_uint8_t(payload, offset);
    offset+=1;
    switch (pdn_type){
        case 1:
            /*IPV4*/

            if(length<4){return;}
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4].data= payload + offset;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4].length=4;
            break;
        case 2:
            /*IPV6*/
            if(length<17){return;}
            ip_len=get_uint8_t(payload, offset);
            offset+=1;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6].data= payload + offset;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6].length=16;

            break;
        case 3:
            /*IPV4/IPV6*/
            if(length<21){return;}
            ip_len=get_uint8_t(payload, offset);
            offset+=1;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4].data= payload + offset;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4].length=16;
            offset+=16;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6].data = payload + offset;
            gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6].length=4;
            break;
        default:
            break;
    }
}


/* 82  RAT type*/
static void
dissect_gtpv2_rat_type( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t rat_type;
    if(length<1 || offset+length > payload_len){return;}
    rat_type=get_uint8_t(payload, offset);

    gtp_info->rat_type=rat_type;
}

/*  IE type 83    (MNC MCC)*/
static void
dissect_gtpv2_serv_net( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint16_t     mcc, mnc;
    uint8_t         mcc1, mcc2, mcc3, mnc1, mnc2, mnc3;
    uint8_t         octet;

    if(payload_len-offset<3){
        return;
    }

    //mcc_mnc = get_uint24_t(payload,offset);

    /* Mobile country code MCC */
    octet = get_uint8_t(payload,offset);
    mcc1 = octet & 0x0f;
    mcc2 = octet >> 4;
    offset++;

    octet = get_uint8_t(payload,offset);
    mcc3 = octet & 0x0f;
    /* MNC, Mobile network code (octet 3 bits 5 to 8, octet 4)  */
    mnc3 = octet >> 4;
    offset++;

    octet = get_uint8_t(payload,offset);
    mnc1 = octet & 0x0f;
    mnc2 = octet >> 4;

    mcc = 100 * mcc1 + 10 * mcc2 + mcc3;
    mnc = 10 * mnc1 + mnc2;
    if ((mnc3 != 0xf) /* || (mcc_mnc == 0xffffff)*/) {
        mnc = 10 * mnc + mnc3;
    }

    gtp_info->mnc = mnc;
    gtp_info->mcc = mcc;

}

/*  87
interface:
0:    S1-U eNodeB GTP-U interface
1:    S1-U SGW GTP-U interface
2:    S12 RNC GTP-U interface
3:    S12 SGW GTP-U interface
4:    S5/S8 SGW GTP-U interface
5:    S5/S8 PGW GTP-U interface
6:    S5/S8 SGW GTP-C interface
7:    S5/S8 PGW GTP-C interface
8:    S5/S8 SGW PMIPv6 interface (the 32 bit GRE key is encoded in 32 bit TEID field)
9:    S5/S8 PGW PMIPv6 interface (the 32 bit GRE key is encoded in the 32 bit TEID field, see subclause 6.3 in 3GPP TS 29.275 [26])
10:    S11 MME GTP-C interface
11:    S11/S4 SGW GTP-C interface
12:    S10 MME GTP-C interface
13:    S3 MME GTP-C interface
14:    S3 SGSN GTP-C interface
15:    S4 SGSN GTP-U interface
16:    S4 SGW GTP-U interface
17:    S4 SGSN GTP-C interface
18:    S16 SGSN GTP-C interface
*/
static void
dissect_gtpv2_f_teid( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t flags;
    uint8_t interface;
    uint8_t ipv4_flag;
    uint8_t ipv6_flag;

    if(length<1 || offset+length > payload_len){return;}

    flags = get_uint8_t(payload, offset);
    ipv4_flag=(flags & 0x80)>>7;
    ipv6_flag=(flags & 0x40)>>6;

    if(1==ipv4_flag && 0==ipv6_flag && length <9){
        return;
    }

    if(0==ipv4_flag && 1==ipv6_flag && length <21){
        return;
    }

    if(1==ipv4_flag && 1==ipv6_flag && length <25){
        return;
    }

    offset+=1;

    interface = (flags & 0x3F) ;


    switch(interface){

        case MR_S1_U_eNodeB_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S1_U_ENODEB_GTP_U);

        case MR_S1_U_SGW_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S1_U_SGW_GTP_U);

        case MR_S12_RNC_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S12_RNC_GTP_U);

        case MR_S12_SGW_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S12_SGW_GTP_U);

        case MR_S5_S8_SGW_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S5_S8_SGW_GTP_U);

        case MR_S5_S8_PGW_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S5_S8_PGW_GTP_U);

        case MR_S5_S8_SGW_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S5_S8_SGW_GTP_C);

        case MR_S5_S8_PGW_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S5_S8_PGW_GTP_C);

        case MR_S11_MME_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S11_MME_GTP_C);

        case MR_S11_S4_SGW_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S11_S4_SGW_GTP_C);

        case MR_S10_MME_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S10_MME_GTP_C);

        case MR_S3_MME_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S3_MME_GTP_C);

        case MR_S3_SGSN_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S3_SGSN_GTP_C);

        case MR_S4_SGSN_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S4_SGSN_GTP_U);

        case MR_S4_SGW_GTP_U:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S4_SGW_GTP_U);

        case MR_S4_SGSN_GTP_C:
            MR_F_TEID_FIELD(EM_GTP_CONTROL_S4_SGSN_GTP_C);

        default:
            break;
    }
}



/*  94  charging id */
static void
dissect_gtpv2_charging_id( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    if(offset+length>payload_len){return;}
    gtp_info->ie_array[EM_GTP_CONTROL_CHARGING_ID].data=&payload[offset];
    gtp_info->ie_array[EM_GTP_CONTROL_CHARGING_ID].length=length;
}


/* 96  MSC,MGW,SGSN,GGSN,MME/SGW,PGW/SGW
static void
dissect_gtpv2_tra_info( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{



}
*/
/*  99 PDN type */
static void
dissect_gtpv2_pdn_type( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t pdn_type;
    if(offset+length>payload_len){return;}

    pdn_type = get_uint8_t(payload, offset);

    gtp_info->pdn_type = pdn_type & 0x7;
}

/* 110  nsapi */
static void
dissect_gtpv2_pdn_numbers( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t nsapi;

    if(offset+length>payload_len){return;}
    nsapi = get_uint8_t(payload, offset);

    gtp_info->nsapi=nsapi;
}


/*  114 UETimeZone */
static void
dissect_gtpv2_ue_time_zone( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t octet;
    char    sign;
    int     hours,minutes;

    if(offset+length>payload_len){return;}

    octet = get_uint8_t(payload, offset);
    sign = (octet & 0x08)?'-':'+';
    octet = (octet >> 4) + (octet & 0x07) * 10;

    hours=octet / 4;
    minutes=octet % 4 * 15;
    snprintf((char *)gtp_info->UETimeZone,64,"GMT %c %d hours %d minutes",sign,hours,minutes);

    gtp_info->ie_array[EM_GTP_CONTROL_UETIMEZONE].data=gtp_info->UETimeZone;
    gtp_info->ie_array[EM_GTP_CONTROL_UETIMEZONE].length=strlen((char *)gtp_info->UETimeZone);

    return;
}


/*121   LAC,RAC    ENOBE_ID,TAC  Cell id,*/
static void
dissect_gtpv2_target_id( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint8_t target_type;
    if(offset+length>payload_len || length < 1){return;}


    target_type = get_uint8_t(payload,offset);
    offset++;
    switch(target_type){
    case 0:
        if(length<6){return;}
        offset+=3;
        gtp_info->lac=ntohs(get_uint16_t(payload,offset));
        offset+=2;

        gtp_info->rac=ntohs(get_uint16_t(payload,offset));
        return;
    case 1:   // Macro eNodeB ID
        if(length<7){return;}
        offset+=3;
        //gtp_info->enobe_id=ntohl(get_uint24_t(payload,offset)) & 0x0fffff;
        offset+=3;

        gtp_info->tac=ntohs(get_uint16_t(payload,offset));
        return;
    case 2:
        if(length<3){return;}
        gtp_info->cell_id=ntohs(get_uint16_t(payload,offset));
        return;

    case 3:    //Home eNodeB ID
        if(length<8){return;}
        offset+=3;
        gtp_info->enobe_id=ntohl(get_uint32_t(payload,offset)) & 0x0fffffff;
        offset+=4;

        gtp_info->tac=ntohs(get_uint16_t(payload,offset));
        return;
    default:
        break;
    }

}

static void
dissect_gtpv2_mbms_session_duration( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint32_t durations_seconds;
    uint32_t days;
    uint32_t hours;
    uint32_t minutes;
    uint32_t seconds;
    uint32_t encoding = 0;
    uint32_t bit_offset = offset * 4;
    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = offset;

    durations_seconds = dpi_get_bits32(&pkt, bit_offset, 17, encoding);
    bit_offset += 17;
    days = dpi_get_bits32(&pkt, bit_offset, 7, encoding);

    if ((durations_seconds == 0) && (days == 0))
    {
        strncpy(gtp_info->session_duration,"Indefinite (always-on)", 30);
    }
    else
    {
        hours = durations_seconds / 3600;
        minutes = (durations_seconds / 3600) / 60;
        seconds = (durations_seconds / 3600) % 60;
        snprintf(gtp_info->session_duration, 64 ,"%d days %02d:%02d:%02d (DD days HH:MM:SS)",
            days, hours, minutes, seconds);
    }

    gtp_info->ie_array[EM_GTP_CONTROL_SESSION_DURATION].data = (const uint8_t *)gtp_info->session_duration;
    gtp_info->ie_array[EM_GTP_CONTROL_SESSION_DURATION].length = strlen(gtp_info->session_duration);

}


static void
dissect_gtpv2_secondary_rat_usage_data_report( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
{
    uint32_t starttime;
    uint32_t endtime;
    offset += 1;

    /* Octet 6 RAT Type */
    offset += 1;

    /* Octet 7 EPS Bearer ID */
    offset += 1;

    /* Octets 8 to 11 Start timestamp */
    starttime = get_uint32_ntohl(payload, offset);
    timestamp_to_utctime(gtp_info->starttime, starttime);
    offset += 4;

    /* Octets 12 to 15 End timestamp */
    endtime = get_uint32_ntohl(payload, offset);
    timestamp_to_utctime(gtp_info->endtime, endtime);
    offset += 4;

    /* 16 to 23 Usage Data DL */
    offset += 8;

    /* 24 to 32 Usage Data UL */
    offset += 8;

    gtp_info->ie_array[EM_GTP_CONTROL_STARTTIME].data = (const uint8_t *)gtp_info->starttime;
    gtp_info->ie_array[EM_GTP_CONTROL_STARTTIME].length = strlen(gtp_info->starttime);
    gtp_info->ie_array[EM_GTP_CONTROL_ENDTIME].data = (const uint8_t *)gtp_info->endtime;
    gtp_info->ie_array[EM_GTP_CONTROL_ENDTIME].length = strlen(gtp_info->endtime);
}



/* 184 apn */
//static void
//dissect_gtpv2_apn_and_relative_capacity( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info)
//{


//}


typedef struct _gtpv2_ie {
    int ie_type;
    void (*decode) ( const uint8_t *, const uint16_t ,uint32_t ,uint16_t ,gtp_info_t *);
} gtpv2_ie_t;


static const gtpv2_ie_t gtpv2_ies[] = {
    {GTPV2_IE_IMSI, dissect_gtpv2_imsi},                                   /* 1, Internal Mobile Subscriber Identity (IMSI) */
    {GTPV2_IE_CAUSE, dissect_gtpv2_cause},                                 /* 2, Cause (without embedded offending IE) 8.4 */
    {GTPV2_IE_TGT_RNC_ID, dissect_gtpv2_tgt_rnc_id},                       /* 57, */
    {GTPV2_IE_TGT_GLOGAL_CELL_ID, dissect_gtpv2_tgt_global_cell_id},       /* 58, */
    {GTPV2_IE_TEID_C, dissect_gtpv2_teid_c},                               /* 59 TEID-C */
    {GTPV2_IE_SAI, dissect_gtpv2_sai},                                     /* 61, LAC SAC*/
    {GTPV2_APN, dissect_gtpv2_apn},                                        /* 71, Access Point Name (APN) 8.6 */
    {GTPV2_MEI, dissect_gtpv2_mei},                                        /* 75, imei*/
    {GTPV2_IE_MSISDN, dissect_gtpv2_msisdn},                               /* 76, msidsn*/
    {GTPV2_PAA, dissect_gtpv2_paa},                                        /* 79, PDN Address Allocation (PAA) 8.14 PDNAddressAndPrefixIPv4 */
    {GTPV2_IE_RAT_TYPE, dissect_gtpv2_rat_type},                           /* 82, RAT Type  8.17 */
    {GTPV2_IE_SERV_NET, dissect_gtpv2_serv_net},                           /* 83, Serving Network 8.18 mnc mcc */
    {GTPV2_IE_ULI, dissect_gtpv2_uli},                                     /* 86, cgi sai rai tai ecgi lai*/
    {GTPV2_IE_F_TEID, dissect_gtpv2_f_teid},                               /* 87, Fully Qualified Tunnel Endpoint Identifier (F-TEID) 8.23 */
    {GTPV2_IE_CHAR_ID,dissect_gtpv2_charging_id},                          /* 94, */
    //{GTPV2_IE_TRA_INFO, dissect_gtpv2_tra_info},                           /* 96, Trace Information 8.31 */
    {GTPV2_IE_PDN_TYPE, dissect_gtpv2_pdn_type},                           /* 99, PDN Type */
    {GTPV2_IE_PDN_NUMBERS,dissect_gtpv2_pdn_numbers},                      /* 110, NSAPI*/
    {GTPV2_IE_UE_TIME_ZONE, dissect_gtpv2_ue_time_zone},                   /* 114, UE Time Zone */
    {GTPV2_IE_TARGET_ID, dissect_gtpv2_target_id},                         /* 121, Target Identification */
    {GTPV2_IE_MBMS_SESSION_DURATION, dissect_gtpv2_mbms_session_duration}, /* 138, 8.69 MBMS Session Duration */
    {GTPV2_IE_SECONDARY_RAT_USAGE_DATA_REPORT, dissect_gtpv2_secondary_rat_usage_data_report}, /* 201, 8.132 Secondary RAT Usage Data Report */
    //{GTPV2_IE_APN_AND_REL_CAP,dissect_gtpv2_apn_and_relative_capacity}     /* 184, APN */
};


int write_gtp_control_log(struct flow_info *flow, int direction,  gtp_info_t *gtp_info)
{
    int idx = 0;
    int i;
    struct tbl_log *log_ptr;
    char ip4_addr[20];
    char ip6_addr[64];

    if(g_config.gtp_filter && (gtp_info->dev_flag == 0 || gtp_info->uli_flag == 0))
        return PKT_OK;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);

    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "gtp_control");
    for(i=0;i<EM_GTP_CONTROL_MAX;i++){
        switch (gtp_control_field_array[i].index){
            case EM_GTP_CONTROL_VERSION:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_control_field_array[i].type,NULL,gtp_info->version);
                break;
            case EM_GTP_CONTROL_MESSAGETYPE:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_control_field_array[i].type,NULL,gtp_info->message_type);
                break;
            case EM_GTP_CONTROL_SEQUENCENUMBER:
                        if (gtp_info->version == 2)
                        {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_control_field_array[i].type,
                                                                                        (const uint8_t *) gtp_info->seq,gtp_info->seq_len);
                        }
                        else
                        {
                    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_info->seq_num);;
                        }
                break;
            case EM_GTP_CONTROL_TEID:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_control_field_array[i].type,NULL,gtp_info->teid);
                break;
            case EM_GTP_CONTROL_MCC:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_info->mcc);
                break;
            case EM_GTP_CONTROL_MNC:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_info->mnc);
                break;
            case EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4:
                if(gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4].length == 4){
                    get_ip4string(ip4_addr, sizeof(ip4_addr), *(const uint32_t*)(gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4].data));
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ip4_addr, strlen(ip4_addr));
                }
                else{
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,1);
                }
                break;
            case EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6:
                if(gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6].length == 16){
                    get_ip6string(ip6_addr, sizeof(ip6_addr), gtp_info->ie_array[EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6].data);
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ip6_addr, strlen(ip6_addr));
                }
                else{
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,1);
                }
                break;
            case EM_GTP_CONTROL_CGI_MNC:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_info->cgi_mnc, strlen(gtp_info->cgi_mnc));
                break;
            default:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, gtp_control_field_array[i].type,
                                                             gtp_info->ie_array[gtp_control_field_array[i].index].data,
                                                             gtp_info->ie_array[gtp_control_field_array[i].index].length);
                        break;

        }

    }

    for (i = EM_GTP_CONTROL_MAX; i < EM_GTP_CONTROL_IP4_MAX -1; i++) {
        char tmpIP[32] = { 0 };
        const uint8_t *data;
        uint16_t length;
        uint8_t type;

        data = gtp_info->ie_array[gtp_control_field_array[i].index].data;
        length = gtp_info->ie_array[gtp_control_field_array[i].index].length;
        type = gtp_control_field_array[i].type;
        if (data != NULL && length == 4) {
            snprintf(tmpIP, sizeof(tmpIP), "%u.%u.%u.%u", data[0], data[1], data[2], data[3]);
        }

        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, type, (const uint8_t*)tmpIP, strlen(tmpIP));

    }

    for (i = EM_GTP_CONTROL_IP4_MAX - 1; i < EM_GTP_CONTROL_IP6_MAX - 2; i++) {
        char tmpIP[64] = { 0 };
        const uint8_t *data;
        uint16_t length;
        uint8_t type;
        data = gtp_info->ie_array[gtp_control_field_array[i].index].data;
        length = gtp_info->ie_array[gtp_control_field_array[i].index].length;
        type = gtp_control_field_array[i].type;

        if (data != NULL && length == 16) {
            payload_to_ipv6(data, 0, tmpIP);
        }

        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, type, (const uint8_t*)tmpIP, strlen(tmpIP));

    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_GTP_CONTROL;
    log_ptr->log_len = idx;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}



static int dissect_gtpv2_ie_common(struct flow_info *flow,
                                        const uint8_t *payload,
                                        const uint32_t payload_len,
                                        uint32_t offset,
                                        gtp_info_t  *gtp_info)
{
    uint8_t      type, instance;
    uint8_t      octet;
    uint16_t     length;
    int          i;

    int ie_number=sizeof(gtpv2_ies)/sizeof(gtpv2_ies[0]);
    while(offset < payload_len){
        if(payload_len-offset<4){
            goto end;
        }
        /* Octet 1  ie type*/
        type =  get_uint8_t(payload, offset);
        offset+=1;

        /* Octet 2 - 3  ie length*/
        length = ntohs(get_uint16_t(payload, offset));
        if(length<1){
            goto end;
        }
        offset+=2;

        /* Octet 4  high:CR Spare low:Instance*/
        octet=get_uint8_t(payload, offset);
        offset+=1;

        /* TODO: call IE dissector here */
        if(offset+length>payload_len || length < 1){
            /*get data address and return*/
            goto end;
        }

        /* get data address*/
        for(i=0;i<ie_number;i++){
            if(gtpv2_ies[i].ie_type == type)
                (*gtpv2_ies[i].decode) (payload, payload_len, offset,length, gtp_info);
        }

        offset+=length;
    }

end:
    return 1;

}



/*
PKT_OK,
PKT_STOLEN,
PKT_REASSEMBLE,
PKT_DROP
*/
int dissect_gtpv2_control(struct flow_info *flow, int direction, uint32_t seq,
                                const uint8_t *payload, const uint32_t payload_len,
                                uint8_t flag)
{

    if(payload==NULL || payload_len <20){
        return PKT_DROP;
    }
        uint8_t     t_flag=0, p_flag=0, mp_flag=0;

    uint8_t     octet;
    uint32_t    offset=0;
    gtp_info_t  gtp_info;
    memset(&gtp_info, 0 ,sizeof(gtp_info_t));

    octet=get_uint8_t(payload, 0);
    offset+=1;

    /*get version*/
    gtp_info.version=(octet & 0xe0)>>5;
    if(gtp_info.version!=2){return 0;}

    p_flag  = (octet & 0x10) >> 4;
    t_flag  = (octet & 0x08) >> 3;
    mp_flag = (octet & 0x04) >> 2;

    /* get message_type */
    gtp_info.message_type=get_uint8_t(payload,offset);
    offset+=1;

    /* get length */
    gtp_info.length=ntohs(get_uint16_t(payload, offset));
    offset+=2;
    if((uint32_t)(gtp_info.length + 4) > payload_len)
        return PKT_OK;

    if(t_flag){
        /*get teid */
        gtp_info.teid=ntohl(get_uint32_t(payload, offset));
        offset+=4;
    }

    /*get seq number */
    gtp_info.seq=&payload[offset];
    gtp_info.seq_len=3;
    offset+=3;

    //if(mp_flag){

    //}else{

    //}
    /*get spare */
    gtp_info.spare=get_uint8_t(payload,offset);
    offset+=1;

     /*
     * Octets   8   7   6   5       4   3   2   1
     *  1       Type
     *  2-3     Length = n
     *  4       CR          Spare   Instance
     * 5-(n+4)  IE specific data
     */
     dissect_gtpv2_ie_common(flow, payload, payload_len,offset,&gtp_info);
     write_gtp_control_log(flow, direction, &gtp_info);

     return PKT_OK;
}

void init_gtp_control_field(void)
{
                              //多两个　EM_GTP_CONTROL_MAX EM_GTP_CONTROL_IP4_MAX
    dpi_register_proto_schema(gtp_control_field_array,EM_GTP_CONTROL_IP6_MAX - 2,"gtp_control");
}

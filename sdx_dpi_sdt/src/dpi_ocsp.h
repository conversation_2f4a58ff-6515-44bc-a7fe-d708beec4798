/****************************************************************************************
 * 文 件 名 : dpi_ocsp.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh             2018/12/10
编码: liugh               2018/12/10
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef __DPI_OCSP__
#define __DPI_OCSP__

#define OCSP_PORT_1 80
#define OCSP_PORT_2 8080


#define OCSP_STRING_LENGTH 512

enum  ocsp_index_em{
    EM_OCSP_RESPONSETYPEID,
    EM_OCSP_BASICOCSPRESPONSE,
    EM_OCSP_ARCHIVECUTOFF,
    EM_OCSP_ACCEPTABLERESPONSES,
    EM_OCSP_SERVICELOCATOR,
    EM_OCSP_CRLID,
    EM_OCSP_REOCSPNONCE,
    EM_OCSP_NULL_ELEMENT,
    EM_OCSP_TBSREQUEST_ELEMENT,
    EM_OCSP_OPTIONALSIGNATURE_ELEMENT,
    EM_OCSP_VERSION,
    EM_OCSP_REQUESTORNAME,
    EM_OCSP_REQUESTLIST,
    EM_OCSP_REQUEST_ELEMENT,
    EM_OCSP_REQUESTEXTENSIONS,
    EM_OCSP_SIGNATUREALGORITHM_ELEMENT,
    EM_OCSP_SIGNATURE,
    EM_OCSP_CERTS,
    EM_OCSP_CERTIFICATE_ELEMENT,
    EM_OCSP_REQCERT_ELEMENT,
    EM_OCSP_SINGLEREQUESTEXTENSIONS,

    EM_OCSP_HASHALGORITHM_ELEMENT_0,
    EM_OCSP_ISSUERNAMEHASH_0,
    EM_OCSP_ISSUERKEYHASH_0,
    EM_OCSP_SERIALNUMBER_0,
    EM_OCSP_HASHALGORITHM_ELEMENT_1,
    EM_OCSP_ISSUERNAMEHASH_1,
    EM_OCSP_ISSUERKEYHASH_1,
    EM_OCSP_SERIALNUMBER_1,
    EM_OCSP_HASHALGORITHM_ELEMENT_2,
    EM_OCSP_ISSUERNAMEHASH_2,
    EM_OCSP_ISSUERKEYHASH_2,
    EM_OCSP_SERIALNUMBER_2,
    EM_OCSP_HASHALGORITHM_ELEMENT_3,
    EM_OCSP_ISSUERNAMEHASH_3,
    EM_OCSP_ISSUERKEYHASH_3,
    EM_OCSP_SERIALNUMBER_3,

    EM_OCSP_CLIENT_REQUEST_EXT,
	EM_OCSP_CLIENT_REQUEST_NOUNCE,


    EM_OCSP_RESPONSESTATUS,
    EM_OCSP_RESPONSEBYTES_ELEMENT,
    EM_OCSP_RESPONSETYPE,
    EM_OCSP_RESPONSE,
    EM_OCSP_TBSRESPONSEDATA_ELEMENT,
    EM_OCSP_RESPONDERID,
    EM_OCSP_PRODUCEDAT,
    EM_OCSP_RESPONSES,
    EM_OCSP_SINGLERESPONSE_ELEMENT,
    EM_OCSP_RESPONSEEXTENSIONS,
    EM_OCSP_BYNAME,
    EM_OCSP_BYKEY,
    EM_OCSP_CERTID_ELEMENT,
    EM_OCSP_CERTSTATUS,
    EM_OCSP_THISUPDATE,
    EM_OCSP_NEXTUPDATE,
    EM_OCSP_SINGLEEXTENSIONS,
    EM_OCSP_GOOD_ELEMENT,
    EM_OCSP_REVOKED_ELEMENT,
    EM_OCSP_UNKNOWN_ELEMENT,
    EM_OCSP_REVOCATIONTIME,
    EM_OCSP_REVOCATIONREASON,
    EM_OCSP_ACCEPTABLERESPONSES_ITEM,
    EM_OCSP_ISSUER,
    EM_OCSP_LOCATOR,
    EM_OCSP_CRLURL,
    EM_OCSP_CRLNUM,
    EM_OCSP_CRLTIME,


    EM_OCSP_X509_VERSION,
    EM_OCSP_X509_SERIALNUMBER,
    EM_OCSP_X509_SIGNATURECERTIFICATE,
    EM_OCSP_X509_ISSUER,
    EM_OCSP_X509_CERTNOTBEFORETIME,
    EM_OCSP_X509_CERTNOTAFTERTIME,
    EM_OCSP_X509_SUBJECTNAME,
    EM_OCSP_X509_PUBLICKEYALGORITHM,
    EM_OCSP_X509_PUBLICKEYBITS,
    EM_OCSP_X509_PUBLICMODULUS,
    EM_OCSP_X509_ISSUERUNIQUEID,
    EM_OCSP_X509_SUBJECTUNIQUEID,
    EM_OCSP_X509_ALGORITHMID,
	
	EM_OCSP_X509_COUNTRY_NAME,
	EM_OCSP_X509_LOCAL_NAME,
	EM_OCSP_X509_STATE_NAME,
	EM_OCSP_X509_STREET_NAME,
	EM_OCSP_X509_ORG_NAME,
	EM_OCSP_X509_ORG_UNIT_NAME,
	EM_OCSP_X509_TITLE,
	EM_OCSP_X509_DISC,
	EM_OCSP_X509_BUS_CAT,
	EM_OCSP_X509_POS_ADDR,
	EM_OCSP_X509_POS_CODE,
	EM_OCSP_X509_POST_BOX,
	EM_OCSP_X509_DEL_OFF_NAME,
	EM_OCSP_X509_TELNUM,
	EM_OCSP_X509_TELEXNUM,
	EM_OCSP_X509_FASTELNUM,

    EM_OCSP_MAX
	
};


 



struct ocsp_data_t{
    const uint8_t data;
    uint16_t      len;
};

typedef struct _ocsp_info_t{
    //long version;
    //uint32_t ser_number;
    //struct ocsp_data_t ocsp_array[EM_OCSP_MAX];

    char ocsp_array[EM_OCSP_MAX][OCSP_STRING_LENGTH];
}ocsp_info_t;





int dissect_ocsp_request(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len);

int dissect_ocsp_response(struct flow_info *flow,  const uint8_t *payload, const uint32_t payload_len);


//__attribute((constructor)) void     before_init_ocsp(void);


#endif


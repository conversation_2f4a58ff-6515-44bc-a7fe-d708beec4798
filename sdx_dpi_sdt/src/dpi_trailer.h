#ifndef _DPI_TRAILER_H_
#define _DPI_TRAILER_H_

#include <string.h>
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <arpa/inet.h>

#ifndef _BSD_SOURCE
#define _BSD_SOURCE
#endif
#include <endian.h>

#define TLV_ARR_MAX 21

#if 1

#define max(x, y) ((x) > (y) ? (x) : (y))
#define min(x, y) ((x) > (y) ? (y) : (x))
#define get_uint8_t(X,O)  (*(const uint8_t *)(((const uint8_t *)X) + O))
#define get_uint16_t(X,O)  (*(const uint16_t *)(((const uint8_t *)X) + O))
#define get_uint32_t(X,O)  (*(const uint32_t *)(((const uint8_t *)X) + O))
#define get_uint64_t(X,O)  (*(const uint64_t *)(((const uint8_t *)X) + O))

#define get_uint16_ntohs(X,O)  (ntohs(*(const uint16_t *)(((const uint8_t *)X) + O)))
#define get_uint32_ntohl(X,O)  (ntohl(*(const uint32_t *)(((const uint8_t *)X) + O)))

#endif

//trailer 类型
typedef enum{

	INVALID_TYPE = 0,

	//以下移动网,有trailer
	RT_6402,
	RT_9800,
	RT_SINO,
	HW_DEFAULT,
	HW_DB,
	HW_YN,
	JL_DEFAULT,
	FL_DEFAULT,
	HZ_SOFT,
    BJ_JSD,

	//以下固网,无trailer,运营商标识在eth header里
	HW_SH_MAC,
	HW_SH_VLAN,
	HY_FS,
	PH_DEFAULT,
    LINE_470,

}Trailer_Type;

//网络类型
typedef enum{
	INVALID_NET = 0,
	FIXED_NET,   //固网
	MOBILE_NET   //移动
}Net_Type;

//此结构用于保存以太网头部部分信息
struct data_link_layer_t
{
	uint8_t   nsp;       //network service provider
	uint8_t   base;      //data source, 2G 3G 4G etc.
	uint16_t  vlan_id[8];   //for HW_SH_VLAN
    uint32_t  mpls_label[12];

	uint16_t  inter_id;  //for HY
	uint64_t  line_id;   //for 470
	uint64_t  data;      //compass above

};

//RT trailer --->
struct rt_trailer{
	uint8_t   base;
	uint16_t  tac;
	uint16_t  plmn_id;
	uint32_t  teid;
	uint32_t  srcip;
	uint32_t  dstip;
	uint32_t  uli;
	uint64_t  imei;
	uint64_t  imsi;
	uint64_t  msisdn;
};
//<--- RT trailer 


//HW default & db trailer --->
enum hw_default_str_type{
	EM_HW_ACCOUNT = 0,
	EM_HW_MSISDN,
	EM_HW_IMEI,
	EM_HW_ESN,
	EM_HW_MEID,
	EM_HW_BSID,
	EM_HW_STR_MAX
};

enum hw_default_short_type{
	EM_HW_LAC = 0,
	EM_HW_SAC,
	EM_HW_CI,
	EM_HW_TAC,
	EM_HW_TAI,
	EM_HW_MNC,
	EM_HW_SHORT_MAX
};

enum hw_default_long_type{
	EM_HW_ECGI = 0,
	EM_HW_GRE_KEY,
	EM_HW_TEID,
	EM_HW_GTP_SIP,
	EM_HW_GTP_DIP,
	EM_HW_LONG_MAX
};

#define TLV_MAX_LENGTH 16 
struct v_s{
	uint8_t len;
	uint8_t str[TLV_MAX_LENGTH];
};

struct hw_default_trailer{
	uint8_t nsp;
	uint8_t base;
    uint8_t data_type;
	uint64_t imsi;
	
	struct v_s tlv_str[EM_HW_STR_MAX];
	uint16_t tlv_short[EM_HW_SHORT_MAX];
	uint32_t tlv_long[EM_HW_LONG_MAX];

    char apn[33];
    char RSClueid[33];
    char BidClueid[33];

};
//<--- HW default & db trailer

//HW yn trailer --->

enum hw_yn_field{

	HW_BEGIN   = 0,
	HW_IMSI    = 2,
	HW_MSISDN  = 12,
	HW_IMEI    = 22,
	HW_CGI     = 32,
	HW_SAI     = 41,
	HW_RAI     = 50,
	HW_TAI     = 58,
	HW_ECGI    = 65,
	HW_RULE_ID = 74,
	
};

struct hw_yn_trailer{
	uint8_t len;
	uint8_t data[81];
};
//<--- HW yn trailer


//FL trailer --->
enum fl_field{

    FL_TEID    = 0,
    FL_SRC_IP  = 8,
    FL_DST_IP  = 12,
    FL_MSISDN  = 16,
    FL_IMEI    = 23,
    FL_IMSI    = 30,
    FL_TAC     = 37,
    FL_PLMN_ID = 39,
    FL_ULI     = 41,
    FL_BASE    = 45,
};

struct fl_trailer{
    uint8_t len;
    uint8_t data[56];
};
//<--- FL trailer

//---> JL trailer
struct jl_trailer{
	uint8_t   base;
	uint8_t   mnc;
	uint16_t  mcc;
	uint16_t  lac;
	uint32_t  cgi_ci;
	uint8_t   esn[5];
	uint8_t   bsid[7];
	uint8_t   meid[8];
	uint16_t  undef[4];       // 0:cell_id, 1:sac, 2:rac, 3:tac
	char      apn[16];
	uint8_t   dev_tag[3][9];  // 0:imsi, 1:msisdn, 2:imei
};
//<--- JL trailer

//---> HZ trailer
struct hz_trailer{
    uint64_t   imsi;
    uint64_t   msisdn;
    uint64_t   imei;
    uint8_t    ncode;
    uint16_t   mcc;
    uint16_t   mnc;
    uint16_t   lac;
    uint16_t   ci;
    uint32_t   ecgi;
    uint16_t   tac;
};
//<--- HZ trailer

//西安327
#define TBL_LOG_MAX_LEN           (1024 * 20)
typedef struct _yv_trailer_info {
  uint8_t  magic_code[2];
  uint16_t ext_data_len;               // ext data 的长度
  char     ext_data[TBL_LOG_MAX_LEN];  // ext data 的字符串
} __attribute__((__packed__)) yv_trailer_info;

/*
 *初始化时根据配置文件获得trailer类型,
 *为了方便计算,网络类型及trailer结构体长度也一并获取
 */
void get_trailer_type(const char* arg, Trailer_Type *trailer_type, Net_Type *net_type, size_t *trailer_len);

//获取以太头信息,等待建流之后传给flow
void get_eth_info(const uint8_t *payload, uint32_t payload_len, struct data_link_layer_t *data_link_layer, Trailer_Type type);


//解析trailer,parse_trailer
int parse_trailer(void** pTrailer, const uint8_t *payload, uint16_t payload_len, Trailer_Type type, size_t len);

void parse_rt_trailer(struct rt_trailer *trailer, const uint8_t *payload, uint16_t payload_len);
void parse_rt_sino_trailer(struct rt_trailer *trailer, const uint8_t *payload, uint16_t payload_len);

void parse_hw_default_trailer(struct hw_default_trailer *trailer, const uint8_t *payload, uint16_t payload_len, int is_jsd);
void parse_hw_yn_trailer(struct hw_yn_trailer *trailer, const uint8_t *payload, uint16_t payload_len);
void parse_jl_trailer(struct jl_trailer *trailer, const uint8_t *payload, uint16_t payload_len);
void parse_fl_trailer(struct fl_trailer *trailer, const uint8_t *payload, uint16_t payload_len);
void parse_hz_soft_trailer(struct hz_trailer *trailer, const uint8_t *payload, uint16_t payload_len);

/*
 hw_yn transfer imei/imsi/msisdn
 src: 源数据
 dst: 目的数据
 len: 源数据长度
 return: 目的数据长度
*/
uint32_t get_bcd(const uint8_t *src, uint8_t *dst, int len);

// 销毁trailer:
void destroy_trailer(void** pTrailer);

#endif // _DPI_TRAILER_H_

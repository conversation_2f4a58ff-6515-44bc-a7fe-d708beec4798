/* ftp://ftp.cc.uoc.gr/mirrors/OpenBSD/src/lib/libc/stdlib/tsearch.c */
/* find or insert datum into search tree */

#include "dpi_b_tree.h"
#include <stdlib.h>

void * ndpi_tsearch(const void *vkey, void **vrootp, int (*compar)(const void *, const void *))
{
    ndpi_node *q;
    char *key = (char *)vkey;
    ndpi_node **rootp = (ndpi_node **)vrootp;

    if(rootp == (ndpi_node **)0)
        return ((void *)0);
    while (*rootp != (ndpi_node *)0) {    /* Knuth's T1: */
        int r;

        if((r = (*compar)(key, (*rootp)->key)) == 0)    /* T2: */
            return ((void *)*rootp);    /* we found it! */
        rootp = (r < 0) ?
            &(*rootp)->left :        /* T3: follow left branch */
            &(*rootp)->right;        /* T4: follow right branch */
    }
    q = (ndpi_node *) malloc(sizeof(ndpi_node));    /* T5: key not found */
    if(q != (ndpi_node *)0) {    /* make new node */
        *rootp = q;            /* link new node to old */
        q->key = key;        /* initialize new node */
        q->left = q->right = (ndpi_node *)0;
    }
    return ((void *)q);
}

/* delete node with given key */
void * ndpi_tdelete(const void *vkey, void **vrootp, int (*compar)(const void *, const void *))
{
    ndpi_node **rootp = (ndpi_node **)vrootp;
    char *key = (char *)vkey;
    ndpi_node *p = (ndpi_node *)1;
    ndpi_node *q;
    ndpi_node *r;
    int cmp;

    if(rootp == (ndpi_node **)0 || *rootp == (ndpi_node *)0)
        return ((ndpi_node *)0);
    while ((cmp = (*compar)(key, (*rootp)->key)) != 0) {
        p = *rootp;
        rootp = (cmp < 0) ?
            &(*rootp)->left :        /* follow left branch */
            &(*rootp)->right;        /* follow right branch */
        if(*rootp == (ndpi_node *)0)
            return ((void *)0);        /* key not found */
    }
    r = (*rootp)->right;            /* D1: */
    if((q = (*rootp)->left) == (ndpi_node *)0)    /* Left (ndpi_node *)0? */
        q = r;
    else if(r != (ndpi_node *)0) {        /* Right link is null? */
        if(r->left == (ndpi_node *)0) {    /* D2: Find successor */
            r->left = q;
            q = r;
        } else {            /* D3: Find (ndpi_node *)0 link */
            for(q = r->left; q->left != (ndpi_node *)0; q = r->left)
                r = q;
            r->left = q->right;
            q->left = (*rootp)->left;
            q->right = (*rootp)->right;
        }
    }
    free((ndpi_node *) *rootp);    /* D4: Free node */
    *rootp = q;                /* link parent to new node */
    return(p);
}

/* Walk the nodes of a tree */
static void ndpi_trecurse(ndpi_node *root, void (*action)(const void *, ndpi_VISIT, int, void*), int level, void *user_data)
{
    if(root->left == (ndpi_node *)0 && root->right == (ndpi_node *)0)
        (*action)(root, ndpi_leaf, level, user_data);
    else {
        (*action)(root, ndpi_preorder, level, user_data);
        if(root->left != (ndpi_node *)0)
            ndpi_trecurse(root->left, action, level + 1, user_data);
        (*action)(root, ndpi_postorder, level, user_data);
        if(root->right != (ndpi_node *)0)
            ndpi_trecurse(root->right, action, level + 1, user_data);
        (*action)(root, ndpi_endorder, level, user_data);
    }
}

/* Walk the nodes of a tree */
void ndpi_twalk(const void *vroot, void (*action)(const void *, ndpi_VISIT, int, void *), void *user_data)
{
    ndpi_node *root = (ndpi_node *)vroot;

    if(root != (ndpi_node *)0 && action != (void (*)(const void *, ndpi_VISIT, int, void*))0)
        ndpi_trecurse(root, action, 0, user_data);
}

/* find a node, or return 0 */
void * ndpi_tfind(const void *vkey, void *vrootp, int (*compar)(const void *, const void *))
{
    char *key = (char *)vkey;
    ndpi_node **rootp = (ndpi_node **)vrootp;

    if(rootp == (ndpi_node **)0)
        return ((ndpi_node *)0);
    while (*rootp != (ndpi_node *)0) {    /* T1: */
        int r;
        if((r = (*compar)(key, (*rootp)->key)) == 0)    /* T2: */
            return (*rootp);        /* key found */
        rootp = (r < 0) ?
            &(*rootp)->left :        /* T3: follow left branch */
            &(*rootp)->right;        /* T4: follow right branch */
    }
    return (ndpi_node *)0;
}

/* ****************************************** */

/* Walk the nodes of a tree */
static void ndpi_tdestroy_recurse(ndpi_node* root, void (*free_action)(void *))
{
    if(root->left != NULL)
        ndpi_tdestroy_recurse(root->left, free_action);
    if(root->right != NULL)
        ndpi_tdestroy_recurse(root->right, free_action);

    (*free_action) ((void *) root->key);
    free(root);
}

void ndpi_tdestroy(void *vrootp, void (*freefct)(void *))
{
    ndpi_node *root = (ndpi_node *) vrootp;

    if(root != NULL)
        ndpi_tdestroy_recurse(root, freefct);
}


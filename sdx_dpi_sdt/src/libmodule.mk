#make trailer -f libmodule.mk
.PHONY:trailer
trailer:
	gcc -c dpi_trailer.c  dpi_rt_trailer.c  dpi_hz_trailer.c dpi_hw_default_trailer.c  dpi_hw_yn_trailer.c dpi_jl_trailer.c dpi_fl_trailer.c
	ar -crv libtrailer.a dpi_trailer.o  dpi_hz_trailer.o dpi_rt_trailer.o dpi_hw_default_trailer.o dpi_hw_yn_trailer.o dpi_jl_trailer.o dpi_fl_trailer.o
	mv -f libtrailer.a  ../lib
	cp -f dpi_trailer.h ../include
	rm -f dpi_trailer.o dpi_rt_trailer.o dpi_hz_trailer.o dpi_hw_default_trailer.o dpi_hw_yn_trailer.o dpi_jl_trailer.o dpi_fl_trailer.o


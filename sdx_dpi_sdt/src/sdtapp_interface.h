#ifndef SDT_APP_INTERFACE_H
#define SDT_APP_INTERFACE_H

#include <yaProtoRecord/precord.h>

#include "libsdt/sdt_types.h"
#include "libsdtacl/yasdtacl.h"
//#include "sdt_plugin_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 该接口由 sdt-host 提供并实现，接口使用方为 libsdt.a
 */
#define  SDT_STREAM_BUFF_SIZE    3000
#define  SDT_DIR_NUM             2
#define  SDT_STREAM_OFFSET_LEN   256
#define  SDT_FIELD_NAME_LEN      64
#define  SDT_PROTO_NAME_SIZE     20


typedef struct _DpiProtoDef
{
    int protoLayer;                 /* 协议层 */
    char protoName[SDT_PROTO_NAME_SIZE];             /* 协议名 */
    int fieldCnt;                   /* 协议字段数 */
}DpiProtoDef;

typedef struct MemoryAlloc_  MemoryAlloc;

enum value_em
{
    VALUE_TYPE_BOOL,
    VALUE_TYPE_INT,
    VALUE_TYPE_UINT,
    VALUE_TYPE_BYTES,
    VALUE_TYPE_TIME,
};

//为了解决 关键字在求值时直接运算,不要提前构造 precord_t 对象
struct value_type
{
    enum value_em   type;
    int             len; //only bytes
    const void     *val;
};

/*
 * 协议解析结果记录类型声明，由 sdt-app 定义
 */
//typedef struct ProtoRecord ProtoRecord;
/*
 * 协议解析结果记录类型声明，由 sdt-app 定义
 */
typedef struct ProtoRecord
{
    u8               direction;
    int              rule_id;
    u8               rule_mode;  // 报文模式还是流模式
    precord_t       *record;
    /* sdtapp fields operation */
    char             proto_name[SDT_PROTO_NAME_SIZE];
    uint16_t         proto_id;

    /* sdt packet payload operation */
    uint8_t          ip_proto;
    uint16_t         l3h_start;       /*二层为基准的网络层head位置偏移量*/
    uint16_t         l4h_start;       /*二层为基准的传输层head位置偏移量也是ip的payload部分（针对tcp，udp）*/
    uint16_t         l4payload_start; /*二层为基准的传输层payload偏移量*/
    PayloadBuff      pPayload;        /*该PayloadBuff指向报文二层开始地方*/
    uint16_t         ip_version;
    uint16_t         ip_len;
    uint16_t         tcp_flags;
    uint16_t         tcp_windowsize;
    uint32_t         seq;

    ip_tuples        tuple;

    const struct pkt_info   *pkt;
    struct flow_info        *flow;         //丑陋的方式, 待调整. 用于求值时, common,link的部分数据来源于flow
    uint16_t        match_data_len; /* tcp重组回调解析函数中的待解析的报文长度. 用于匹配信息统计,超时预警.
                                        传递路径: flow -> tbl -> record */
    struct value_type val_temp; //返回 临时对象生命空间
    char              val_temp_buff[1024];  //有些需要转换的值,需要使用之后的数据做运算.
}ProtoRecord;

/* 协议字段头 */
typedef struct _dpi_field_table_t
{
    uint32_t        index;
    uint8_t         type;
    const char     *field_name;
    uint32_t        proto_id; //字段的 pschema ID
    uint32_t        field_id; //字段的 pschema ID
    struct value_type*(*callback)(ProtoRecord *p);
} dpi_field_table;

/* 表达式运行专用API */
struct value_type* sdtAppProtoRecord_getFieldByIDv3(ProtoRecord *pRec, int protoID, int fieldID);


/* 协议字典 */

int           sdtAppProtoDict_Register(dpi_field_table *protoTbl,
                                                 void *protoHash,
                                                 int protoID,
                                                 int fieldNum,
                                                 const char *name);
void*         sdtAppProtoDict_Init(void);
int           sdtAppProtoDict_Fini(void *vdict);
int           sdtAppProtoDict_getProtoCnt(void *vdict);
ProtoDef*     sdtAppProtoDict_getProtoByIndex(void *vdict, int protoID);
FieldHdrDef*  sdtAppProtoDict_getFieldDefByIndex(ProtoDef *fieldDef, int fieldIndex);
/* 调用示例：
 * "http.host"   -> protoName = "http", fieldName = "host"
 * "tcp_payload" -> protoName = NULL, fieldName = "tcp_payload"
 */

FieldHdrDef*  sdtAppProtoDict_getFieldDefByName(void *vdict, char *protoName, char *fieldName);


/* ProtoRecord 与 PayloadBuff */
int           sdtAppProtoRecord_getFieldCnt(ProtoRecord *pRec);
int           sdtAppProtoRecord_getIpTuple(ProtoRecord *pRec, ip_tuples *pTuple);
int           sdtAppProtoRecord_getProtoId(ProtoRecord *pRec);
const char*   sdtAppProtoRecord_getProtoName(ProtoRecord *pRec);


int           sdtAppProtoRecord_getIpSequence(ProtoRecord *pRec,ys_ipseq *ipSeqs, int dir);
uint32_t      sdtAppProtoRecord_getMatchDataLen(ProtoRecord *pRec);


int SdtAppRuleHash_ruleTransactStart(int transactId);
int SdtAppRuleHash_ruleTransactFinish(int transactId, SdtErrorMsg_t *pErr);

int SdtAppRuleHash_RuleInitClear(void);
int SdtAppRuleHash_RuleInitStart(void);
int SdtAppRuleHash_ruleInitEnd(void);
int SdtAppRuleHash_insertRules(SdtMatchResult *ruleAction);

/* 规则命中统计记录 */
rule_web_stat* SdtAppRuleHash_getStatistics(u32 rule_hash_code);


//SDT 参数传输到 libsdt引擎
int get_sdt_sysconfig(enum EN_SDT_SYSCONFIG);

int set_sdt_sysconfig(enum EN_SDT_SYSCONFIG type, void *arg);

//protoID 是否可以 IPFF
int proto_mode_would_ipff(int protoid);

//获取设备id
const char* get_sdt_sysconfig_device_id(void);

//存在有效规则即为工作中，否则为空闲
int get_sdt_program_status(void);

#ifdef __cplusplus
}
#endif

#endif /* end of SDT_APP_INTERFACE_H */

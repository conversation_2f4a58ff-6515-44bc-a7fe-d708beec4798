#include "dpi_sdt_eval_field_callback.h"
struct value_type*cb_ip_flag(ProtoRecord *pRec)
{
    struct value_type   *p     = &pRec->val_temp;
    struct dpi_iphdr    *iph4  = (struct dpi_iphdr*)  pRec->pkt->iph4;
    if(iph4)
    {
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(iph4->frag_off>>5); //只保留FLAG(3 bit)
        return p;
    }
    return NULL;
}
struct value_type*cb_ip_len(ProtoRecord *pRec)
{
    struct value_type   *p     = &pRec->val_temp;
    struct dpi_iphdr    *iph4  = (struct dpi_iphdr*)  pRec->pkt->iph4;
    if(iph4)
    {
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)ntohs(iph4->tot_len);
        return p;
    }
    return NULL;
}
struct value_type*cb_ip_header(ProtoRecord *pRec)
{
    struct value_type   *p     = &pRec->val_temp;
    struct dpi_iphdr    *iph4  = (struct dpi_iphdr*)  pRec->pkt->iph4;
    if(iph4)
    {
        p->type = VALUE_TYPE_BYTES;
        p->len  = iph4->ihl * 5;
        p->val  = (void*)(size_t)iph4;
        return p;
    }
    return NULL;
}
struct value_type*cb_ip_ttl(ProtoRecord *pRec)
{
    struct value_type   *p     = &pRec->val_temp;
    struct dpi_iphdr    *iph4  = (struct dpi_iphdr*)  pRec->pkt->iph4;
    if(iph4)
    {
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)iph4->ttl;
        return p;
    }
    return NULL;
}
struct value_type*cb_ip_payload(ProtoRecord *pRec)
{
    struct value_type   *p     = &pRec->val_temp;
    struct dpi_iphdr    *iph4  = (struct dpi_iphdr*)  pRec->pkt->iph4;
    if(iph4)
    {
        int iphdr_len = iph4->ihl*4;
        int iptot_len = ntohs(iph4->tot_len);
        p->type = VALUE_TYPE_BYTES;
        p->len  = iptot_len - iphdr_len;
        p->val  = (void*)(((unsigned char*)pRec->pkt->iph4) + iphdr_len);
        return p;
    }
    return NULL;
}
struct value_type*cb_ip_payload_len(ProtoRecord *pRec)
{
    struct value_type   *p     = &pRec->val_temp;
    struct dpi_iphdr    *iph4  = (struct dpi_iphdr*)  pRec->pkt->iph4;
    if(iph4)
    {
        int iphdr_len = iph4->ihl*4;
        int iptot_len = ntohs(iph4->tot_len);
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(iptot_len - iphdr_len);
        return p;
    }
    return NULL;
}
//////////////////////////
struct value_type*cb_udp_header(ProtoRecord *pRec)
{
    if(pRec->pkt->udph)
    {
        const struct dpi_udphdr *udph = (const struct dpi_udphdr *)pRec->pkt->udph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = 8;
        p->val  = udph;
        return p;
    }
    return NULL;
}
struct value_type* cb_udp_payload(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = pRec->pkt->payload_len;
    p->val  = pRec->pkt->payload;
    return p;
}
struct value_type* cb_udp_payload_length(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)pRec->pkt->payload_len;
    return p;
}
/////////////////////////////////////
struct value_type*cb_tcp_header(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = tcph->doff * 4;
        p->val  = tcph;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_header_len(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(tcph->doff * 4);
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        uint32_t var = 0;
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)pRec->pkt->tcph;
        var |= tcph->fin << 0;
        var |= tcph->syn << 1;
        var |= tcph->rst << 2;
        var |= tcph->psh << 3;
        var |= tcph->ack << 4;
        var |= tcph->urg << 5;
        var |= tcph->ece << 6;
        var |= tcph->cwr << 7;

        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)var;
        return p;
    }
    return NULL;
}

struct value_type*cb_tcp_flag_fin(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr*tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->fin;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_syn(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->syn;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_rst(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->rst;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_psh(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr*tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->psh;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_ack(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr*tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->ack;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_ugr(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr*tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->urg;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_ece(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr*tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->ece;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_cwr(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr*tcph = (const struct dpi_tcphdr*)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)tcph->cwr;
        return p;
    }
    return NULL;
}
struct value_type*cb_tcp_flag_ns(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = NULL;
    return p;
}
struct value_type*cb_tcp_windowsize(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)pRec->pkt->tcph;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)ntohs(tcph->window);
        return p;
    }
    return NULL;
}
struct value_type* cb_tcp_payload(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = pRec->pkt->payload_len;
    p->val  = pRec->pkt->payload;
    return p;
}
struct value_type* cb_tcp_payload_length(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)pRec->pkt->payload_len;
    return p;
}

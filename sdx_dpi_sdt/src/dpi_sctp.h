#include <stdio.h>

#ifndef _YV_PROTO_SCTP_H_
#define _YV_PROTO_SCTP_H_

struct sctp_info{
    const struct dpi_sctphdr *sctph;
    const uint8_t  *cookie;
    const uint8_t  *payload;
    uint8_t  type, flag, dir;
    uint16_t length, s_id, s_seq, num_in, num_out, cookie_len, num_gaps, num_dup_tsn;
    uint32_t t_seq, proto_id, init_tag, init_tsn, a_rwnd, tsn_ack;
    int32_t chunk_type, payload_len;
};

void dpi_dissect_sctp(struct flow_info *flow, int src_to_dst_direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
#endif


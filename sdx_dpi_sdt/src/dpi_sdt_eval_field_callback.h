#include "sdtapp_interface.h"
#include "libsdt/sdt_types.h"
#include "dpi_detect.h"

#ifndef DPI_SDT_EVAL_FIELD_CALLBACK_H
#define DPI_SDT_EVAL_FIELD_CALLBACK_H

#ifdef __cplusplus
extern "C" {
#endif

struct value_type*cb_ip_flag(ProtoRecord *pRec);
struct value_type*cb_ip_len(ProtoRecord *pRec);
struct value_type*cb_ip_header(ProtoRecord *pRec);
struct value_type*cb_ip_ttl(ProtoRecord *pRec);
struct value_type*cb_ip_payload(ProtoRecord *pRec);
struct value_type*cb_ip_payload_len(ProtoRecord *pRec);

struct value_type*cb_udp_header(ProtoRecord *pRec);
struct value_type*cb_udp_payload(ProtoRecord *pRec);
struct value_type*cb_udp_payload_length(ProtoRecord *pRec);

struct value_type*cb_tcp_header(ProtoRecord *pRec);
struct value_type*cb_tcp_header_len(ProtoRecord *pRec);
struct value_type*cb_tcp_flag(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_fin(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_syn(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_rst(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_psh(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_ack(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_ugr(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_ece(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_cwr(ProtoRecord *pRec);
struct value_type*cb_tcp_flag_ns(ProtoRecord *pRec);
struct value_type*cb_tcp_windowsize(ProtoRecord *pRec);
struct value_type*cb_tcp_payload(ProtoRecord *pRec);
struct value_type*cb_tcp_payload_length(ProtoRecord *pRec);

struct value_type*cb_share_linename1(ProtoRecord *pRec);
struct value_type*cb_share_begin_time(ProtoRecord *pRec);
struct value_type*cb_share_end_time(ProtoRecord *pRec);
struct value_type*cb_share_dru_time(ProtoRecord *pRec);
struct value_type*cb_share_ip_ver(ProtoRecord *pRec);
struct value_type*cb_share_src_addr(ProtoRecord *pRec);
struct value_type*cb_share_dst_addr(ProtoRecord *pRec);
struct value_type*cb_share_src_port(ProtoRecord *pRec);
struct value_type*cb_share_dst_port(ProtoRecord *pRec);
struct value_type*cb_share_porto(ProtoRecord *pRec);
struct value_type*cb_share_src_addr_v6(ProtoRecord *pRec);
struct value_type*cb_share_dst_addr_v6(ProtoRecord *pRec);
struct value_type*cb_share_proto_info(ProtoRecord *pRec);
struct value_type*cb_share_proto_type(ProtoRecord *pRec);
struct value_type*cb_share_proto_name(ProtoRecord *pRec);
struct value_type*cb_share_pkt_num(ProtoRecord *pRec);
struct value_type*cb_share_pkt_len(ProtoRecord *pRec);
struct value_type*cb_share_etags(ProtoRecord *pRec);
struct value_type*cb_share_ttags(ProtoRecord *pRec);
struct value_type*cb_share_atags(ProtoRecord *pRec);
struct value_type*cb_share_utags(ProtoRecord *pRec);
struct value_type*cb_share_mpls_lable1(ProtoRecord *pRec);
struct value_type*cb_share_mpls_lable2(ProtoRecord *pRec);
struct value_type*cb_share_mpls_lable3(ProtoRecord *pRec);
struct value_type*cb_share_mpls_lable4(ProtoRecord *pRec);
struct value_type*cb_share_vlan_id1(ProtoRecord *pRec);
struct value_type*cb_share_vlan_id2(ProtoRecord *pRec);
struct value_type*cb_share_src_mac(ProtoRecord *pRec);
struct value_type*cb_share_dst_mac(ProtoRecord *pRec);
struct value_type*cb_share_src_ountry(ProtoRecord *pRec);
struct value_type*cb_share_src_state(ProtoRecord *pRec);
struct value_type*cb_share_src_city(ProtoRecord *pRec);
struct value_type*cb_share_src_lon(ProtoRecord *pRec);
struct value_type*cb_share_src_lat(ProtoRecord *pRec);
struct value_type*cb_share_src_isp(ProtoRecord *pRec);
struct value_type*cb_share_src_asn(ProtoRecord *pRec);
struct value_type*cb_share_dst_ountry(ProtoRecord *pRec);
struct value_type*cb_share_dst_state(ProtoRecord *pRec);
struct value_type*cb_share_dst_city(ProtoRecord *pRec);
struct value_type*cb_share_dst_lon(ProtoRecord *pRec);
struct value_type*cb_share_dst_lat(ProtoRecord *pRec);
struct value_type*cb_share_dst_isp(ProtoRecord *pRec);
struct value_type*cb_share_dst_asn(ProtoRecord *pRec);
struct value_type*cb_share_out_addr_type(ProtoRecord *pRec);
struct value_type*cb_share_out_src_addr(ProtoRecord *pRec);
struct value_type*cb_share_out_dst_addr(ProtoRecord *pRec);
struct value_type*cb_share_out_ipv6_src(ProtoRecord *pRec);
struct value_type*cb_share_out_ipv6_dst(ProtoRecord *pRec);
struct value_type*cb_share_out_port_src(ProtoRecord *pRec);
struct value_type*cb_share_out_port_dst(ProtoRecord *pRec);
struct value_type*cb_share_out_proto(ProtoRecord *pRec);
struct value_type*cb_share_capture_time(ProtoRecord *pRec);
struct value_type*cb_share_src_mac_oui(ProtoRecord *pRec);
struct value_type*cb_share_dst_mac_oui(ProtoRecord *pRec);
struct value_type*cb_link_portinfo(ProtoRecord *pRec);
struct value_type*cb_link_portinfoatt(ProtoRecord *pRec);
struct value_type*cb_link_uppaylen(ProtoRecord *pRec);
struct value_type*cb_link_downpaylen(ProtoRecord *pRec);
struct value_type*cb_link_tcpflag(ProtoRecord *pRec);
struct value_type*cb_link_uplinkpktnum(ProtoRecord *pRec);
struct value_type*cb_link_uplinksize(ProtoRecord *pRec);
struct value_type*cb_link_uplinkbigpktlen(ProtoRecord *pRec);
struct value_type*cb_link_uplinksmapktlen(ProtoRecord *pRec);
struct value_type*cb_link_uplinkbigpktint(ProtoRecord *pRec);
struct value_type*cb_link_uplinksmapktint(ProtoRecord *pRec);
struct value_type*cb_link_downlinkpktnum(ProtoRecord *pRec);
struct value_type*cb_link_downlinksize(ProtoRecord *pRec);
struct value_type*cb_link_downlinkbigpktlen(ProtoRecord *pRec);
struct value_type*cb_link_downlinksmapktlen(ProtoRecord *pRec);
struct value_type*cb_link_downlinkbigpktint(ProtoRecord *pRec);
struct value_type*cb_link_downlinksmapktint(ProtoRecord *pRec);
struct value_type*cb_link_firttlbycli(ProtoRecord *pRec);
struct value_type*cb_link_firttlbysrv(ProtoRecord *pRec);
struct value_type*cb_link_appdirec(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagsfincnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagssyncnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagsrstcnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagspshcnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagsackcnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagsurgcnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagsececnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagscwrcnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagsnscnt(ProtoRecord *pRec);
struct value_type*cb_link_tcpflagssynackcnt(ProtoRecord *pRec);
struct value_type*cb_link_etags(ProtoRecord *pRec);
struct value_type*cb_link_ttags(ProtoRecord *pRec);
struct value_type*cb_link_uplinkchecksum(ProtoRecord *pRec);
struct value_type*cb_link_downlinkchecksum(ProtoRecord *pRec);
struct value_type*cb_link_uplinkdesbytes(ProtoRecord *pRec);
struct value_type*cb_link_downlinkdesbytes(ProtoRecord *pRec);
struct value_type*cb_link_stream(ProtoRecord *pRec);
struct value_type*cb_link_uplinkstream(ProtoRecord *pRec);
struct value_type*cb_link_downlinkstream(ProtoRecord *pRec);
struct value_type*cb_link_trans_payload_hex(ProtoRecord *pRec);
struct value_type*cb_link_uplinktranspayhex(ProtoRecord *pRec);
struct value_type*cb_link_downlinktranspayhex(ProtoRecord *pRec);
struct value_type*cb_link_uplinkpaylenset(ProtoRecord *pRec);
struct value_type*cb_link_downlinkpaylenset(ProtoRecord *pRec);
struct value_type*cb_link_establish(ProtoRecord *pRec);
struct value_type*cb_link_uplinksynseqnum(ProtoRecord *pRec);
struct value_type*cb_link_downlinksynseqnum(ProtoRecord *pRec);
struct value_type*cb_link_uplinksyntcpwins(ProtoRecord *pRec);
struct value_type*cb_link_downlinksyntcpwins(ProtoRecord *pRec);
struct value_type*cb_link_uplinktcpopts(ProtoRecord *pRec);
struct value_type*cb_link_downlinktcpopts(ProtoRecord *pRec);
struct value_type*cb_link_upsesbytes(ProtoRecord *pRec);
struct value_type*cb_link_downsesbytes(ProtoRecord *pRec);
struct value_type*cb_link_sesbytes(ProtoRecord *pRec);
struct value_type*cb_link_sesbytesratio(ProtoRecord *pRec);
struct value_type*cb_link_paylenratio(ProtoRecord *pRec);

#ifdef __cplusplus
}
#endif

#endif

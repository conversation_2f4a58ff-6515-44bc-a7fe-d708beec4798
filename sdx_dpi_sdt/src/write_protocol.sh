#!/bin/sh

cd $(dirname $0)

DPI_HEAD_FILE="dpi_proto_ids.h"
DPI_SRC_FILE="dpi_proto_ids.c"
PROTO_LIST="PROTOCOL_LIST.txt"
PROTO_LOG="TBL_LOG_"
PROTO_MACRO="PROTOCOL_"

rm -rf $DPI_HEAD_FILE $DPI_SRC_FILE


#############################################################
#   dpi_proto_ids.h
#############################################################
echo "#ifndef __DPI_PROTO_IDS_H__" >> $DPI_HEAD_FILE
echo "#define __DPI_PROTO_IDS_H__" >> $DPI_HEAD_FILE

echo -e "\n" >> $DPI_HEAD_FILE
echo "#include <stdio.h>" >> $DPI_HEAD_FILE
echo "#include <stdint.h>" >> $DPI_HEAD_FILE

#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define tbl_log_type
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo -e "\n\n" >> $DPI_HEAD_FILE
echo "enum tbl_log_type {" >> $DPI_HEAD_FILE
echo "    TBL_LOG_UNKNOWN," >> $DPI_HEAD_FILE
for line in `cat $PROTO_LIST`
do
    typeset -u log_macro
    log_macro=$line
    if [ "$log_macro" = "FTP" ];then
        log_macro=$log_macro"_CONTROL"
    elif [ "$log_macro" = "SMTP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "ESMTP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "POP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "IMAP" ];then
        log_macro="MAIL_"$log_macro
    fi
    echo "    $PROTO_LOG$log_macro," >> $DPI_HEAD_FILE
done
echo "    TBL_LOG_MAX" >> $DPI_HEAD_FILE
echo "};" >> $DPI_HEAD_FILE


#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define PROTOCOL_TYPE
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo -e "\n\n" >> $DPI_HEAD_FILE
echo "enum PROTOCOL_TYPE {" >> $DPI_HEAD_FILE
echo "    PROTOCOL_UNKNOWN," >> $DPI_HEAD_FILE
for line in `cat $PROTO_LIST`
do
    typeset -u log_macro
    log_macro=$line
    if [[ "$log_macro" =~ "NAPP" ]];then
        continue
    fi
    if [ "$log_macro" = "SMTP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "ESMTP" ];then
        log_macro="MAIL_"$log_macro        
    elif [ "$log_macro" = "POP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "IMAP" ];then
        log_macro="MAIL_"$log_macro
    fi

    if [ "$log_macro" = "FTP" ];then
        echo "    "$PROTO_MACRO$log_macro"_CONTROL," >> $DPI_HEAD_FILE
        echo "    "$PROTO_MACRO$log_macro"_DATA," >> $DPI_HEAD_FILE
    else
        echo "    "$PROTO_MACRO$log_macro"," >> $DPI_HEAD_FILE
    fi
done
echo "    PROTOCOL_MAX" >> $DPI_HEAD_FILE
echo "};" >> $DPI_HEAD_FILE

#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define enum sdt
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo -e "\n\n" >> $DPI_HEAD_FILE


#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define struct tbl_log_file
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo -e "\n\n" >> $DPI_HEAD_FILE

echo -e "typedef void (*call_dissector_init_func)(void);" >> $DPI_HEAD_FILE
echo -e "\n\n" >> $DPI_HEAD_FILE
echo -e "struct tbl_log_file
{
    enum tbl_log_type type;
    int has_content;
    const char *protoname;
    FILE *fp_tbl[32];
    uint8_t is_empty[32];   // 打开文件是否为空 注意：json会默认写头，如果没写入value值默认也为空文件
    FILE *fp_content;
    unsigned int  content_offset;
    unsigned int  log_num[32];
    unsigned int  timeout_sec[32];
    char filename[32][128];
    call_dissector_init_func init_func;
};" >> $DPI_HEAD_FILE

echo -e "\n\n" >> $DPI_HEAD_FILE
echo "extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];" >> $DPI_HEAD_FILE




#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define init dessector
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#echo -e "\n\n" >> $DPI_HEAD_FILE
#for line in `cat $PROTO_LIST`
#do
#    typeset -l proto_init
#    proto_init=$line
#    if [[ "$proto_init" =~ "napp" ]];then
#        continue
#    else
#        echo "void init_"$proto_init"_dissector(void);" >> $DPI_HEAD_FILE
#    fi
#done

echo -e "\n\n" >> $DPI_HEAD_FILE
echo "#endif" >> $DPI_HEAD_FILE


##############################################################
# dpi_proto_ids.c
##############################################################

echo "#include \"$DPI_HEAD_FILE\"" >> $DPI_SRC_FILE
echo "#include \"dpi_detect.h\""  >> $DPI_SRC_FILE


#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define struct tbl_log_file tbl_log_array
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
:<<!
echo -e "\n\n" >> $DPI_SRC_FILE
echo "struct tbl_log_file tbl_log_array[TBL_LOG_MAX] =" >> $DPI_SRC_FILE
echo "{" >> $DPI_SRC_FILE
for line in `cat $PROTO_LIST`
do
    typeset -u log_macro
    log_macro=$line
    if [ "$log_macro" = "FTP" ];then
        log_macro=$log_macro"_CONTROL"
    elif [ "$log_macro" = "SMTP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "ESMTP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "POP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "IMAP" ];then
        log_macro="MAIL_"$log_macro
    elif [ "$log_macro" = "SDP" ];then
        log_macro=$log_macro"_L5"
    elif [ "$log_macro" = "ISUP" ];then
        log_macro=$log_macro"_L5"
    fi

    typeset -l proto_dir_name
    proto_dir_name=$line
    if [ "$proto_dir_name" = "http" ];then
        content_flag=1
    else
        content_flag=0
    fi
    echo "    {"  >> $DPI_SRC_FILE
    echo "        .type = $PROTO_LOG$log_macro," >> $DPI_SRC_FILE
    echo "        .has_content = $content_flag," >> $DPI_SRC_FILE
    if [[ "$proto_dir_name" =~ "napp" ]];then
        echo "        .protoname = \"$proto_dir_name\"" >> $DPI_SRC_FILE
    else
        echo "        .protoname = \"$proto_dir_name\"," >> $DPI_SRC_FILE
        echo "        .init_dissector_func = init_"$proto_dir_name"_dissector" >> $DPI_SRC_FILE
    fi
    echo "    }," >> $DPI_SRC_FILE
done
echo "};" >> $DPI_SRC_FILE
!

#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define const char *protocol_name_array[PROTOCOL_MAX]
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
echo -e "\n\n" >> $DPI_SRC_FILE
echo "const char *protocol_name_array[PROTOCOL_MAX] =" >> $DPI_SRC_FILE
echo "{" >> $DPI_SRC_FILE
echo "    \"UNKNOWN\"," >> $DPI_SRC_FILE
for line in `cat $PROTO_LIST`
do
    typeset -u log_macro
    log_macro=$line
    if [ "$log_macro" = "FTP" ];then
        echo "    "\"$log_macro"_CONTROL\"," >> $DPI_SRC_FILE
        echo "    "\"$log_macro"_DATA\"," >> $DPI_SRC_FILE
    else
        echo "    "\"$log_macro"\"," >> $DPI_SRC_FILE
    fi
done
echo "};" >> $DPI_SRC_FILE

echo -e "\n\n" >> $DPI_SRC_FILE
echo "struct guess_proto_data tcp_port_proto[65536];" >> $DPI_SRC_FILE
echo "struct guess_proto_data udp_port_proto[65536];"  >> $DPI_SRC_FILE
echo "struct guess_proto_data sctp_port_proto[65536];"  >> $DPI_SRC_FILE

echo -e "\n" >> $DPI_SRC_FILE
echo "struct check_proto_data tcp_detection_array[PROTOCOL_MAX];" >> $DPI_SRC_FILE
echo "struct check_proto_data udp_detection_array[PROTOCOL_MAX];" >> $DPI_SRC_FILE
echo "struct check_proto_data sctp_detection_array[PROTOCOL_MAX];" >> $DPI_SRC_FILE
echo -e "\n" >> $DPI_SRC_FILE


#chmod 664 $DPI_SRC_FILE
#chmod 664 $DPI_HEAD_FILE

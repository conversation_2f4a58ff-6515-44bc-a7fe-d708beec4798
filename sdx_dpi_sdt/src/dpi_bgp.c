﻿/****************************************************************************************
 * �� �� �� : dpi_bgp.c
 * ��Ŀ���� :
 * ģ �� �� :
 * ��    �� :
 * ����ϵͳ : LINUX
 * �޸ļ�¼ : ��
 * ��    �� : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
���: chenzq              2019/06/19
����: chenzq            2019/06/19
�޸�:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* ��˾���ܼ���Ȩ˵��
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"
#include "dpi_bgp.h"

#define BGP_MAX_PACKET_SIZE            4096
#define BGP_MARKER_SIZE                  16    /* size of BGP marker */
#define BGP_HEADER_SIZE                  19    /* size of BGP header, including marker */
#define BGP_MIN_NOTIFICATION_MSG_SIZE    21


/* Link-State NLRI types */
#define LINK_STATE_NODE_NLRI                    1
#define LINK_STATE_LINK_NLRI                    2
#define LINK_STATE_IPV4_TOPOLOGY_PREFIX_NLRI    3
#define LINK_STATE_IPV6_TOPOLOGY_PREFIX_NLRI    4

/* Link-State NLRI Protocol-ID values */
#define BGP_LS_NLRI_PROTO_ID_UNKNOWN       0
#define BGP_LS_NLRI_PROTO_ID_IS_IS_LEVEL_1 1
#define BGP_LS_NLRI_PROTO_ID_IS_IS_LEVEL_2 2
#define BGP_LS_NLRI_PROTO_ID_OSPF          3
#define BGP_LS_NLRI_PROTO_ID_DIRECT        4
#define BGP_LS_NLRI_PROTO_ID_STATIC        5
#define BGP_LS_NLRI_PROTO_ID_BGP           7

/* Link-State routing universes */
#define BGP_LS_NLRI_ROUTING_UNIVERSE_LEVEL_3     0
#define BGP_LS_NLRI_ROUTING_UNIVERSE_LEVEL_1     1

#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_UNKNOWN    0
#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_INTRA_AREA 1
#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_INTER_AREA 2
#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_EXTERNAL_1 3
#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_EXTERNAL_2 4
#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_NSSA_1     5
#define BGP_LS_PREFIX_OSPF_ROUTE_TYPE_NSSA_2     6

/* RFC7752 */
#define BGP_NLRI_TLV_LOCAL_NODE_DESCRIPTORS         256
#define BGP_NLRI_TLV_REMOTE_NODE_DESCRIPTORS        257
#define BGP_NLRI_TLV_LINK_LOCAL_REMOTE_IDENTIFIERS  258
#define BGP_NLRI_TLV_IPV4_INTERFACE_ADDRESS         259
#define BGP_NLRI_TLV_IPV4_NEIGHBOR_ADDRESS          260
#define BGP_NLRI_TLV_IPV6_INTERFACE_ADDRESS         261
#define BGP_NLRI_TLV_IPV6_NEIGHBOR_ADDRESS          262
#define BGP_NLRI_TLV_MULTI_TOPOLOGY_ID              263
#define BGP_NLRI_TLV_OSPF_ROUTE_TYPE                264
#define BGP_NLRI_TLV_IP_REACHABILITY_INFORMATION    265
#define BGP_NLRI_TLV_NODE_MSD                       266
#define BGP_NLRI_TLV_LINK_MSD                       267

#define BGP_NLRI_TLV_AUTONOMOUS_SYSTEM              512
#define BGP_NLRI_TLV_BGP_LS_IDENTIFIER              513
#define BGP_NLRI_TLV_AREA_ID                        514
#define BGP_NLRI_TLV_IGP_ROUTER_ID                  515
#define BGP_NLRI_TLV_BGP_ROUTER_ID                  516

#define BGP_NLRI_TLV_NODE_FLAG_BITS                 1024
#define BGP_NLRI_TLV_OPAQUE_NODE_PROPERTIES         1025
#define BGP_NLRI_TLV_NODE_NAME                      1026
#define BGP_NLRI_TLV_IS_IS_AREA_IDENTIFIER          1027
#define BGP_NLRI_TLV_IPV4_ROUTER_ID_OF_LOCAL_NODE   1028
#define BGP_NLRI_TLV_IPV6_ROUTER_ID_OF_LOCAL_NODE   1029
#define BGP_NLRI_TLV_IPV4_ROUTER_ID_OF_REMOTE_NODE  1030
#define BGP_NLRI_TLV_IPV6_ROUTER_ID_OF_REMOTE_NODE  1031

#define BGP_NLRI_TLV_ADMINISTRATIVE_GROUP_COLOR     1088
#define BGP_NLRI_TLV_MAX_LINK_BANDWIDTH             1089
#define BGP_NLRI_TLV_MAX_RESERVABLE_LINK_BANDWIDTH  1090
#define BGP_NLRI_TLV_UNRESERVED_BANDWIDTH           1091
#define BGP_NLRI_TLV_TE_DEFAULT_METRIC              1092
#define BGP_NLRI_TLV_LINK_PROTECTION_TYPE           1093
#define BGP_NLRI_TLV_MPLS_PROTOCOL_MASK             1094
#define BGP_NLRI_TLV_METRIC                         1095
#define BGP_NLRI_TLV_SHARED_RISK_LINK_GROUP         1096
#define BGP_NLRI_TLV_OPAQUE_LINK_ATTRIBUTE          1097
#define BGP_NLRI_TLV_LINK_NAME_ATTRIBUTE            1098

#define BGP_NLRI_TLV_IGP_FLAGS                      1152
#define BGP_NLRI_TLV_ROUTE_TAG                      1153
#define BGP_NLRI_TLV_EXTENDED_TAG                   1154
#define BGP_NLRI_TLV_PREFIX_METRIC                  1155
#define BGP_NLRI_TLV_OSPF_FORWARDING_ADDRESS        1156
#define BGP_NLRI_TLV_OPAQUE_PREFIX_ATTRIBUTE        1157
#define BGP_NLRI_TLV_EXTENDED_ADMINISTRATIVE_GROUP  1173


/* Link-State NLRI TLV lengths */
#define BGP_NLRI_TLV_LEN_AUTONOMOUS_SYSTEM              4
#define BGP_NLRI_TLV_LEN_BGP_LS_IDENTIFIER              4
#define BGP_NLRI_TLV_LEN_AREA_ID                        4
#define BGP_NLRI_TLV_LEN_IPV4_ROUTER_ID                 4
#define BGP_NLRI_TLV_LEN_IPV6_ROUTER_ID                 16
#define BGP_NLRI_TLV_LEN_IPV4_ROUTER_ID_OF_LOCAL_NODE   BGP_NLRI_TLV_LEN_IPV4_ROUTER_ID
#define BGP_NLRI_TLV_LEN_IPV6_ROUTER_ID_OF_LOCAL_NODE   BGP_NLRI_TLV_LEN_IPV6_ROUTER_ID
#define BGP_NLRI_TLV_LEN_IPV4_ROUTER_ID_OF_REMOTE_NODE  BGP_NLRI_TLV_LEN_IPV4_ROUTER_ID
#define BGP_NLRI_TLV_LEN_IPV6_ROUTER_ID_OF_REMOTE_NODE  BGP_NLRI_TLV_LEN_IPV6_ROUTER_ID
#define BGP_NLRI_TLV_LEN_LINK_LOCAL_REMOTE_IDENTIFIERS  8
#define BGP_NLRI_TLV_LEN_IPV4_INTERFACE_ADDRESS         4
#define BGP_NLRI_TLV_LEN_IPV4_NEIGHBOR_ADDRESS          4
#define BGP_NLRI_TLV_LEN_IPV6_INTERFACE_ADDRESS         16
#define BGP_NLRI_TLV_LEN_IPV6_NEIGHBOR_ADDRESS          16
#define BGP_NLRI_TLV_LEN_MULTI_TOPOLOGY_ID              2
#define BGP_NLRI_TLV_LEN_ADMINISTRATIVE_GROUP_COLOR     4
#define BGP_NLRI_TLV_LEN_MAX_LINK_BANDWIDTH             4
#define BGP_NLRI_TLV_LEN_MAX_RESERVABLE_LINK_BANDWIDTH  4
#define BGP_NLRI_TLV_LEN_UNRESERVED_BANDWIDTH           32
#define BGP_NLRI_TLV_LEN_TE_DEFAULT_METRIC_OLD          3
#define BGP_NLRI_TLV_LEN_TE_DEFAULT_METRIC_NEW          4
#define BGP_NLRI_TLV_LEN_LINK_PROTECTION_TYPE           2
#define BGP_NLRI_TLV_LEN_MPLS_PROTOCOL_MASK             1
#define BGP_NLRI_TLV_LEN_MAX_METRIC                     3
#define BGP_NLRI_TLV_LEN_IGP_FLAGS                      1
#define BGP_NLRI_TLV_LEN_PREFIX_METRIC                  4
#define BGP_NLRI_TLV_LEN_NODE_FLAG_BITS                 1

/* draft-gredler-idr-bgp-ls-segment-routing-ext-01 */
#define BGP_LS_SR_TLV_SR_CAPABILITY                 1034
#define BGP_LS_SR_TLV_SR_ALGORITHM                  1035
#define BGP_LS_SR_TLV_SR_LOCAL_BLOCK                1036
#define BGP_LS_SR_TLV_FLEX_ALGO_DEF                 1039
#define BGP_LS_SR_TLV_FLEX_ALGO_EXC_ANY_AFFINITY    1040
#define BGP_LS_SR_TLV_FLEX_ALGO_INC_ANY_AFFINITY    1041
#define BGP_LS_SR_TLV_FLEX_ALGO_INC_ALL_AFFINITY    1042
#define BGP_LS_SR_TLV_ADJ_SID                       1099
#define BGP_LS_SR_TLV_LAN_ADJ_SID                   1100
#define BGP_LS_SR_TLV_PEER_NODE_SID                 1101
#define BGP_LS_SR_TLV_PEER_ADJ_SID                  1102
#define BGP_LS_SR_TLV_PEER_SET_SID                  1103
#define BGP_LS_SR_TLV_PREFIX_SID                    1158
#define BGP_LS_SR_TLV_RANGE                         1159
#define BGP_LS_SR_TLV_BINDING_SID                   1160
#define BGP_LS_SR_SUBTLV_BINDING_SID_LABEL          1161
#define BGP_LS_SR_SUBTLV_BINDING_ERO_METRIC         1162
#define BGP_LS_SR_SUBTLV_BINDING_IPV4_ERO           1163
#define BGP_LS_SR_SUBTLV_BINDING_IPV6_ERO           1164
#define BGP_LS_SR_SUBTLV_BINDING_UNNUM_IFID_ERO     1165
#define BGP_LS_SR_SUBTLV_BINDING_IPV4_BAK_ERO       1166
#define BGP_LS_SR_SUBTLV_BINDING_IPV6_BAK_ERO       1167
#define BGP_LS_SR_SUBTLV_BINDING_UNNUM_IFID_BAK_ERO 1168
#define BGP_LS_SR_TLV_PREFIX_ATTR_FLAGS             1170

/* RFC8571 BGP-LS Advertisement of IGP TE Metric Extensions */
#define BGP_LS_IGP_TE_METRIC_DELAY                  1114
#define BGP_LS_IGP_TE_METRIC_DELAY_MIN_MAX          1115
#define BGP_LS_IGP_TE_METRIC_DELAY_VARIATION        1116
#define BGP_LS_IGP_TE_METRIC_LOSS                   1117
#define BGP_LS_IGP_TE_METRIC_BANDWIDTH_RESIDUAL     1118
#define BGP_LS_IGP_TE_METRIC_BANDWIDTH_AVAILABLE    1119
#define BGP_LS_IGP_TE_METRIC_BANDWIDTH_UTILIZED     1120

#define BGP_LS_IGP_TE_METRIC_FLAG_A                 0x80
#define BGP_LS_IGP_TE_METRIC_FLAG_RESERVED          0x7F

/* draft-ietf-idr-bgp-ls-app-specific-attr-07 */
#define BGP_LS_APP_SPEC_LINK_ATTR                   1122

/* Prefix-SID TLV flags, draft-gredler-idr-bgp-ls-segment-routing-ext-01:

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is IS-IS  |R |N |P |E |V |L |  |  |
                            +--+--+--+--+--+--+--+--+

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is OSPF   |  |NP|M |E |V |L |  |  |
                            +--+--+--+--+--+--+--+--+
*/
#define BGP_LS_SR_PREFIX_SID_FLAG_R  0x80
#define BGP_LS_SR_PREFIX_SID_FLAG_N  0x40
#define BGP_LS_SR_PREFIX_SID_FLAG_NP 0x40
#define BGP_LS_SR_PREFIX_SID_FLAG_P  0x20
#define BGP_LS_SR_PREFIX_SID_FLAG_M  0x20
#define BGP_LS_SR_PREFIX_SID_FLAG_E  0x10
#define BGP_LS_SR_PREFIX_SID_FLAG_V  0x08
#define BGP_LS_SR_PREFIX_SID_FLAG_L  0x04

/* Adjacency-SID TLV flags, draft-gredler-idr-bgp-ls-segment-routing-ext-01:

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is IS-IS  |F |B |V |L |S |  |  |  |
                            +--+--+--+--+--+--+--+--+

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is OSPF   |B |V |L |S |  |  |  |  |
                            +--+--+--+--+--+--+--+--+
*/
#define BGP_LS_SR_ADJACENCY_SID_FLAG_FI 0x80
#define BGP_LS_SR_ADJACENCY_SID_FLAG_BO 0x80
#define BGP_LS_SR_ADJACENCY_SID_FLAG_BI 0x40
#define BGP_LS_SR_ADJACENCY_SID_FLAG_VO 0x40
#define BGP_LS_SR_ADJACENCY_SID_FLAG_VI 0x20
#define BGP_LS_SR_ADJACENCY_SID_FLAG_LO 0x20
#define BGP_LS_SR_ADJACENCY_SID_FLAG_LI 0x10
#define BGP_LS_SR_ADJACENCY_SID_FLAG_SO 0x10
#define BGP_LS_SR_ADJACENCY_SID_FLAG_SI 0x08

/* BGP Peering SIDs TLV flags, rfc9086:

   0  1  2  3  4  5  6  7
   +--+--+--+--+--+--+--+--+
   |V |L |B |P |  |  |  |  | rfc9086
   +--+--+--+--+--+--+--+--+
*/
#define BGP_LS_SR_PEER_SID_FLAG_V   0x80
#define BGP_LS_SR_PEER_SID_FLAG_L   0x40
#define BGP_LS_SR_PEER_SID_FLAG_B   0x20
#define BGP_LS_SR_PEER_SID_FLAG_P   0x10

/* SR-Capabilities TLV flags, draft-gredler-idr-bgp-ls-segment-routing-ext-01:

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is IS-IS  |I |V |H |  |  |  |  |  |
                            +--+--+--+--+--+--+--+--+
*/
#define BGP_LS_SR_CAPABILITY_FLAG_I 0x80
#define BGP_LS_SR_CAPABILITY_FLAG_V 0x40
#define BGP_LS_SR_CAPABILITY_FLAG_H 0x20

/* Prefix Attribute Flags TLV flags, rfc9085:

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is IS-IS  |X |R |N |E |  |  |  |  | rfc7794,rfc9088
                            +--+--+--+--+--+--+--+--+

                             0  1  2  3  4  5  6  7
                            +--+--+--+--+--+--+--+--+
   if Protocol-ID is OSPF   |A |N |E |  |  |  |  |  | rfc7684,rfc9089
                            +--+--+--+--+--+--+--+--+
*/
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_XI 0x80
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_RI 0x40
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_NI 0x20
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_EI 0x10
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_AO 0x80
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_NO 0x40
#define BGP_LS_SR_PREFIX_ATTR_FLAGS_FLAG_EO 0x20

/* Link Attribute Application Identifiers, https://www.iana.org/assignments/igp-parameters/igp-parameters.xhtml:

   0  1  2  3  4  5  6  7
   +--+--+--+--+--+--+--+--+
   |R |S |F |X |  |  |  |  | rfc8919,rfc8920
   +--+--+--+--+--+--+--+--+
*/
#define BGP_LS_APP_SPEC_LINK_ATTRS_SABM_R  0x80000000
#define BGP_LS_APP_SPEC_LINK_ATTRS_SABM_S  0x40000000
#define BGP_LS_APP_SPEC_LINK_ATTRS_SABM_F  0x20000000
#define BGP_LS_APP_SPEC_LINK_ATTRS_SABM_X  0x10000000


/* BGP Prefix-SID TLV type */
#define BGP_PREFIX_SID_TLV_LABEL_INDEX     1 /* Label-Index [RFC8669]                           */
#define BGP_PREFIX_SID_TLV_2               2 /* Deprecated [RFC8669]                            */
#define BGP_PREFIX_SID_TLV_ORIGINATOR_SRGB 3 /* Originator SRGB [RFC8669]                       */
#define BGP_PREFIX_SID_TLV_4               4 /* Deprecated [draft-ietf-bess-srv6-services]      */
#define BGP_PREFIX_SID_TLV_SRV6_L3_SERVICE 5 /* SRv6 L3 Service [draft-ietf-bess-srv6-services] */
#define BGP_PREFIX_SID_TLV_SRV6_L2_SERVICE 6 /* SRv6 L2 Service [draft-ietf-bess-srv6-services] */

/* BGP_PREFIX_SID TLV lengths   */
#define BGP_PREFIX_SID_TLV_LEN_LABEL_INDEX 7

/* BGP SRv6 Service Sub-TLV */
#define SRV6_SERVICE_SRV6_SID_INFORMATION 1

/* BGP SRv6 Service Data Sub-Sub-TLV */
#define SRV6_SERVICE_DATA_SRV6_SID_STRUCTURE 1

/* SRv6 Endpoint behavior */
#define SRV6_ENDPOINT_BEHAVIOR_END                    0x0001 /* End [draft-ietf-spring-srv6-network-programming]                                         */
#define SRV6_ENDPOINT_BEHAVIOR_END_PSP                0x0002 /* End with PSP [draft-ietf-spring-srv6-network-programming]                                */
#define SRV6_ENDPOINT_BEHAVIOR_END_USP                0x0003 /* End with USP [draft-ietf-spring-srv6-network-programming]                                */
#define SRV6_ENDPOINT_BEHAVIOR_END_PSP_USP            0x0004 /* End with PSP & USP [draft-ietf-spring-srv6-network-programming]                          */
#define SRV6_ENDPOINT_BEHAVIOR_END_X                  0x0005 /* End.X [draft-ietf-spring-srv6-network-programming]                                       */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_PSP              0x0006 /* End.X with PSP [draft-ietf-spring-srv6-network-programming]                              */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_USP              0x0007 /* End.X with UPS [draft-ietf-spring-srv6-network-programming]                              */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_PSP_USP          0x0008 /* End.X with PSP & USP [draft-ietf-spring-srv6-network-programming]                        */
#define SRV6_ENDPOINT_BEHAVIOR_END_T                  0x0009 /* End.T [draft-ietf-spring-srv6-network-programming]                                       */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_PSP              0x000A /* End.T with PSP [draft-ietf-spring-srv6-network-programming]                              */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_USP              0x000B /* End.T with USP [draft-ietf-spring-srv6-network-programming]                              */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_PSP_USP          0x000C /* End.T with PSP & USP [draft-ietf-spring-srv6-network-programming]                        */
#define SRV6_ENDPOINT_BEHAVIOR_END_B6_ENCAPS          0x000E /* End.B6.Encaps [draft-ietf-spring-srv6-network-programming]                               */
#define SRV6_ENDPOINT_BEHAVIOR_END_BM                 0x000F /* End.BM [draft-ietf-spring-srv6-network-programming]                                      */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX6                0x0010 /* End.DX6 [draft-ietf-spring-srv6-network-programming]                                     */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX4                0x0011 /* End.DX4 [draft-ietf-spring-srv6-network-programming]                                     */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT6                0x0012 /* End.DT6 [draft-ietf-spring-srv6-network-programming]                                     */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT4                0x0013 /* End.DT4 [draft-ietf-spring-srv6-network-programming]                                     */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT46               0x0014 /* End.DT46 [draft-ietf-spring-srv6-network-programming]                                    */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX2                0x0015 /* End.DX2 [draft-ietf-spring-srv6-network-programming]                                     */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX2V               0x0016 /* End.DX2V [draft-ietf-spring-srv6-network-programming]                                    */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT2U               0x0017 /* End.DX2U [draft-ietf-spring-srv6-network-programming]                                    */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT2M               0x0018 /* End.DT2M [draft-ietf-spring-srv6-network-programming]                                    */
#define SRV6_ENDPOINT_BEHAVIOR_END_B6_ENCAPS_RED      0x001B /* End.B6.Encaps.Red [draft-ietf-spring-srv6-network-programming]                           */
#define SRV6_ENDPOINT_BEHAVIOR_END_USD                0x001C /* End with USD [draft-ietf-spring-srv6-network-programming]                                */
#define SRV6_ENDPOINT_BEHAVIOR_END_PSP_USD            0x001D /* End with PSP & USD [draft-ietf-spring-srv6-network-programming]                          */
#define SRV6_ENDPOINT_BEHAVIOR_END_USP_USD            0x001E /* End with USP & USD [draft-ietf-spring-srv6-network-programming]                          */
#define SRV6_ENDPOINT_BEHAVIOR_END_PSP_USP_USD        0x001F /* End with PSP, USP & USD [draft-ietf-spring-srv6-network-programming]                     */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_USD              0x0020 /* End.X with USD [draft-ietf-spring-srv6-network-programming]                              */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_PSP_USD          0x0021 /* End.X with PSP & USD [draft-ietf-spring-srv6-network-programming]                        */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_USP_USD          0x0022 /* End.X with USP & USD [draft-ietf-spring-srv6-network-programming]                        */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_PSP_USP_USD      0x0023 /* End.X with PSP, USP & USD [draft-ietf-spring-srv6-network-programming]                   */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_USD              0x0024 /* End.T with USD [draft-ietf-spring-srv6-network-programming]                              */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_PSP_USD          0x0025 /* End.T with PSP & USD [draft-ietf-spring-srv6-network-programming]                        */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_USP_USD          0x0026 /* End.T with USP & USD [draft-ietf-spring-srv6-network-programming]                        */
#define SRV6_ENDPOINT_BEHAVIOR_END_T_PSP_USP_USD      0x0027 /* End.T with PSP, USP & USD [draft-ietf-spring-srv6-network-programming]                   */
#define SRV6_ENDPOINT_BEHAVIOR_END_ONLY_CSID          0x002A /* End with NEXT-ONLY-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]              */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID               0x002B /* End with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]                   */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP           0x002C /* End with NEXT-CSID & PSP [draft-filsfils-spring-net-pgm-extension-srv6-usid]             */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_USP           0x002D /* End with NEXT-CSID & USP [draft-filsfils-spring-net-pgm-extension-srv6-usid]             */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP_USP       0x002E /* End with NEXT-CSID, PSP & USP [draft-filsfils-spring-net-pgm-extension-srv6-usid]        */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_USD           0x002F /* End with NEXT-CSID & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]             */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP_USD       0x0030 /* End with NEXT-CSID, PSP & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]        */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_USP_USD       0x0031 /* End with NEXT-CSID, USP & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]        */
#define SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP_USP_USD   0x0032 /* End with NEXT-CSID, PSP, USP & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]   */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_ONLY_CSID        0x0033 /* End.X with NEXT-ONLY-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]            */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID             0x0034 /* End.X with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]                 */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP         0x0035 /* End.X with NEXT-CSID & PSP [draft-filsfils-spring-net-pgm-extension-srv6-usid]           */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_USP         0x0036 /* End.X with NEXT-CSID & USP [draft-filsfils-spring-net-pgm-extension-srv6-usid]           */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP_USP     0x0037 /* End.X with NEXT-CSID, PSP & USP [draft-filsfils-spring-net-pgm-extension-srv6-usid]      */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_USD         0x0038 /* End.X with NEXT-CSID & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]           */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP_USD     0x0039 /* End.X with NEXT-CSID, PSP & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]      */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_USP_USD     0x003A /* End.X with NEXT-CSID, USP & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid]      */
#define SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP_USP_USD 0x003B /* End.X with NEXT-CSID, PSP, USP & USD [draft-filsfils-spring-net-pgm-extension-srv6-usid] */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX6_CSID           0x003C /* End.DX6 with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]               */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX4_CSID           0x003D /* End.DX4 with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]               */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT6_CSID           0x003E /* End.DT6 with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]               */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT4_CSID           0x003F /* End.DT4 with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]               */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT46_CSID          0x0040 /* End.DT46 with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]              */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX2_CSID           0x0041 /* End.DX2 with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]               */
#define SRV6_ENDPOINT_BEHAVIOR_END_DX2V_CSID          0x0042 /* End.DX2V with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]              */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT2U_CSID          0x0043 /* End.DT2U with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]              */
#define SRV6_ENDPOINT_BEHAVIOR_END_DT2M_CSID          0x0044 /* End.DT2M with NEXT-CSID [draft-filsfils-spring-net-pgm-extension-srv6-usid]              */
#define SRV6_ENDPOINT_BEHAVIOR_OPAQUE                 0xFFFF /* Opaque [draft-ietf-spring-srv6-network-programming]                                      */


/* well-known communities, as defined by IANA  */
/* https://www.iana.org/assignments/bgp-well-known-communities/bgp-well-known-communities.xhtml */
#define BGP_COMM_GRACEFUL_SHUTDOWN   0xFFFF0000
#define BGP_COMM_ACCEPT_OWN          0xFFFF0001
#define BGP_COMM_BLACKHOLE           0xFFFF029A
#define BGP_COMM_NO_EXPORT           0xFFFFFF01
#define BGP_COMM_NO_ADVERTISE        0xFFFFFF02
#define BGP_COMM_NO_EXPORT_SUBCONFED 0xFFFFFF03
#define BGP_COMM_NOPEER              0xFFFFFF04
#define FOURHEX0                     0x00000000
#define FOURHEXF                     0xFFFF0000

/* mcast-vpn route types draft-ietf-l3vpn-2547bis-mcast-bgp-08.txt */
#define MCAST_VPN_RTYPE_INTRA_AS_IPMSI_AD 1
#define MCAST_VPN_RTYPE_INTER_AS_IPMSI_AD 2
#define MCAST_VPN_RTYPE_SPMSI_AD          3
#define MCAST_VPN_RTYPE_LEAF_AD           4
#define MCAST_VPN_RTYPE_SOURCE_ACTIVE_AD  5
#define MCAST_VPN_RTYPE_SHARED_TREE_JOIN  6
#define MCAST_VPN_RTYPE_SOURCE_TREE_JOIN  7


/* https://www.iana.org/assignments/capability-codes/capability-codes.xhtml (last updated 2018-08-21) */
/* BGP capability code */
#define BGP_CAPABILITY_RESERVED                      0  /* RFC5492 */
#define BGP_CAPABILITY_MULTIPROTOCOL                 1  /* RFC2858 */
#define BGP_CAPABILITY_ROUTE_REFRESH                 2  /* RFC2918 */
#define BGP_CAPABILITY_COOPERATIVE_ROUTE_FILTERING   3  /* RFC5291 */
#define BGP_CAPABILITY_MULTIPLE_ROUTE_DEST           4  /* RFC8277 Deprecated */
#define BGP_CAPABILITY_EXTENDED_NEXT_HOP             5  /* RFC5549 */
#define BGP_CAPABILITY_EXTENDED_MESSAGE              6  /* draft-ietf-idr-bgp-extended-messages */
#define BGP_CAPABILITY_BGPSEC                        7  /* RFC8205 */
#define BGP_CAPABILITY_MULTIPLE_LABELS               8  /* RFC8277 */
#define BGP_CAPABILITY_BGP_ROLE                      9  /* draft-ietf-idr-bgp-open-policy */
#define BGP_CAPABILITY_GRACEFUL_RESTART             64  /* RFC4724 */
#define BGP_CAPABILITY_4_OCTET_AS_NUMBER            65  /* RFC6793 */
#define BGP_CAPABILITY_DYNAMIC_CAPABILITY_CISCO     66  /* Cisco Dynamic capabaility*/
#define BGP_CAPABILITY_DYNAMIC_CAPABILITY           67  /* draft-ietf-idr-dynamic-cap */
#define BGP_CAPABILITY_MULTISESSION                 68  /* draft-ietf-idr-bgp-multisession */
#define BGP_CAPABILITY_ADDITIONAL_PATHS             69  /* [RFC7911] */
#define BGP_CAPABILITY_ENHANCED_ROUTE_REFRESH       70  /* [RFC7313] */
#define BGP_CAPABILITY_LONG_LIVED_GRACEFUL_RESTART  71  /* draft-uttaro-idr-bgp-persistence */
#define BGP_CAPABILITY_CP_ORF                       72  /* [RFC7543] */
#define BGP_CAPABILITY_FQDN                         73  /* draft-walton-bgp-hostname-capability */
#define BGP_CAPABILITY_ROUTE_REFRESH_CISCO         128  /* Cisco */
#define BGP_CAPABILITY_ORF_CISCO                   130  /* Cisco */
#define BGP_CAPABILITY_MULTISESSION_CISCO          131  /* Cisco */

static const struct int_to_string capability_vals[] = {
    { BGP_CAPABILITY_RESERVED,                      "Reserved capability" },
    { BGP_CAPABILITY_MULTIPROTOCOL,                 "Multiprotocol extensions capability" },
    { BGP_CAPABILITY_ROUTE_REFRESH,                 "Route refresh capability" },
    { BGP_CAPABILITY_COOPERATIVE_ROUTE_FILTERING,   "Cooperative route filtering capability" },
    { BGP_CAPABILITY_MULTIPLE_ROUTE_DEST,           "Multiple routes to a destination capability" },
    { BGP_CAPABILITY_EXTENDED_NEXT_HOP,             "Extended Next Hop Encoding" },
    { BGP_CAPABILITY_EXTENDED_MESSAGE,              "BGP-Extended Message" },
    { BGP_CAPABILITY_BGPSEC,                        "BGPsec capability" },
    { BGP_CAPABILITY_MULTIPLE_LABELS,               "Multiple Labels capability" },
    { BGP_CAPABILITY_BGP_ROLE,                      "BGP Role" },
    { BGP_CAPABILITY_GRACEFUL_RESTART,              "Graceful Restart capability" },
    { BGP_CAPABILITY_4_OCTET_AS_NUMBER,             "Support for 4-octet AS number capability" },
    { BGP_CAPABILITY_DYNAMIC_CAPABILITY_CISCO,      "Deprecated Dynamic Capability (Cisco)" },
    { BGP_CAPABILITY_DYNAMIC_CAPABILITY,            "Support for Dynamic capability" },
    { BGP_CAPABILITY_MULTISESSION,                  "Multisession BGP Capability" },
    { BGP_CAPABILITY_ADDITIONAL_PATHS,              "Support for Additional Paths" },
    { BGP_CAPABILITY_ENHANCED_ROUTE_REFRESH,        "Enhanced route refresh capability" },
    { BGP_CAPABILITY_LONG_LIVED_GRACEFUL_RESTART,   "Long-Lived Graceful Restart (LLGR) Capability" },
    { BGP_CAPABILITY_CP_ORF,                        "CP-ORF Capability" },
    { BGP_CAPABILITY_FQDN,                          "FQDN Capability" },
    { BGP_CAPABILITY_ROUTE_REFRESH_CISCO,           "Route refresh capability (Cisco)" },
    { BGP_CAPABILITY_ORF_CISCO,                     "Cooperative route filtering capability (Cisco)" },
    { BGP_CAPABILITY_MULTISESSION_CISCO,            "Multisession BGP Capability (Cisco)" },
    { 0, NULL }
};

static const struct int_to_string community_vals[] = {
    { BGP_COMM_GRACEFUL_SHUTDOWN,   "GRACEFUL_SHUTDOWN" },
    { BGP_COMM_ACCEPT_OWN,          "ACCEPT_OWN" },
    { BGP_COMM_BLACKHOLE,           "BLACKHOLE" },
    { BGP_COMM_NO_EXPORT,           "NO_EXPORT" },
    { BGP_COMM_NO_ADVERTISE,        "NO_ADVERTISE" },
    { BGP_COMM_NO_EXPORT_SUBCONFED, "NO_EXPORT_SUBCONFED" },
    { BGP_COMM_NOPEER,              "NOPEER" },
    { 0,                            NULL }
};

/* Capability Message action code */
static const struct int_to_string bgpcap_action[] = {
    { 0, "advertising a capability" },
    { 1, "removing a capability" },
    { 0, NULL }
};

static const struct int_to_string mcast_vpn_route_type[] = {
    { MCAST_VPN_RTYPE_INTRA_AS_IPMSI_AD, "Intra-AS I-PMSI A-D route" },
    { MCAST_VPN_RTYPE_INTER_AS_IPMSI_AD, "Inter-AS I-PMSI A-D route" },
    { MCAST_VPN_RTYPE_SPMSI_AD         , "S-PMSI A-D route" },
    { MCAST_VPN_RTYPE_LEAF_AD          , "Leaf A-D route" },
    { MCAST_VPN_RTYPE_SOURCE_ACTIVE_AD , "Source Active A-D route" },
    { MCAST_VPN_RTYPE_SHARED_TREE_JOIN , "Shared Tree Join route" },
    { MCAST_VPN_RTYPE_SOURCE_TREE_JOIN , "Source Tree Join route" },
    { 0, NULL }
};

/* NLRI type value_string as defined in idr-ls */
static const struct int_to_string bgp_ls_nlri_type_vals[] = {
        { LINK_STATE_LINK_NLRI,                 "Link NLRI" },
        { LINK_STATE_NODE_NLRI,                 "Node NLRI" },
        { LINK_STATE_IPV4_TOPOLOGY_PREFIX_NLRI, "IPv4 Topology Prefix NLRI" },
        { LINK_STATE_IPV6_TOPOLOGY_PREFIX_NLRI, "IPv6 Topology Prefix NLRI" },
        {0, NULL },
};

/* Link-State NLRI Protocol-ID value strings */
static const struct int_to_string link_state_nlri_protocol_id_values[] = {
        {BGP_LS_NLRI_PROTO_ID_UNKNOWN, "Unknown" },
        {BGP_LS_NLRI_PROTO_ID_IS_IS_LEVEL_1, "IS-IS Level 1"},
        {BGP_LS_NLRI_PROTO_ID_IS_IS_LEVEL_2, "IS-IS Level 2"},
        {BGP_LS_NLRI_PROTO_ID_OSPF, "OSPF"},
        {BGP_LS_NLRI_PROTO_ID_DIRECT, "Direct"},
        {BGP_LS_NLRI_PROTO_ID_STATIC, "Static"},
        {BGP_LS_NLRI_PROTO_ID_BGP, "BGP"},
        {0, NULL},
};

/* Link-State routing universes */
static const struct int_to_string link_state_nlri_routing_universe_values[] = {
        {BGP_LS_NLRI_ROUTING_UNIVERSE_LEVEL_3, "L3 packet topology" },
        {BGP_LS_NLRI_ROUTING_UNIVERSE_LEVEL_1, "L1 optical topology"},
        {0, NULL}
};

/* Link state prefix NLRI OSPF Route Type */
static const struct int_to_string link_state_prefix_descriptors_ospf_route_type[] = {
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_UNKNOWN,     "Unknown" },
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_INTRA_AREA,  "Intra-Area"},
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_INTER_AREA,  "Inter Area"},
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_EXTERNAL_1,  "External 1"},
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_EXTERNAL_2,  "External 2"},
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_NSSA_1,      "NSSA 1"},
        {BGP_LS_PREFIX_OSPF_ROUTE_TYPE_NSSA_2,      "NSSA 2"},
        {0, NULL}
};

/* Link state Flex Algo Metric Type: draft-ietf-lsr-flex-algo-17 */
static const struct int_to_string flex_algo_metric_types[] = {
    { 0, "IGP Metric"},
    { 1, "Min Unidirectional Link Delay"},
    { 2, "TE Metric"},
    { 0, NULL }
};

/* Link state IGP Algorithm Type: https://www.iana.org/assignments/igp-parameters/igp-parameters.xhtml */
static const struct int_to_string igp_algo_types[] = {
    { 0,   "Shortest Path First (SPF)" },
    { 1,   "Strict Shortest Path First (Strict SPF)" },
    { 0,   NULL }
};

/* Link state IGP MSD Type: https://www.iana.org/assignments/igp-parameters/igp-parameters.xhtml */
static const struct int_to_string igp_msd_types[] = {
    { 0,   "Reserved" },
    { 1,   "Base MPLS Imposition MSD" },
    { 2,   "ERLD-MSD" },
    { 41,  "SRH Max SL" },
    { 42,  "SRH Max End Pop" },
    { 44,  "SRH Max H.Encaps" },
    { 45,  "SRH Max End D" },
    { 0,   NULL }
};


/* NLRI type as define in BGP flow spec RFC */
#define BGPNLRI_FSPEC_DST_PFIX      1 /* RFC 5575         */
#define BGPNLRI_FSPEC_SRC_PFIX      2 /* RFC 5575         */
#define BGPNLRI_FSPEC_IP_PROTO      3 /* RFC 5575         */
#define BGPNLRI_FSPEC_PORT          4 /* RFC 5575         */
#define BGPNLRI_FSPEC_DST_PORT      5 /* RFC 5575         */
#define BGPNLRI_FSPEC_SRC_PORT      6 /* RFC 5575         */
#define BGPNLRI_FSPEC_ICMP_TP       7 /* RFC 5575         */
#define BGPNLRI_FSPEC_ICMP_CD       8 /* RFC 5575         */
#define BGPNLRI_FSPEC_TCP_FLAGS     9 /* RFC 5575         */
#define BGPNLRI_FSPEC_PCK_LEN      10 /* RFC 5575         */
#define BGPNLRI_FSPEC_DSCP         11 /* RFC 5575         */
#define BGPNLRI_FSPEC_FRAGMENT     12 /* RFC 5575         */

/* BGP flow spec NLRI operator bitmask */
#define BGPNLRI_FSPEC_END_OF_LST         0x80
#define BGPNLRI_FSPEC_AND_BIT            0x40
#define BGPNLRI_FSPEC_VAL_LEN            0x30
#define BGPNLRI_FSPEC_UNUSED_BIT4        0x08
#define BGPNLRI_FSPEC_UNUSED_BIT5        0x04
#define BGPNLRI_FSPEC_LESS_THAN          0x04
#define BGPNLRI_FSPEC_GREATER_THAN       0x02
#define BGPNLRI_FSPEC_EQUAL              0x01
#define BGPNLRI_FSPEC_TCPF_NOTBIT        0x02
#define BGPNLRI_FSPEC_TCPF_MATCHBIT      0x01
#define BGPNLRI_FSPEC_DSCP_BITMASK       0x3F

/* BGP flow spec specific filter value: TCP flags, Packet fragment ... */
#define BGPNLRI_FSPEC_TH_FIN  0x01
#define BGPNLRI_FSPEC_TH_SYN  0x02
#define BGPNLRI_FSPEC_TH_RST  0x04
#define BGPNLRI_FSPEC_TH_PUSH 0x08
#define BGPNLRI_FSPEC_TH_ACK  0x10
#define BGPNLRI_FSPEC_TH_URG  0x20
#define BGPNLRI_FSPEC_TH_ECN  0x40
#define BGPNLRI_FSPEC_TH_CWR  0x80

#define BGPNLRI_FSPEC_FG_DF   0x01
#define BGPNLRI_FSPEC_FG_ISF  0x02
#define BGPNLRI_FSPEC_FG_FF   0x04
#define BGPNLRI_FSPEC_FG_LF   0x08

/* NLRI type value_string as define in BGP flow spec RFC */

static const struct int_to_string flowspec_nlri_opvaluepair_type[] = {
    { BGPNLRI_FSPEC_DST_PFIX, "Destination prefix filter" },
    { BGPNLRI_FSPEC_SRC_PFIX, "Source prefix filter" },
    { BGPNLRI_FSPEC_IP_PROTO, "Protocol / Next Header filter" },
    { BGPNLRI_FSPEC_PORT,     "Port filter" },
    { BGPNLRI_FSPEC_DST_PORT, "Destination port filter" },
    { BGPNLRI_FSPEC_SRC_PORT, "Source port filter" },
    { BGPNLRI_FSPEC_ICMP_TP,  "ICMP type filter" },
    { BGPNLRI_FSPEC_ICMP_CD,  "ICMP code filter" },
    { BGPNLRI_FSPEC_TCP_FLAGS,"TCP flags filter" },
    { BGPNLRI_FSPEC_PCK_LEN,  "Packet Length filter" },
    { BGPNLRI_FSPEC_DSCP,     "DSCP marking filter" },
    { BGPNLRI_FSPEC_FRAGMENT, "IP fragment filter" },
    {0, NULL },
};

/* Subtype Route Refresh, draft-ietf-idr-bgp-enhanced-route-refresh-02 */
static const struct int_to_string route_refresh_subtype_vals[] = {
    { 0, "Normal route refresh request [RFC2918] with/without ORF [RFC5291]" },
    { 1, "Demarcation of the beginning of a route refresh" },
    { 2, "Demarcation of the ending of a route refresh" },
    { 0,  NULL }
};

static const struct int_to_string bgp_prefix_sid_type[] = {
    { BGP_PREFIX_SID_TLV_LABEL_INDEX,     "Label-Index" },
    { BGP_PREFIX_SID_TLV_2,               "Deprecated" },
    { BGP_PREFIX_SID_TLV_ORIGINATOR_SRGB, "Originator SRGB" },
    { BGP_PREFIX_SID_TLV_4,               "Deprecated" },
    { BGP_PREFIX_SID_TLV_SRV6_L3_SERVICE, "SRv6 L3 Service" },
    { BGP_PREFIX_SID_TLV_SRV6_L2_SERVICE, "SRv6 L2 Service" },
    { 0, NULL }
};

static const struct int_to_string srv6_service_sub_tlv_type[] = {
    { SRV6_SERVICE_SRV6_SID_INFORMATION,   "SRv6 SID Information" },
    { 0,  NULL }
};

static const struct int_to_string srv6_service_data_sub_sub_tlv_type[] = {
    { SRV6_SERVICE_DATA_SRV6_SID_STRUCTURE,   "SRv6 SID Structure" },
    { 0,  NULL }
};

/* SRv6 Endpoint behavior value_string [draft-ietf-spring-srv6-network-programming-24]. */
static const struct int_to_string srv6_endpoint_behavior[] = {
    { SRV6_ENDPOINT_BEHAVIOR_END,                    "End" },
    { SRV6_ENDPOINT_BEHAVIOR_END_PSP,                "End with PSP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_USP,                "End with USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_PSP_USP,            "End with PSP & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X,                  "End.X" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_PSP,              "End.X with PSP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_USP,              "End.X with USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_PSP_USP,          "End.X with PSP & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T,                  "End.T" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_PSP,              "End.T with PSP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_USP,              "End.T with USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_PSP_USP,          "End.T with PSP & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_B6_ENCAPS,          "End.B6.Encaps" },
    { SRV6_ENDPOINT_BEHAVIOR_END_BM,                 "End.BM" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX6,                "End.DX6" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX4,                "End.DX4" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT6,                "End.DT6" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT4,                "End.DT4" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT46,               "End.DT46" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX2,                "End.DX2" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX2V,               "End.DX2V" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT2U,               "End.DT2U" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT2M,               "End.DT2M" },
    { SRV6_ENDPOINT_BEHAVIOR_END_B6_ENCAPS_RED,      "End.B6.Encaps.Red" },
    { SRV6_ENDPOINT_BEHAVIOR_END_USD,                "End with USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_PSP_USD,            "End with PSP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_USP_USD,            "End with USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_PSP_USP_USD,        "End with PSP, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_USD,              "End.X with USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_PSP_USD,          "End.X with PSP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_USP_USD,          "End.X with USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_PSP_USP_USD,      "End.X with PSP, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_USD,              "End.T with USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_PSP_USD,          "End.T with PSP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_USP_USD,          "End.T with USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_T_PSP_USP_USD,      "End.T with PSP, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_ONLY_CSID,          "End with NEXT-ONLY-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID,               "End with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP,           "End with NEXT-CSID & PSP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_USP,           "End with NEXT-CSID & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP_USP,       "End with NEXT-CSID, PSP & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_USD,           "End with NEXT-CSID & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP_USD,       "End with NEXT-CSID, PSP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_USP_USD,       "End with NEXT-CSID, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_CSID_PSP_USP_USD,   "End with NEXT-CSID, PSP, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_ONLY_CSID,        "End.X with NEXT-ONLY-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID,             "End.X with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP,         "End.X with NEXT-CSID & PSP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_USP,         "End.X with NEXT-CSID & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP_USP,     "End.X with NEXT-CSID, PSP & USP" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_USD,         "End.X with NEXT-CSID & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP_USD,     "End.X with NEXT-CSID, PSP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_USP_USD,     "End.X with NEXT-CSID, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_X_CSID_PSP_USP_USD, "End.X with NEXT-CSID, PSP, USP & USD" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX6_CSID,           "End.DX6 with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX4_CSID,           "End.DX4 with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT6_CSID,           "End.DT6 with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT4_CSID,           "End.DT4 with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT46_CSID,          "End.DT46 with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX2_CSID,           "End.DX2 with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DX2V_CSID,          "End.DX2V with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT2U_CSID,          "End.DT2U with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_END_DT2M_CSID,          "End.DT2M with NEXT-CSID" },
    { SRV6_ENDPOINT_BEHAVIOR_OPAQUE,                 "Opaque" },
    { 0,  NULL }
};


/* Extended community type */
/* according to IANA's number assignment at: http://www.iana.org/assignments/bgp-extended-communities */
/* BGP transitive extended community type high octet */
/* Range 0x00-0x3f First Come First Served */
/* Range 0x80-0x8f Reserved for Experimental */
/* Range 0x90-0xbf Standards Action */

#define BGP_EXT_COM_TYPE_AUTH               0x80    /* FCFS or Standard/Early/Experimental allocated */
#define BGP_EXT_COM_TYPE_TRAN               0x40    /* Non-transitive or Transitive */

#define BGP_EXT_COM_TYPE_HIGH_TR_AS2        0x00    /* Transitive Two-Octet AS-Specific Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_TR_IP4        0x01    /* Transitive IPv4-Address-specific Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_TR_AS4        0x02    /* Transitive Four-Octet AS-Specific Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_TR_OPAQUE     0x03    /* Transitive Opaque Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_TR_QOS        0x04    /* QoS Marking [Thomas_Martin_Knoll] */
#define BGP_EXT_COM_TYPE_HIGH_TR_COS        0x05    /* CoS Capability [Thomas_Martin_Knoll] */
#define BGP_EXT_COM_TYPE_HIGH_TR_EVPN       0x06    /* EVPN (Sub-Types are defined in the "EVPN Extended Community Sub-Types" registry) */
#define BGP_EXT_COM_TYPE_HIGH_TR_FLOW_I     0x07    /* FlowSpec Transitive Extended Communities [draft-ietf-idr-flowspec-interfaceset] */
#define BGP_EXT_COM_TYPE_HIGH_TR_FLOW       0x08    /* Flow spec redirect/mirror to IP next-hop [draft-simpson-idr-flowspec-redirect] */
#define BGP_EXT_COM_TYPE_HIGH_TR_FLOW_R     0x09    /* FlowSpec Redirect to indirection-id Extended Community [draft-ietf-idr-flowspec-path-redirect] */
#define BGP_EXT_COM_TYPE_HIGH_TR_EXP        0x80    /* Generic Transitive Experimental Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_TR_EXP_2      0x81    /* Generic Transitive Experimental Use Extended Community Part 2 [RFC7674] */
#define BGP_EXT_COM_TYPE_HIGH_TR_EXP_3      0x82    /* Generic Transitive Experimental Use Extended Community Part 3 [RFC7674] */
#define BGP_EXT_COM_TYPE_HIGH_TR_EXP_EIGRP  0x88    /* EIGRP attributes - http://www.cisco.com/c/en/us/td/docs/ios/12_0s/feature/guide/seipecec.html */

/* BGP non transitive extended community type high octet */
/* 0x40-0x7f First Come First Served */
/* 0xc0-0xcf Reserved for Experimental Use (see [RFC4360]) */
/* 0xd0-0xff Standards Action */
/* 0x45-0x7f Unassigned */
#define BGP_EXT_COM_TYPE_HIGH_NTR_AS2       0x40    /* Non-Transitive Two-Octet AS-Specific Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_NTR_IP4       0x41    /* Non-Transitive IPv4-Address-specific Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_NTR_AS4       0x42    /* Non-Transitive Four-Octet AS-Specific Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_NTR_OPAQUE    0x43    /* Non-Transitive Opaque Extended Community */
#define BGP_EXT_COM_TYPE_HIGH_NTR_QOS       0x44    /* QoS Marking [Thomas_Martin_Knoll] */


/* EVPN Extended Community Sub-Types */
#define BGP_EXT_COM_STYPE_EVPN_MMAC         0x00    /* MAC Mobility [draft-ietf-l2vpn-pbb-evpn] */
#define BGP_EXT_COM_STYPE_EVPN_LABEL        0x01    /* ESI MPLS Label [draft-ietf-l2vpn-evpn] */
#define BGP_EXT_COM_STYPE_EVPN_IMP          0x02    /* ES Import [draft-sajassi-l2vpn-evpn-segment-route] */
#define BGP_EXT_COM_STYPE_EVPN_ROUTERMAC    0x03    /* draft-sajassi-l2vpn-evpn-inter-subnet-forwarding */
#define BGP_EXT_COM_STYPE_EVPN_L2ATTR       0x04    /* RFC 8214 */
#define BGP_EXT_COM_STYPE_EVPN_ETREE        0x05    /* RFC 8317 */
#define BGP_EXT_COM_STYPE_EVPN_DF           0x06    /* RFC 8584 */
#define BGP_EXT_COM_STYPE_EVPN_ISID         0x07    /* draft-sajassi-bess-evpn-virtual-eth-segment */
#define BGP_EXT_COM_STYPE_EVPN_ND           0x08    /* draft-snr-bess-evpn-na-flags */
#define BGP_EXT_COM_STYPE_EVPN_MCFLAGS      0x09    /* draft-ietf-bess-evpn-igmp-mld-proxy */
#define BGP_EXT_COM_STYPE_EVPN_EVIRT0       0x0a    /* draft-ietf-bess-evpn-igmp-mld-proxy */
#define BGP_EXT_COM_STYPE_EVPN_EVIRT1       0x0b    /* draft-ietf-bess-evpn-igmp-mld-proxy */
#define BGP_EXT_COM_STYPE_EVPN_EVIRT2       0x0c    /* draft-ietf-bess-evpn-igmp-mld-proxy */
#define BGP_EXT_COM_STYPE_EVPN_EVIRT3       0x0d    /* draft-ietf-bess-evpn-igmp-mld-proxy */
#define BGP_EXT_COM_STYPE_EVPN_ATTACHCIRT   0x0e    /* draft-sajassi-bess-evpn-ac-aware-bundling */

/* RFC 7432 Flag single active mode */
#define BGP_EXT_COM_ESI_LABEL_FLAGS         0x01    /* bitmask: set for single active multi-homing site */

/* RFC 7432 Flag Sticky/Static MAC */
#define BGP_EXT_COM_EVPN_MMAC_STICKY        0x01    /* Bitmask: Set for sticky/static MAC address */

/* RFC 8214 Flags EVPN L2 Attributes */
#define BGP_EXT_COM_EVPN_L2ATTR_FLAG_B         0x01    /* Backup PE */
#define BGP_EXT_COM_EVPN_L2ATTR_FLAG_P         0x02    /* Primary PE */
#define BGP_EXT_COM_EVPN_L2ATTR_FLAG_C         0x04    /* Control word required */
/* draft-yu-bess-evpn-l2-attributes-04 */
#define BGP_EXT_COM_EVPN_L2ATTR_FLAG_F         0x08    /* Send and receive flow label */
#define BGP_EXT_COM_EVPN_L2ATTR_FLAG_CI        0x10    /* CWI extended community can be included */
#define BGP_EXT_COM_EVPN_L2ATTR_FLAG_RESERVED  0xFFE0  /* Reserved */

/* RFC 8317 Flags EVPN E-Tree Attributes */
#define BGP_EXT_COM_EVPN_ETREE_FLAG_L         0x01  /* Leaf-Indication */
#define BGP_EXT_COM_EVPN_ETREE_FLAG_RESERVED  0xFE  /* Reserved */

/* EPVN route AD NLRI ESI type */
#define BGP_NLRI_EVPN_ESI_VALUE             0x00    /* ESI type 0, 9 bytes interger */
#define BGP_NLRI_EVPN_ESI_LACP              0x01    /* ESI type 1, LACP 802.1AX */
#define BGP_NLRI_EVPN_ESI_MSTP              0x02    /* ESI type 2, MSTP defined ESI */
#define BGP_NLRI_EVPN_ESI_MAC               0x03    /* ESI type 3, MAC allocated value */
#define BGP_NLRI_EVPN_ESI_RID               0x04    /* ESI type 4, Router ID as ESI */
#define BGP_NLRI_EVPN_ESI_ASN               0x05    /* ESI type 5, ASN as ESI */
#define BGP_NLRI_EVPN_ESI_RES               0xFF    /* ESI 0xFF reserved */


/* Transitive Two-Octet AS-Specific Extended Community Sub-Types */
/* 0x04 Unassigned */
/* 0x06-0x07 Unassigned */
/* 0x0b-0x0f Unassigned */
/* 0x11-0xff Unassigned */
#define BGP_EXT_COM_STYPE_AS2_RT        0x02    /* Route Target [RFC4360] */
#define BGP_EXT_COM_STYPE_AS2_RO        0x03    /* Route Origin [RFC4360] */
#define BGP_EXT_COM_STYPE_AS2_OSPF_DID  0x05    /* OSPF Domain Identifier [RFC4577] */
#define BGP_EXT_COM_STYPE_AS2_DCOLL     0x08    /* BGP Data Collection [RFC4384] */
#define BGP_EXT_COM_STYPE_AS2_SRC_AS    0x09    /* Source AS [RFC6514] */
#define BGP_EXT_COM_STYPE_AS2_L2VPN     0x0a    /* L2VPN Identifier [RFC6074] */
#define BGP_EXT_COM_STYPE_AS2_CVPND     0x0010  /* Cisco VPN-Distinguisher [Eric_Rosen] */

/* Non-Transitive Two-Octet AS-Specific Extended Community Sub-Types */
/* 0x00-0xbf First Come First Served */
/* 0xc0-0xff IETF Review*/

#define BGP_EXT_COM_STYPE_AS2_LBW       0x04    /* Link Bandwidth Extended Community [draft-ietf-idr-link-bandwidth-00] */
#define BGP_EXT_COM_STYPE_AS2_VNI       0x80    /* Virtual-Network Identifier Extended Community [draft-drao-bgp-l3vpn-virtual-network-overlays] */

/* Transitive Four-Octet AS-Specific Extended Community Sub-Types */
/* 0x00-0xbf First Come First Served */
/* 0xc0-0xff IETF Review */

#define BGP_EXT_COM_STYPE_AS4_RT        0x02    /* Route Target [RFC5668] */
#define BGP_EXT_COM_STYPE_AS4_RO        0x03    /* Route Origin [RFC5668] */
#define BGP_EXT_COM_STYPE_AS4_GEN       0x04    /* Generic (deprecated) [draft-ietf-idr-as4octet-extcomm-generic-subtype] */
#define BGP_EXT_COM_STYPE_AS4_OSPF_DID  0x05    /* OSPF Domain Identifier [RFC4577] */
#define BGP_EXT_COM_STYPE_AS4_BGP_DC    0x08    /* BGP Data Collection [RFC4384] */
#define BGP_EXT_COM_STYPE_AS4_S_AS      0x09    /* Source AS [RFC6514] */
#define BGP_EXT_COM_STYPE_AS4_CIS_V     0x10    /* Cisco VPN Identifier [Eric_Rosen] */
#define BGP_EXT_COM_STYPE_AS4_RT_REC    0x13    /* Route-Target Record [draft-ietf-bess-service-chaining] */

/* Non-Transitive Four-Octet AS-Specific Extended Community Sub-Types */

/*
 * #define BGP_EXT_COM_STYPE_AS4_GEN       0x04
 * Generic (deprecated) [draft-ietf-idr-as4octet-extcomm-generic-subtype]
*/

/* Transitive IPv4-Address-Specific Extended Community Sub-Types */

#define BGP_EXT_COM_STYPE_IP4_RT        0x02    /* Route Target [RFC4360] */
#define BGP_EXT_COM_STYPE_IP4_RO        0x03    /* Route Origin [RFC4360] */
#define BGP_EXT_COM_STYPE_IP4_OSPF_DID  0x05    /* OSPF Domain Identifier [RFC4577] */
#define BGP_EXT_COM_STYPE_IP4_OSPF_RID  0x07    /* OSPF Router ID [RFC4577] */
#define BGP_EXT_COM_STYPE_IP4_L2VPN     0x0a    /* L2VPN Identifier [RFC6074] */
#define BGP_EXT_COM_STYPE_IP4_VRF_I     0x0b    /* VRF Route Import [RFC6514] */
#define BGP_EXT_COM_STYPE_IP4_CIS_D     0x10    /* Cisco VPN-Distinguisher [Eric_Rosen] */
#define BGP_EXT_COM_STYPE_IP4_SEG_NH    0x12    /* Inter-area P2MP Segmented Next-Hop [draft-ietf-mpls-seamless-mcast] */

/* Transitive Opaque Extended Community Sub-Types */

#define BGP_EXT_COM_STYPE_OPA_COST      0x01    /* Cost Community [draft-ietf-idr-custom-decision] */
#define BGP_EXT_COM_STYPE_OPA_OSPF_RT   0x06    /* OSPF Route Type [RFC4577] */
#define BGP_EXT_COM_STYPE_OPA_COLOR     0x0b    /* Color Extended Community [RFC5512] */
#define BGP_EXT_COM_STYPE_OPA_ENCAP     0x0c    /* Encapsulation Extended Community [RFC5512] */
#define BGP_EXT_COM_STYPE_OPA_DGTW      0x0d    /* Default Gateway  [Yakov_Rekhter] */

/* BGP Cost Community Point of Insertion Types */

#define BGP_EXT_COM_COST_POI_ORIGIN     1       /* Evaluate after "Prefer lowest Origin" step */
#define BGP_EXT_COM_COST_POI_ASPATH     2       /* Evaluate after "Prefer shortest AS_PATH" step */
#define BGP_EXT_COM_COST_POI_MED        4       /* Evaluate after "Prefer lowest MED" step */
#define BGP_EXT_COM_COST_POI_LP         5       /* Evaluate after "Prefer highest Local Preference" step */
#define BGP_EXT_COM_COST_POI_AIGP       26      /* Evaluate after "Prefer lowest Accumulated IGP Cost" step */
#define BGP_EXT_COM_COST_POI_ABS        128     /* Pre-bestpath POI */
#define BGP_EXT_COM_COST_POI_IGP        129     /* Evaluate after "Prefer smallest IGP metric to next-hop" step */
#define BGP_EXT_COM_COST_POI_EI         130     /* Evaluate after "Prefer eBGP to iBGP" step */
#define BGP_EXT_COM_COST_POI_RID        131     /* Evaluate after "Prefer lowest BGP RID" step */

#define BGP_EXT_COM_COST_CID_REP        0x80    /* Bitmask - value replace/evaluate after bit */

/* BGP Tunnel Encapsulation Attribute Tunnel Types */

#define BGP_EXT_COM_TUNNEL_RESERVED     0       /* Reserved [RFC5512] */
#define BGP_EXT_COM_TUNNEL_L2TPV3       1       /* L2TPv3 over IP [RFC5512] */
#define BGP_EXT_COM_TUNNEL_GRE          2       /* GRE [RFC5512] */
#define BGP_EXT_COM_TUNNEL_ENDP         3       /* Transmit tunnel endpoint [RFC5566] */
#define BGP_EXT_COM_TUNNEL_IPSEC        4       /* IPsec in Tunnel-mode [RFC5566] */
#define BGP_EXT_COM_TUNNEL_IPIPSEC      5       /* IP in IP tunnel with IPsec Transport Mode [RFC5566] */
#define BGP_EXT_COM_TUNNEL_MPLSIP       6       /* MPLS-in-IP tunnel with IPsec Transport Mode [RFC5566] */
#define BGP_EXT_COM_TUNNEL_IPIP         7       /* IP in IP [RFC5512] */
#define BGP_EXT_COM_TUNNEL_VXLAN        8       /* VXLAN Encapsulation [draft-sd-l2vpn-evpn-overlay] */
#define BGP_EXT_COM_TUNNEL_NVGRE        9       /* NVGRE Encapsulation [draft-sd-l2vpn-evpn-overlay] */
#define BGP_EXT_COM_TUNNEL_MPLS         10      /* MPLS Encapsulation [draft-sd-l2vpn-evpn-overlay] */
#define BGP_EXT_COM_TUNNEL_MPLSGRE      11      /* MPLS in GRE Encapsulation [draft-sd-l2vpn-evpn-overlay] */
#define BGP_EXT_COM_TUNNEL_VXLANGPE     12      /* VxLAN GPE Encapsulation [draft-sd-l2vpn-evpn-overlay] */
#define BGP_EXT_COM_TUNNEL_MPLSUDP      13      /* MPLS in UDP Encapsulation [draft-ietf-l3vpn-end-system] */

/* Non-Transitive Opaque Extended Community Sub-Types */

#define BGP_EXT_COM_STYPE_OPA_OR_VAL_ST 0x00    /* BGP Origin Validation State [draft-ietf-sidr-origin-validation-signaling] */

/* BGP Generic Transitive Experimental Use Extended Community Sub-Types */

#define BGP_EXT_COM_STYPE_EXP_OSPF_RT   0x00    /* OSPF Route Type, deprecated [RFC4577] */
#define BGP_EXT_COM_STYPE_EXP_OSPF_RID  0x01    /* OSPF Router ID, deprecated [RFC4577] */
#define BGP_EXT_COM_STYPE_EXP_SEC_GROUP 0x04    /* Security Group [https://github.com/Juniper/contrail-controller/wiki/BGP-Extended-Communities#security-group] */
#define BGP_EXT_COM_STYPE_EXP_OSPF_DID  0x05    /* OSPF Domain ID, deprecated [RFC4577] */
#define BGP_EXT_COM_STYPE_EXP_F_TR      0x06    /* Flow spec traffic-rate [RFC5575] */
#define BGP_EXT_COM_STYPE_EXP_F_TA      0x07    /* Flow spec traffic-action [RFC5575] */
#define BGP_EXT_COM_STYPE_EXP_F_RED     0x08    /* Flow spec redirect [RFC5575] */
#define BGP_EXT_COM_STYPE_EXP_F_RMARK   0x09    /* Flow spec traffic-remarking [RFC5575] */
#define BGP_EXT_COM_STYPE_EXP_L2        0x0a    /* Layer2 Info Extended Community [RFC4761] */
#define BGP_EXT_COM_STYPE_EXP_ETREE     0x0b    /* E-Tree Info [RFC7796] */
#define BGP_EXT_COM_STYPE_EXP_TAG       0x84    /* Tag [https://github.com/Juniper/contrail-controller/wiki/BGP-Extended-Communities#tag] */
#define BGP_EXT_COM_STYPE_EXP_SUB_CLUS  0x85    /* Origin Sub-Cluster [https://github.com/robric/wiki-contrail-controller/blob/master/BGP-Extended-Communities.md] */

/* BGP Generic Transitive Experimental Use Extended Community Part 2 */

#define BGP_EXT_COM_STYPE_EXP_2_FLOW_RED 0x08

/* BGP Generic Transitive Experimental Use Extended Community Part 3 */

#define BGP_EXT_COM_STYPE_EXP_3_SEC_GROUP 0x04
#define BGP_EXT_COM_STYPE_EXP_3_FLOW_RED  0x08
#define BGP_EXT_COM_STYPE_EXP_3_TAG4      0x84
#define BGP_EXT_COM_STYPE_EXP_3_SUB_CLUS  0x85

/* BGP Transitive Experimental EIGRP route attribute Sub-Types */

#define BGP_EXT_COM_STYPE_EXP_EIGRP_FT  0x00    /* Route Flags, Route Tag */
#define BGP_EXT_COM_STYPE_EXP_EIGRP_AD  0x01    /* ASN, Delay */
#define BGP_EXT_COM_STYPE_EXP_EIGRP_RHB 0x02    /* Reliability, Hop Count, Bandwidth */
#define BGP_EXT_COM_STYPE_EXP_EIGRP_LM  0x03    /* Load, MTU */
#define BGP_EXT_COM_STYPE_EXP_EIGRP_EAR 0x04    /* External ASN, RID of the redistributing router */
#define BGP_EXT_COM_STYPE_EXP_EIGRP_EPM 0x05    /* External Protocol ID, metric */
#define BGP_EXT_COM_STYPE_EXP_EIGRP_RID 0x06    /* Originating EIGRP Router ID of the route */

#define BGP_EXT_COM_EXP_EIGRP_FLAG_RT   0x8000  /* Route flag - Internal/External */


/* according to IANA's number assignment at: http://www.iana.org/assignments/bgp-extended-communities */

                                        /* RFC 4360 */
#define BGP_EXT_COM_RT_AS2        0x0002  /* Route Target,Format AS(2bytes):AN(4bytes) */
#define BGP_EXT_COM_RT_IP4        0x0102  /* Route Target,Format IP address:AN(2bytes) */
#define BGP_EXT_COM_RT_AS4        0x0202  /* Route Target,Format AS(4bytes):AN(2bytes) */

/* extended community option flow flec action bit S and T */
#define BGP_EXT_COM_FSPEC_ACT_S 0x02
#define BGP_EXT_COM_FSPEC_ACT_T 0x01

/* extended community l2vpn flags */

#define BGP_EXT_COM_L2_FLAG_D     0x80
#define BGP_EXT_COM_L2_FLAG_Z1    0x40
#define BGP_EXT_COM_L2_FLAG_F     0x20
#define BGP_EXT_COM_L2_FLAG_Z345  0x1c
#define BGP_EXT_COM_L2_FLAG_C     0x02
#define BGP_EXT_COM_L2_FLAG_S     0x01

/* extended community E-Tree Info flags */

#define BGP_EXT_COM_ETREE_FLAG_RESERVED   0xFFFC
#define BGP_EXT_COM_ETREE_FLAG_P          0x0002
#define BGP_EXT_COM_ETREE_FLAG_V          0x0001

/* Extended community QoS Marking technology type */
#define QOS_TECH_TYPE_DSCP         0x00  /* DiffServ enabled IP (DSCP encoding) */
#define QOS_TECH_TYPE_802_1q       0x01  /* Ethernet using 802.1q priority tag */
#define QOS_TECH_TYPE_E_LSP        0x02  /* MPLS using E-LSP */
#define QOS_TECH_TYPE_VC           0x03  /* Virtual Channel (VC) encoding using separate channels for */
                                         /* QoS forwarding / one channel per class (e.g. ATM VCs, FR  */
                                         /* VCs, MPLS L-LSPs) */
#define QOS_TECH_TYPE_GMPLS_TIME   0x04   /* GMPLS - time slot encoding */
#define QOS_TECH_TYPE_GMPLS_LAMBDA 0x05  /* GMPLS - lambda encoding */
#define QOS_TECH_TYPE_GMPLS_FIBRE  0x06  /* GMPLS - fibre encoding */

/* OSPF codes for  BGP_EXT_COM_OSPF_RTYPE draft-rosen-vpns-ospf-bgp-mpls  */
#define BGP_OSPF_RTYPE_RTR      1 /* OSPF Router LSA */
#define BGP_OSPF_RTYPE_NET      2 /* OSPF Network LSA */
#define BGP_OSPF_RTYPE_SUM      3 /* OSPF Summary LSA */
#define BGP_OSPF_RTYPE_EXT      5 /* OSPF External LSA, note that ASBR doesn't apply to MPLS-VPN */
#define BGP_OSPF_RTYPE_NSSA     7 /* OSPF NSSA External*/
#define BGP_OSPF_RTYPE_SHAM     129 /* OSPF-MPLS-VPN Sham link */
#define BGP_OSPF_RTYPE_METRIC_TYPE 0x1 /* Type-1 (clear) or Type-2 (set) external metric */

/* Extended community & Route distinguisher formats */
#define FORMAT_AS2_LOC      0x00    /* Format AS(2bytes):AN(4bytes) */
#define FORMAT_IP_LOC       0x01    /* Format IP address:AN(2bytes) */
#define FORMAT_AS4_LOC      0x02    /* Format AS(4bytes):AN(2bytes) */

//static const true_false_string tfs_bgpext_com_type_auth = {
//    "Allocated on First Come First Serve Basis",
//    "Allocated on Standard Action, Early Allocation or Experimental Basis"
//};

static const struct int_to_string bgpext_com_type_high[] = {
    { BGP_EXT_COM_TYPE_HIGH_TR_AS2,         "Transitive 2-Octet AS-Specific" },
    { BGP_EXT_COM_TYPE_HIGH_TR_IP4,         "Transitive IPv4-Address-Specific" },
    { BGP_EXT_COM_TYPE_HIGH_TR_AS4,         "Transitive 4-Octet AS-Specific" },
    { BGP_EXT_COM_TYPE_HIGH_TR_OPAQUE,      "Transitive Opaque" },
    { BGP_EXT_COM_TYPE_HIGH_TR_QOS,         "Transitive QoS Marking" },
    { BGP_EXT_COM_TYPE_HIGH_TR_COS,         "Transitive CoS Capability" },
    { BGP_EXT_COM_TYPE_HIGH_TR_EVPN,        "Transitive EVPN" },
    { BGP_EXT_COM_TYPE_HIGH_TR_FLOW_I,      "FlowSpec Transitive" },
    { BGP_EXT_COM_TYPE_HIGH_TR_FLOW,        "Transitive Flow spec redirect/mirror to IP next-hop" },
    { BGP_EXT_COM_TYPE_HIGH_TR_FLOW_R,      "Transitive FlowSpec Redirect to indirection-id" },
    { BGP_EXT_COM_TYPE_HIGH_TR_EXP,         "Generic Transitive Experimental Use"},
    { BGP_EXT_COM_TYPE_HIGH_TR_EXP_2,       "Generic Transitive Experimental Use Part 2"},
    { BGP_EXT_COM_TYPE_HIGH_TR_EXP_3,       "Generic Transitive Experimental Use Part 3 "},
    { BGP_EXT_COM_TYPE_HIGH_TR_EXP_EIGRP,   "Transitive Experimental EIGRP" },
    { BGP_EXT_COM_TYPE_HIGH_NTR_AS2,        "Non-Transitive 2-Octet AS-Specific" },
    { BGP_EXT_COM_TYPE_HIGH_NTR_IP4,        "Non-Transitive IPv4-Address-Specific" },
    { BGP_EXT_COM_TYPE_HIGH_NTR_AS4,        "Non-Transitive 4-Octet AS-Specific" },
    { BGP_EXT_COM_TYPE_HIGH_NTR_OPAQUE,     "Non-Transitive Opaque" },
    { BGP_EXT_COM_TYPE_HIGH_NTR_QOS,        "Non-Transitive QoS Marking" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_exp_2[] = {
    { BGP_EXT_COM_STYPE_EXP_2_FLOW_RED,      "Flow spec redirect IPv4 format"},
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_exp_3[] = {
    { BGP_EXT_COM_STYPE_EXP_3_SEC_GROUP,     "Security Group AS4"},
    { BGP_EXT_COM_STYPE_EXP_3_FLOW_RED,      "Flow spec redirect AS-4byte format"},
    { BGP_EXT_COM_STYPE_EXP_3_TAG4,          "Tag4"},
    { BGP_EXT_COM_STYPE_EXP_3_SUB_CLUS,      "Origin Sub-Cluster4"},
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_evpn[] = {
    { BGP_EXT_COM_STYPE_EVPN_MMAC,        "MAC Mobility" },
    { BGP_EXT_COM_STYPE_EVPN_LABEL,       "ESI MPLS Label" },
    { BGP_EXT_COM_STYPE_EVPN_IMP,         "ES Import" },
    { BGP_EXT_COM_STYPE_EVPN_ROUTERMAC,   "EVPN Router MAC" },
    { BGP_EXT_COM_STYPE_EVPN_L2ATTR,      "Layer 2 Attributes" },
    { BGP_EXT_COM_STYPE_EVPN_ETREE,       "E-Tree" },
    { BGP_EXT_COM_STYPE_EVPN_DF,          "DF Election" },
    { BGP_EXT_COM_STYPE_EVPN_ISID,        "I-SID" },
    { BGP_EXT_COM_STYPE_EVPN_ND,          "ND" },
    { BGP_EXT_COM_STYPE_EVPN_MCFLAGS,     "Multicast Flags Extended Community" },
    { BGP_EXT_COM_STYPE_EVPN_EVIRT0,      "EVI-RT Type 0 Extended Community" },
    { BGP_EXT_COM_STYPE_EVPN_EVIRT1,      "EVI-RT Type 1 Extended Community" },
    { BGP_EXT_COM_STYPE_EVPN_EVIRT2,      "EVI-RT Type 2 Extended Community" },
    { BGP_EXT_COM_STYPE_EVPN_EVIRT3,      "EVI-RT Type 3 Extended Community" },
    { BGP_EXT_COM_STYPE_EVPN_ATTACHCIRT,  "EVPN Attachment Circuit" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_as2[] = {
    { BGP_EXT_COM_STYPE_AS2_RT,       "Route Target" },
    { BGP_EXT_COM_STYPE_AS2_RO,       "Route Origin" },
    { BGP_EXT_COM_STYPE_AS2_OSPF_DID, "OSPF Domain Identifier" },
    { BGP_EXT_COM_STYPE_AS2_DCOLL,    "BGP Data Collection" },
    { BGP_EXT_COM_STYPE_AS2_SRC_AS,   "Source AS" },
    { BGP_EXT_COM_STYPE_AS2_L2VPN,    "L2VPN Identifier" },
    { BGP_EXT_COM_STYPE_AS2_CVPND,    "Cisco VPN-Distinguisher" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_ntr_as2[] = {
    { BGP_EXT_COM_STYPE_AS2_LBW, "Link Bandwidth" },
    { BGP_EXT_COM_STYPE_AS2_VNI, "Virtual-Network Identifier" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_as4[] = {
    { BGP_EXT_COM_STYPE_AS4_RT,       "Route Target" },
    { BGP_EXT_COM_STYPE_AS4_RO,       "Route Origin" },
    { BGP_EXT_COM_STYPE_AS4_GEN,      "Generic" },
    { BGP_EXT_COM_STYPE_AS4_BGP_DC,   "BGP Data Collection"},
    { BGP_EXT_COM_STYPE_AS4_OSPF_DID, "OSPF Domain Identifier" },
    { BGP_EXT_COM_STYPE_AS4_S_AS,     "Source AS" },
    { BGP_EXT_COM_STYPE_AS4_CIS_V,    "Cisco VPN Identifier" },
    { BGP_EXT_COM_STYPE_AS4_RT_REC,   "Route-Target Record"},
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_ntr_as4[] = {
    { BGP_EXT_COM_STYPE_AS4_GEN, "Generic" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_IP4[] = {
    { BGP_EXT_COM_STYPE_IP4_RT,       "Route Target" },
    { BGP_EXT_COM_STYPE_IP4_RO,       "Route Origin" },
    { BGP_EXT_COM_STYPE_IP4_OSPF_DID, "OSPF Domain Identifier" },
    { BGP_EXT_COM_STYPE_IP4_OSPF_RID, "OSPF Router ID" },
    { BGP_EXT_COM_STYPE_IP4_L2VPN,    "L2VPN Identifier" },
    { BGP_EXT_COM_STYPE_IP4_VRF_I,    "VRF Route Import" },
    { BGP_EXT_COM_STYPE_IP4_CIS_D,    "Cisco VPN-Distinguisher" },
    { BGP_EXT_COM_STYPE_IP4_SEG_NH,   "Inter-area P2MP Segmented Next-Hop" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_ntr_IP4[] = {
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_opaque[] = {
    { BGP_EXT_COM_STYPE_OPA_COST,    "Cost" },
    { BGP_EXT_COM_STYPE_OPA_OSPF_RT, "OSPF Route Type" },
    { BGP_EXT_COM_STYPE_OPA_COLOR,   "Color" },
    { BGP_EXT_COM_STYPE_OPA_ENCAP,   "Encapsulation" },
    { BGP_EXT_COM_STYPE_OPA_DGTW,    "Default Gateway" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_cost_poi_type[] = {
    { BGP_EXT_COM_COST_POI_ORIGIN,  "\"Lowest Origin code\" step" },
    { BGP_EXT_COM_COST_POI_ASPATH,  "\"Shortest AS_PATH\" step" },
    { BGP_EXT_COM_COST_POI_MED,     "\"Lowest MED\" step" },
    { BGP_EXT_COM_COST_POI_LP,      "\"Highest Local Preference\" step" },
    { BGP_EXT_COM_COST_POI_AIGP,    "\"Lowest Accumulated IGP Cost\" step" },
    { BGP_EXT_COM_COST_POI_ABS,     "Before BGP Best Path algorithm" },
    { BGP_EXT_COM_COST_POI_IGP,     "\"Smallest IGP Metric\" step" },
    { BGP_EXT_COM_COST_POI_EI,      "\"Prefer eBGP to iBGP\" step" },
    { BGP_EXT_COM_COST_POI_RID,     "\"Smallest BGP RID\" step" },
    { 0,NULL}
};

static const struct int_to_string bgpext_com_tunnel_type[] = {
    { BGP_EXT_COM_TUNNEL_RESERVED,      "Reserved" },
    { BGP_EXT_COM_TUNNEL_L2TPV3,        "L2TPv3 over IP" },
    { BGP_EXT_COM_TUNNEL_GRE,           "GRE" },
    { BGP_EXT_COM_TUNNEL_ENDP,          "Transmit tunnel endpoint" },
    { BGP_EXT_COM_TUNNEL_IPSEC,         "IPsec in Tunnel-mode" },
    { BGP_EXT_COM_TUNNEL_IPIPSEC,       "IP in IP tunnel with IPsec Transport Mode" },
    { BGP_EXT_COM_TUNNEL_MPLSIP,        "MPLS-in-IP tunnel with IPsec Transport Mode" },
    { BGP_EXT_COM_TUNNEL_IPIP,          "IP in IP" },
    { BGP_EXT_COM_TUNNEL_VXLAN,         "VXLAN Encapsulation" },
    { BGP_EXT_COM_TUNNEL_NVGRE,         "NVGRE Encapsulation" },
    { BGP_EXT_COM_TUNNEL_MPLS,          "MPLS Encapsulation" },
    { BGP_EXT_COM_TUNNEL_MPLSGRE,       "MPLS in GRE Encapsulation" },
    { BGP_EXT_COM_TUNNEL_VXLANGPE,      "VxLAN GPE Encapsulation" },
    { BGP_EXT_COM_TUNNEL_MPLSUDP,       "MPLS in UDP Encapsulation" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_ntr_opaque[] = {
    { BGP_EXT_COM_STYPE_OPA_COST,       "Cost" },
    { BGP_EXT_COM_STYPE_OPA_OR_VAL_ST,  "BGP Origin Validation state" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_exp[] = {
    { BGP_EXT_COM_STYPE_EXP_OSPF_RT,    "OSPF Route Type" },
    { BGP_EXT_COM_STYPE_EXP_OSPF_RID,   "OSPF Router ID" },
    { BGP_EXT_COM_STYPE_EXP_SEC_GROUP,  "Security Group" },
    { BGP_EXT_COM_STYPE_EXP_OSPF_DID,   "OSPF Domain Identifier" },
    { BGP_EXT_COM_STYPE_EXP_F_TR,       "Flow spec traffic-rate" },
    { BGP_EXT_COM_STYPE_EXP_F_TA,       "Flow spec traffic-action" },
    { BGP_EXT_COM_STYPE_EXP_F_RED,      "Flow spec redirect AS 2 bytes" },
    { BGP_EXT_COM_STYPE_EXP_F_RMARK,    "Flow spec traffic-remarking" },
    { BGP_EXT_COM_STYPE_EXP_L2,         "Layer2 Info" },
    { BGP_EXT_COM_STYPE_EXP_ETREE,      "E-Tree Info" },
    { BGP_EXT_COM_STYPE_EXP_TAG,        "Tag" },
    { BGP_EXT_COM_STYPE_EXP_SUB_CLUS,   "Origin Sub-Cluster" },
    { 0, NULL}
};

static const struct int_to_string bgpext_com_stype_tr_eigrp[] = {
    { BGP_EXT_COM_STYPE_EXP_EIGRP_FT,   "EIGRP Route Flags, Route Tag" },
    { BGP_EXT_COM_STYPE_EXP_EIGRP_AD,   "EIGRP AS Number, Delay" },
    { BGP_EXT_COM_STYPE_EXP_EIGRP_RHB,  "EIGRP Reliability, Hop Count, Bandwidth" },
    { BGP_EXT_COM_STYPE_EXP_EIGRP_LM,   "EIGRP Load, MTU" },
    { BGP_EXT_COM_STYPE_EXP_EIGRP_EAR,  "EIGRP External AS Number, Router ID" },
    { BGP_EXT_COM_STYPE_EXP_EIGRP_EPM,  "EIGRP External Protocol, Metric" },
    { BGP_EXT_COM_STYPE_EXP_EIGRP_RID,  "EIGRP Originating Router ID" },
    { 0, NULL}
};

static const struct int_to_string flow_spec_op_len_val[] = {
    { 0, "1 byte: 1 <<"  },
    { 1, "2 bytes: 1 <<" },
    { 2, "4 bytes: 1 <<" },
    { 3, "8 bytes: 1 <<" },
    { 0, NULL  }
};

static const struct int_to_string qos_tech_type[] = {
    { QOS_TECH_TYPE_DSCP,         "DiffServ enabled IP (DSCP encoding)" },
    { QOS_TECH_TYPE_802_1q,       "Ethernet using 802.1q priority tag" },
    { QOS_TECH_TYPE_E_LSP,        "MPLS using E-LSP" },
    { QOS_TECH_TYPE_VC,           "Virtual Channel (VC) encoding" },
    { QOS_TECH_TYPE_GMPLS_TIME,   "GMPLS - time slot encoding" },
    { QOS_TECH_TYPE_GMPLS_LAMBDA, "GMPLS - lambda encoding" },
    { QOS_TECH_TYPE_GMPLS_FIBRE,  "GMPLS - fibre encoding" },
    { 0, NULL }
};


/* BGP message types */
#define BGP_OPEN          1
#define BGP_UPDATE        2
#define BGP_NOTIFICATION  3
#define BGP_KEEPALIVE     4
#define BGP_ROUTE_REFRESH 5
#define BGP_CAPABILITY    6
#define BGP_ROUTE_REFRESH_CISCO 0x80

#define BGP_MAJOR_ERROR_MSG_HDR       1
#define BGP_MAJOR_ERROR_OPEN_MSG      2
#define BGP_MAJOR_ERROR_UPDATE_MSG    3
#define BGP_MAJOR_ERROR_HT_EXPIRED    4
#define BGP_MAJOR_ERROR_STATE_MACHINE 5
#define BGP_MAJOR_ERROR_CEASE         6
#define BGP_MAJOR_ERROR_CAP_MSG       7

static const struct int_to_string bgpnotify_major[] = {
    { BGP_MAJOR_ERROR_MSG_HDR,       "Message Header Error" },
    { BGP_MAJOR_ERROR_OPEN_MSG,      "OPEN Message Error" },
    { BGP_MAJOR_ERROR_UPDATE_MSG,    "UPDATE Message Error" },
    { BGP_MAJOR_ERROR_HT_EXPIRED,    "Hold Timer Expired" },
    { BGP_MAJOR_ERROR_STATE_MACHINE, "Finite State Machine Error" },
    { BGP_MAJOR_ERROR_CEASE,         "Cease" },
    { BGP_MAJOR_ERROR_CAP_MSG,       "CAPABILITY Message Error" },
    { 0, NULL }
};

static const struct int_to_string bgpattr_origin[] = {
    { 0, "IGP" },
    { 1, "EGP" },
    { 2, "INCOMPLETE" },
    { 0, NULL }
};

/* RFC 2858 subsequent address family numbers */
#define SAFNUM_UNICAST          1
#define SAFNUM_MULCAST          2
#define SAFNUM_UNIMULC          3
#define SAFNUM_MPLS_LABEL       4  /* rfc3107 */
#define SAFNUM_MCAST_VPN        5  /* draft-ietf-l3vpn-2547bis-mcast-bgp-08.txt */
#define SAFNUM_ENCAPSULATION    7  /* rfc5512 */
#define SAFNUM_TUNNEL          64  /* draft-nalawade-kapoor-tunnel-safi-02.txt */
#define SAFNUM_VPLS            65
#define SAFNUM_MDT             66  /* rfc6037 */
#define SAFNUM_EVPN            70  /* EVPN RFC */
#define SAFNUM_BGP_LS          71  /* RFC7752 */
#define SAFNUM_BGP_LS_VPN      72  /* RFC7752 */
#define SAFNUM_LAB_VPNUNICAST 128  /* Draft-rosen-rfc2547bis-03 */
#define SAFNUM_LAB_VPNMULCAST 129
#define SAFNUM_LAB_VPNUNIMULC 130
#define SAFNUM_ROUTE_TARGET   132  /* RFC 4684 Constrained Route Distribution for BGP/MPLS IP VPN */
#define SAFNUM_FSPEC_RULE     133  /* RFC 5575 BGP flow spec SAFI */
#define SAFNUM_FSPEC_VPN_RULE 134  /* RFC 5575 BGP flow spec SAFI VPN */

/* Subsequent address family identifier, RFC2858 */
static const struct int_to_string bgpattr_nlri_safi[] = {
    { 0,                        "Reserved" },
    { SAFNUM_UNICAST,           "Unicast" },
    { SAFNUM_MULCAST,           "Multicast" },
    { SAFNUM_UNIMULC,           "Unicast+Multicast" },
    { SAFNUM_MPLS_LABEL,        "Labeled Unicast"},
    { SAFNUM_MCAST_VPN,         "MCAST-VPN"},
    { SAFNUM_ENCAPSULATION,     "Encapsulation"},
    { SAFNUM_TUNNEL,            "Tunnel"},
    { SAFNUM_VPLS,              "VPLS"},
    { SAFNUM_BGP_LS,            "BGP-LS"},
    { SAFNUM_BGP_LS_VPN,        "BGP-LS-VPN"},
    { SAFNUM_LAB_VPNUNICAST,    "Labeled VPN Unicast" },        /* draft-rosen-rfc2547bis-03 */
    { SAFNUM_LAB_VPNMULCAST,    "Labeled VPN Multicast" },
    { SAFNUM_LAB_VPNUNIMULC,    "Labeled VPN Unicast+Multicast" },
    { SAFNUM_ROUTE_TARGET,      "Route Target Filter" },
    { SAFNUM_EVPN,              "EVPN" },
    { SAFNUM_FSPEC_RULE,        "Flow Spec Filter" },
    { SAFNUM_FSPEC_VPN_RULE,    "Flow Spec Filter VPN" },
    { 0, NULL }
};

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

enum bgp_index_em {
    
    EM_BGP_LENGTH,
    EM_BGP_TYPE,
    EM_BGP_TYPE_STR,
    EM_BGP_OPEN_VERSION,
    EM_BGP_OPEN_MYAS,
    EM_BGP_OPEN_HOLDTIME,
    EM_BGP_OPEN_IDENTIFIER,
    EM_BGP_OPEN_OPT_LEN,
    EM_BGP_OPEN_AH_CODE,
    EM_BGP_OPEN_AH_DATA,
    EM_BGP_OPEN_CAPABILITY_TYPES,
    EM_BGP_OPEN_PARAM_TYPE,
    EM_BGP_UPDATE_WR_LEN,
    EM_BGP_UPDATE_WITHDRAWN_ROUTES,
    EM_BGP_UPDATE_NLRI_LENGTH,
    EM_BGP_UPDATE_ORIGIN,
    EM_BGP_UPDATE_ORIGIN_STR,
    EM_BGP_UPDATE_AS_SEGMENT,
    EM_BGP_UPDATE_ASN,
    EM_BGP_UPDATE_AS_PATH,
    EM_BGP_UPDATE_NEXT_HOP,
    EM_BGP_UPDATE_MED,
    EM_BGP_UPDATE_LOCAL_PREF,
    EM_BGP_UPDATE_AGGREGATOR_AS,
    EM_BGP_UPDATE_AGGREGATOR_IP,
    EM_BGP_UPDATE_COMMUNITIES,
    EM_BGP_UPDATE_COMMUNITIES_AS,
    EM_BGP_UPDATE_COMMUNITIES_VALUE,
    EM_BGP_UPDATE_EXT_COMMUNITIES_TYPE_HIGH,
    EM_BGP_UPDATE_EXT_COMMUNITIES,
    EM_BGP_UPDATE_ORIGINATOR_ID,
    EM_BGP_UPDATE_CLUSTER_LIST,
    EM_BGP_UPDATE_MP_REACH_NLRI_AFI,
    EM_BGP_UPDATE_MP_REACH_NLRI_SAFI,
    EM_BGP_UPDATE_MP_REACH_NEXTHOP,
    EM_BGP_UPDATE_MP_REACH_NLRI,
    EM_BGP_UPDATE_MP_UNREACH_NLRI_AFI,
    EM_BGP_UPDATE_MP_UNREACH_NLRI_SAFI,
    EM_BGP_UPDATE_MP_UNREACH_NLRI,
    EM_BGP_UPDATE_NETWORK_LAYER_RECHABILITY,
    EM_BGP_UPDATE_MP_REACHABILITY,
    EM_BGP_UPDATE_MP_UNREACHABILITY,
    EM_BGP_NOTIFICATION_MAEC,
    EM_BGP_NOTIFICATION_MAE_STR,
    EM_BGP_NOTIFICATION_MIEC,
    EM_BGP_NOTIFICATION_ERR_DATA,    
    EM_BGP_ROUTE_REFRESH_AFI,
    EM_BGP_ROUTE_REFRESH_SUBTYPE,
    EM_BGP_ROUTE_REFRESH_SUBTYPE_STR,
    EM_BGP_ROUTE_REFRESH_SAFI,
    EM_BGP_ROUTE_REFRESH_SAFI_STR,
    EM_BGP_MARKER,
    EM_BGP_MAX,
};


static dpi_field_table  bgp_field_array_sdt[] = {
    DPI_FIELD_D(EM_BGP_LENGTH,                              YA_FT_UINT16,   "BgpLength"),
    DPI_FIELD_D(EM_BGP_TYPE,                                YA_FT_UINT8,    "BgpType"),
    DPI_FIELD_D(EM_BGP_TYPE_STR,                            YA_FT_STRING,   "BgpTypeStr"),
    DPI_FIELD_D(EM_BGP_OPEN_VERSION,                        YA_FT_UINT8,    "OpenVersion"),
    DPI_FIELD_D(EM_BGP_OPEN_MYAS,                           YA_FT_UINT16,   "OpenMyas"),
    DPI_FIELD_D(EM_BGP_OPEN_HOLDTIME,                       YA_FT_UINT16,   "OpenHoldtime"),
    DPI_FIELD_D(EM_BGP_OPEN_IDENTIFIER,                     YA_FT_STRING,   "OpenIdentifier"),
    DPI_FIELD_D(EM_BGP_OPEN_OPT_LEN,                        YA_FT_UINT8,    "OpenOPTLength"),
    DPI_FIELD_D(EM_BGP_OPEN_AH_CODE,                        YA_FT_UINT8,    "OpenAuthenticationCode"),
    DPI_FIELD_D(EM_BGP_OPEN_AH_DATA,                        YA_FT_STRING,   "OpenAuthenticationData"),
    DPI_FIELD_D(EM_BGP_OPEN_CAPABILITY_TYPES,               YA_FT_STRING,   "OpenCapabilityTypes"),
    DPI_FIELD_D(EM_BGP_OPEN_PARAM_TYPE,                     YA_FT_UINT8,    "OpenParamType"),
    DPI_FIELD_D(EM_BGP_UPDATE_WR_LEN,                       YA_FT_UINT16,   "UpdateWRLength"),
    DPI_FIELD_D(EM_BGP_UPDATE_WITHDRAWN_ROUTES,             YA_FT_STRING,   "UpdateWithDrawnRoutes"),
    DPI_FIELD_D(EM_BGP_UPDATE_NLRI_LENGTH,                  YA_FT_UINT16,   "UpdateNLRILength"),
    DPI_FIELD_D(EM_BGP_UPDATE_ORIGIN,                       YA_FT_UINT8,    "UpdateOrigin"),
    DPI_FIELD_D(EM_BGP_UPDATE_ORIGIN_STR,                   YA_FT_STRING,   "UpdateOriginStr"),
    DPI_FIELD_D(EM_BGP_UPDATE_AS_SEGMENT,                   YA_FT_UINT32,   "UpdateAsSegmentCount"),
    DPI_FIELD_D(EM_BGP_UPDATE_ASN,                          YA_FT_UINT32,   "UpdateASN"),
    DPI_FIELD_D(EM_BGP_UPDATE_AS_PATH,                      YA_FT_STRING,   "UpdateAsPath"),
    DPI_FIELD_D(EM_BGP_UPDATE_NEXT_HOP,                     YA_FT_STRING,   "UpdateNextHop"),
    DPI_FIELD_D(EM_BGP_UPDATE_MED,                          YA_FT_UINT32,   "UpdateMed"),
    DPI_FIELD_D(EM_BGP_UPDATE_LOCAL_PREF,                   YA_FT_UINT32,   "UpdateLocalPref"),
    DPI_FIELD_D(EM_BGP_UPDATE_AGGREGATOR_AS,                YA_FT_STRING,   "UpdateAggregatorAs"),
    DPI_FIELD_D(EM_BGP_UPDATE_AGGREGATOR_IP,                YA_FT_STRING,   "UpdateAggregatorIp"),
    DPI_FIELD_D(EM_BGP_UPDATE_COMMUNITIES,                  YA_FT_STRING,   "UpdateCommunities"),
    DPI_FIELD_D(EM_BGP_UPDATE_COMMUNITIES_AS,               YA_FT_STRING,   "UpdateCommunitiesAs"),
    DPI_FIELD_D(EM_BGP_UPDATE_COMMUNITIES_VALUE,            YA_FT_STRING,   "UpdateCommunitiesValue"),
    DPI_FIELD_D(EM_BGP_UPDATE_EXT_COMMUNITIES_TYPE_HIGH,    YA_FT_STRING,   "ext_UpdateCommunitiesTypeHigh"),
    DPI_FIELD_D(EM_BGP_UPDATE_EXT_COMMUNITIES,              YA_FT_STRING,   "ext_UpdateCommunities"),
    DPI_FIELD_D(EM_BGP_UPDATE_ORIGINATOR_ID,                YA_FT_STRING,   "UpdateOriginatorId"),
    DPI_FIELD_D(EM_BGP_UPDATE_CLUSTER_LIST,                 YA_FT_STRING,   "UpdateClusterList"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_REACH_NLRI_AFI,            YA_FT_STRING,   "UpdateMPReachNLRIAfi"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_REACH_NLRI_SAFI,           YA_FT_STRING,   "UpdateMPReachNLRISafi"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_REACH_NEXTHOP,             YA_FT_STRING,   "UpdateMPReachNexthop"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_REACH_NLRI,                YA_FT_STRING,   "UpdateMPReachNLRI"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_UNREACH_NLRI_AFI,          YA_FT_STRING,   "UpdateMPUnreachNLRIAfi"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_UNREACH_NLRI_SAFI,         YA_FT_STRING,   "UpdateMPUnreachNLRISafi"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_UNREACH_NLRI,              YA_FT_STRING,   "UpdateMPUnreachNLRI"),
    DPI_FIELD_D(EM_BGP_UPDATE_NETWORK_LAYER_RECHABILITY,    YA_FT_STRING,   "UpdateNetworkLayerRechability"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_REACHABILITY,              YA_FT_STRING,   "UpdateMPReachability"),
    DPI_FIELD_D(EM_BGP_UPDATE_MP_UNREACHABILITY,            YA_FT_STRING,   "UpdateMPUnReachability"),
    DPI_FIELD_D(EM_BGP_NOTIFICATION_MAEC,                   YA_FT_UINT8,    "NotificationMAEC"),
    DPI_FIELD_D(EM_BGP_NOTIFICATION_MAE_STR,                YA_FT_STRING,   "NotificationMAEStr"),
    DPI_FIELD_D(EM_BGP_NOTIFICATION_MIEC,                   YA_FT_UINT8,    "NotificationMIEC"),
    DPI_FIELD_D(EM_BGP_NOTIFICATION_ERR_DATA,               YA_FT_STRING,   "NotificationErrData"),
    DPI_FIELD_D(EM_BGP_ROUTE_REFRESH_AFI,                   YA_FT_UINT16,   "RouteRefreshAFI"),
    DPI_FIELD_D(EM_BGP_ROUTE_REFRESH_SUBTYPE,               YA_FT_UINT8,    "RouteRefreshSubtype"),
    DPI_FIELD_D(EM_BGP_ROUTE_REFRESH_SUBTYPE_STR,           YA_FT_STRING,   "RouteRefreshSubtypeStr"),
    DPI_FIELD_D(EM_BGP_ROUTE_REFRESH_SAFI,                  YA_FT_UINT8,    "RouteRefreshSAFI"),
    DPI_FIELD_D(EM_BGP_ROUTE_REFRESH_SAFI_STR,              YA_FT_STRING,   "RouteRefreshSAFIStr"),
    DPI_FIELD_D(EM_BGP_MARKER,                              YA_FT_STRING,   "marker"),

};



static int32_t bgp_asn_len = 0;

static int bgp_field_element(struct tbl_log *log_ptr,struct flow_info *flow _U_, int direction _U_, struct bgp_info *info, int *idx, int i)
{
    int j;

    switch(i){
    case EM_BGP_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_len);
        break;
    case EM_BGP_TYPE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_type);
        break;
    case EM_BGP_TYPE_STR:
        if (info->bgp_type_str)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_type_str, strlen(info->bgp_type_str));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_OPEN_VERSION:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_version);
        break;
    case EM_BGP_OPEN_MYAS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_myas);
        break;
    case EM_BGP_OPEN_HOLDTIME:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_holdtime);
        break;
    case EM_BGP_OPEN_IDENTIFIER:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_identifier, strlen(info->bgp_open_identifier));
        break;
    case EM_BGP_OPEN_OPT_LEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_opt_len);
        break;
    case EM_BGP_OPEN_AH_CODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_ah_code);
        break;
    case EM_BGP_OPEN_AH_DATA:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_open_ah_data, strlen(info->bgp_open_ah_data));
        break;
    case EM_BGP_OPEN_CAPABILITY_TYPES:
    {
        char tmp[2048];
        int l = 0;
        memset(tmp, 0, sizeof(tmp));
        for (j = 0; j < info->cap_size && l < (int)sizeof(tmp); j++) {
            const char *p_val = val_to_string(info->cap_info[j].cap_type, capability_vals);
            if (j > 0)
                l += snprintf(tmp + l, sizeof(tmp) - l, ",");
            l += snprintf(tmp + l, sizeof(tmp) - l, "(%d)%s", info->cap_info[j].cap_type, p_val);
        }

        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        break;
    }
    case EM_BGP_OPEN_PARAM_TYPE:
        if (info->bgp_open_param_type == 0)
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        else
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->bgp_open_param_type);
        break;
    case EM_BGP_UPDATE_WR_LEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_wr_len);
        break;
    case EM_BGP_UPDATE_WITHDRAWN_ROUTES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_withdrawn_routes, info->bgp_update_wr_len);
        break;
    case EM_BGP_UPDATE_NLRI_LENGTH:
        if (info->bgp_update_nlri_length)
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_nlri_length);
        else

            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_UPDATE_ORIGIN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_origin);
        break;
    case EM_BGP_UPDATE_ORIGIN_STR:
        if (info->bgp_update_origin_str) {
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_origin_str, strlen(info->bgp_update_origin_str));
        }
        else{
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_BGP_UPDATE_AS_SEGMENT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_number_as_segment);
        break;
    case EM_BGP_UPDATE_ASN:
        if (info->bgp_update_asn_num > 0)
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_as_path[0]); // 只输出第一个 asn
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_UPDATE_AS_PATH:
        if (info->bgp_update_asn_num > 0) {
            char tmp[1024];
            char s1[64];
            memset(tmp, 0, sizeof(tmp));
            for (j = 0; j < info->bgp_update_asn_num; j++) {
                memset(s1, 0, sizeof(s1));
                snprintf(s1, sizeof(s1), "%u", info->bgp_update_as_path[j]);
                strcat(tmp, s1);
                strcat(tmp, " ");
            }
            tmp[strlen(tmp) - 1] = '\0';
            
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        }
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_UPDATE_NEXT_HOP:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_next_hop, strlen(info->bgp_update_next_hop));
        break;
    case EM_BGP_UPDATE_MED:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_med);
        break;
    case EM_BGP_UPDATE_LOCAL_PREF:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_local_pref);
        break;
    case EM_BGP_UPDATE_AGGREGATOR_AS:
        write_fstring_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "%lu", info->bgp_update_aggregator_as);
        break;
    case EM_BGP_UPDATE_AGGREGATOR_IP:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_aggregatorIp, strlen(info->bgp_update_aggregatorIp));
        break;
    case EM_BGP_UPDATE_COMMUNITIES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_communities, strlen(info->bgp_update_communities));
        break;
    case EM_BGP_UPDATE_COMMUNITIES_AS:
    {
        char tmp[1024];
        char s1[32];
        memset(tmp, 0, sizeof(tmp));
        for (j = 0; j < info->comm_size; j++) {
            memset(s1, 0, sizeof(s1));
            snprintf(s1, sizeof(s1), "%d", info->comm_info[j].as);
            strcat(tmp, s1);

            if (j != info->comm_size - 1)
                strcat(tmp, ",");
        }

        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        break;
    }
    case EM_BGP_UPDATE_COMMUNITIES_VALUE:
    {
        char tmp[1024];
        char s1[32];
        memset(tmp, 0, sizeof(tmp));
        for (j = 0; j < info->comm_size; j++) {
            memset(s1, 0, sizeof(s1));
            snprintf(s1, sizeof(s1), "%d", info->comm_info[j].value);
            strcat(tmp, s1);

            if (j != info->comm_size - 1)
                strcat(tmp, ",");
        }

        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        break;
    }
    case EM_BGP_UPDATE_EXT_COMMUNITIES_TYPE_HIGH:
    {
        char tmp[2048];
        char s1[100];
        memset(tmp, 0, sizeof(tmp));
        for (j = 0; j < info->bgp_ext_com_type_size; j++) {
            const char *p = val_to_string(info->bgp_ext_com_type_high[j], bgpext_com_type_high);
            memset(s1, 0, sizeof(s1));
            snprintf(s1, sizeof(s1), "(%d)%s", info->bgp_ext_com_type_high[j], p);
            strcat(tmp, s1);

            if (j != info->bgp_ext_com_type_size - 1)
                strcat(tmp, ",");
        }

        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        break;
    }
    case EM_BGP_UPDATE_ORIGINATOR_ID:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_originator_id, strlen(info->bgp_update_originator_id));
        break;
    case EM_BGP_UPDATE_CLUSTER_LIST:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_cluster_list, strlen(info->bgp_update_cluster_list));
        break;
    case EM_BGP_UPDATE_MP_REACH_NLRI_AFI:
        write_fstring_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "%u", info->bgp_update_mp_reach_nlri_afi);
        break;
    case EM_BGP_UPDATE_MP_REACH_NLRI_SAFI:
        write_fstring_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "%u", info->bgp_update_mp_reach_nlri_safi);
        break;
    case EM_BGP_UPDATE_MP_REACH_NEXTHOP:
        if (info->bgp_update_mp_reach_nexthop_len > 0) {
            char tmp[100];
            memset(tmp, 0, sizeof(tmp));
            get_iparray_to_string(tmp, sizeof(tmp), info->bgp_update_mp_reach_nexthop);

            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        }
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_UPDATE_MP_REACH_NLRI:
    {
        char tmp[100];
        memset(tmp, 0, sizeof(tmp));

        if (info->bgp_update_mp_reach_nlri.type == 1 && info->bgp_update_mp_reach_nlri.evpn.evpn_length) {
            snprintf(tmp, sizeof(tmp), "%u.%u.%u.%u:%hu"
                                , info->bgp_update_mp_reach_nlri.evpn.route_distinguisher[2]
                                , info->bgp_update_mp_reach_nlri.evpn.route_distinguisher[3]
                                , info->bgp_update_mp_reach_nlri.evpn.route_distinguisher[4]
                                , info->bgp_update_mp_reach_nlri.evpn.route_distinguisher[5]
                                , get_uint16_ntohs(info->bgp_update_mp_reach_nlri.evpn.route_distinguisher, 6));

            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        }
        else if (info->bgp_update_mp_reach_nlri.type == 2 && info->bgp_update_mp_reach_nlri.ls.nlri_length) {
            char tmp[200];
            memset(tmp, 0, sizeof(tmp));

            snprintf(tmp, sizeof(tmp), "NLRI Type: (%d)%s, NLRI Length: %d"
                                , info->bgp_update_mp_reach_nlri.ls.nlri_type
                                , val_to_string(info->bgp_update_mp_reach_nlri.ls.nlri_type, bgp_ls_nlri_type_vals)
                                , info->bgp_update_mp_reach_nlri.ls.nlri_length);

            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        }
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    }
    case EM_BGP_UPDATE_MP_UNREACH_NLRI_AFI:
        if (info->bgp_update_mp_unreach_nlri_afi)
            write_fstring_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "%u", info->bgp_update_mp_unreach_nlri_afi);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_UPDATE_MP_UNREACH_NLRI_SAFI:
        if (info->bgp_update_mp_unreach_nlri_safi)
            write_fstring_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "%u", info->bgp_update_mp_unreach_nlri_safi);
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_BGP_UPDATE_MP_UNREACH_NLRI:
    {
        char tmp[100];
        memset(tmp, 0, sizeof(tmp));

        if (info->bgp_update_mp_unreach_nlri.type == 1 && info->bgp_update_mp_unreach_nlri.evpn.evpn_length) {
            snprintf(tmp, sizeof(tmp), "%u.%u.%u.%u:%hu"
                                    , info->bgp_update_mp_unreach_nlri.evpn.route_distinguisher[2]
                                    , info->bgp_update_mp_unreach_nlri.evpn.route_distinguisher[3]
                                    , info->bgp_update_mp_unreach_nlri.evpn.route_distinguisher[4]
                                    , info->bgp_update_mp_unreach_nlri.evpn.route_distinguisher[5]
                                    , get_uint16_ntohs(info->bgp_update_mp_unreach_nlri.evpn.route_distinguisher, 6));

            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        }
        else if (info->bgp_update_mp_unreach_nlri.type == 2 && info->bgp_update_mp_unreach_nlri.ls.nlri_length) {
            char tmp[200];
            memset(tmp, 0, sizeof(tmp));

            snprintf(tmp, sizeof(tmp), "NLRI Type: (%d)%s, NLRI Length: %d"
                , info->bgp_update_mp_unreach_nlri.ls.nlri_type
                , val_to_string(info->bgp_update_mp_unreach_nlri.ls.nlri_type, bgp_ls_nlri_type_vals)
                , info->bgp_update_mp_unreach_nlri.ls.nlri_length);

            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp, strlen(tmp));
        }
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    }
    case EM_BGP_UPDATE_NETWORK_LAYER_RECHABILITY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_network_layer_rechability, strlen(info->bgp_update_network_layer_rechability));
        break;
    case EM_BGP_UPDATE_MP_REACHABILITY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_mp_rechability, strlen(info->bgp_update_mp_rechability));
        break;
    case EM_BGP_UPDATE_MP_UNREACHABILITY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_update_mp_unrechability, strlen(info->bgp_update_mp_unrechability));
        break;
    case EM_BGP_NOTIFICATION_MAEC:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_notification_mae_code);
        break;
    case EM_BGP_NOTIFICATION_MAE_STR:
        //write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_notification_mae_str, strlen(info->bgp_notification_mae_str));
        if (info->bgp_type == BGP_NOTIFICATION && info->bgp_notification_mae_str != NULL) {
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_notification_mae_str, strlen(info->bgp_notification_mae_str));
        } else {
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_BGP_NOTIFICATION_MIEC:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_notification_mie_code);
        break;
    case EM_BGP_NOTIFICATION_ERR_DATA:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                        info->bgp_notification_err_data, info->bgp_notification_err_length);
        break;
    case EM_BGP_ROUTE_REFRESH_AFI:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_route_refresh_afi);
        break;
    case EM_BGP_ROUTE_REFRESH_SUBTYPE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_route_refresh_subtype);
        break;
    case EM_BGP_ROUTE_REFRESH_SUBTYPE_STR:
        //write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_route_refresh_subtype_str, strlen(info->bgp_route_refresh_subtype_str));
        if (info->bgp_route_refresh_subtype_str) {
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_route_refresh_subtype_str, strlen(info->bgp_route_refresh_subtype_str));
        }
        else {
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_BGP_ROUTE_REFRESH_SAFI:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_route_refresh_safi);
        break;
    case EM_BGP_ROUTE_REFRESH_SAFI_STR:
        if (info->bgp_route_refresh_safi_str) {
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->bgp_route_refresh_safi_str, strlen(info->bgp_route_refresh_safi_str));
        }
        else {
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_BGP_MARKER:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->marker, 16);
        break;
#if 0 
    case EM_LDAP_MESSAGEID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->MessageID);
        break;
    case EM_LDAP_BINDREQUEST_NAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->BindRequest_name, strlen(info->BindRequest_name));
        break;
    case EM_LDAP_AUTHENTICATIONTYPE:
        if (info->authenticationType)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->authenticationType, strlen(info->authenticationType));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
#endif
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    return 0;
}


static int write_bgp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    struct bgp_info *info=(struct bgp_info *)field_info;
    if(!info){
        return PKT_DROP;
    }

    init_log_ptr_data(log_ptr, flow,PROTOCOL_BGP);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "bgp");

    for(i=0;i<EM_BGP_MAX;i++){
        bgp_field_element(log_ptr, flow, direction, info, &idx, i);
    }

    if (flow->tuple.inner.proto == IPPROTO_UDP)
        log_ptr->log_type = TBL_LOG_BGP;
    else
        log_ptr->log_type = TBL_LOG_BGP;

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    log_ptr->proto_id = PROTOCOL_BGP;
    log_ptr->flow = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}

/*ȥ���ַ������һ��������*/
static
void bgp_del_last_pun(char *str, const char del)
{
    int len = 0;
    len = strlen(str);
    if (str[len - 1] == del)
    {
        str[len - 1] = '\0';
    }
}

static void
identify_bgp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len _U_)
{
    UNUSED(payload);
    UNUSED(payload_len);

    if (g_config.protocol_switch[PROTOCOL_BGP] == 0) return;

    uint16_t s_port = 0, d_port = 0;

    s_port = ntohs(flow->tuple.inner.port_src);
    d_port = ntohs(flow->tuple.inner.port_dst);
    if (d_port == 179 || s_port == 179) {
        flow->real_protocol_id = PROTOCOL_BGP;
    }

    return;
}

static const struct int_to_string bgptypevals[] = {
    { BGP_OPEN,                "open" },
    { BGP_UPDATE,              "update" },
    { BGP_NOTIFICATION,        "notification" },
    { BGP_KEEPALIVE,           "keepalive" },
    { BGP_ROUTE_REFRESH,       "refresh" },
    { BGP_CAPABILITY,          "capability" },
    { BGP_ROUTE_REFRESH_CISCO, "cisco refresh" },
    { 0, NULL }
};

/*
 * Detect IPv4/IPv6 prefixes  conform to BGP Additional Path but NOT conform to standard BGP
 *
 * A real BGP speaker would rely on the BGP Additional Path in the BGP Open messages.
 * But it is not suitable for a packet analyse because the BGP sessions are not supposed to
 * restart very often, and Open messages from both sides of the session would be needed
 * to determine the result of the capability negociation.
 * Code inspired from the decode_prefix4 function
 */
static int
detect_add_path_prefix46(const uint8_t *tvb, uint32_t offset, uint32_t end, uint32_t max_bit_length) {
    uint32_t addr_len;
    uint8_t prefix_len;
    uint32_t o;
    /* Must be compatible with BGP Additional Path  */
    for (o = offset + 4; o < end; o += 4) {
        prefix_len = get_uint8_t(tvb, o);
        if (prefix_len > max_bit_length) {
            return 0; /* invalid prefix length - not BGP add-path */
        }
        addr_len = (prefix_len + 7) / 8;
        o += 1 + addr_len;
        if (o > end) {
            return 0; /* invalid offset - not BGP add-path */
        }
        if (prefix_len % 8) {
            /* detect bits set after the end of the prefix */
            if (get_uint8_t(tvb, o - 1)  & (0xFF >> (prefix_len % 8))) {
                return 0; /* invalid prefix content - not BGP add-path */
            }
        }
    }
    /* Must NOT be compatible with standard BGP */
    for (o = offset; o < end; ) {
        prefix_len = get_uint8_t(tvb, o);
        if (prefix_len == 0 && end - offset > 1) {
            return 1; /* prefix length is zero (i.e. matching all IP prefixes) and remaining bytes within the NLRI is greater than or equal to 1 - may be BGP add-path */
        }
        if (prefix_len > max_bit_length) {
            return 1; /* invalid prefix length - may be BGP add-path */
        }
        addr_len = (prefix_len + 7) / 8;
        o += 1 + addr_len;
        if (o > end) {
            return 1; /* invalid offset - may be BGP add-path */
        }
        if (prefix_len % 8) {
            /* detect bits set after the end of the prefix */
            if (get_uint8_t(tvb, o - 1) & (0xFF >> (prefix_len % 8))) {
                return 1; /* invalid prefix content - may be BGP add-path (or a bug) */
            }
        }
    }
    return 0; /* valid - do not assume Additional Path */
}
static int
detect_add_path_prefix4(const uint8_t *tvb, uint32_t offset, uint32_t end) {
    return detect_add_path_prefix46(tvb, offset, end, 32);
}
static int
detect_add_path_prefix6(const uint8_t *tvb, uint32_t offset, uint32_t end) {
    return detect_add_path_prefix46(tvb, offset, end, 128);
}

static void
decode_ipv4_ipv6(const uint8_t *tvb, uint32_t tvb_len, uint32_t offset, uint32_t prefix_len, char *ip_addr, uint8_t is_ipv4)
{
    UNUSED(tvb_len);
    uint32_t i = 0;
    uint32_t addr_len = (prefix_len + 7) / 8;

    char tmp_addr[256] = { '\0' };
    if (is_ipv4 == 1) {
        if (addr_len > 4) {
            return;
        }
        snprintf(tmp_addr, sizeof(tmp_addr), "%u.%u.%u.%u",
            ((addr_len >= 1) ? get_uint8_t(tvb, offset) : 0),
            ((addr_len >= 2) ? get_uint8_t(tvb, offset + 1) : 0),
            ((addr_len >= 3) ? get_uint8_t(tvb, offset + 2) : 0),
            ((addr_len >= 4) ? get_uint8_t(tvb, offset + 3) : 0) );
    }
    else {
        if (addr_len > 16) {
            return;
        }
        for (i = 0; i < addr_len; i += 2) {
            uint16_t val = 0;
            val = get_uint16_ntohs(tvb, offset + i);
            //snprintf(tmp_addr + strlen(tmp_addr), sizeof(tmp_addr), "%x%x:", tvb[offset + i], tvb[offset + 1 + i]);
            snprintf(tmp_addr + strlen(tmp_addr), sizeof(tmp_addr), "%x:", val);
        }
        if (addr_len < 16) {
            snprintf(tmp_addr + strlen(tmp_addr), sizeof(tmp_addr), ":");
        }
        else {
            //ȥ�����һ������
            int len = 0;
            len = strlen(tmp_addr);
            if (tmp_addr[len - 1] == ':')
            {
                tmp_addr[len - 1] = '\0';
            }
        }
    }
    //���������ӵ��ַ�������
    snprintf(tmp_addr + strlen(tmp_addr), sizeof(tmp_addr), "/%u", prefix_len);
    //snprintf(ip_addr, 256, "%s", tmp_addr);
    memcpy(ip_addr, tmp_addr, sizeof(tmp_addr));
    //ip_addr = tmp_addr;
}


static int
decode_path_prefix4(const uint8_t *tvb,uint32_t tvb_len, uint32_t offset, char *out, size_t out_len)
{
    // uint32_t path_identifier;
    uint8_t p_len;
    uint32_t addr_len;
    char ip_addr[256];

    // path_identifier = get_uint32_ntohl(tvb, offset);
    p_len = get_uint8_t(tvb, offset + 4);

    if (p_len > 32)
        return -1;

    addr_len = (p_len + 7) / 8;

    if (out == NULL)
        return (addr_len + 4 + 1);

    decode_ipv4_ipv6(tvb, tvb_len, offset + 4 + 1, p_len, ip_addr, 1);
    //snprintf(pinfo->bgp_update_network_layer_rechability, 256, "%s", ip_addr);

    size_t used_len = strlen(out);
    if (out_len - used_len > (strlen(ip_addr) + 1))
        sprintf(out + used_len, "%s,", ip_addr);

    return  (addr_len + 4 + 1);
}


/*
 * Decode an IPv4 prefix.
 */
static int
decode_prefix4(const uint8_t *tvb, uint32_t tvb_len, uint32_t offset, char *out, size_t out_len)
{
    uint8_t p_len;
    uint32_t addr_len;
    char ip_addr[256];

    p_len = get_uint8_t(tvb, offset);

    if (p_len > 32)
        return -1;

    addr_len = (p_len + 7) / 8;

    if (out == NULL)
        return (addr_len + 1);

    decode_ipv4_ipv6(tvb, tvb_len, offset + 1, p_len, ip_addr, 1);
    //snprintf(pinfo->bgp_update_network_layer_rechability, 256, "%s", ip_addr);

    size_t used_len = strlen(out);
    if (out_len - used_len > (strlen(ip_addr) + 1))
        sprintf(out + used_len, "%s,", ip_addr);

    return  (addr_len + 1);
}

/*
 * Decode an IPv6 prefix with path ID.
 */
static int
decode_path_prefix6(const uint8_t *tvb,uint32_t tvb_len, uint32_t offset, char *out, size_t out_len)
{
    // uint32_t path_identifier;
    uint8_t p_len;
    uint32_t addr_len;
    char ip_addr[256];

    // path_identifier = get_uint32_ntohl(tvb, offset);
    p_len = get_uint8_t(tvb, offset + 4);

    if (p_len > 128)
        return -1;

    addr_len = (p_len + 7) / 8;

    if (out == NULL)
        return (addr_len + 4 + 1);

    decode_ipv4_ipv6(tvb, tvb_len, offset + 4 + 1, p_len, ip_addr, 0);
    //snprintf(pinfo->bgp_update_network_layer_rechability, 256, "%s", ip_addr);
    //snprintf(pinfo->bgp_update_network_layer_rechability + strlen(pinfo->bgp_update_network_layer_rechability), 256, "%s,", ip_addr);

    size_t used_len = strlen(out);
    if (out_len - used_len > (strlen(ip_addr) + 1))
        sprintf(out + used_len, "%s,", ip_addr);

    return (addr_len + 4 + 1);
}

/*
 * Decode an IPv6 prefix.
 */
static int
decode_prefix6(const uint8_t *tvb, uint32_t tvb_len, uint32_t offset,uint16_t tlen, char *out, size_t out_len)
{
    UNUSED(tlen);
    uint8_t             plen;     /* prefix length                      */
    // uint32_t            length;   /* number of octets needed for prefix */
    uint32_t addr_len;
    char ip_addr[256];

    plen = get_uint8_t(tvb, offset);

    //tvb_get_ipv6_addr_with_prefix_len(tvb, offset + 1, &addr, plen);
    //int tvb_get_ipv6_addr_with_prefix_len(tvbuff_t *tvb, int offset, ws_in6_addr *addr, guint32 prefix_len)
    

    if (plen > 128)
        return -1;

    addr_len = (plen + 7) / 8;

    if (out == NULL)
        return addr_len + 1;

    decode_ipv4_ipv6(tvb, tvb_len, offset + 1, plen, ip_addr, 0);
    // snprintf(pinfo->bgp_update_network_layer_rechability + strlen(pinfo->bgp_update_network_layer_rechability), 256, "%s,", ip_addr);

    size_t used_len = strlen(out);
    if (out_len - used_len > (strlen(ip_addr) + 1))
        sprintf(out + used_len, "%s,", ip_addr);

    return addr_len + 1;
}

/*
 * Decode an MPLS label stack
 * XXX - We should change *buf to **buf, use wmem_alloc() and drop the buflen
 * argument.
 */
static int
decode_MPLS_stack(const uint8_t *tvb, uint32_t tvb_len, uint32_t offset, struct bgp_info * info)
{
    UNUSED(info);
    uint32_t     label_entry;    /* an MPLS label entry (label + COS field + stack bit   */
    uint32_t        indx;          /* index for the label stack */
    struct dpi_pkt_st pkt;
    pkt.payload = tvb;
    pkt.payload_len = tvb_len;

    indx = offset;
    label_entry = 0x000000;

    //wmem_strbuf_truncate(stack_strbuf, 0);

    while ((label_entry & BGP_MPLS_BOTTOM_L_STACK) == 0) {

        //label_entry = tvb_get_ntoh24(tvb, indx);
        if (dpi_get_be24(&pkt, indx, &label_entry) == -1)
            return -1;

        /* withdrawn routes may contain 0 or 0x800000 in the first label */
        if ((indx == offset) && (label_entry == 0 || label_entry == 0x800000)) {
            //wmem_strbuf_append(stack_strbuf, "0 (withdrawn)");
            return (1);
        }


        indx += 3;
    }

    return((indx - offset) / 3);
}
/*
 * Decode a multiprotocol prefix
 */
static int
decode_prefix_MP(uint16_t afi, uint8_t safi, uint32_t tlen, const uint8_t *tvb, uint32_t tvb_len, uint32_t offset, const char *tag, struct bgp_info *pinfo, uint8_t reach_flag)
{
    UNUSED(tag);
    int total_length = 0;
    uint16_t total_length_16 = 0;
    // uint16_t nlri_type = 0;
    uint8_t tot_flow_len = 0;
    uint16_t len_16 = 0;
    uint8_t offset_len = 0;
    uint32_t end = 0;
    uint32_t plen;
    uint8_t tplen = 0;
    uint32_t labnum;
    int32_t length;
    char ip_addr[256];
    uint16_t rd_type = 0;

    switch (afi) {
    case AFNUM_INET:
        switch (safi) {
        case SAFNUM_UNICAST:
        case SAFNUM_MULCAST:
        case SAFNUM_UNIMULC:
            total_length = decode_prefix4(tvb, tvb_len, offset,
                pinfo->bgp_update_network_layer_rechability, sizeof(pinfo->bgp_update_network_layer_rechability));
            
            break;
        case SAFNUM_MPLS_LABEL:
            plen = get_uint8_t(tvb, offset);

            labnum = decode_MPLS_stack(tvb, tvb_len, offset + 1, pinfo);
            offset += (1 + labnum * 3);
            if (plen <= (labnum * 3 * 8)) {
                return -1;
            }
            plen -= (labnum * 3 * 8);

            if (plen > 32) {
                return -1;
            }
            length = (plen + 7) / 8;
            if (length < 0) {
                return -1;
            }

            decode_ipv4_ipv6(tvb, tvb_len, offset, plen, ip_addr, 1);
            if (reach_flag == 1) {
                snprintf(pinfo->bgp_update_mp_rechability + strlen(pinfo->bgp_update_mp_rechability), 256, "%s,", ip_addr);
            } else {
                snprintf(pinfo->bgp_update_mp_unrechability + strlen(pinfo->bgp_update_mp_unrechability), 256, "%s,", ip_addr);
            }
            
            total_length = (1 + labnum * 3) + length;
            break;
        case SAFNUM_MCAST_VPN:
            length = get_uint8_t(tvb, offset + 1);
            total_length = length + 2;
            if (total_length < 0)
                return -1;
            break;
        case SAFNUM_MDT:
            length = get_uint8_t(tvb, offset + 1);
            total_length = length + 1;
            if (total_length < 0)
                return -1;
            break;
        case SAFNUM_ROUTE_TARGET:
            plen = get_uint8_t(tvb, offset);
            length = (plen + 7) / 8;
            total_length = 1 + length;
            break;
        case SAFNUM_ENCAPSULATION:

            total_length = 5; /* length(1 octet) + address(4 octets) */
            break;
        case SAFNUM_TUNNEL:
            plen = get_uint8_t(tvb, offset);
            plen -= 16;
            length = (plen + 7) / 8;
            total_length = 1 + 2 + length; /* length field + Tunnel Id + IPv4 len */
            break;
        case SAFNUM_LAB_VPNUNICAST:
        case SAFNUM_LAB_VPNMULCAST:
        case SAFNUM_LAB_VPNUNIMULC:
            plen = get_uint8_t(tvb, offset);
            labnum = decode_MPLS_stack(tvb, tvb_len, offset + 1, pinfo);
            offset += (1 + labnum * 3);
            if (plen <= (labnum * 3 * 8)) {
                return -1;
            }
            plen -= (labnum * 3 * 8);

            if (plen < 8 * 8) {
                return -1;
            }
            plen -= 8 * 8;

            length = (plen + 7) / 8;
            total_length = (1 + labnum * 3 + 8) + length;
            break;
        case SAFNUM_FSPEC_RULE:
        case SAFNUM_FSPEC_VPN_RULE:

            if (afi != AFNUM_INET && afi != AFNUM_INET6) {
                return(-1);
            }

            tot_flow_len = get_uint8_t(tvb, offset);
            /* if nlri length is greater than 240 bytes, it is encoded over 2 bytes */
            /* with most significant nibble all in one. 240 is encoded 0xf0f0, 241 0xf0f1 */
            /* max possible value value is 4095 Oxffff */

            if (tot_flow_len >= 240)
            {
                len_16 = get_uint16_ntohs(tvb, offset);
                tot_flow_len = len_16 & 0x0FFF; /* remove most significant nibble */
                offset_len = 2;
            }
            else {
                offset_len = 1;
            }

            total_length = tot_flow_len + offset_len - 1;
            if (total_length < 0)
                return(-1);
            total_length++;
            break;
        default:
            break;
        }
        break;
    case AFNUM_INET6:
        switch (safi) {
        case SAFNUM_UNICAST:
        case SAFNUM_MULCAST:
        case SAFNUM_UNIMULC:
            /* parse each prefix */

            end = offset + tlen;
            if (detect_add_path_prefix6(tvb, offset, end)) {
                /* IPv4 prefixes with Path Id */
                total_length = decode_path_prefix6(tvb, tvb_len, offset, 
                        pinfo->bgp_update_network_layer_rechability, sizeof(pinfo->bgp_update_network_layer_rechability));
            }
            else {
                total_length = decode_prefix6(tvb, tvb_len, offset, 0,
                    pinfo->bgp_update_network_layer_rechability, sizeof(pinfo->bgp_update_network_layer_rechability));
            }
            if (total_length < 0)
                return -1;
            break;
        case SAFNUM_MPLS_LABEL:
            tplen = get_uint8_t(tvb, offset);
            plen = tplen;
            labnum = decode_MPLS_stack(tvb,tvb_len,  offset + 1, pinfo);

            offset += (1 + labnum * 3);
            if (plen <= (labnum * 3 * 8)) {
                return -1;
            }
            plen -= (labnum * 3 * 8);

            //length = tvb_get_ipv6_addr_with_prefix_len(tvb, offset, &ip6addr, plen);
            length = (plen + 7) / 8;
            if (length < 0) {
                return -1;
            }
            total_length = (1 + labnum * 3) + length;
            break;
        case SAFNUM_ENCAPSULATION:
            /*plen = tvb_get_guint8(tvb, offset);
            if (plen != 128) {
                proto_tree_add_expert_format(tree, pinfo, &ei_bgp_length_invalid, tvb, offset, 1,
                    "%s IPv6 address length %u invalid",
                    tag, plen);
                return -1;
            }
            offset += 1;

            proto_tree_add_item(tree, hf_bgp_endpoint_address_ipv6, tvb, offset, 16, ENC_NA);*/

            total_length = 17; /* length(1 octet) + address(16 octets) */
            break;
        case SAFNUM_TUNNEL:
            tplen = get_uint8_t(tvb, offset);
            plen = tplen;
            if (plen <= 16) {
                return -1;
            }
            //tnl_id = tvb_get_ntohs(tvb, offset + 1);
            offset += 3; /* Length + Tunnel Id */
            plen -= 16; /* 2-octet Identifier */
            //length = tvb_get_ipv6_addr_with_prefix_len(tvb, offset, &ip6addr, plen);
            if (plen > 128)
                return -1;
            length = (plen + 7) / 8;
            if (length < 0) {
                return -1;
            }
            /*set_address(&addr, AT_IPv6, 16, ip6addr.bytes);
            prefix_tree = proto_tree_add_subtree_format(tree, tvb, start_offset,
                (offset + length) - start_offset,
                ett_bgp_prefix, NULL,
                "Tunnel Identifier=0x%x IPv6=%s/%u",
                tnl_id, address_to_str(wmem_packet_scope(), &addr), plen);
            proto_tree_add_item(prefix_tree, hf_bgp_prefix_length, tvb, start_offset, 1, ENC_BIG_ENDIAN);

            proto_tree_add_item(prefix_tree, hf_bgp_mp_nlri_tnl_id, tvb,
                start_offset + 1, 2, ENC_BIG_ENDIAN);
            proto_tree_add_ipv6(prefix_tree, hf_addr6, tvb, offset, length, &ip6addr);*/

            total_length = (1 + 2) + length; /* length field + Tunnel Id + IPv4 len */
            break;
        case SAFNUM_LAB_VPNUNICAST:
        case SAFNUM_LAB_VPNMULCAST:
        case SAFNUM_LAB_VPNUNIMULC:
            tplen = get_uint8_t(tvb, offset);
            plen = tplen;
            //stack_strbuf = wmem_strbuf_new_label(wmem_packet_scope());
            labnum = decode_MPLS_stack(tvb, tvb_len, offset + 1, pinfo);
            offset += (1 + labnum * 3);
            if (plen <= (labnum * 3 * 8)) {
                return -1;
            }
            plen -= (labnum * 3 * 8);
            rd_type = get_uint16_ntohs(tvb, offset);
            if (plen < 8 * 8) {
                return -1;
            }
            plen -= 8 * 8;

            switch (rd_type) {
            case FORMAT_AS2_LOC:
                if (plen > 128)
                    return -1;

                length = (plen + 7) / 8;

                if (length < 0) {
                    return -1;
                }
                total_length = (1 + labnum * 3 + 8) + length;
                break;

            case FORMAT_IP_LOC:
                if (plen > 128)
                    return -1;

                length = (plen + 7) / 8;

                if (length < 0) {
                    return -1;
                }
                total_length = (1 + labnum * 3 + 8) + length;
                break;
            case FORMAT_AS4_LOC:
                if (plen > 128)
                    return -1;

                length = (plen + 7) / 8;

                if (length < 0) {
                    return -1;
                }
                total_length = (1 + labnum * 3 + 8) + length;
                break;
            default:
                return -1;
            }
            break;
        case SAFNUM_FSPEC_RULE:
        case SAFNUM_FSPEC_VPN_RULE:
            return -1;
            /*total_length = decode_flowspec_nlri(tree, tvb, offset, afi, safi, pinfo);
            if (total_length < 0)
                return(-1);
            total_length++;
            break;*/
        default:
            break;
        }        
        break;
    case AFNUM_L2VPN:
    case AFNUM_L2VPN_OLD:
        switch (safi) {
        case SAFNUM_LAB_VPNUNICAST:
        case SAFNUM_LAB_VPNMULCAST:
        case SAFNUM_LAB_VPNUNIMULC:
        case SAFNUM_VPLS:
            len_16 = get_uint16_ntohs(tvb, offset);
            plen = len_16;

            /* FIXME there are subTLVs left to decode ... for now lets omit them */
            total_length = plen + 2;
            break;
        case SAFNUM_EVPN:
            return -1;
        default:
            return -1;
        }
        break;
    case AFNUM_BGP_LS:
        // nlri_type = get_uint16_ntohs(tvb, offset);
        total_length_16 = get_uint16_ntohs(tvb, offset + 2);
        total_length = total_length_16;
        length = total_length;
        total_length += 4;
        break;
    default:
        return -1;
    }

    return total_length;
}

/*
 * Dissect a BGP capability.
 */
static int dissect_bgp_capability_item(const uint8_t *payload, int offset, bool action, capcability_info *info) {
    if (!payload || !info)
        return offset;

    uint8_t clen, ctype;

    ctype = payload[offset];
    info->cap_type = ctype;
    offset += 1;

    clen = payload[offset];
    info->cap_len = clen;
    offset += 1;

    if (action) {
        info->cap_action = payload[offset];
        offset += 1;
    }

    /* check the capability type */
    switch (ctype) {
        case BGP_CAPABILITY_RESERVED:
            if (clen != 0) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u wrong, must be = 0", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
            }
            offset += clen;
            break;
        case BGP_CAPABILITY_MULTIPROTOCOL:
            if (clen != 4) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u is wrong, must be = 4", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else {
                /* AFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_mp_afi, tvb, offset, 2, ENC_BIG_ENDIAN);
                offset += 2;

                /* Reserved */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_reserved, tvb, offset, 1, ENC_NA);
                offset += 1;

                /* SAFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_mp_safi, tvb, offset, 1, ENC_BIG_ENDIAN);
                offset += 1;
            }
            break;
        case BGP_CAPABILITY_EXTENDED_NEXT_HOP: {
            int eclen = offset + clen;
            while (offset <= eclen - 6) {
                /* AFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_enh_afi, tvb, offset, 2, ENC_BIG_ENDIAN);
                offset += 2;

                /* SAFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_enh_safi, tvb, offset, 2, ENC_BIG_ENDIAN);
                offset += 2;

                /* AFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_enh_nhafi, tvb, offset, 2, ENC_BIG_ENDIAN);
                offset += 2;
            }
            if (offset != eclen) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u is wrong, must be multiple of 6", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, eclen - offset, ENC_NA);
                offset = eclen;
            }
        }
                                               break;
        case BGP_CAPABILITY_GRACEFUL_RESTART:
            if ((clen < 6) && (clen != 2)) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u too short, must be greater than 6", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else {
                int eclen = offset + clen;

                if (clen == 2) {
//                    expert_add_info(pinfo, ti_len, &ei_bgp_cap_gr_helper_mode_only);
                }

                /* Timers */
//                proto_tree_add_bitmask(cap_tree, tvb, offset, hf_bgp_cap_gr_timers, ett_bgp_cap, timer_flags, ENC_BIG_ENDIAN);
                offset += 2;

                /*
                 * what follows is alist of AFI/SAFI/flag triplets
                 * read it until the TLV ends
                 */
                while (offset < eclen) {

                    /* AFI */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_gr_afi, tvb, offset, 2, ENC_BIG_ENDIAN);
                    offset += 2;

                    /* SAFI */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_gr_safi, tvb, offset, 1, ENC_BIG_ENDIAN);
                    offset += 1;

                    /* Flags */
//                    proto_tree_add_bitmask(cap_tree, tvb, offset, hf_bgp_cap_gr_flag, ett_bgp_cap, flags, ENC_BIG_ENDIAN);
                    offset += 1;
                }
            }
            break;
        case BGP_CAPABILITY_4_OCTET_AS_NUMBER:
            if (clen != 4) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u is wrong, must be = 4", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else {
//                proto_tree_add_item(cap_tree, hf_bgp_cap_4as, tvb, offset, 4, ENC_BIG_ENDIAN);
                offset += 4;
            }
            break;
        case BGP_CAPABILITY_DYNAMIC_CAPABILITY:
            if (clen > 0) {
                int eclen = offset + clen;

                while (offset < eclen) {
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_dc, tvb, offset, 1, ENC_BIG_ENDIAN);
                    offset += 1;
                }
            }
            break;
        case BGP_CAPABILITY_ADDITIONAL_PATHS:
            if (clen % 4 != 0) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u is wrong, must be multiple of  4", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else { /* AFI SAFI Send-receive*/
                int eclen = offset + clen;

                while (offset < eclen) {
                    /* AFI */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_ap_afi, tvb, offset, 2, ENC_BIG_ENDIAN);
                    offset += 2;

                    /* SAFI */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_ap_safi, tvb, offset, 1, ENC_BIG_ENDIAN);
                    offset += 1;

                    /* Send-Receive */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_ap_sendreceive, tvb, offset, 1, ENC_BIG_ENDIAN);
                    offset += 1;
                }
            }
            break;

        case BGP_CAPABILITY_FQDN: 
        {
            uint8_t hostname_len, domain_name_len;

//            proto_tree_add_item(cap_tree, hf_bgp_cap_fqdn_hostname_len, tvb, offset, 1, ENC_NA);
            hostname_len = payload[offset];
            offset += 1;

//            proto_tree_add_item(cap_tree, hf_bgp_cap_fqdn_hostname, tvb, offset, hostname_len, ENC_ASCII | ENC_NA);
            offset += hostname_len;

//            proto_tree_add_item(cap_tree, hf_bgp_cap_fqdn_domain_name_len, tvb, offset, 1, ENC_NA);
            domain_name_len = payload[offset];
            offset += 1;

//            proto_tree_add_item(cap_tree, hf_bgp_cap_fqdn_domain_name, tvb, offset, domain_name_len, ENC_ASCII | ENC_NA);
            offset += domain_name_len;

            break;
        }

        case BGP_CAPABILITY_ENHANCED_ROUTE_REFRESH:
        case BGP_CAPABILITY_ROUTE_REFRESH_CISCO:
        case BGP_CAPABILITY_ROUTE_REFRESH:
        case BGP_CAPABILITY_CP_ORF:
            if (clen != 0) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u wrong, must be = 0", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
            }
            offset += clen;
            break;
        case BGP_CAPABILITY_ORF_CISCO:
        case BGP_CAPABILITY_COOPERATIVE_ROUTE_FILTERING:
            if (clen < 6) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u too short, must be greater than 6", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else {
                uint8_t orfnum;       /* number of ORFs */
                int i;
                /* AFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_orf_afi, tvb, offset, 2, ENC_BIG_ENDIAN);
                offset += 2;

                /* Reserved */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_reserved, tvb, offset, 1, ENC_NA);
                offset += 1;

                /* SAFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_orf_safi, tvb, offset, 1, ENC_BIG_ENDIAN);
                offset += 1;

                /* Number of ORFs */
                orfnum = payload[offset];
//                proto_tree_add_item(cap_tree, hf_bgp_cap_orf_number, tvb, offset, 1, ENC_BIG_ENDIAN);
                offset += 1;
                for (i = 0; i < orfnum; i++) {
                    /* ORF Type */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_orf_type, tvb, offset, 1, ENC_BIG_ENDIAN);
                    offset += 1;

                    /* Send/Receive */
//                    proto_tree_add_item(cap_tree, hf_bgp_cap_orf_sendreceive, tvb, offset, 1, ENC_BIG_ENDIAN);
                    offset += 1;
                }
            }

            break;
        case BGP_CAPABILITY_MULTISESSION_CISCO:
            if (clen < 1) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u too short, must be greater than 1", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else {
//                proto_tree_add_item(cap_tree, hf_bgp_cap_multisession_flags, tvb, offset, 1, ENC_BIG_ENDIAN);
                offset += 1;
            }

            break;
        case BGP_CAPABILITY_BGPSEC:
            if (clen != 3) {
//                expert_add_info_format(pinfo, ti_len, &ei_bgp_cap_len_bad, "Capability length %u is wrong, must be = 3", clen);
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
                offset += clen;
            }
            else {

                /* BGPsec Flags */
//                proto_tree_add_bitmask(cap_tree, tvb, offset, hf_bgp_cap_bgpsec_flags, ett_bgp_cap, bgpsec_flags, ENC_BIG_ENDIAN);
                offset += 1;

                /* BGPsec AFI */
//                proto_tree_add_item(cap_tree, hf_bgp_cap_bgpsec_afi, tvb, offset, 2, ENC_BIG_ENDIAN);
                offset += 2;
            }

            break;
            /* unknown capability */
        default:
            if (clen != 0) {
//                proto_tree_add_item(cap_tree, hf_bgp_cap_unknown, tvb, offset, clen, ENC_NA);
            }
            offset += clen;
            break;
    }

    return offset;
}

static void
dissect_bgp_open(const uint8_t *payload, const uint32_t payload_len, struct bgp_info *info)
{
    UNUSED(payload_len);
    uint32_t offset = 0;
    uint8_t opt_len = 0;
    uint32_t oend = 0;
    uint8_t ptype = 0, plen = 0;
    uint32_t     cend;      /* capabilities end      */
    int        len_tmp;

    offset = BGP_MARKER_SIZE + 2 + 1;
    info->bgp_open_version = get_uint8_t(payload, offset);
    offset += 1;
    info->bgp_open_myas = get_uint16_ntohs(payload, offset);
    offset += 2;
    info->bgp_open_holdtime = get_uint16_ntohs(payload, offset);
    offset += 2;
    snprintf(info->bgp_open_identifier, sizeof(info->bgp_open_identifier), "%u.%u.%u.%u", payload[offset], payload[offset + 1], payload[offset + 2], payload[offset + 3]);
    offset += 4;
    opt_len = get_uint8_t(payload, offset);
    info->bgp_open_opt_len = opt_len;
    offset += 1;

    if (opt_len > 0) {
        oend = offset + opt_len;

        while (offset < oend) {
            ptype = get_uint8_t(payload, offset);
            info->bgp_open_param_type = ptype;
            offset += 1;
            plen = get_uint8_t(payload, offset);
            offset += 1;

            switch (ptype)
            {
            case BGP_OPTION_AUTHENTICATION:
                info->bgp_open_ah_code = ptype;
                snprintf(info->bgp_open_ah_data, plen + 1, "%s", payload + offset);
                offset += plen;
                break;
            case BGP_OPTION_CAPABILITY:
                len_tmp = offset;
                cend = (uint32_t)len_tmp + plen;


                /* step through all of the capabilities */
                while (len_tmp < (int)cend) {
                    len_tmp = dissect_bgp_capability_item(payload, len_tmp, false, &info->cap_info[info->cap_size++]);
                }

                offset = len_tmp;
                break;
            default:
                break;
            }
        }
    }
}


/*
 * Heuristic for auto-detection of ASN length 2 or 4 bytes
 */

static int
heuristic_as2_or_4_from_as_path(const uint8_t *tvb, int32_t as_path_offset, int32_t end_attr_offset, uint8_t bgpa_type, int32_t *number_as_segment)
{
    int32_t counter_as_segment = 0;
    int32_t offset_check = 0;
    uint8_t assumed_as_len = 0;
    int32_t asn_is_null = 0;
    int32_t j = 0;
    int32_t k = 0;
    int32_t k_save = 0;
    uint8_t next_type = 0;
    uint8_t length = 0;
    /* Heuristic is done in two phases
     * First we try to identify the as length (2 or 4 bytes)
     * then we do check that our assumption is ok
     * recalculating the offset and checking we end up with the right result
    * k is used to navigate into the AS_PATH */
    k = as_path_offset;
    /* case of AS_PATH type being explicitly 4 bytes ASN */
    if (bgpa_type == BGPTYPE_AS4_PATH) {
        /* We calculate numbers of segments and return the as length */
        assumed_as_len = 4;
        while (k < end_attr_offset)
        {
            /* we skip segment type and point to length */
            k++;
            length = get_uint8_t(tvb, k);
            /* length read let's move to first ASN */
            k++;
            /* we move to the next segment */
            k = k + (length*assumed_as_len);
            counter_as_segment++;
        }
        *number_as_segment = counter_as_segment;
        return(4);
    }
    /* case of user specified ASN length */
    if (bgp_asn_len != 0) {
        /* We calculate numbers of segments and return the as length */
        assumed_as_len = bgp_asn_len;
        while (k < end_attr_offset)
        {
            /* we skip segment type and point to length */
            k++;
            length = get_uint8_t(tvb, k);
            /* length read let's move to first ASN */
            k++;
            /* we move to the next segment */
            k = k + (length*assumed_as_len);
            /* if I am not facing the last segment k need to point to next length */
            counter_as_segment++;
        }
        *number_as_segment = counter_as_segment;
        return(bgp_asn_len);
    }
    /* case of a empty path attribute */
    if (as_path_offset == end_attr_offset)
    {
        *number_as_segment = 0;
        return(bgp_asn_len);
    }
    /* case of we run the heuristic to find the as length */
    k_save = k;
    /* we do run the heuristic on first segment and look at next segment if it exists */
    k++;
    length = get_uint8_t(tvb, k++);
    /* let's do some checking with an as length 2 bytes */
    offset_check = k + 2 * length;
    next_type = get_uint8_t(tvb, offset_check);
    /* we do have one segment made of 2 bytes ASN we do reach the end of the attribute taking
     * 2 bytes ASN for our calculation */
    if (offset_check == end_attr_offset)
        assumed_as_len = 2;
    /* else we do check if we see a valid AS segment type after (length * AS 2 bytes) */
    else if (next_type == AS_SET ||
        next_type == AS_SEQUENCE ||
        next_type == AS_CONFED_SEQUENCE ||
        next_type == AS_CONFED_SET) {
        /* that's a good sign to assume ASN 2 bytes let's check that 2 first bytes of each ASN doesn't eq 0 to confirm */
        for (j = 0; j < length && !asn_is_null; j++) {
            if (get_uint16_ntohs(tvb, k + (2 * j)) == 0) {
                asn_is_null = 1;
            }
        }
        if (asn_is_null == 0)
            assumed_as_len = 2;
        else
            assumed_as_len = 4;
    }
    else
        /* we didn't find a valid AS segment type in the next coming segment assuming 2 bytes ASN */
        assumed_as_len = 4;
    /* now that we have our assumed as length let's check we can calculate the attribute length properly */
    k = k_save;
    while (k < end_attr_offset)
    {
        /* we skip the AS type */
        k++;
        /* we get the length of the AS segment */
        length = get_uint8_t(tvb, k);
        /* let's point to the fist byte of the AS segment */
        k++;
        /* we move to the next segment */
        k = k + (length*assumed_as_len);
        counter_as_segment++;
    }
    if (k == end_attr_offset) {
        /* success */
        *number_as_segment = counter_as_segment;
        return(assumed_as_len);
    }
    else
        /* we are in trouble */
        return(-1);
}

/*
 * Dissect BGP update extended communities
 */
static int dissect_bgp_update_ext_com(const uint8_t *payload, uint16_t tlen, int offset, struct bgp_info *info) {

    int             end = 0;
    uint8_t         com_type_high_byte;
    uint8_t         com_stype_low_byte;
    // uint32_t        encaps_tunnel_type;

    end = offset + tlen;

    while (offset < end) {
        com_type_high_byte = payload[offset]; /* high community type octet */
        com_stype_low_byte = payload[offset + 1]; /* sub type low community type octet */

        info->bgp_ext_com_type_high[info->bgp_ext_com_type_size++] = com_type_high_byte;
        
        /* In the switch(), handlers of individual types and subtypes should
         * add and dissect the remaining 7 octets. Dissectors should use the
         * proto_item_set_text() on the community_item to set the community
         * name in the displayed label as specifically as possible, and
         * proto_item_append_text() to add reasonable details.
         *
         * The intended text label of the community_item for each extended
         * community attribute is:
         *
         * Community Name: Values [General Community Type Name]
         *
         * For example:
         * Route Target: 1:1 [Transitive 2-Octet AS-Specific]
         * Unknown subtype 0x01: 0x8081 0x0000 0x2800 [Non-Transitive Opaque]
         * Unknown type 0x88 subtype 0x00: 0x0000 0x0000 0x0000 [Unknown community]
         *
         * The [] part with general community name is added at the end
         * of the switch().
         *
         * The first option (Route Target) shows a fully recognized and
         * dissected extended community. Note that the line immediately calls
         * the community by its most specific known type (Route Target), while
         * the general type is shown in the brackets. The second option shows a
         * community whose Type is recognized (Non-Transitive Opaque) but whose
         * Subtype is not known. The third option shows an unrecognized
         * extended community.
         *
         * Printing out the community raw value as 3 short ints is intentional:
         * With an unknown community, we cannot assume any particular internal
         * value format, and dumping the value in short ints provides for easy
         * readability.
         */

        switch (com_type_high_byte) {
        case BGP_EXT_COM_TYPE_HIGH_TR_AS2: /* Transitive Two-Octet AS-Specific Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_as2, tvb, offset + 1, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as2, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an4, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

            break;

        case BGP_EXT_COM_TYPE_HIGH_NTR_AS2: /* Non-Transitive Two-Octet AS-Specific Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_ntr_as2, tvb, offset + 1, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as2, tvb, offset + 2, 2, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_AS2_LBW:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_link_bw, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;

            default:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an4, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_IP4: /* Transitive IPv4-Address-specific Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_IP4, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_IP4_OSPF_RID:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_ospf_rid, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
                break;

            default:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_IP4, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an2, tvb, offset + 6, 2, ENC_BIG_ENDIAN);
                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_NTR_IP4: /* Non-Transitive IPv4-Address-specific Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_ntr_IP4, tvb, offset + 1, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_IP4, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an2, tvb, offset + 6, 2, ENC_BIG_ENDIAN);

            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_AS4: /* Transitive Four-Octet AS-Specific Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_as4, tvb, offset + 1, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as4, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an2, tvb, offset + 6, 2, ENC_BIG_ENDIAN);

            break;

        case BGP_EXT_COM_TYPE_HIGH_NTR_AS4: /* Non-Transitive Four-Octet AS-Specific Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_ntr_as4, tvb, offset + 1, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as4, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an2, tvb, offset + 6, 2, ENC_BIG_ENDIAN);

            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_OPAQUE: /* Transitive Opaque Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_opaque, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_OPA_COST:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_cost_poi, tvb, offset + 2, 1, ENC_BIG_ENDIAN);
//                proto_tree_add_item(cost_com_cid_tree, hf_bgp_ext_com_cost_cid_rep, tvb, offset + 3, 1, ENC_BIG_ENDIAN);
//                cost_com_item = proto_tree_add_item(community_tree, hf_bgp_ext_com_cost_cost, tvb, offset + 4, 4, ENC_BIG_ENDIAN);
            }
            break;

            case BGP_EXT_COM_STYPE_OPA_OSPF_RT:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_ospf_rt_area, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_ospf_rt_type, tvb, offset + 6, 1, ENC_BIG_ENDIAN);
//                proto_tree_add_item(ospf_rt_opt_tree, hf_bgp_ext_com_value_ospf_rt_options_mt, tvb, offset + 7, 1, ENC_BIG_ENDIAN);
            }
            break;

            case BGP_EXT_COM_STYPE_OPA_ENCAP:
                /* Community octets 2 through 5 are reserved and carry no useful value according to RFC 5512. */
//                proto_tree_add_item_ret_uint(community_tree, hf_bgp_ext_com_tunnel_type, tvb, offset + 6, 2, ENC_BIG_ENDIAN, &encaps_tunnel_type);
//                save_path_attr_encaps_tunnel_type(pinfo, encaps_tunnel_type);

                break;

            case BGP_EXT_COM_STYPE_OPA_COLOR:
            case BGP_EXT_COM_STYPE_OPA_DGTW:
            default:
                /* The particular Opaque subtype is unknown or the
                 * dissector is not written yet. We will dump the
                 * entire community value in 2-byte short words.
                 */
//                proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
//                    tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
//                    tvb_get_ntohs(tvb, offset + 2),
//                    tvb_get_ntohs(tvb, offset + 4),
//                    tvb_get_ntohs(tvb, offset + 6));

                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_NTR_OPAQUE: /* Non-Transitive Opaque Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_ntr_opaque, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_OPA_COST:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_cost_poi, tvb, offset + 2, 1, ENC_BIG_ENDIAN);
//                proto_tree_add_item(cost_com_cid_tree, hf_bgp_ext_com_cost_cid_rep, tvb, offset + 3, 1, ENC_BIG_ENDIAN);

            }
            break;

            default:
                /* The particular Opaque subtype is unknown or the
                 * dissector is not written yet. We will dump the
                 * entire community value in 2-byte short words.
                 */
//                proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
//                    tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
//                    tvb_get_ntohs(tvb, offset + 2),
//                    tvb_get_ntohs(tvb, offset + 4),
//                    tvb_get_ntohs(tvb, offset + 6));

                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_QOS: /* QoS Marking [Thomas_Martin_Knoll] */
        case BGP_EXT_COM_TYPE_HIGH_NTR_QOS: /* QoS Marking [Thomas_Martin_Knoll] */
        {
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_qos_set_number, tvb, offset + 2, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_qos_tech_type, tvb, offset + 3, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_qos_marking_o, tvb, offset + 4, 2, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_qos_marking_a, tvb, offset + 6, 1, ENC_BIG_ENDIAN);
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_qos_default_to_zero, tvb, offset + 7, 1, ENC_BIG_ENDIAN);
        }
        break;

        case BGP_EXT_COM_TYPE_HIGH_TR_COS: /* CoS Capability [Thomas_Martin_Knoll] */
        {
            int i;

            for (i = 1; i < 8; i++) {

//                proto_tree_add_bitmask(community_tree, tvb, offset + i, hf_bgp_ext_com_cos_flags,
//                    ett_bgp_ext_com_flags, cos_flags, ENC_BIG_ENDIAN);
            }
        }
        break;

        case BGP_EXT_COM_TYPE_HIGH_TR_EVPN: /* EVPN (Sub-Types are defined in the "EVPN Extended Community Sub-Types" registry) */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_evpn, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_EVPN_MMAC:
            {
//                proto_tree_add_item(evpn_mmac_flag_tree, hf_bgp_ext_com_evpn_mmac_flag_sticky, tvb, offset + 2, 1, ENC_BIG_ENDIAN);
                /* Octet at offset 3 is reserved per RFC 7432 Section 7.7 */
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_evpn_mmac_seq, tvb, offset + 4, 4, ENC_BIG_ENDIAN);
            
            }
            break;

            case BGP_EXT_COM_STYPE_EVPN_LABEL:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_l2_esi_label_flag, tvb, offset + 2, 1, ENC_BIG_ENDIAN);
                /* Octets at offsets 3 and 4 are reserved perf RFC 7432 Section 7.5 */
//                proto_tree_add_item(community_tree, hf_bgp_update_mpls_label_value, tvb, offset + 5, 3, ENC_BIG_ENDIAN);

            }
            break;

            case BGP_EXT_COM_STYPE_EVPN_IMP:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_evpn_esirt, tvb, offset + 2, 6, ENC_NA);

                break;

            case BGP_EXT_COM_STYPE_EVPN_ROUTERMAC:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_evpn_routermac, tvb, offset + 2, 6, ENC_NA);

                break;

            case BGP_EXT_COM_STYPE_EVPN_L2ATTR:
            {

            }
            break;

            case BGP_EXT_COM_STYPE_EVPN_ETREE:
            {
//                proto_tree_add_bitmask(community_tree, tvb, offset + 2, hf_bgp_ext_com_evpn_etree_flags,
//                    ett_bgp_ext_com_evpn_etree_flags, etree_flags, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_evpn_etree_reserved, tvb, offset + 3, 2, ENC_NA);

//                proto_tree_add_item(community_tree, hf_bgp_update_mpls_label_value_20bits, tvb, offset + 5, 3, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_update_mpls_traffic_class, tvb, offset + 5, 3, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_update_mpls_bottom_stack, tvb, offset + 5, 3, ENC_BIG_ENDIAN);
            }
            break;

            default:
                /* The particular EVPN subtype is unknown or the
                 * dissector is not written yet. We will dump the
                 * entire community value in 2-byte short words.
                 */
//                proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
//                    tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
//                    tvb_get_ntohs(tvb, offset + 2),
//                    tvb_get_ntohs(tvb, offset + 4),
//                    tvb_get_ntohs(tvb, offset + 6));

                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_EXP: /* Generic Transitive Experimental Extended Community */
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_exp, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_EXP_OSPF_RT:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_ospf_rt_area, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_ospf_rt_type, tvb, offset + 6, 1, ENC_BIG_ENDIAN);

            }
            break;

            case BGP_EXT_COM_STYPE_EXP_OSPF_RID:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_ospf_rid, tvb, offset + 2, 4, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_OSPF_DID:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_as2, tvb, offset + 1, 1, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as2, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an4, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_F_TR:  /* Flow spec traffic-rate [RFC5575] */
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as2, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
                /* remaining 4 bytes gives traffic rate in IEEE floating point */
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_flow_rate_float, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_F_TA:  /* Flow spec traffic-action [RFC5575] */
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_flow_act_allset, tvb, offset + 2, 5, ENC_NA);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_flow_act_samp_act, tvb, offset + 7, 1, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_flow_act_term_act, tvb, offset + 7, 1, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_F_RED: /* Flow spec redirect [RFC5575] */
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as2, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an4, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_F_RMARK: /* Flow spec traffic-remarking [RFC5575] */
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_fs_remark, tvb, offset + 7, 1, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_L2:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_l2_encaps, tvb, offset + 2, 1, ENC_BIG_ENDIAN);

//                proto_tree_add_bitmask(community_tree, tvb, offset + 3, hf_bgp_ext_com_l2_c_flags, ett_bgp_ext_com_l2_flags, com_l2_flags, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_l2_mtu, tvb, offset + 4, 2, ENC_BIG_ENDIAN);
            }
            break;

            case BGP_EXT_COM_STYPE_EXP_ETREE:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_etree_root_vlan, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_etree_leaf_vlan, tvb, offset + 4, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_bitmask(community_tree, tvb, offset + 6, hf_bgp_ext_com_etree_flags, ett_bgp_ext_com_etree_flags, com_etree_flags, ENC_BIG_ENDIAN);
            }
            break;

            default:
                /* The particular Experimental subtype is unknown or
                 * the dissector is not written yet. We will dump the
                 * entire community value in 2-byte short words.
                 */
                //proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
                //    tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
                //    tvb_get_ntohs(tvb, offset + 2),
                //    tvb_get_ntohs(tvb, offset + 4),
                //    tvb_get_ntohs(tvb, offset + 6));


                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_EXP_2:
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_exp_2, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_EXP_2_FLOW_RED:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_IP4, tvb, offset + 2, 4, ENC_NA);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an2, tvb, offset + 6, 2, ENC_BIG_ENDIAN);
            }
            break;

            default:
                /* The particular Experimental subtype is unknown or
                 * the dissector is not written yet. We will dump the
                 * entire community value in 2-byte short words.
                 */
//                proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
//                    tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
//                    tvb_get_ntohs(tvb, offset + 2),
//                    tvb_get_ntohs(tvb, offset + 4),
//                    tvb_get_ntohs(tvb, offset + 6));

                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_EXP_3:
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_exp_3, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_EXP_3_FLOW_RED:
            {
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_as4, tvb, offset + 2, 4, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_value_an2, tvb, offset + 6, 2, ENC_BIG_ENDIAN);
            }
            break;

            default:
                /* The particular Experimental subtype is unknown or
                 * the dissector is not written yet. We will dump the
                 * entire community value in 2-byte short words.
                 */
//                proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
//                    tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
//                    tvb_get_ntohs(tvb, offset + 2),
//                    tvb_get_ntohs(tvb, offset + 4),
//                    tvb_get_ntohs(tvb, offset + 6));

                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_EXP_EIGRP:
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_tr_exp_eigrp, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

            switch (com_stype_low_byte) {
            case BGP_EXT_COM_STYPE_EXP_EIGRP_FT:
            {
//                proto_tree_add_item(eigrp_flags_tree, hf_bgp_ext_com_eigrp_flags_rt, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_rtag, tvb, offset + 4, 4, ENC_BIG_ENDIAN);
            }
            break;

            case BGP_EXT_COM_STYPE_EXP_EIGRP_AD:
            {
//                uint32_t raw_value;

//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_asn, tvb, offset + 2, 2, ENC_BIG_ENDIAN);

//                raw_value = get_uint32_ntohl(payload, offset + 4);
//                proto_tree_add_uint_format_value(community_tree, hf_bgp_ext_com_eigrp_delay,
//                    tvb, offset + 4, 4, raw_value, "%u (%u usec)", raw_value, raw_value * 10 / 256);

            }
            break;

            case BGP_EXT_COM_STYPE_EXP_EIGRP_RHB:
            {
//                uint32_t raw_value;

//                raw_value = payload[offset + 2];
//                proto_tree_add_uint_format_value(community_tree, hf_bgp_ext_com_eigrp_rly,
//                    tvb, offset + 2, 1, raw_value, "%u (%u%%)", raw_value, (raw_value * 100) / 255);

//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_hops, tvb, offset + 3, 1, ENC_BIG_ENDIAN);

//                raw_value = get_uint32_ntohl(payload, offset + 4);
//                proto_tree_add_uint_format_value(community_tree, hf_bgp_ext_com_eigrp_bw,
//                    tvb, offset + 4, 4, raw_value, "%u (%u Kbps)", raw_value, raw_value ? (2560000000U / raw_value) : 0);
            }
            break;

            case BGP_EXT_COM_STYPE_EXP_EIGRP_LM:
            {
//                uint32_t raw_value;

//                raw_value = payload[offset + 3];
//                proto_tree_add_uint_format_value(community_tree, hf_bgp_ext_com_eigrp_load,
//                    tvb, offset + 3, 1, raw_value, "%u (%u%%)", raw_value, (raw_value * 100) / 255);

//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_mtu, tvb, offset + 4, 4, ENC_BIG_ENDIAN);
            }
            break;

            case BGP_EXT_COM_STYPE_EXP_EIGRP_EAR:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_e_asn, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_e_rid, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_EIGRP_EPM:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_e_pid, tvb, offset + 2, 2, ENC_BIG_ENDIAN);
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_e_m, tvb, offset + 4, 4, ENC_BIG_ENDIAN);

                break;

            case BGP_EXT_COM_STYPE_EXP_EIGRP_RID:
//                proto_tree_add_item(community_tree, hf_bgp_ext_com_eigrp_rid, tvb, offset + 4, 4, ENC_NA);
                break;
            }
            break;

        case BGP_EXT_COM_TYPE_HIGH_TR_FLOW: /* Flow spec redirect/mirror to IP next-hop [draft-simpson-idr-flowspec-redirect] */
        default:
//            proto_tree_add_item(community_tree, hf_bgp_ext_com_stype_low_unknown, tvb, offset + 1, 1, ENC_BIG_ENDIAN);

//            proto_tree_add_uint64_format_value(community_tree, hf_bgp_ext_com_value_raw, tvb, offset + 2, 6,
//                tvb_get_ntoh48(tvb, offset + 2), "0x%04x 0x%04x 0x%04x",
//                tvb_get_ntohs(tvb, offset + 2),
//                tvb_get_ntohs(tvb, offset + 4),
//               tvb_get_ntohs(tvb, offset + 6));


            break;
        }

        offset = offset + 8;
    }
    return(0);

}

/*
 * Dissect BGP path attributes
 *
 */
static void
dissect_bgp_path_attr(const uint8_t *payload, const uint32_t payload_len, uint16_t path_attr_len, uint32_t _offset, struct bgp_info *info)
{
    uint32_t offset = 0;
    uint8_t bgpa_flags = 0;
    uint8_t bgpa_type = 0;
    uint16_t alen = 0, aoff = 0, tlen = 0;
    uint16_t asn_len = 0;
    uint8_t origin = 0;
    uint16_t i = 0;
    uint16_t q = 0, end = 0; //tmp
    uint16_t community_as = 0, community_value = 0;
    char t_clusterid[32];
    uint8_t nexthop_len = 0;
    uint16_t af = 0;
    uint8_t saf = 0;
    uint16_t aoff_save;
    uint16_t off = 0;
    uint8_t snpa = 0;
    int32_t advance = 0;
    int32_t number_as_segment = 0;
    int32_t k;
    // uint8_t type;
    uint8_t length, j;

    //�������ʼ��Ϊ0�� ��ֹ���ѭ������������һֱ׷��
    memset(info->bgp_update_communities, 0, sizeof(info->bgp_update_communities));
    memset(info->bgp_update_cluster_list, 0, sizeof(info->bgp_update_cluster_list));

    offset = _offset;

    while (i < path_attr_len) {
        bgpa_flags    = get_uint8_t(payload, offset + i);
        bgpa_type    = get_uint8_t(payload, offset + i + 1);

        /* check for the Extended Length bit */
        if (bgpa_flags & BGP_ATTR_FLAG_EXTENDED_LENGTH) {
            alen = get_uint16_ntohs(payload, offset + i + BGP_SIZE_OF_PATH_ATTRIBUTE);
            aoff = BGP_SIZE_OF_PATH_ATTRIBUTE + 2;
        }
        else {
            alen = get_uint8_t(payload, offset + i + BGP_SIZE_OF_PATH_ATTRIBUTE);
            aoff = BGP_SIZE_OF_PATH_ATTRIBUTE + 1;
        }
        tlen = alen;

        /* Path Attribute Type */
        switch (bgpa_type) {
        case BGPTYPE_ORIGIN:
            if (tlen == 1) {
                origin = get_uint8_t(payload, offset + i + aoff);
                info->bgp_update_origin = origin;
                info->bgp_update_origin_str = val_to_string(origin, bgpattr_origin);
            }
            break;
        case BGPTYPE_AS_PATH:
        case BGPTYPE_AS4_PATH:
            /* Apply heuristic to guess if we are facing 2 or 4 bytes ASN
                  (o + i + aoff) =
                  (o + current attribute + aoff bytes to first tuple)
                  heuristic also tell us how many AS segments we have */
            asn_len = heuristic_as2_or_4_from_as_path(payload, offset + i + aoff, offset + i + aoff + tlen,
                bgpa_type, &number_as_segment);
            info->bgp_update_number_as_segment = number_as_segment;

            q = offset + i + aoff;
            for (k = 0; k < number_as_segment; k++) {
                // type = payload[q];
                length = payload[q + 1];

                q += 2;
                for (j = 0; j < length; j++) {
                    if (asn_len == 2) {
                        info->bgp_update_as_path[info->bgp_update_asn_num++] = get_uint16_ntohs(payload, q);
                    }
                    else if (asn_len == 4) {
                        info->bgp_update_as_path[info->bgp_update_asn_num++] = get_uint32_ntohl(payload, q);
                    }
                    q += asn_len;
                }
            }

            break;
        case BGPTYPE_NEXT_HOP:
            if (tlen == 4) {
                snprintf(info->bgp_update_next_hop, sizeof(info->bgp_update_next_hop), "%u.%u.%u.%u",
                    payload[offset + i + aoff], payload[offset + i + aoff + 1], payload[offset + i + aoff + 2], payload[offset + i + aoff + 3]);
            }
            break;
        case BGPTYPE_MULTI_EXIT_DISC:
            if (tlen == 4) {
                info->bgp_update_med = get_uint32_ntohl(payload, offset + i + aoff);
            }
            break;
        case BGPTYPE_LOCAL_PREF:
            if (tlen == 4) {
                info->bgp_update_local_pref = get_uint32_ntohl(payload, offset + i + aoff);
            }
            break;
        case BGPTYPE_AGGREGATOR:
            if (tlen != 6 && tlen != 8) {
                break;
            }
            /* FALL THROUGH */
        case BGPTYPE_AS4_AGGREGATOR:
            if (bgpa_type == BGPTYPE_AS4_AGGREGATOR && tlen != 8) {

            } else {
                asn_len = tlen - 4;
                if (asn_len == 2) {
                    uint16_t aggregator_as = get_uint16_ntohs(payload, offset + i + aoff);
                    info->bgp_update_aggregator_as = aggregator_as;
                } else {
                    uint32_t aggregator_as = get_uint32_ntohl(payload, offset + i + aoff);
                    info->bgp_update_aggregator_as = aggregator_as;
                }
                
                snprintf(info->bgp_update_aggregatorIp, sizeof(info->bgp_update_aggregatorIp), "%u.%u.%u.%u",
                    payload[offset + i + aoff + asn_len], payload[offset + i + aoff + asn_len + 1],
                    payload[offset + i + aoff + asn_len + 2], payload[offset + i + aoff + asn_len + 3]);
            }
            break;
        case BGPTYPE_COMMUNITIES:
            if (tlen % 4 != 0) {
                break;
            }

            /* (o + i + aoff) =
               (o + current attribute + aoff bytes to first tuple) */
            q = offset + i + aoff;
            end = q + tlen;

            while (q < end) {
                uint32_t community = get_uint32_ntohl(payload, q);

                if ((community & 0xFFFF0000) == FOURHEX0 ||
                    (community & 0xFFFF0000) == FOURHEXF) {
                } else {
                    community_as = get_uint16_ntohs(payload, q);
                    community_value = get_uint16_ntohs(payload, q + 2);

                    snprintf(info->bgp_update_communities + strlen(info->bgp_update_communities), 20,
                        "%d:%d ", community_as, community_value);    

                    info->comm_info[info->comm_size].as = community_as;
                    info->comm_info[info->comm_size++].value = community_value;
                }
                q += 4;
            }
#if 0

            /* snarf each community */
            while (q < end) {
                /* check for reserved values */
                guint32 community = tvb_get_ntohl(tvb, q);
                if ((community & 0xFFFF0000) == FOURHEX0 ||
                    (community & 0xFFFF0000) == FOURHEXF) {
                    proto_tree_add_item(communities_tree, hf_bgp_update_path_attribute_community_well_known,
                        tvb, q, 4, ENC_BIG_ENDIAN);
                    proto_item_append_text(ti_pa, "%s ", val_to_str_const(community, community_vals, "Reserved"));
                    proto_item_append_text(ti_communities, "%s ", val_to_str_const(community, community_vals, "Reserved"));
                }
#endif

            break;
        case BGPTYPE_ORIGINATOR_ID:
            if (tlen != 4) {
            }
            else {
                snprintf(info->bgp_update_originator_id, sizeof(info->bgp_update_originator_id), "%u.%u.%u.%u",
                    payload[offset + i + aoff], payload[offset + i + aoff + 1], payload[offset + i + aoff + 2], payload[offset + i + aoff + 3]);
            }
            break;
        case BGPTYPE_MP_REACH_NLRI:
            /*
             * RFC 2545 specifies that there may be more than one
             * address in the MP_REACH_NLRI attribute in section
             * 3, "Constructing the Next Hop field".
             *
             * Yes, RFC 2858 says you can't do that, and, yes, RFC
             * 2858 obsoletes RFC 2283, which says you can do that,
             * but that doesn't mean we shouldn't dissect packets
             * that conform to RFC 2283 but not RFC 2858, as some
             * device on the network might implement the 2283-style
             * BGP extensions rather than RFC 2858-style extensions.
             */
            af = get_uint16_ntohs(payload, offset + i + aoff);
            info->bgp_update_mp_reach_nlri_afi = af;
            saf = get_uint8_t(payload, offset + i + aoff + 2);
            info->bgp_update_mp_reach_nlri_safi = saf;
            nexthop_len = get_uint8_t(payload, offset + i + aoff + 3);
            info->bgp_update_mp_reach_nexthop_len = DPI_MIN(nexthop_len, (uint8_t)sizeof(info->bgp_update_mp_reach_nexthop));

            memcpy(info->bgp_update_mp_reach_nexthop, payload + offset + i + aoff + 3 + 1, info->bgp_update_mp_reach_nexthop_len);

            aoff_save = aoff;
            tlen -= nexthop_len + 4;
            aoff += nexthop_len + 4;

            off = 0;
            snpa = get_uint8_t(payload, offset + i + aoff);
            off++;
            if (snpa) {
                for (/*nothing*/; snpa > 0; snpa--) {
                    uint8_t snpa_length = get_uint8_t(payload, offset + i + aoff + off);

                    off++;
                    off += snpa_length;
                }
            }
            tlen -= off;
            aoff += off;

            info->bgp_update_mp_reach_nlri.length = DPI_MIN((uint16_t)sizeof(info->bgp_update_mp_reach_nlri.nlri), tlen);
            memcpy(info->bgp_update_mp_reach_nlri.nlri, payload + offset + i + aoff, info->bgp_update_mp_reach_nlri.length);

            if ((af == AFNUM_L2VPN_OLD || af == AFNUM_L2VPN) && saf == SAFNUM_EVPN) {
                info->bgp_update_mp_reach_nlri.type = 1;
                info->bgp_update_mp_reach_nlri.evpn.route_type = info->bgp_update_mp_reach_nlri.nlri[0];
                info->bgp_update_mp_reach_nlri.evpn.evpn_length = info->bgp_update_mp_reach_nlri.nlri[1];
                if (info->bgp_update_mp_reach_nlri.evpn.evpn_length > 0) {
                    memcpy(info->bgp_update_mp_reach_nlri.evpn.route_distinguisher, info->bgp_update_mp_reach_nlri.nlri + 2, 8);
                    info->bgp_update_mp_reach_nlri.evpn.esi_type = info->bgp_update_mp_reach_nlri.nlri[10];
                }
            }
            else if (af == AFNUM_BGP_LS) {
                info->bgp_update_mp_reach_nlri.type = 2;
                info->bgp_update_mp_reach_nlri.ls.nlri_type = get_uint16_ntohs(info->bgp_update_mp_reach_nlri.nlri, 0);
                info->bgp_update_mp_reach_nlri.ls.nlri_length = get_uint16_ntohs(info->bgp_update_mp_reach_nlri.nlri, 2);

            }

            if (tlen) {
                if (af != AFNUM_INET && af != AFNUM_INET6 && af != AFNUM_L2VPN && af != AFNUM_BGP_LS) {
                    //proto_tree_add_expert(subtree3, pinfo, &ei_bgp_unknown_afi, tvb, o + i + aoff, tlen);
                }
                else {
                    while (tlen > 0) {
                        //memset(info->bgp_update_network_layer_rechability, 0, sizeof(info->bgp_update_network_layer_rechability));
                        advance = decode_prefix_MP(af, saf, tlen, payload, payload_len, offset + i + aoff, "MP Reach NLRI", info, 1);
                        if (advance < 0)
                            break;
                        
                        tlen -= advance;
                        aoff += advance;
                    }
                    //ȥ���ַ������һ������
                    bgp_del_last_pun(info->bgp_update_network_layer_rechability, ',');
                }
            }
            aoff = aoff_save;
            break;
        case BGPTYPE_MP_UNREACH_NLRI:
            af = get_uint16_ntohs(payload, offset + i + aoff);
            info->bgp_update_mp_unreach_nlri_afi = af;
            saf = get_uint8_t(payload, offset + i + aoff + 2);
            info->bgp_update_mp_unreach_nlri_safi = saf;

            info->bgp_update_mp_unreach_nlri.length = DPI_MIN((uint16_t)sizeof(info->bgp_update_mp_unreach_nlri.nlri), tlen - 3);
            memcpy(info->bgp_update_mp_unreach_nlri.nlri, payload + offset + i + aoff + 3, info->bgp_update_mp_unreach_nlri.length);

            if ((af == AFNUM_L2VPN_OLD || af == AFNUM_L2VPN) && saf == SAFNUM_EVPN) {
                info->bgp_update_mp_unreach_nlri.type = 1;
                info->bgp_update_mp_unreach_nlri.evpn.route_type = info->bgp_update_mp_unreach_nlri.nlri[0];
                info->bgp_update_mp_unreach_nlri.evpn.evpn_length = info->bgp_update_mp_unreach_nlri.nlri[1];
                if (info->bgp_update_mp_unreach_nlri.evpn.evpn_length > 0) {
                    memcpy(info->bgp_update_mp_unreach_nlri.evpn.route_distinguisher, info->bgp_update_mp_unreach_nlri.nlri + 2, 8);
                    info->bgp_update_mp_unreach_nlri.evpn.esi_type = info->bgp_update_mp_unreach_nlri.nlri[10];
                }
            }
            else if (af == AFNUM_BGP_LS) {
                info->bgp_update_mp_unreach_nlri.type = 2;
                info->bgp_update_mp_unreach_nlri.ls.nlri_type = get_uint16_ntohs(info->bgp_update_mp_unreach_nlri.nlri, 0);
                info->bgp_update_mp_unreach_nlri.ls.nlri_length = get_uint16_ntohs(info->bgp_update_mp_unreach_nlri.nlri, 2);

            }

            nexthop_len = get_uint8_t(payload, offset + i + aoff + 3);

            aoff_save = aoff;
            tlen -= 3;
            aoff += 3;
            if (tlen > 0) {
                while (tlen > 0) {
                    advance = decode_prefix_MP(af, saf, tlen, payload, payload_len, offset + i + aoff, "MP Unreach NLRI", info, 0);
                    if (advance < 0)
                        break;
                    tlen -= advance;
                    aoff += advance;
                }
            }
            aoff = aoff_save;
            break;
        case BGPTYPE_CLUSTER_LIST:
            if (tlen % 4 != 0) {
                break;
            }

            /* (o + i + aoff) =
               (o + current attribute + aoff bytes to first tuple) */
            q = offset + i + aoff;
            end = q + tlen;
            /* snarf each cluster identifier */
            while (q < end) {
                snprintf(t_clusterid, sizeof(t_clusterid), "%u.%u.%u.%u", payload[q - 3 + aoff], payload[q - 2 + aoff], payload[q - 1 + aoff], payload[q + aoff]);
                snprintf(info->bgp_update_cluster_list + strlen(info->bgp_update_cluster_list), sizeof(t_clusterid) + 1, "%s,", t_clusterid);
                q += 4;
            }

            //ȥ�����һ������
            int len = 0;
            len = strlen(info->bgp_update_cluster_list);
            if (info->bgp_update_cluster_list[len - 1] == ',')
            {
                info->bgp_update_cluster_list[len - 1] = '\0';
            }

            break;
        case BGPTYPE_EXTENDED_COMMUNITY:
            if (tlen % 8 == 0) {
                dissect_bgp_update_ext_com(payload, tlen, offset + i + aoff, info);
            }
            break;
        default:
            break;
        }
        

        i += alen + aoff;
    }
}

static void
dissect_bgp_update(const uint8_t *payload, const uint32_t payload_len, struct bgp_info *info)
{
    uint32_t off_tmp = 0;
    uint32_t offset = 0;
    uint16_t hlen = 0;
    uint16_t len = 0;
    uint32_t end = 0;
    int i = 0;
    int l = 0;

    hlen = get_uint16_ntohs(payload, BGP_MARKER_SIZE);
    offset = BGP_HEADER_SIZE;

    /* check for withdrawals */
    len = get_uint16_ntohs(payload, offset);
    offset += 2;
    if(len)
    {
        off_tmp = offset;
        end = offset + len;
        if (detect_add_path_prefix4(payload, off_tmp, end))
        {
            while (off_tmp < end)
            {
                i = decode_path_prefix4(payload, payload_len, off_tmp,
                                info->bgp_update_withdrawn_routes, sizeof(info->bgp_update_withdrawn_routes));
                if (i < 0)
                    return;
                off_tmp += i;
            }
        }
        else
        {
            while (off_tmp < end)
            {
                i = decode_prefix4(payload, payload_len, off_tmp,
                                info->bgp_update_withdrawn_routes, sizeof(info->bgp_update_withdrawn_routes));
                if (i < 0)
                    return;
                off_tmp += i;
            }
        }

        bgp_del_last_pun(info->bgp_update_withdrawn_routes, ',');
        info->bgp_update_wr_len = (uint16_t)strlen(info->bgp_update_withdrawn_routes);
    }

    offset += len;

    /* check for advertisements */
    len = get_uint16_ntohs(payload, offset);

    if(offset + len > payload_len)
        return;

    /* path attributes */
    if (len > 0) {
        dissect_bgp_path_attr(payload, payload_len, len, offset + 2, info);
        offset += 2 + len;

        /* NLRI */
        len = hlen - offset;

        info->bgp_update_nlri_length = payload[offset];
        // 可以获取nlri的数据了

    }

//    int l = 0;
    while (offset + 4 <= hlen) {
        uint8_t prefix = payload[offset];
        l += snprintf(info->bgp_update_network_layer_rechability + l, sizeof(info->bgp_update_network_layer_rechability),
                      "%u.%u.%u.%u/%u ", payload[offset+1], payload[offset+2], payload[offset+3], prefix > 24 ? payload[offset+4] : 0, prefix);
        offset += (prefix > 24 ? 5 :  4);
    }

}

static void
dissect_bgp_notification(const uint8_t *payload, const uint32_t payload_len, struct bgp_info *info)
{
    uint32_t offset = 0;
    uint16_t hlen = 0;
    uint32_t clen = 0;
    uint8_t minor_cease = 0;
    uint8_t major_error = 0;

    hlen = get_uint16_ntohs(payload, BGP_MARKER_SIZE);
    if (hlen != payload_len)
        return;

    offset = BGP_MARKER_SIZE + 2 + 1;
    major_error = get_uint8_t(payload, offset);
    info->bgp_notification_mae_code = major_error;
    info->bgp_notification_mae_str = val_to_string(major_error, bgpnotify_major);
    offset += 1;

    offset += 1;

    if (hlen > BGP_MIN_NOTIFICATION_MSG_SIZE) {
        minor_cease = get_uint8_t(payload, offset - 1);
        info->bgp_notification_mie_code = minor_cease;
        clen = get_uint8_t(payload, offset);
        if (hlen - BGP_MIN_NOTIFICATION_MSG_SIZE - 1 == (uint16_t)clen
            && major_error == BGP_MAJOR_ERROR_CEASE
            && (minor_cease == 2 || minor_cease == 4))
        {
            offset += 1;
        
            // info->bgp_notification_err_length = DPI_MIN(clen, sizeof(info->bgp_notification_err_data));
      info->bgp_notification_err_length =(uint16_t) clen > sizeof(info->bgp_notification_err_data) ? sizeof(info->bgp_notification_err_data) -1 : clen;
            memset(info->bgp_notification_err_data, 0, sizeof(info->bgp_notification_err_data));
            memcpy(info->bgp_notification_err_data, payload + offset, info->bgp_notification_err_length);
        }
        else if (major_error == BGP_MAJOR_ERROR_OPEN_MSG && minor_cease == 7) {
            while(offset < hlen && info->cap_size < array_length(info->cap_info))
                offset = dissect_bgp_capability_item(payload, offset, false, &info->cap_info[info->cap_size++]);
        }
        else if (major_error == BGP_MAJOR_ERROR_OPEN_MSG && minor_cease == 2) {
            snprintf(info->bgp_notification_err_data, sizeof(info->bgp_notification_err_data),
                                            "Bad Peer AS: %u", get_uint16_ntohs(payload, offset));
            info->bgp_notification_err_length = strlen(info->bgp_notification_err_data);
        }
        else{
        }
    }

}

static void
dissect_bgp_route_refresh(const uint8_t *payload, const uint32_t payload_len, struct bgp_info *info)
{
    int32_t offset = 0;
    uint16_t hlen = 0;
    uint8_t subtype = 0;
    uint8_t safi = 0;
    uint8_t orftype = 0;
    uint16_t orflen = 0;
    int32_t pend = 0;
    uint8_t entryflag = 0;
    int advance = 0;
    // int32_t entrylen = 0;

    hlen = get_uint16_ntohs(payload, offset);
    offset = BGP_HEADER_SIZE;

    info->bgp_route_refresh_afi = get_uint16_ntohs(payload, offset);
    offset += 2;

    subtype = get_uint8_t(payload, offset);
    info->bgp_route_refresh_subtype = subtype;
    info->bgp_route_refresh_subtype_str = val_to_string(subtype, route_refresh_subtype_vals);
    offset += 1;

    safi = get_uint8_t(payload, offset);
    info->bgp_route_refresh_safi = safi;
    info->bgp_route_refresh_safi_str = val_to_string(safi, bgpattr_nlri_safi);
    offset += 1;

    if (hlen == BGP_HEADER_SIZE + 4)
        return;

    while (offset < hlen) {
        offset += 1;

        orftype = get_uint8_t(payload, offset);
        offset += 1;

        orflen = get_uint16_ntohs(payload, offset);

        offset += 2;

        if (orftype != BGP_ORF_PREFIX_CISCO) {
            offset += orflen;
            continue;
        }
        pend = offset + orflen;
        while (offset < pend) {
            entryflag = get_uint8_t(payload, offset);
            if (((entryflag & BGP_ORF_ACTION) >> 6) == BGP_ORF_REMOVEALL) {
                offset++;
                continue;
            }
            offset++;

            //proto_tree_add_item(subtree1, hf_bgp_route_refresh_orf_entry_sequence, tvb, p, 4, ENC_BIG_ENDIAN);
            offset += 4;

            //proto_tree_add_item(subtree1, hf_bgp_route_refresh_orf_entry_prefixmask_lower, tvb, p, 1, ENC_BIG_ENDIAN);
            offset++;

            //proto_tree_add_item(subtree1, hf_bgp_route_refresh_orf_entry_prefixmask_upper, tvb, p, 1, ENC_BIG_ENDIAN);
            offset++;

            advance = decode_prefix4(payload, payload_len, offset,
                    info->bgp_update_network_layer_rechability, sizeof(info->bgp_update_network_layer_rechability));

            if (advance < 0)
                break;
            // entrylen = 7 + 1 + advance;

            //proto_item_set_len(ti1, entrylen);
            offset += advance;

        }
    }
}

static void
dissect_bgp_capability(const uint8_t *payload, const uint32_t payload_len, struct bgp_info *info)
{
    UNUSED(payload);
    UNUSED(payload_len);
    UNUSED(info);

}
static int 
dissect_bgp_pdu(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, struct bgp_info *info)
{
    if(NULL==payload || payload_len<=0){
        return -1;
    }
    uint16_t bgp_len = 0;
    uint8_t bgp_type = 0;

    bgp_len = get_uint16_ntohs(payload, BGP_MARKER_SIZE);
    bgp_type = get_uint8_t(payload, BGP_MARKER_SIZE + 2);
    info->bgp_len = bgp_len;
    info->bgp_type_str = val_to_string(bgp_type, bgptypevals);
    info->bgp_type = bgp_type;
    if (bgp_len < BGP_HEADER_SIZE || bgp_len > BGP_MAX_PACKET_SIZE) {
        return -1;
    }
    info->marker = payload;

    switch (bgp_type) {
    case BGP_OPEN:
        dissect_bgp_open(payload, payload_len, info);
        break;
    case BGP_UPDATE:
        dissect_bgp_update(payload, payload_len, info);
        break;
    case BGP_NOTIFICATION:
        dissect_bgp_notification(payload, payload_len, info);
        break;
    case BGP_KEEPALIVE:
        /* no data in KEEPALIVE messages */
        break;
    case BGP_ROUTE_REFRESH_CISCO:
    case BGP_ROUTE_REFRESH:
        dissect_bgp_route_refresh(payload, payload_len, info);
        break;
    case BGP_CAPABILITY:
        dissect_bgp_capability(payload, payload_len, info);
        break;
    default:
        break;
    }
    write_bgp_log(flow, direction, info, NULL);

    return 0;
}

static int
dissect_bgp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(seq);
    UNUSED(flag);

    uint16_t bgp_len;
    uint32_t offset = 0;
    struct  bgp_info info;
    memset(&info, 0, sizeof(info));
    while ( offset + BGP_MARKER_SIZE + 2 < payload_len){
        bgp_len = get_uint16_ntohs(payload, offset + BGP_MARKER_SIZE);

        if (bgp_len < BGP_MARKER_SIZE || bgp_len + offset > payload_len)
            return -1;

        dissect_bgp_pdu(flow, direction, payload + offset, bgp_len, &info);

        offset += bgp_len;
    }

    return 0;
}

static void init_bgp_dissector(void)
{
    //不要输出 字段头文件了
    dpi_register_proto_schema(bgp_field_array_sdt, EM_BGP_MAX, "bgp");

    port_add_proto_head(IPPROTO_TCP, 179, PROTOCOL_BGP);

    tcp_detection_array[PROTOCOL_BGP].proto = PROTOCOL_BGP;
    tcp_detection_array[PROTOCOL_BGP].identify_func = identify_bgp;
    tcp_detection_array[PROTOCOL_BGP].dissect_func = dissect_bgp;


    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_BGP].excluded_protocol_bitmask, PROTOCOL_BGP);
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_BGP].excluded_protocol_bitmask, PROTOCOL_BGP);


    map_fields_info_register(bgp_field_array_sdt,PROTOCOL_BGP, EM_BGP_MAX,"bgp");

    return;
}

static __attribute((constructor)) void     before_init_bgp(void) {
    register_tbl_array(TBL_LOG_BGP, 0, "bgp", init_bgp_dissector);
}

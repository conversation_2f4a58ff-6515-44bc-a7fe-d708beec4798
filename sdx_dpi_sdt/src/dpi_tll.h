#ifndef _DPI_TLL_H_
#define _DPI_TLL_H_

#include "dpi_detect.h"
#include "dpi_common.h"

typedef struct _tll_config {
	int			flow_interval;			/* 保健间隔，典型值为25s，某些偶见协议(暗扣为21/25/110/143) */
	int			flow_interval_total;	/* 流总长超时时间 */
	int			aggeration_size;		/* 聚合通联日志报文的数量，积累到此数量后发送出去 */
	int			send_interval;			/* 聚合日志发送的时间间隔， 默认60s, 范围 1~3600s */
} tll_config;

#pragma pack(1)
//对于多路由数据，该数据结构记录了该数据流中某条线路的包个数
typedef struct LINESTAT_T {
	uint32_t lineNO[4];					/* 线路号 */
	uint32_t packetLine[2];				/* 该线路号对应的该流报文0代表c->s  1代表s->c*/
	uint64_t octetsLine[2];				/* 该线路号对应的该流报文长度0代表c->s  1代表s->c*/
} LINE_STAT_T;

//通联MSG的主数据结构
typedef struct DATASTATE_T {
	uint16_t    protocol;				/* 原始报文的四层协议*/
	uint32_t    networkid;				/* 链路IP：MPLS、VLAN等的ID号 */
	uint32_t    startTime_s;			/* 该流开始时间，单位秒 */
	uint16_t    startTime_ms;			/* 该流开始时间，单位毫秒 */
	uint32_t    endTime_s;				/* 该流结束时间，单位秒 */
	uint16_t    endTime_ms;				/* 该流结束时间，单位毫秒 */
	uint32_t    packetAll[2];			/* 该流报文总数  0代表c->s  1代表s->c */
	uint64_t    octetsAll[2];			/* 该流报文总字节数统计  0代表c->s  1代表s->c*/
//   uint8_t     direction;        /* 流方向标识，  0 c->s  1 s-c*/
	uint16_t    linkNOcount;			/* 线路数统计信息 */
	LINE_STAT_T lineStatContent[2];	    /* 线路统计信息，本次测试可以不支持*/
}DATA_STATE_T;

// 信号数据的来源
//typedef struct DataSRC {
//	uint8_t		Global_LineNO[16];	/* 调用转换库，可转换为线路名称，用于线路名称规则匹配及线路数据统计 */
//	uint16_t	LineType;			/* X */
//	uint8_t		PosType;			/* Y */
//	uint8_t		SysNO_DeviceType;	/* 高4位: Z 低4位： 设备序号 */
//	uint8_t		DeviceSeq;			/* 设备类型 */
//	uint8_t		Board_Type_Seq;		/* 高4位: 板卡（端口）类型, 低4位： 板卡（端口）序号 */
//	uint8_t		SendPort_Reserve;	/* 发送端口编号 */
//}Data_SRC;
//
//typedef struct MacPacketHeader {
//	//MAC信息
//	uint8_t		dstMac[6];		/* 接收端的mac地址 */
//	uint8_t		srcMac[6];		/* 发送端口的mac地址 */
//	uint16_t	wProtocol;		/* 默认为 0x0800 */
//	uint8_t		mac_Seq;		/* mac帧序号
//								 * 由发送该mac帧的设备填入，设备上每个以太网端口对发送到不同目的mac地址的数据帧分别计数
//								 * 从0x00开始， 到0xff自动翻转为0x00
//								 */
//
//	uint8_t		DataType;		/* 数据类型 what， 具体如下：
//								* 0x00
//								* 0x01
//								* 0x02
//								* 0x03
//								* 0x04
//								* 0x05
//								* 0x06
//								* 0x07-3F 保留
//								* 0x40 匹配命中ipv4数据
//								* 0x41 未匹配命中ipv4数据
//								* 0x42 非ip数据
//								* 0x43 ip线路统计数据
//								* 0x44 ip统计结果数据
//								* 0x45 匹配命中ipv6数据
//								* 0x46 为匹配命中ipv6数据
//								* 0x47 通联日志
//								* 0x48 采样ip数据
//								* 0x49-0x7F 保留
//								* 0x80
//								* 0x81 GFP承载业务数据
//								* 0x82 M-MPLS承载业务数据
//								* 0x83 Nx64k承载业务数据
//								* 0x84 10G SDH原始数据
//								* 0x85 2.5G SDH原始数据
//								* 0x86 622M SDH原始数据
//								* 0x87 155M SDH原始数据
//								* 0x88 只能服用数据
//								* 0x89 SDH支路原始数据
//								* 0x8A-0xFF
//								*/
//
//	uint16_t	wDataLen;		/* 以字节为单位的what数据(需要发送的有效数据)长度
//								*  值为 bLinkType + bLinkLen + len(data)
//								*/
//
//	Data_SRC	Datasrc;		/* 信号数据的来源 where */
//
//	uint8_t		TimeStamp[8];	/* 数据时间戳 when
//								* 仅对IP数据有效， 表示接收到的每个IP包的时间戳
//								* 前4字节为秒数， 后4字节为微秒数
//								*/
//
//	uint8_t		bLinkType;		/* 链路信息，后面紧跟报文 what
//								* 0x00 无指示，此时无链路层信息字段
//								* 0x01 VPI VCI
//								* 0x02 废弃
//								* 0x03 废弃
//								* 0x04 DLCI (FR)
//								* 0x05 废弃
//								* 0x06 MAC地址
//								* 0x07 废弃
//								* 0x08 废弃
//								* 0x09 废弃
//								* 0x0A PPP
//								* 0x0B Cisco PPP
//								* 0xFF 未定义的链路层信息
//								*/
//	uint8_t		bLinkLen;		/* 链路层长度 */
//} MacPacket_Header;
//
//
////数据组装格式，以ipv4为例，ipv6替换struct ip的结构即可
//typedef struct _PKT_STATE_T {
//	MacPacket_Header			mac_header;	/* eth头 */
//	const struct dpi_iphdr		*iphdr;		/* ipv4头 */
//	const struct dpi_ipv6hdr	*iph6;		/* ipv6头 */
//	const struct dpi_udphdr		*udphdr;	/* udp头 */
//	struct DATA_STATE_T			*stateContent; /* 流统计信息内容 */
//
//	uint8_t						ipversion;
//	int							pkt_len;
//} PKT_STATE_T;

#pragma pack()

struct flow_info;

int dissect_tll(struct flow_info *flow, int direction, uint32_t seq,
                const uint8_t *payload, uint32_t payload_len, uint8_t flag);
void write_tll_log(struct flow_info *flow, int direction);				
int dpi_copy_record_to_tll(struct flow_info * flow, void *data);
int dpi_tll_record_backfill(precord_t *precord, DATA_STATE_T *tll_msg);
#endif

#include "dpi_pint.h"

static const uint8_t bit_mask8[] = {
    0x00,
    0x01,
    0x03,
    0x07,
    0x0f,
    0x1f,
    0x3f,
    0x7f,
    0xff
};
    
/*
 * This function will dissect a sequence of bits that does not need to be byte aligned; the bits
 * set will be shown in the tree as ..10 10.. and the integer value returned if return_value is set.
 * Offset should be given in bits from the start of the tvb.
 * The function tolerates requests for more than 64 bits, but will only return the least significant 64 bits.
 */
static uint64_t _dpi_get_bits64(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t total_no_of_bits)
{
    uint8_t u8;
    uint16_t u16;
    uint32_t u32;
    uint64_t value;
    uint32_t octet_offset = bit_offset >> 3;
    uint8_t required_bits_in_first_octet = 8 - (bit_offset % 8);

    if (required_bits_in_first_octet > total_no_of_bits)
    {
        /* the required bits don't extend to the end of the first octet */
        uint8_t right_shift = required_bits_in_first_octet - total_no_of_bits;
        if (dpi_get_uint8(pkt, octet_offset, &u8) == -1)
            return 0;
        value = (u8 >> right_shift) & bit_mask8[total_no_of_bits % 8];
    }
    else
    {
        uint8_t remaining_bit_length = total_no_of_bits;

        /* get the bits up to the first octet boundary */
        value = 0;
        required_bits_in_first_octet %= 8;
        if(required_bits_in_first_octet != 0)
        {
            if (dpi_get_uint8(pkt, octet_offset, &u8) == -1)
                return 0;
            value = u8 & bit_mask8[required_bits_in_first_octet];
            remaining_bit_length -= required_bits_in_first_octet;
            octet_offset ++;
        }
        /* take the biggest words, shorts or octets that we can */
        while (remaining_bit_length > 7)
        {
            switch (remaining_bit_length >> 4)
            {
            case 0:
                /* 8 - 15 bits. (note that 0 - 7 would have dropped out of the while() loop) */
                value <<= 8;
                if (dpi_get_uint8(pkt, octet_offset, &u8) == -1)
                    return 0;                
                value += u8;
                remaining_bit_length -= 8;
                octet_offset ++;
                break;

            case 1:
                /* 16 - 31 bits */
                value <<= 16;
                if (dpi_get_be16(pkt, octet_offset, &u16) == -1)
                    return 0;                
                value += u16;
                remaining_bit_length -= 16;
                octet_offset += 2;
                break;

            case 2:
            case 3:
                /* 32 - 63 bits */
                value <<= 32;
                if (dpi_get_be32(pkt, octet_offset, &u32) == -1)
                    return 0;        
                value += u32;
                remaining_bit_length -= 32;
                octet_offset += 4;
                break;

            default:
                /* 64 bits (or more???) */
                if (dpi_get_be64(pkt, octet_offset, &value) == -1)
                    return 0;                    
                remaining_bit_length -= 64;
                octet_offset += 8;
                break;
            }
        }
        /* get bits from any partial octet at the tail */
        if(remaining_bit_length)
        {
            value <<= remaining_bit_length;
            if (dpi_get_uint8(pkt, octet_offset, &u8) == -1)
                return 0;            
            value += (u8 >> (8 - remaining_bit_length));
        }
    }
    return value;
}

/* Get 1 - 8 bits */
uint8_t dpi_get_bits8(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits)
{
    return (uint8_t)_dpi_get_bits64(pkt, bit_offset, no_of_bits);
}

/* Get 9 - 16 bits */
uint16_t dpi_get_bits16(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits,const uint32_t encoding)
{
    UNUSED(encoding);
    /* note that encoding has no meaning here, as the tvb is considered to contain an octet array */
    return (uint16_t)_dpi_get_bits64(pkt, bit_offset, no_of_bits);
}

/* Get 1 - 32 bits */
uint32_t dpi_get_bits32(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits, const uint32_t encoding)
{
    UNUSED(encoding);
    /* note that encoding has no meaning here, as the tvb is considered to contain an octet array */
    return (uint32_t)_dpi_get_bits64(pkt, bit_offset, no_of_bits);
}

/* Get 1 - 64 bits */
uint64_t dpi_get_bits64(struct dpi_pkt_st *pkt, uint32_t bit_offset, const uint32_t no_of_bits, const uint32_t encoding)
{
    UNUSED(encoding);
    /* note that encoding has no meaning here, as the tvb is considered to contain an octet array */
    return _dpi_get_bits64(pkt, bit_offset, no_of_bits);
}
/* Get 1 - 32 bits (should be deprecated as same as tvb_get_bits32??) */
uint32_t tvb_get_bits(struct dpi_pkt_st *pkt, const uint32_t bit_offset, const uint32_t no_of_bits, const uint32_t encoding)
{
    UNUSED(encoding);
    /* note that encoding has no meaning here, as the tvb is considered to contain an octet array */
    return (uint32_t)_dpi_get_bits64(pkt, bit_offset, no_of_bits);
}


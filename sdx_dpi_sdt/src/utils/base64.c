/* This is a public domain base64 implementation written by <PERSON><PERSON>g. */

#include <string.h>
#include <stdio.h>
#include <iconv.h>
#include <errno.h>
#include "base64.h"


#define BASE64_PAD '='
#define BASE64DE_FIRST '+'
#define BASE64DE_LAST 'z'

static const unsigned short gbkUcs2Tab[][2]={
	{0xa1a1, 0x3000}, //　
	{0xa1a2, 0x3001}, //、
	{0xa1a3, 0x3002}, //。
	{0xa1a4, 0x30fb}, //・
	{0xa1a5, 0x2c9}, //ˉ
	{0xa1a6, 0x2c7}, //ˇ
	{0xa1a7, 0xa8}, //¨
	{0xa1a8, 0x3003}, //〃
	{0xa1a9, 0x3005}, //々
	{0xa1aa, 0x2015}, //―
	{0xa1ab, 0xff5e}, //～
	{0xa1ac, 0x2016}, //‖
	{0xa1ad, 0x2026}, //…
	{0xa<PERSON><PERSON>, 0x2018}, //‘
	{0xa1af, 0x2019}, //’
	{0xa1b0, 0x201c}, //“
	{0xa1b1, 0x201d}, //”
	{0xa1b2, 0x3014}, //〔
	{0xa1b3, 0x3015}, //〕
	{0xa1b4, 0x3008}, //〈
	{0xa1b5, 0x3009}, //〉
	{0xa1b6, 0x300a}, //《
	{0xa1b7, 0x300b}, //》
	{0xa1b8, 0x300c}, //「
	{0xa1b9, 0x300d}, //」
	{0xa1ba, 0x300e}, //『
	{0xa1bb, 0x300f}, //』
	{0xa1bc, 0x3016}, //〖
	{0xa1bd, 0x3017}, //〗
	{0xa1be, 0x3010}, //【
	{0xa1bf, 0x3011}, //】
	{0xa1c0, 0xb1}, //±
	{0xa1c1, 0xd7}, //×
	{0xa1c2, 0xf7}, //÷
	{0xa1c3, 0x2236}, //∶
	{0xa1c4, 0x2227}, //∧
	{0xa1c5, 0x2228}, //∨
	{0xa1c6, 0x2211}, //∑
	{0xa1c7, 0x220f}, //∏
	{0xa1c8, 0x222a}, //∪
	{0xa1c9, 0x2229}, //∩
	{0xa1ca, 0x2208}, //∈
	{0xa1cb, 0x2237}, //∷
	{0xa1cc, 0x221a}, //√
	{0xa1cd, 0x22a5}, //⊥
	{0xa1ce, 0x2225}, //∥
	{0xa1cf, 0x2220}, //∠
	{0xa1d0, 0x2312}, //⌒
	{0xa1d1, 0x2299}, //⊙
	{0xa1d2, 0x222b}, //∫
	{0xa1d3, 0x222e}, //∮
	{0xa1d4, 0x2261}, //≡
	{0xa1d5, 0x224c}, //≌
	{0xa1d6, 0x2248}, //≈
	{0xa1d7, 0x223d}, //∽
	{0xa1d8, 0x221d}, //∝
	{0xa1d9, 0x2260}, //≠
	{0xa1da, 0x226e}, //≮
	{0xa1db, 0x226f}, //≯
	{0xa1dc, 0x2264}, //≤
	{0xa1dd, 0x2265}, //≥
	{0xa1de, 0x221e}, //∞
	{0xa1df, 0x2235}, //∵
	{0xa1e0, 0x2234}, //∴
	{0xa1e1, 0x2642}, //♂
	{0xa1e2, 0x2640}, //♀
	{0xa1e3, 0xb0}, //°
	{0xa1e4, 0x2032}, //′
	{0xa1e5, 0x2033}, //″
	{0xa1e6, 0x2103}, //℃
	{0xa1e7, 0xff04}, //＄
	{0xa1e8, 0xa4}, //¤
	{0xa1e9, 0xffe0}, //￠
	{0xa1ea, 0xffe1}, //￡
	{0xa1eb, 0x2030}, //‰
	{0xa1ec, 0xa7}, //§
	{0xa1ed, 0x2116}, //№
	{0xa1ee, 0x2606}, //☆
	{0xa1ef, 0x2605}, //★
	{0xa1f0, 0x25cb}, //○
	{0xa1f1, 0x25cf}, //●
	{0xa1f2, 0x25ce}, //◎
	{0xa1f3, 0x25c7}, //◇
	{0xa1f4, 0x25c6}, //◆
	{0xa1f5, 0x25a1}, //□
	{0xa1f6, 0x25a0}, //■
	{0xa1f7, 0x25b3}, //△
	{0xa1f8, 0x25b2}, //▲
	{0xa1f9, 0x203b}, //※
	{0xa1fa, 0x2192}, //→
	{0xa1fb, 0x2190}, //←
	{0xa1fc, 0x2191}, //↑
	{0xa1fd, 0x2193}, //↓
	{0xa1fe, 0x3013}, //〓
	{0xa2b1, 0x2488}, //⒈
	{0xa2b2, 0x2489}, //⒉
	{0xa2b3, 0x248a}, //⒊
	{0xa2b4, 0x248b}, //⒋
	{0xa2b5, 0x248c}, //⒌
	{0xa2b6, 0x248d}, //⒍
	{0xa2b7, 0x248e}, //⒎
	{0xa2b8, 0x248f}, //⒏
	{0xa2b9, 0x2490}, //⒐
	{0xa2ba, 0x2491}, //⒑
	{0xa2bb, 0x2492}, //⒒
	{0xa2bc, 0x2493}, //⒓
	{0xa2bd, 0x2494}, //⒔
	{0xa2be, 0x2495}, //⒕
	{0xa2bf, 0x2496}, //⒖
	{0xa2c0, 0x2497}, //⒗
	{0xa2c1, 0x2498}, //⒘
	{0xa2c2, 0x2499}, //⒙
	{0xa2c3, 0x249a}, //⒚
	{0xa2c4, 0x249b}, //⒛
	{0xa2c5, 0x2474}, //⑴
	{0xa2c6, 0x2475}, //⑵
	{0xa2c7, 0x2476}, //⑶
	{0xa2c8, 0x2477}, //⑷
	{0xa2c9, 0x2478}, //⑸
	{0xa2ca, 0x2479}, //⑹
	{0xa2cb, 0x247a}, //⑺
	{0xa2cc, 0x247b}, //⑻
	{0xa2cd, 0x247c}, //⑼
	{0xa2ce, 0x247d}, //⑽
	{0xa2cf, 0x247e}, //⑾
	{0xa2d0, 0x247f}, //⑿
	{0xa2d1, 0x2480}, //⒀
	{0xa2d2, 0x2481}, //⒁
	{0xa2d3, 0x2482}, //⒂
	{0xa2d4, 0x2483}, //⒃
	{0xa2d5, 0x2484}, //⒄
	{0xa2d6, 0x2485}, //⒅
	{0xa2d7, 0x2486}, //⒆
	{0xa2d8, 0x2487}, //⒇
	{0xa2d9, 0x2460}, //①
	{0xa2da, 0x2461}, //②
	{0xa2db, 0x2462}, //③
	{0xa2dc, 0x2463}, //④
	{0xa2dd, 0x2464}, //⑤
	{0xa2de, 0x2465}, //⑥
	{0xa2df, 0x2466}, //⑦
	{0xa2e0, 0x2467}, //⑧
	{0xa2e1, 0x2468}, //⑨
	{0xa2e2, 0x2469}, //⑩
	{0xa2e5, 0x3220}, //㈠
	{0xa2e6, 0x3221}, //㈡
	{0xa2e7, 0x3222}, //㈢
	{0xa2e8, 0x3223}, //㈣
	{0xa2e9, 0x3224}, //㈤
	{0xa2ea, 0x3225}, //㈥
	{0xa2eb, 0x3226}, //㈦
	{0xa2ec, 0x3227}, //㈧
	{0xa2ed, 0x3228}, //㈨
	{0xa2ee, 0x3229}, //㈩
	{0xa2f1, 0x2160}, //Ⅰ
	{0xa2f2, 0x2161}, //Ⅱ
	{0xa2f3, 0x2162}, //Ⅲ
	{0xa2f4, 0x2163}, //Ⅳ
	{0xa2f5, 0x2164}, //Ⅴ
	{0xa2f6, 0x2165}, //Ⅵ
	{0xa2f7, 0x2166}, //Ⅶ
	{0xa2f8, 0x2167}, //Ⅷ
	{0xa2f9, 0x2168}, //Ⅸ
	{0xa2fa, 0x2169}, //Ⅹ
	{0xa2fb, 0x216a}, //Ⅺ
	{0xa2fc, 0x216b}, //Ⅻ
	{0xa3a1, 0xff01}, //！
	{0xa3a2, 0xff02}, //＂
	{0xa3a3, 0xff03}, //＃
	{0xa3a4, 0xffe5}, //￥
	{0xa3a5, 0xff05}, //％
	{0xa3a6, 0xff06}, //＆
	{0xa3a7, 0xff07}, //＇
	{0xa3a8, 0xff08}, //（
	{0xa3a9, 0xff09}, //）
	{0xa3aa, 0xff0a}, //＊
	{0xa3ab, 0xff0b}, //＋
	{0xa3ac, 0xff0c}, //，
	{0xa3ad, 0xff0d}, //－
	{0xa3ae, 0xff0e}, //．
	{0xa3af, 0xff0f}, //／
	{0xa3b0, 0xff10}, //０
	{0xa3b1, 0xff11}, //１
	{0xa3b2, 0xff12}, //２
	{0xa3b3, 0xff13}, //３
	{0xa3b4, 0xff14}, //４
	{0xa3b5, 0xff15}, //５
	{0xa3b6, 0xff16}, //６
	{0xa3b7, 0xff17}, //７
	{0xa3b8, 0xff18}, //８
	{0xa3b9, 0xff19}, //９
	{0xa3ba, 0xff1a}, //：
	{0xa3bb, 0xff1b}, //；
	{0xa3bc, 0xff1c}, //＜
	{0xa3bd, 0xff1d}, //＝
	{0xa3be, 0xff1e}, //＞
	{0xa3bf, 0xff1f}, //？
	{0xa3c0, 0xff20}, //＠
	{0xa3c1, 0xff21}, //Ａ
	{0xa3c2, 0xff22}, //Ｂ
	{0xa3c3, 0xff23}, //Ｃ
	{0xa3c4, 0xff24}, //Ｄ
	{0xa3c5, 0xff25}, //Ｅ
	{0xa3c6, 0xff26}, //Ｆ
	{0xa3c7, 0xff27}, //Ｇ
	{0xa3c8, 0xff28}, //Ｈ
	{0xa3c9, 0xff29}, //Ｉ
	{0xa3ca, 0xff2a}, //Ｊ
	{0xa3cb, 0xff2b}, //Ｋ
	{0xa3cc, 0xff2c}, //Ｌ
	{0xa3cd, 0xff2d}, //Ｍ
	{0xa3ce, 0xff2e}, //Ｎ
	{0xa3cf, 0xff2f}, //Ｏ
	{0xa3d0, 0xff30}, //Ｐ
	{0xa3d1, 0xff31}, //Ｑ
	{0xa3d2, 0xff32}, //Ｒ
	{0xa3d3, 0xff33}, //Ｓ
	{0xa3d4, 0xff34}, //Ｔ
	{0xa3d5, 0xff35}, //Ｕ
	{0xa3d6, 0xff36}, //Ｖ
	{0xa3d7, 0xff37}, //Ｗ
	{0xa3d8, 0xff38}, //Ｘ
	{0xa3d9, 0xff39}, //Ｙ
	{0xa3da, 0xff3a}, //Ｚ
	{0xa3db, 0xff3b}, //［
	{0xa3dc, 0xff3c}, //＼
	{0xa3dd, 0xff3d}, //］
	{0xa3de, 0xff3e}, //＾
	{0xa3df, 0xff3f}, //＿
	{0xa3e0, 0xff40}, //｀
	{0xa3e1, 0xff41}, //ａ
	{0xa3e2, 0xff42}, //ｂ
	{0xa3e3, 0xff43}, //ｃ
	{0xa3e4, 0xff44}, //ｄ
	{0xa3e5, 0xff45}, //ｅ
	{0xa3e6, 0xff46}, //ｆ
	{0xa3e7, 0xff47}, //ｇ
	{0xa3e8, 0xff48}, //ｈ
	{0xa3e9, 0xff49}, //ｉ
	{0xa3ea, 0xff4a}, //ｊ
	{0xa3eb, 0xff4b}, //ｋ
	{0xa3ec, 0xff4c}, //ｌ
	{0xa3ed, 0xff4d}, //ｍ
	{0xa3ee, 0xff4e}, //ｎ
	{0xa3ef, 0xff4f}, //ｏ
	{0xa3f0, 0xff50}, //ｐ
	{0xa3f1, 0xff51}, //ｑ
	{0xa3f2, 0xff52}, //ｒ
	{0xa3f3, 0xff53}, //ｓ
	{0xa3f4, 0xff54}, //ｔ
	{0xa3f5, 0xff55}, //ｕ
	{0xa3f6, 0xff56}, //ｖ
	{0xa3f7, 0xff57}, //ｗ
	{0xa3f8, 0xff58}, //ｘ
	{0xa3f9, 0xff59}, //ｙ
	{0xa3fa, 0xff5a}, //ｚ
	{0xa3fb, 0xff5b}, //｛
	{0xa3fc, 0xff5c}, //｜
	{0xa3fd, 0xff5d}, //｝
	{0xa3fe, 0xffe3}, //￣
	{0xa4a1, 0x3041}, //ぁ
	{0xa4a2, 0x3042}, //あ
	{0xa4a3, 0x3043}, //ぃ
	{0xa4a4, 0x3044}, //い
	{0xa4a5, 0x3045}, //ぅ
	{0xa4a6, 0x3046}, //う
	{0xa4a7, 0x3047}, //ぇ
	{0xa4a8, 0x3048}, //え
	{0xa4a9, 0x3049}, //ぉ
	{0xa4aa, 0x304a}, //お
	{0xa4ab, 0x304b}, //か
	{0xa4ac, 0x304c}, //が
	{0xa4ad, 0x304d}, //き
	{0xa4ae, 0x304e}, //ぎ
	{0xa4af, 0x304f}, //く
	{0xa4b0, 0x3050}, //ぐ
	{0xa4b1, 0x3051}, //け
	{0xa4b2, 0x3052}, //げ
	{0xa4b3, 0x3053}, //こ
	{0xa4b4, 0x3054}, //ご
	{0xa4b5, 0x3055}, //さ
	{0xa4b6, 0x3056}, //ざ
	{0xa4b7, 0x3057}, //し
	{0xa4b8, 0x3058}, //じ
	{0xa4b9, 0x3059}, //す
	{0xa4ba, 0x305a}, //ず
	{0xa4bb, 0x305b}, //せ
	{0xa4bc, 0x305c}, //ぜ
	{0xa4bd, 0x305d}, //そ
	{0xa4be, 0x305e}, //ぞ
	{0xa4bf, 0x305f}, //た
	{0xa4c0, 0x3060}, //だ
	{0xa4c1, 0x3061}, //ち
	{0xa4c2, 0x3062}, //ぢ
	{0xa4c3, 0x3063}, //っ
	{0xa4c4, 0x3064}, //つ
	{0xa4c5, 0x3065}, //づ
	{0xa4c6, 0x3066}, //て
	{0xa4c7, 0x3067}, //で
	{0xa4c8, 0x3068}, //と
	{0xa4c9, 0x3069}, //ど
	{0xa4ca, 0x306a}, //な
	{0xa4cb, 0x306b}, //に
	{0xa4cc, 0x306c}, //ぬ
	{0xa4cd, 0x306d}, //ね
	{0xa4ce, 0x306e}, //の
	{0xa4cf, 0x306f}, //は
	{0xa4d0, 0x3070}, //ば
	{0xa4d1, 0x3071}, //ぱ
	{0xa4d2, 0x3072}, //ひ
	{0xa4d3, 0x3073}, //び
	{0xa4d4, 0x3074}, //ぴ
	{0xa4d5, 0x3075}, //ふ
	{0xa4d6, 0x3076}, //ぶ
	{0xa4d7, 0x3077}, //ぷ
	{0xa4d8, 0x3078}, //へ
	{0xa4d9, 0x3079}, //べ
	{0xa4da, 0x307a}, //ぺ
	{0xa4db, 0x307b}, //ほ
	{0xa4dc, 0x307c}, //ぼ
	{0xa4dd, 0x307d}, //ぽ
	{0xa4de, 0x307e}, //ま
	{0xa4df, 0x307f}, //み
	{0xa4e0, 0x3080}, //む
	{0xa4e1, 0x3081}, //め
	{0xa4e2, 0x3082}, //も
	{0xa4e3, 0x3083}, //ゃ
	{0xa4e4, 0x3084}, //や
	{0xa4e5, 0x3085}, //ゅ
	{0xa4e6, 0x3086}, //ゆ
	{0xa4e7, 0x3087}, //ょ
	{0xa4e8, 0x3088}, //よ
	{0xa4e9, 0x3089}, //ら
	{0xa4ea, 0x308a}, //り
	{0xa4eb, 0x308b}, //る
	{0xa4ec, 0x308c}, //れ
	{0xa4ed, 0x308d}, //ろ
	{0xa4ee, 0x308e}, //ゎ
	{0xa4ef, 0x308f}, //わ
	{0xa4f0, 0x3090}, //ゐ
	{0xa4f1, 0x3091}, //ゑ
	{0xa4f2, 0x3092}, //を
	{0xa4f3, 0x3093}, //ん
	{0xa5a1, 0x30a1}, //ァ
	{0xa5a2, 0x30a2}, //ア
	{0xa5a3, 0x30a3}, //ィ
	{0xa5a4, 0x30a4}, //イ
	{0xa5a5, 0x30a5}, //ゥ
	{0xa5a6, 0x30a6}, //ウ
	{0xa5a7, 0x30a7}, //ェ
	{0xa5a8, 0x30a8}, //エ
	{0xa5a9, 0x30a9}, //ォ
	{0xa5aa, 0x30aa}, //オ
	{0xa5ab, 0x30ab}, //カ
	{0xa5ac, 0x30ac}, //ガ
	{0xa5ad, 0x30ad}, //キ
	{0xa5ae, 0x30ae}, //ギ
	{0xa5af, 0x30af}, //ク
	{0xa5b0, 0x30b0}, //グ
	{0xa5b1, 0x30b1}, //ケ
	{0xa5b2, 0x30b2}, //ゲ
	{0xa5b3, 0x30b3}, //コ
	{0xa5b4, 0x30b4}, //ゴ
	{0xa5b5, 0x30b5}, //サ
	{0xa5b6, 0x30b6}, //ザ
	{0xa5b7, 0x30b7}, //シ
	{0xa5b8, 0x30b8}, //ジ
	{0xa5b9, 0x30b9}, //ス
	{0xa5ba, 0x30ba}, //ズ
	{0xa5bb, 0x30bb}, //セ
	{0xa5bc, 0x30bc}, //ゼ
	{0xa5bd, 0x30bd}, //ソ
	{0xa5be, 0x30be}, //ゾ
	{0xa5bf, 0x30bf}, //タ
	{0xa5c0, 0x30c0}, //ダ
	{0xa5c1, 0x30c1}, //チ
	{0xa5c2, 0x30c2}, //ヂ
	{0xa5c3, 0x30c3}, //ッ
	{0xa5c4, 0x30c4}, //ツ
	{0xa5c5, 0x30c5}, //ヅ
	{0xa5c6, 0x30c6}, //テ
	{0xa5c7, 0x30c7}, //デ
	{0xa5c8, 0x30c8}, //ト
	{0xa5c9, 0x30c9}, //ド
	{0xa5ca, 0x30ca}, //ナ
	{0xa5cb, 0x30cb}, //ニ
	{0xa5cc, 0x30cc}, //ヌ
	{0xa5cd, 0x30cd}, //ネ
	{0xa5ce, 0x30ce}, //ノ
	{0xa5cf, 0x30cf}, //ハ
	{0xa5d0, 0x30d0}, //バ
	{0xa5d1, 0x30d1}, //パ
	{0xa5d2, 0x30d2}, //ヒ
	{0xa5d3, 0x30d3}, //ビ
	{0xa5d4, 0x30d4}, //ピ
	{0xa5d5, 0x30d5}, //フ
	{0xa5d6, 0x30d6}, //ブ
	{0xa5d7, 0x30d7}, //プ
	{0xa5d8, 0x30d8}, //ヘ
	{0xa5d9, 0x30d9}, //ベ
	{0xa5da, 0x30da}, //ペ
	{0xa5db, 0x30db}, //ホ
	{0xa5dc, 0x30dc}, //ボ
	{0xa5dd, 0x30dd}, //ポ
	{0xa5de, 0x30de}, //マ
	{0xa5df, 0x30df}, //ミ
	{0xa5e0, 0x30e0}, //ム
	{0xa5e1, 0x30e1}, //メ
	{0xa5e2, 0x30e2}, //モ
	{0xa5e3, 0x30e3}, //ャ
	{0xa5e4, 0x30e4}, //ヤ
	{0xa5e5, 0x30e5}, //ュ
	{0xa5e6, 0x30e6}, //ユ
	{0xa5e7, 0x30e7}, //ョ
	{0xa5e8, 0x30e8}, //ヨ
	{0xa5e9, 0x30e9}, //ラ
	{0xa5ea, 0x30ea}, //リ
	{0xa5eb, 0x30eb}, //ル
	{0xa5ec, 0x30ec}, //レ
	{0xa5ed, 0x30ed}, //ロ
	{0xa5ee, 0x30ee}, //ヮ
	{0xa5ef, 0x30ef}, //ワ
	{0xa5f0, 0x30f0}, //ヰ
	{0xa5f1, 0x30f1}, //ヱ
	{0xa5f2, 0x30f2}, //ヲ
	{0xa5f3, 0x30f3}, //ン
	{0xa5f4, 0x30f4}, //ヴ
	{0xa5f5, 0x30f5}, //ヵ
	{0xa5f6, 0x30f6}, //ヶ
	{0xa6a1, 0x391}, //Α
	{0xa6a2, 0x392}, //Β
	{0xa6a3, 0x393}, //Γ
	{0xa6a4, 0x394}, //Δ
	{0xa6a5, 0x395}, //Ε
	{0xa6a6, 0x396}, //Ζ
	{0xa6a7, 0x397}, //Η
	{0xa6a8, 0x398}, //Θ
	{0xa6a9, 0x399}, //Ι
	{0xa6aa, 0x39a}, //Κ
	{0xa6ab, 0x39b}, //Λ
	{0xa6ac, 0x39c}, //Μ
	{0xa6ad, 0x39d}, //Ν
	{0xa6ae, 0x39e}, //Ξ
	{0xa6af, 0x39f}, //Ο
	{0xa6b0, 0x3a0}, //Π
	{0xa6b1, 0x3a1}, //Ρ
	{0xa6b2, 0x3a3}, //Σ
	{0xa6b3, 0x3a4}, //Τ
	{0xa6b4, 0x3a5}, //Υ
	{0xa6b5, 0x3a6}, //Φ
	{0xa6b6, 0x3a7}, //Χ
	{0xa6b7, 0x3a8}, //Ψ
	{0xa6b8, 0x3a9}, //Ω
	{0xa6c1, 0x3b1}, //α
	{0xa6c2, 0x3b2}, //β
	{0xa6c3, 0x3b3}, //γ
	{0xa6c4, 0x3b4}, //δ
	{0xa6c5, 0x3b5}, //ε
	{0xa6c6, 0x3b6}, //ζ
	{0xa6c7, 0x3b7}, //η
	{0xa6c8, 0x3b8}, //θ
	{0xa6c9, 0x3b9}, //ι
	{0xa6ca, 0x3ba}, //κ
	{0xa6cb, 0x3bb}, //λ
	{0xa6cc, 0x3bc}, //μ
	{0xa6cd, 0x3bd}, //ν
	{0xa6ce, 0x3be}, //ξ
	{0xa6cf, 0x3bf}, //ο
	{0xa6d0, 0x3c0}, //π
	{0xa6d1, 0x3c1}, //ρ
	{0xa6d2, 0x3c3}, //σ
	{0xa6d3, 0x3c4}, //τ
	{0xa6d4, 0x3c5}, //υ
	{0xa6d5, 0x3c6}, //φ
	{0xa6d6, 0x3c7}, //χ
	{0xa6d7, 0x3c8}, //ψ
	{0xa6d8, 0x3c9}, //ω
	{0xa7a1, 0x410}, //А
	{0xa7a2, 0x411}, //Б
	{0xa7a3, 0x412}, //В
	{0xa7a4, 0x413}, //Г
	{0xa7a5, 0x414}, //Д
	{0xa7a6, 0x415}, //Е
	{0xa7a7, 0x401}, //Ё
	{0xa7a8, 0x416}, //Ж
	{0xa7a9, 0x417}, //З
	{0xa7aa, 0x418}, //И
	{0xa7ab, 0x419}, //Й
	{0xa7ac, 0x41a}, //К
	{0xa7ad, 0x41b}, //Л
	{0xa7ae, 0x41c}, //М
	{0xa7af, 0x41d}, //Н
	{0xa7b0, 0x41e}, //О
	{0xa7b1, 0x41f}, //П
	{0xa7b2, 0x420}, //Р
	{0xa7b3, 0x421}, //С
	{0xa7b4, 0x422}, //Т
	{0xa7b5, 0x423}, //У
	{0xa7b6, 0x424}, //Ф
	{0xa7b7, 0x425}, //Х
	{0xa7b8, 0x426}, //Ц
	{0xa7b9, 0x427}, //Ч
	{0xa7ba, 0x428}, //Ш
	{0xa7bb, 0x429}, //Щ
	{0xa7bc, 0x42a}, //Ъ
	{0xa7bd, 0x42b}, //Ы
	{0xa7be, 0x42c}, //Ь
	{0xa7bf, 0x42d}, //Э
	{0xa7c0, 0x42e}, //Ю
	{0xa7c1, 0x42f}, //Я
	{0xa7d1, 0x430}, //а
	{0xa7d2, 0x431}, //б
	{0xa7d3, 0x432}, //в
	{0xa7d4, 0x433}, //г
	{0xa7d5, 0x434}, //д
	{0xa7d6, 0x435}, //е
	{0xa7d7, 0x451}, //ё
	{0xa7d8, 0x436}, //ж
	{0xa7d9, 0x437}, //з
	{0xa7da, 0x438}, //и
	{0xa7db, 0x439}, //й
	{0xa7dc, 0x43a}, //к
	{0xa7dd, 0x43b}, //л
	{0xa7de, 0x43c}, //м
	{0xa7df, 0x43d}, //н
	{0xa7e0, 0x43e}, //о
	{0xa7e1, 0x43f}, //п
	{0xa7e2, 0x440}, //р
	{0xa7e3, 0x441}, //с
	{0xa7e4, 0x442}, //т
	{0xa7e5, 0x443}, //у
	{0xa7e6, 0x444}, //ф
	{0xa7e7, 0x445}, //х
	{0xa7e8, 0x446}, //ц
	{0xa7e9, 0x447}, //ч
	{0xa7ea, 0x448}, //ш
	{0xa7eb, 0x449}, //щ
	{0xa7ec, 0x44a}, //ъ
	{0xa7ed, 0x44b}, //ы
	{0xa7ee, 0x44c}, //ь
	{0xa7ef, 0x44d}, //э
	{0xa7f0, 0x44e}, //ю
	{0xa7f1, 0x44f}, //я
	{0xa8a1, 0x101}, //ā
	{0xa8a2, 0xe1}, //á
	{0xa8a3, 0x1ce}, //ǎ
	{0xa8a4, 0xe0}, //à
	{0xa8a5, 0x113}, //ē
	{0xa8a6, 0xe9}, //é
	{0xa8a7, 0x11b}, //ě
	{0xa8a8, 0xe8}, //è
	{0xa8a9, 0x12b}, //ī
	{0xa8aa, 0xed}, //í
	{0xa8ab, 0x1d0}, //ǐ
	{0xa8ac, 0xec}, //ì
	{0xa8ad, 0x14d}, //ō
	{0xa8ae, 0xf3}, //ó
	{0xa8af, 0x1d2}, //ǒ
	{0xa8b0, 0xf2}, //ò
	{0xa8b1, 0x16b}, //ū
	{0xa8b2, 0xfa}, //ú
	{0xa8b3, 0x1d4}, //ǔ
	{0xa8b4, 0xf9}, //ù
	{0xa8b5, 0x1d6}, //ǖ
	{0xa8b6, 0x1d8}, //ǘ
	{0xa8b7, 0x1da}, //ǚ
	{0xa8b8, 0x1dc}, //ǜ
	{0xa8b9, 0xfc}, //ü
	{0xa8ba, 0xea}, //ê
	{0xa8c5, 0x3105}, //ㄅ
	{0xa8c6, 0x3106}, //ㄆ
	{0xa8c7, 0x3107}, //ㄇ
	{0xa8c8, 0x3108}, //ㄈ
	{0xa8c9, 0x3109}, //ㄉ
	{0xa8ca, 0x310a}, //ㄊ
	{0xa8cb, 0x310b}, //ㄋ
	{0xa8cc, 0x310c}, //ㄌ
	{0xa8cd, 0x310d}, //ㄍ
	{0xa8ce, 0x310e}, //ㄎ
	{0xa8cf, 0x310f}, //ㄏ
	{0xa8d0, 0x3110}, //ㄐ
	{0xa8d1, 0x3111}, //ㄑ
	{0xa8d2, 0x3112}, //ㄒ
	{0xa8d3, 0x3113}, //ㄓ
	{0xa8d4, 0x3114}, //ㄔ
	{0xa8d5, 0x3115}, //ㄕ
	{0xa8d6, 0x3116}, //ㄖ
	{0xa8d7, 0x3117}, //ㄗ
	{0xa8d8, 0x3118}, //ㄘ
	{0xa8d9, 0x3119}, //ㄙ
	{0xa8da, 0x311a}, //ㄚ
	{0xa8db, 0x311b}, //ㄛ
	{0xa8dc, 0x311c}, //ㄜ
	{0xa8dd, 0x311d}, //ㄝ
	{0xa8de, 0x311e}, //ㄞ
	{0xa8df, 0x311f}, //ㄟ
	{0xa8e0, 0x3120}, //ㄠ
	{0xa8e1, 0x3121}, //ㄡ
	{0xa8e2, 0x3122}, //ㄢ
	{0xa8e3, 0x3123}, //ㄣ
	{0xa8e4, 0x3124}, //ㄤ
	{0xa8e5, 0x3125}, //ㄥ
	{0xa8e6, 0x3126}, //ㄦ
	{0xa8e7, 0x3127}, //ㄧ
	{0xa8e8, 0x3128}, //ㄨ
	{0xa8e9, 0x3129}, //ㄩ
	{0xa9a4, 0x2500}, //─
	{0xa9a5, 0x2501}, //━
	{0xa9a6, 0x2502}, //│
	{0xa9a7, 0x2503}, //┃
	{0xa9a8, 0x2504}, //┄
	{0xa9a9, 0x2505}, //┅
	{0xa9aa, 0x2506}, //┆
	{0xa9ab, 0x2507}, //┇
	{0xa9ac, 0x2508}, //┈
	{0xa9ad, 0x2509}, //┉
	{0xa9ae, 0x250a}, //┊
	{0xa9af, 0x250b}, //┋
	{0xa9b0, 0x250c}, //┌
	{0xa9b1, 0x250d}, //┍
	{0xa9b2, 0x250e}, //┎
	{0xa9b3, 0x250f}, //┏
	{0xa9b4, 0x2510}, //┐
	{0xa9b5, 0x2511}, //┑
	{0xa9b6, 0x2512}, //┒
	{0xa9b7, 0x2513}, //┓
	{0xa9b8, 0x2514}, //└
	{0xa9b9, 0x2515}, //┕
	{0xa9ba, 0x2516}, //┖
	{0xa9bb, 0x2517}, //┗
	{0xa9bc, 0x2518}, //┘
	{0xa9bd, 0x2519}, //┙
	{0xa9be, 0x251a}, //┚
	{0xa9bf, 0x251b}, //┛
	{0xa9c0, 0x251c}, //├
	{0xa9c1, 0x251d}, //┝
	{0xa9c2, 0x251e}, //┞
	{0xa9c3, 0x251f}, //┟
	{0xa9c4, 0x2520}, //┠
	{0xa9c5, 0x2521}, //┡
	{0xa9c6, 0x2522}, //┢
	{0xa9c7, 0x2523}, //┣
	{0xa9c8, 0x2524}, //┤
	{0xa9c9, 0x2525}, //┥
	{0xa9ca, 0x2526}, //┦
	{0xa9cb, 0x2527}, //┧
	{0xa9cc, 0x2528}, //┨
	{0xa9cd, 0x2529}, //┩
	{0xa9ce, 0x252a}, //┪
	{0xa9cf, 0x252b}, //┫
	{0xa9d0, 0x252c}, //┬
	{0xa9d1, 0x252d}, //┭
	{0xa9d2, 0x252e}, //┮
	{0xa9d3, 0x252f}, //┯
	{0xa9d4, 0x2530}, //┰
	{0xa9d5, 0x2531}, //┱
	{0xa9d6, 0x2532}, //┲
	{0xa9d7, 0x2533}, //┳
	{0xa9d8, 0x2534}, //┴
	{0xa9d9, 0x2535}, //┵
	{0xa9da, 0x2536}, //┶
	{0xa9db, 0x2537}, //┷
	{0xa9dc, 0x2538}, //┸
	{0xa9dd, 0x2539}, //┹
	{0xa9de, 0x253a}, //┺
	{0xa9df, 0x253b}, //┻
	{0xa9e0, 0x253c}, //┼
	{0xa9e1, 0x253d}, //┽
	{0xa9e2, 0x253e}, //┾
	{0xa9e3, 0x253f}, //┿
	{0xa9e4, 0x2540}, //╀
	{0xa9e5, 0x2541}, //╁
	{0xa9e6, 0x2542}, //╂
	{0xa9e7, 0x2543}, //╃
	{0xa9e8, 0x2544}, //╄
	{0xa9e9, 0x2545}, //╅
	{0xa9ea, 0x2546}, //╆
	{0xa9eb, 0x2547}, //╇
	{0xa9ec, 0x2548}, //╈
	{0xa9ed, 0x2549}, //╉
	{0xa9ee, 0x254a}, //╊
	{0xa9ef, 0x254b}, //╋
	{0xb0a1, 0x554a}, //啊
	{0xb0a2, 0x963f}, //阿
	{0xb0a3, 0x57c3}, //埃
	{0xb0a4, 0x6328}, //挨
	{0xb0a5, 0x54ce}, //哎
	{0xb0a6, 0x5509}, //唉
	{0xb0a7, 0x54c0}, //哀
	{0xb0a8, 0x7691}, //皑
	{0xb0a9, 0x764c}, //癌
	{0xb0aa, 0x853c}, //蔼
	{0xb0ab, 0x77ee}, //矮
	{0xb0ac, 0x827e}, //艾
	{0xb0ad, 0x788d}, //碍
	{0xb0ae, 0x7231}, //爱
	{0xb0af, 0x9698}, //隘
	{0xb0b0, 0x978d}, //鞍
	{0xb0b1, 0x6c28}, //氨
	{0xb0b2, 0x5b89}, //安
	{0xb0b3, 0x4ffa}, //俺
	{0xb0b4, 0x6309}, //按
	{0xb0b5, 0x6697}, //暗
	{0xb0b6, 0x5cb8}, //岸
	{0xb0b7, 0x80fa}, //胺
	{0xb0b8, 0x6848}, //案
	{0xb0b9, 0x80ae}, //肮
	{0xb0ba, 0x6602}, //昂
	{0xb0bb, 0x76ce}, //盎
	{0xb0bc, 0x51f9}, //凹
	{0xb0bd, 0x6556}, //敖
	{0xb0be, 0x71ac}, //熬
	{0xb0bf, 0x7ff1}, //翱
	{0xb0c0, 0x8884}, //袄
	{0xb0c1, 0x50b2}, //傲
	{0xb0c2, 0x5965}, //奥
	{0xb0c3, 0x61ca}, //懊
	{0xb0c4, 0x6fb3}, //澳
	{0xb0c5, 0x82ad}, //芭
	{0xb0c6, 0x634c}, //捌
	{0xb0c7, 0x6252}, //扒
	{0xb0c8, 0x53ed}, //叭
	{0xb0c9, 0x5427}, //吧
	{0xb0ca, 0x7b06}, //笆
	{0xb0cb, 0x516b}, //八
	{0xb0cc, 0x75a4}, //疤
	{0xb0cd, 0x5df4}, //巴
	{0xb0ce, 0x62d4}, //拔
	{0xb0cf, 0x8dcb}, //跋
	{0xb0d0, 0x9776}, //靶
	{0xb0d1, 0x628a}, //把
	{0xb0d2, 0x8019}, //耙
	{0xb0d3, 0x575d}, //坝
	{0xb0d4, 0x9738}, //霸
	{0xb0d5, 0x7f62}, //罢
	{0xb0d6, 0x7238}, //爸
	{0xb0d7, 0x767d}, //白
	{0xb0d8, 0x67cf}, //柏
	{0xb0d9, 0x767e}, //百
	{0xb0da, 0x6446}, //摆
	{0xb0db, 0x4f70}, //佰
	{0xb0dc, 0x8d25}, //败
	{0xb0dd, 0x62dc}, //拜
	{0xb0de, 0x7a17}, //稗
	{0xb0df, 0x6591}, //斑
	{0xb0e0, 0x73ed}, //班
	{0xb0e1, 0x642c}, //搬
	{0xb0e2, 0x6273}, //扳
	{0xb0e3, 0x822c}, //般
	{0xb0e4, 0x9881}, //颁
	{0xb0e5, 0x677f}, //板
	{0xb0e6, 0x7248}, //版
	{0xb0e7, 0x626e}, //扮
	{0xb0e8, 0x62cc}, //拌
	{0xb0e9, 0x4f34}, //伴
	{0xb0ea, 0x74e3}, //瓣
	{0xb0eb, 0x534a}, //半
	{0xb0ec, 0x529e}, //办
	{0xb0ed, 0x7eca}, //绊
	{0xb0ee, 0x90a6}, //邦
	{0xb0ef, 0x5e2e}, //帮
	{0xb0f0, 0x6886}, //梆
	{0xb0f1, 0x699c}, //榜
	{0xb0f2, 0x8180}, //膀
	{0xb0f3, 0x7ed1}, //绑
	{0xb0f4, 0x68d2}, //棒
	{0xb0f5, 0x78c5}, //磅
	{0xb0f6, 0x868c}, //蚌
	{0xb0f7, 0x9551}, //镑
	{0xb0f8, 0x508d}, //傍
	{0xb0f9, 0x8c24}, //谤
	{0xb0fa, 0x82de}, //苞
	{0xb0fb, 0x80de}, //胞
	{0xb0fc, 0x5305}, //包
	{0xb0fd, 0x8912}, //褒
	{0xb0fe, 0x5265}, //剥
	{0xb1a1, 0x8584}, //薄
	{0xb1a2, 0x96f9}, //雹
	{0xb1a3, 0x4fdd}, //保
	{0xb1a4, 0x5821}, //堡
	{0xb1a5, 0x9971}, //饱
	{0xb1a6, 0x5b9d}, //宝
	{0xb1a7, 0x62b1}, //抱
	{0xb1a8, 0x62a5}, //报
	{0xb1a9, 0x66b4}, //暴
	{0xb1aa, 0x8c79}, //豹
	{0xb1ab, 0x9c8d}, //鲍
	{0xb1ac, 0x7206}, //爆
	{0xb1ad, 0x676f}, //杯
	{0xb1ae, 0x7891}, //碑
	{0xb1af, 0x60b2}, //悲
	{0xb1b0, 0x5351}, //卑
	{0xb1b1, 0x5317}, //北
	{0xb1b2, 0x8f88}, //辈
	{0xb1b3, 0x80cc}, //背
	{0xb1b4, 0x8d1d}, //贝
	{0xb1b5, 0x94a1}, //钡
	{0xb1b6, 0x500d}, //倍
	{0xb1b7, 0x72c8}, //狈
	{0xb1b8, 0x5907}, //备
	{0xb1b9, 0x60eb}, //惫
	{0xb1ba, 0x7119}, //焙
	{0xb1bb, 0x88ab}, //被
	{0xb1bc, 0x5954}, //奔
	{0xb1bd, 0x82ef}, //苯
	{0xb1be, 0x672c}, //本
	{0xb1bf, 0x7b28}, //笨
	{0xb1c0, 0x5d29}, //崩
	{0xb1c1, 0x7ef7}, //绷
	{0xb1c2, 0x752d}, //甭
	{0xb1c3, 0x6cf5}, //泵
	{0xb1c4, 0x8e66}, //蹦
	{0xb1c5, 0x8ff8}, //迸
	{0xb1c6, 0x903c}, //逼
	{0xb1c7, 0x9f3b}, //鼻
	{0xb1c8, 0x6bd4}, //比
	{0xb1c9, 0x9119}, //鄙
	{0xb1ca, 0x7b14}, //笔
	{0xb1cb, 0x5f7c}, //彼
	{0xb1cc, 0x78a7}, //碧
	{0xb1cd, 0x84d6}, //蓖
	{0xb1ce, 0x853d}, //蔽
	{0xb1cf, 0x6bd5}, //毕
	{0xb1d0, 0x6bd9}, //毙
	{0xb1d1, 0x6bd6}, //毖
	{0xb1d2, 0x5e01}, //币
	{0xb1d3, 0x5e87}, //庇
	{0xb1d4, 0x75f9}, //痹
	{0xb1d5, 0x95ed}, //闭
	{0xb1d6, 0x655d}, //敝
	{0xb1d7, 0x5f0a}, //弊
	{0xb1d8, 0x5fc5}, //必
	{0xb1d9, 0x8f9f}, //辟
	{0xb1da, 0x58c1}, //壁
	{0xb1db, 0x81c2}, //臂
	{0xb1dc, 0x907f}, //避
	{0xb1dd, 0x965b}, //陛
	{0xb1de, 0x97ad}, //鞭
	{0xb1df, 0x8fb9}, //边
	{0xb1e0, 0x7f16}, //编
	{0xb1e1, 0x8d2c}, //贬
	{0xb1e2, 0x6241}, //扁
	{0xb1e3, 0x4fbf}, //便
	{0xb1e4, 0x53d8}, //变
	{0xb1e5, 0x535e}, //卞
	{0xb1e6, 0x8fa8}, //辨
	{0xb1e7, 0x8fa9}, //辩
	{0xb1e8, 0x8fab}, //辫
	{0xb1e9, 0x904d}, //遍
	{0xb1ea, 0x6807}, //标
	{0xb1eb, 0x5f6a}, //彪
	{0xb1ec, 0x8198}, //膘
	{0xb1ed, 0x8868}, //表
	{0xb1ee, 0x9cd6}, //鳖
	{0xb1ef, 0x618b}, //憋
	{0xb1f0, 0x522b}, //别
	{0xb1f1, 0x762a}, //瘪
	{0xb1f2, 0x5f6c}, //彬
	{0xb1f3, 0x658c}, //斌
	{0xb1f4, 0x6fd2}, //濒
	{0xb1f5, 0x6ee8}, //滨
	{0xb1f6, 0x5bbe}, //宾
	{0xb1f7, 0x6448}, //摈
	{0xb1f8, 0x5175}, //兵
	{0xb1f9, 0x51b0}, //冰
	{0xb1fa, 0x67c4}, //柄
	{0xb1fb, 0x4e19}, //丙
	{0xb1fc, 0x79c9}, //秉
	{0xb1fd, 0x997c}, //饼
	{0xb1fe, 0x70b3}, //炳
	{0xb2a1, 0x75c5}, //病
	{0xb2a2, 0x5e76}, //并
	{0xb2a3, 0x73bb}, //玻
	{0xb2a4, 0x83e0}, //菠
	{0xb2a5, 0x64ad}, //播
	{0xb2a6, 0x62e8}, //拨
	{0xb2a7, 0x94b5}, //钵
	{0xb2a8, 0x6ce2}, //波
	{0xb2a9, 0x535a}, //博
	{0xb2aa, 0x52c3}, //勃
	{0xb2ab, 0x640f}, //搏
	{0xb2ac, 0x94c2}, //铂
	{0xb2ad, 0x7b94}, //箔
	{0xb2ae, 0x4f2f}, //伯
	{0xb2af, 0x5e1b}, //帛
	{0xb2b0, 0x8236}, //舶
	{0xb2b1, 0x8116}, //脖
	{0xb2b2, 0x818a}, //膊
	{0xb2b3, 0x6e24}, //渤
	{0xb2b4, 0x6cca}, //泊
	{0xb2b5, 0x9a73}, //驳
	{0xb2b6, 0x6355}, //捕
	{0xb2b7, 0x535c}, //卜
	{0xb2b8, 0x54fa}, //哺
	{0xb2b9, 0x8865}, //补
	{0xb2ba, 0x57e0}, //埠
	{0xb2bb, 0x4e0d}, //不
	{0xb2bc, 0x5e03}, //布
	{0xb2bd, 0x6b65}, //步
	{0xb2be, 0x7c3f}, //簿
	{0xb2bf, 0x90e8}, //部
	{0xb2c0, 0x6016}, //怖
	{0xb2c1, 0x64e6}, //擦
	{0xb2c2, 0x731c}, //猜
	{0xb2c3, 0x88c1}, //裁
	{0xb2c4, 0x6750}, //材
	{0xb2c5, 0x624d}, //才
	{0xb2c6, 0x8d22}, //财
	{0xb2c7, 0x776c}, //睬
	{0xb2c8, 0x8e29}, //踩
	{0xb2c9, 0x91c7}, //采
	{0xb2ca, 0x5f69}, //彩
	{0xb2cb, 0x83dc}, //菜
	{0xb2cc, 0x8521}, //蔡
	{0xb2cd, 0x9910}, //餐
	{0xb2ce, 0x53c2}, //参
	{0xb2cf, 0x8695}, //蚕
	{0xb2d0, 0x6b8b}, //残
	{0xb2d1, 0x60ed}, //惭
	{0xb2d2, 0x60e8}, //惨
	{0xb2d3, 0x707f}, //灿
	{0xb2d4, 0x82cd}, //苍
	{0xb2d5, 0x8231}, //舱
	{0xb2d6, 0x4ed3}, //仓
	{0xb2d7, 0x6ca7}, //沧
	{0xb2d8, 0x85cf}, //藏
	{0xb2d9, 0x64cd}, //操
	{0xb2da, 0x7cd9}, //糙
	{0xb2db, 0x69fd}, //槽
	{0xb2dc, 0x66f9}, //曹
	{0xb2dd, 0x8349}, //草
	{0xb2de, 0x5395}, //厕
	{0xb2df, 0x7b56}, //策
	{0xb2e0, 0x4fa7}, //侧
	{0xb2e1, 0x518c}, //册
	{0xb2e2, 0x6d4b}, //测
	{0xb2e3, 0x5c42}, //层
	{0xb2e4, 0x8e6d}, //蹭
	{0xb2e5, 0x63d2}, //插
	{0xb2e6, 0x53c9}, //叉
	{0xb2e7, 0x832c}, //茬
	{0xb2e8, 0x8336}, //茶
	{0xb2e9, 0x67e5}, //查
	{0xb2ea, 0x78b4}, //碴
	{0xb2eb, 0x643d}, //搽
	{0xb2ec, 0x5bdf}, //察
	{0xb2ed, 0x5c94}, //岔
	{0xb2ee, 0x5dee}, //差
	{0xb2ef, 0x8be7}, //诧
	{0xb2f0, 0x62c6}, //拆
	{0xb2f1, 0x67f4}, //柴
	{0xb2f2, 0x8c7a}, //豺
	{0xb2f3, 0x6400}, //搀
	{0xb2f4, 0x63ba}, //掺
	{0xb2f5, 0x8749}, //蝉
	{0xb2f6, 0x998b}, //馋
	{0xb2f7, 0x8c17}, //谗
	{0xb2f8, 0x7f20}, //缠
	{0xb2f9, 0x94f2}, //铲
	{0xb2fa, 0x4ea7}, //产
	{0xb2fb, 0x9610}, //阐
	{0xb2fc, 0x98a4}, //颤
	{0xb2fd, 0x660c}, //昌
	{0xb2fe, 0x7316}, //猖
	{0xb3a1, 0x573a}, //场
	{0xb3a2, 0x5c1d}, //尝
	{0xb3a3, 0x5e38}, //常
	{0xb3a4, 0x957f}, //长
	{0xb3a5, 0x507f}, //偿
	{0xb3a6, 0x80a0}, //肠
	{0xb3a7, 0x5382}, //厂
	{0xb3a8, 0x655e}, //敞
	{0xb3a9, 0x7545}, //畅
	{0xb3aa, 0x5531}, //唱
	{0xb3ab, 0x5021}, //倡
	{0xb3ac, 0x8d85}, //超
	{0xb3ad, 0x6284}, //抄
	{0xb3ae, 0x949e}, //钞
	{0xb3af, 0x671d}, //朝
	{0xb3b0, 0x5632}, //嘲
	{0xb3b1, 0x6f6e}, //潮
	{0xb3b2, 0x5de2}, //巢
	{0xb3b3, 0x5435}, //吵
	{0xb3b4, 0x7092}, //炒
	{0xb3b5, 0x8f66}, //车
	{0xb3b6, 0x626f}, //扯
	{0xb3b7, 0x64a4}, //撤
	{0xb3b8, 0x63a3}, //掣
	{0xb3b9, 0x5f7b}, //彻
	{0xb3ba, 0x6f88}, //澈
	{0xb3bb, 0x90f4}, //郴
	{0xb3bc, 0x81e3}, //臣
	{0xb3bd, 0x8fb0}, //辰
	{0xb3be, 0x5c18}, //尘
	{0xb3bf, 0x6668}, //晨
	{0xb3c0, 0x5ff1}, //忱
	{0xb3c1, 0x6c89}, //沉
	{0xb3c2, 0x9648}, //陈
	{0xb3c3, 0x8d81}, //趁
	{0xb3c4, 0x886c}, //衬
	{0xb3c5, 0x6491}, //撑
	{0xb3c6, 0x79f0}, //称
	{0xb3c7, 0x57ce}, //城
	{0xb3c8, 0x6a59}, //橙
	{0xb3c9, 0x6210}, //成
	{0xb3ca, 0x5448}, //呈
	{0xb3cb, 0x4e58}, //乘
	{0xb3cc, 0x7a0b}, //程
	{0xb3cd, 0x60e9}, //惩
	{0xb3ce, 0x6f84}, //澄
	{0xb3cf, 0x8bda}, //诚
	{0xb3d0, 0x627f}, //承
	{0xb3d1, 0x901e}, //逞
	{0xb3d2, 0x9a8b}, //骋
	{0xb3d3, 0x79e4}, //秤
	{0xb3d4, 0x5403}, //吃
	{0xb3d5, 0x75f4}, //痴
	{0xb3d6, 0x6301}, //持
	{0xb3d7, 0x5319}, //匙
	{0xb3d8, 0x6c60}, //池
	{0xb3d9, 0x8fdf}, //迟
	{0xb3da, 0x5f1b}, //弛
	{0xb3db, 0x9a70}, //驰
	{0xb3dc, 0x803b}, //耻
	{0xb3dd, 0x9f7f}, //齿
	{0xb3de, 0x4f88}, //侈
	{0xb3df, 0x5c3a}, //尺
	{0xb3e0, 0x8d64}, //赤
	{0xb3e1, 0x7fc5}, //翅
	{0xb3e2, 0x65a5}, //斥
	{0xb3e3, 0x70bd}, //炽
	{0xb3e4, 0x5145}, //充
	{0xb3e5, 0x51b2}, //冲
	{0xb3e6, 0x866b}, //虫
	{0xb3e7, 0x5d07}, //崇
	{0xb3e8, 0x5ba0}, //宠
	{0xb3e9, 0x62bd}, //抽
	{0xb3ea, 0x916c}, //酬
	{0xb3eb, 0x7574}, //畴
	{0xb3ec, 0x8e0c}, //踌
	{0xb3ed, 0x7a20}, //稠
	{0xb3ee, 0x6101}, //愁
	{0xb3ef, 0x7b79}, //筹
	{0xb3f0, 0x4ec7}, //仇
	{0xb3f1, 0x7ef8}, //绸
	{0xb3f2, 0x7785}, //瞅
	{0xb3f3, 0x4e11}, //丑
	{0xb3f4, 0x81ed}, //臭
	{0xb3f5, 0x521d}, //初
	{0xb3f6, 0x51fa}, //出
	{0xb3f7, 0x6a71}, //橱
	{0xb3f8, 0x53a8}, //厨
	{0xb3f9, 0x8e87}, //躇
	{0xb3fa, 0x9504}, //锄
	{0xb3fb, 0x96cf}, //雏
	{0xb3fc, 0x6ec1}, //滁
	{0xb3fd, 0x9664}, //除
	{0xb3fe, 0x695a}, //楚
	{0xb4a1, 0x7840}, //础
	{0xb4a2, 0x50a8}, //储
	{0xb4a3, 0x77d7}, //矗
	{0xb4a4, 0x6410}, //搐
	{0xb4a5, 0x89e6}, //触
	{0xb4a6, 0x5904}, //处
	{0xb4a7, 0x63e3}, //揣
	{0xb4a8, 0x5ddd}, //川
	{0xb4a9, 0x7a7f}, //穿
	{0xb4aa, 0x693d}, //椽
	{0xb4ab, 0x4f20}, //传
	{0xb4ac, 0x8239}, //船
	{0xb4ad, 0x5598}, //喘
	{0xb4ae, 0x4e32}, //串
	{0xb4af, 0x75ae}, //疮
	{0xb4b0, 0x7a97}, //窗
	{0xb4b1, 0x5e62}, //幢
	{0xb4b2, 0x5e8a}, //床
	{0xb4b3, 0x95ef}, //闯
	{0xb4b4, 0x521b}, //创
	{0xb4b5, 0x5439}, //吹
	{0xb4b6, 0x708a}, //炊
	{0xb4b7, 0x6376}, //捶
	{0xb4b8, 0x9524}, //锤
	{0xb4b9, 0x5782}, //垂
	{0xb4ba, 0x6625}, //春
	{0xb4bb, 0x693f}, //椿
	{0xb4bc, 0x9187}, //醇
	{0xb4bd, 0x5507}, //唇
	{0xb4be, 0x6df3}, //淳
	{0xb4bf, 0x7eaf}, //纯
	{0xb4c0, 0x8822}, //蠢
	{0xb4c1, 0x6233}, //戳
	{0xb4c2, 0x7ef0}, //绰
	{0xb4c3, 0x75b5}, //疵
	{0xb4c4, 0x8328}, //茨
	{0xb4c5, 0x78c1}, //磁
	{0xb4c6, 0x96cc}, //雌
	{0xb4c7, 0x8f9e}, //辞
	{0xb4c8, 0x6148}, //慈
	{0xb4c9, 0x74f7}, //瓷
	{0xb4ca, 0x8bcd}, //词
	{0xb4cb, 0x6b64}, //此
	{0xb4cc, 0x523a}, //刺
	{0xb4cd, 0x8d50}, //赐
	{0xb4ce, 0x6b21}, //次
	{0xb4cf, 0x806a}, //聪
	{0xb4d0, 0x8471}, //葱
	{0xb4d1, 0x56f1}, //囱
	{0xb4d2, 0x5306}, //匆
	{0xb4d3, 0x4ece}, //从
	{0xb4d4, 0x4e1b}, //丛
	{0xb4d5, 0x51d1}, //凑
	{0xb4d6, 0x7c97}, //粗
	{0xb4d7, 0x918b}, //醋
	{0xb4d8, 0x7c07}, //簇
	{0xb4d9, 0x4fc3}, //促
	{0xb4da, 0x8e7f}, //蹿
	{0xb4db, 0x7be1}, //篡
	{0xb4dc, 0x7a9c}, //窜
	{0xb4dd, 0x6467}, //摧
	{0xb4de, 0x5d14}, //崔
	{0xb4df, 0x50ac}, //催
	{0xb4e0, 0x8106}, //脆
	{0xb4e1, 0x7601}, //瘁
	{0xb4e2, 0x7cb9}, //粹
	{0xb4e3, 0x6dec}, //淬
	{0xb4e4, 0x7fe0}, //翠
	{0xb4e5, 0x6751}, //村
	{0xb4e6, 0x5b58}, //存
	{0xb4e7, 0x5bf8}, //寸
	{0xb4e8, 0x78cb}, //磋
	{0xb4e9, 0x64ae}, //撮
	{0xb4ea, 0x6413}, //搓
	{0xb4eb, 0x63aa}, //措
	{0xb4ec, 0x632b}, //挫
	{0xb4ed, 0x9519}, //错
	{0xb4ee, 0x642d}, //搭
	{0xb4ef, 0x8fbe}, //达
	{0xb4f0, 0x7b54}, //答
	{0xb4f1, 0x7629}, //瘩
	{0xb4f2, 0x6253}, //打
	{0xb4f3, 0x5927}, //大
	{0xb4f4, 0x5446}, //呆
	{0xb4f5, 0x6b79}, //歹
	{0xb4f6, 0x50a3}, //傣
	{0xb4f7, 0x6234}, //戴
	{0xb4f8, 0x5e26}, //带
	{0xb4f9, 0x6b86}, //殆
	{0xb4fa, 0x4ee3}, //代
	{0xb4fb, 0x8d37}, //贷
	{0xb4fc, 0x888b}, //袋
	{0xb4fd, 0x5f85}, //待
	{0xb4fe, 0x902e}, //逮
	{0xb5a1, 0x6020}, //怠
	{0xb5a2, 0x803d}, //耽
	{0xb5a3, 0x62c5}, //担
	{0xb5a4, 0x4e39}, //丹
	{0xb5a5, 0x5355}, //单
	{0xb5a6, 0x90f8}, //郸
	{0xb5a7, 0x63b8}, //掸
	{0xb5a8, 0x80c6}, //胆
	{0xb5a9, 0x65e6}, //旦
	{0xb5aa, 0x6c2e}, //氮
	{0xb5ab, 0x4f46}, //但
	{0xb5ac, 0x60ee}, //惮
	{0xb5ad, 0x6de1}, //淡
	{0xb5ae, 0x8bde}, //诞
	{0xb5af, 0x5f39}, //弹
	{0xb5b0, 0x86cb}, //蛋
	{0xb5b1, 0x5f53}, //当
	{0xb5b2, 0x6321}, //挡
	{0xb5b3, 0x515a}, //党
	{0xb5b4, 0x8361}, //荡
	{0xb5b5, 0x6863}, //档
	{0xb5b6, 0x5200}, //刀
	{0xb5b7, 0x6363}, //捣
	{0xb5b8, 0x8e48}, //蹈
	{0xb5b9, 0x5012}, //倒
	{0xb5ba, 0x5c9b}, //岛
	{0xb5bb, 0x7977}, //祷
	{0xb5bc, 0x5bfc}, //导
	{0xb5bd, 0x5230}, //到
	{0xb5be, 0x7a3b}, //稻
	{0xb5bf, 0x60bc}, //悼
	{0xb5c0, 0x9053}, //道
	{0xb5c1, 0x76d7}, //盗
	{0xb5c2, 0x5fb7}, //德
	{0xb5c3, 0x5f97}, //得
	{0xb5c4, 0x7684}, //的
	{0xb5c5, 0x8e6c}, //蹬
	{0xb5c6, 0x706f}, //灯
	{0xb5c7, 0x767b}, //登
	{0xb5c8, 0x7b49}, //等
	{0xb5c9, 0x77aa}, //瞪
	{0xb5ca, 0x51f3}, //凳
	{0xb5cb, 0x9093}, //邓
	{0xb5cc, 0x5824}, //堤
	{0xb5cd, 0x4f4e}, //低
	{0xb5ce, 0x6ef4}, //滴
	{0xb5cf, 0x8fea}, //迪
	{0xb5d0, 0x654c}, //敌
	{0xb5d1, 0x7b1b}, //笛
	{0xb5d2, 0x72c4}, //狄
	{0xb5d3, 0x6da4}, //涤
	{0xb5d4, 0x7fdf}, //翟
	{0xb5d5, 0x5ae1}, //嫡
	{0xb5d6, 0x62b5}, //抵
	{0xb5d7, 0x5e95}, //底
	{0xb5d8, 0x5730}, //地
	{0xb5d9, 0x8482}, //蒂
	{0xb5da, 0x7b2c}, //第
	{0xb5db, 0x5e1d}, //帝
	{0xb5dc, 0x5f1f}, //弟
	{0xb5dd, 0x9012}, //递
	{0xb5de, 0x7f14}, //缔
	{0xb5df, 0x98a0}, //颠
	{0xb5e0, 0x6382}, //掂
	{0xb5e1, 0x6ec7}, //滇
	{0xb5e2, 0x7898}, //碘
	{0xb5e3, 0x70b9}, //点
	{0xb5e4, 0x5178}, //典
	{0xb5e5, 0x975b}, //靛
	{0xb5e6, 0x57ab}, //垫
	{0xb5e7, 0x7535}, //电
	{0xb5e8, 0x4f43}, //佃
	{0xb5e9, 0x7538}, //甸
	{0xb5ea, 0x5e97}, //店
	{0xb5eb, 0x60e6}, //惦
	{0xb5ec, 0x5960}, //奠
	{0xb5ed, 0x6dc0}, //淀
	{0xb5ee, 0x6bbf}, //殿
	{0xb5ef, 0x7889}, //碉
	{0xb5f0, 0x53fc}, //叼
	{0xb5f1, 0x96d5}, //雕
	{0xb5f2, 0x51cb}, //凋
	{0xb5f3, 0x5201}, //刁
	{0xb5f4, 0x6389}, //掉
	{0xb5f5, 0x540a}, //吊
	{0xb5f6, 0x9493}, //钓
	{0xb5f7, 0x8c03}, //调
	{0xb5f8, 0x8dcc}, //跌
	{0xb5f9, 0x7239}, //爹
	{0xb5fa, 0x789f}, //碟
	{0xb5fb, 0x8776}, //蝶
	{0xb5fc, 0x8fed}, //迭
	{0xb5fd, 0x8c0d}, //谍
	{0xb5fe, 0x53e0}, //叠
	{0xb6a1, 0x4e01}, //丁
	{0xb6a2, 0x76ef}, //盯
	{0xb6a3, 0x53ee}, //叮
	{0xb6a4, 0x9489}, //钉
	{0xb6a5, 0x9876}, //顶
	{0xb6a6, 0x9f0e}, //鼎
	{0xb6a7, 0x952d}, //锭
	{0xb6a8, 0x5b9a}, //定
	{0xb6a9, 0x8ba2}, //订
	{0xb6aa, 0x4e22}, //丢
	{0xb6ab, 0x4e1c}, //东
	{0xb6ac, 0x51ac}, //冬
	{0xb6ad, 0x8463}, //董
	{0xb6ae, 0x61c2}, //懂
	{0xb6af, 0x52a8}, //动
	{0xb6b0, 0x680b}, //栋
	{0xb6b1, 0x4f97}, //侗
	{0xb6b2, 0x606b}, //恫
	{0xb6b3, 0x51bb}, //冻
	{0xb6b4, 0x6d1e}, //洞
	{0xb6b5, 0x515c}, //兜
	{0xb6b6, 0x6296}, //抖
	{0xb6b7, 0x6597}, //斗
	{0xb6b8, 0x9661}, //陡
	{0xb6b9, 0x8c46}, //豆
	{0xb6ba, 0x9017}, //逗
	{0xb6bb, 0x75d8}, //痘
	{0xb6bc, 0x90fd}, //都
	{0xb6bd, 0x7763}, //督
	{0xb6be, 0x6bd2}, //毒
	{0xb6bf, 0x728a}, //犊
	{0xb6c0, 0x72ec}, //独
	{0xb6c1, 0x8bfb}, //读
	{0xb6c2, 0x5835}, //堵
	{0xb6c3, 0x7779}, //睹
	{0xb6c4, 0x8d4c}, //赌
	{0xb6c5, 0x675c}, //杜
	{0xb6c6, 0x9540}, //镀
	{0xb6c7, 0x809a}, //肚
	{0xb6c8, 0x5ea6}, //度
	{0xb6c9, 0x6e21}, //渡
	{0xb6ca, 0x5992}, //妒
	{0xb6cb, 0x7aef}, //端
	{0xb6cc, 0x77ed}, //短
	{0xb6cd, 0x953b}, //锻
	{0xb6ce, 0x6bb5}, //段
	{0xb6cf, 0x65ad}, //断
	{0xb6d0, 0x7f0e}, //缎
	{0xb6d1, 0x5806}, //堆
	{0xb6d2, 0x5151}, //兑
	{0xb6d3, 0x961f}, //队
	{0xb6d4, 0x5bf9}, //对
	{0xb6d5, 0x58a9}, //墩
	{0xb6d6, 0x5428}, //吨
	{0xb6d7, 0x8e72}, //蹲
	{0xb6d8, 0x6566}, //敦
	{0xb6d9, 0x987f}, //顿
	{0xb6da, 0x56e4}, //囤
	{0xb6db, 0x949d}, //钝
	{0xb6dc, 0x76fe}, //盾
	{0xb6dd, 0x9041}, //遁
	{0xb6de, 0x6387}, //掇
	{0xb6df, 0x54c6}, //哆
	{0xb6e0, 0x591a}, //多
	{0xb6e1, 0x593a}, //夺
	{0xb6e2, 0x579b}, //垛
	{0xb6e3, 0x8eb2}, //躲
	{0xb6e4, 0x6735}, //朵
	{0xb6e5, 0x8dfa}, //跺
	{0xb6e6, 0x8235}, //舵
	{0xb6e7, 0x5241}, //剁
	{0xb6e8, 0x60f0}, //惰
	{0xb6e9, 0x5815}, //堕
	{0xb6ea, 0x86fe}, //蛾
	{0xb6eb, 0x5ce8}, //峨
	{0xb6ec, 0x9e45}, //鹅
	{0xb6ed, 0x4fc4}, //俄
	{0xb6ee, 0x989d}, //额
	{0xb6ef, 0x8bb9}, //讹
	{0xb6f0, 0x5a25}, //娥
	{0xb6f1, 0x6076}, //恶
	{0xb6f2, 0x5384}, //厄
	{0xb6f3, 0x627c}, //扼
	{0xb6f4, 0x904f}, //遏
	{0xb6f5, 0x9102}, //鄂
	{0xb6f6, 0x997f}, //饿
	{0xb6f7, 0x6069}, //恩
	{0xb6f8, 0x800c}, //而
	{0xb6f9, 0x513f}, //儿
	{0xb6fa, 0x8033}, //耳
	{0xb6fb, 0x5c14}, //尔
	{0xb6fc, 0x9975}, //饵
	{0xb6fd, 0x6d31}, //洱
	{0xb6fe, 0x4e8c}, //二
	{0xb7a1, 0x8d30}, //贰
	{0xb7a2, 0x53d1}, //发
	{0xb7a3, 0x7f5a}, //罚
	{0xb7a4, 0x7b4f}, //筏
	{0xb7a5, 0x4f10}, //伐
	{0xb7a6, 0x4e4f}, //乏
	{0xb7a7, 0x9600}, //阀
	{0xb7a8, 0x6cd5}, //法
	{0xb7a9, 0x73d0}, //珐
	{0xb7aa, 0x85e9}, //藩
	{0xb7ab, 0x5e06}, //帆
	{0xb7ac, 0x756a}, //番
	{0xb7ad, 0x7ffb}, //翻
	{0xb7ae, 0x6a0a}, //樊
	{0xb7af, 0x77fe}, //矾
	{0xb7b0, 0x9492}, //钒
	{0xb7b1, 0x7e41}, //繁
	{0xb7b2, 0x51e1}, //凡
	{0xb7b3, 0x70e6}, //烦
	{0xb7b4, 0x53cd}, //反
	{0xb7b5, 0x8fd4}, //返
	{0xb7b6, 0x8303}, //范
	{0xb7b7, 0x8d29}, //贩
	{0xb7b8, 0x72af}, //犯
	{0xb7b9, 0x996d}, //饭
	{0xb7ba, 0x6cdb}, //泛
	{0xb7bb, 0x574a}, //坊
	{0xb7bc, 0x82b3}, //芳
	{0xb7bd, 0x65b9}, //方
	{0xb7be, 0x80aa}, //肪
	{0xb7bf, 0x623f}, //房
	{0xb7c0, 0x9632}, //防
	{0xb7c1, 0x59a8}, //妨
	{0xb7c2, 0x4eff}, //仿
	{0xb7c3, 0x8bbf}, //访
	{0xb7c4, 0x7eba}, //纺
	{0xb7c5, 0x653e}, //放
	{0xb7c6, 0x83f2}, //菲
	{0xb7c7, 0x975e}, //非
	{0xb7c8, 0x5561}, //啡
	{0xb7c9, 0x98de}, //飞
	{0xb7ca, 0x80a5}, //肥
	{0xb7cb, 0x532a}, //匪
	{0xb7cc, 0x8bfd}, //诽
	{0xb7cd, 0x5420}, //吠
	{0xb7ce, 0x80ba}, //肺
	{0xb7cf, 0x5e9f}, //废
	{0xb7d0, 0x6cb8}, //沸
	{0xb7d1, 0x8d39}, //费
	{0xb7d2, 0x82ac}, //芬
	{0xb7d3, 0x915a}, //酚
	{0xb7d4, 0x5429}, //吩
	{0xb7d5, 0x6c1b}, //氛
	{0xb7d6, 0x5206}, //分
	{0xb7d7, 0x7eb7}, //纷
	{0xb7d8, 0x575f}, //坟
	{0xb7d9, 0x711a}, //焚
	{0xb7da, 0x6c7e}, //汾
	{0xb7db, 0x7c89}, //粉
	{0xb7dc, 0x594b}, //奋
	{0xb7dd, 0x4efd}, //份
	{0xb7de, 0x5fff}, //忿
	{0xb7df, 0x6124}, //愤
	{0xb7e0, 0x7caa}, //粪
	{0xb7e1, 0x4e30}, //丰
	{0xb7e2, 0x5c01}, //封
	{0xb7e3, 0x67ab}, //枫
	{0xb7e4, 0x8702}, //蜂
	{0xb7e5, 0x5cf0}, //峰
	{0xb7e6, 0x950b}, //锋
	{0xb7e7, 0x98ce}, //风
	{0xb7e8, 0x75af}, //疯
	{0xb7e9, 0x70fd}, //烽
	{0xb7ea, 0x9022}, //逢
	{0xb7eb, 0x51af}, //冯
	{0xb7ec, 0x7f1d}, //缝
	{0xb7ed, 0x8bbd}, //讽
	{0xb7ee, 0x5949}, //奉
	{0xb7ef, 0x51e4}, //凤
	{0xb7f0, 0x4f5b}, //佛
	{0xb7f1, 0x5426}, //否
	{0xb7f2, 0x592b}, //夫
	{0xb7f3, 0x6577}, //敷
	{0xb7f4, 0x80a4}, //肤
	{0xb7f5, 0x5b75}, //孵
	{0xb7f6, 0x6276}, //扶
	{0xb7f7, 0x62c2}, //拂
	{0xb7f8, 0x8f90}, //辐
	{0xb7f9, 0x5e45}, //幅
	{0xb7fa, 0x6c1f}, //氟
	{0xb7fb, 0x7b26}, //符
	{0xb7fc, 0x4f0f}, //伏
	{0xb7fd, 0x4fd8}, //俘
	{0xb7fe, 0x670d}, //服
	{0xb8a1, 0x6d6e}, //浮
	{0xb8a2, 0x6daa}, //涪
	{0xb8a3, 0x798f}, //福
	{0xb8a4, 0x88b1}, //袱
	{0xb8a5, 0x5f17}, //弗
	{0xb8a6, 0x752b}, //甫
	{0xb8a7, 0x629a}, //抚
	{0xb8a8, 0x8f85}, //辅
	{0xb8a9, 0x4fef}, //俯
	{0xb8aa, 0x91dc}, //釜
	{0xb8ab, 0x65a7}, //斧
	{0xb8ac, 0x812f}, //脯
	{0xb8ad, 0x8151}, //腑
	{0xb8ae, 0x5e9c}, //府
	{0xb8af, 0x8150}, //腐
	{0xb8b0, 0x8d74}, //赴
	{0xb8b1, 0x526f}, //副
	{0xb8b2, 0x8986}, //覆
	{0xb8b3, 0x8d4b}, //赋
	{0xb8b4, 0x590d}, //复
	{0xb8b5, 0x5085}, //傅
	{0xb8b6, 0x4ed8}, //付
	{0xb8b7, 0x961c}, //阜
	{0xb8b8, 0x7236}, //父
	{0xb8b9, 0x8179}, //腹
	{0xb8ba, 0x8d1f}, //负
	{0xb8bb, 0x5bcc}, //富
	{0xb8bc, 0x8ba3}, //讣
	{0xb8bd, 0x9644}, //附
	{0xb8be, 0x5987}, //妇
	{0xb8bf, 0x7f1a}, //缚
	{0xb8c0, 0x5490}, //咐
	{0xb8c1, 0x5676}, //噶
	{0xb8c2, 0x560e}, //嘎
	{0xb8c3, 0x8be5}, //该
	{0xb8c4, 0x6539}, //改
	{0xb8c5, 0x6982}, //概
	{0xb8c6, 0x9499}, //钙
	{0xb8c7, 0x76d6}, //盖
	{0xb8c8, 0x6e89}, //溉
	{0xb8c9, 0x5e72}, //干
	{0xb8ca, 0x7518}, //甘
	{0xb8cb, 0x6746}, //杆
	{0xb8cc, 0x67d1}, //柑
	{0xb8cd, 0x7aff}, //竿
	{0xb8ce, 0x809d}, //肝
	{0xb8cf, 0x8d76}, //赶
	{0xb8d0, 0x611f}, //感
	{0xb8d1, 0x79c6}, //秆
	{0xb8d2, 0x6562}, //敢
	{0xb8d3, 0x8d63}, //赣
	{0xb8d4, 0x5188}, //冈
	{0xb8d5, 0x521a}, //刚
	{0xb8d6, 0x94a2}, //钢
	{0xb8d7, 0x7f38}, //缸
	{0xb8d8, 0x809b}, //肛
	{0xb8d9, 0x7eb2}, //纲
	{0xb8da, 0x5c97}, //岗
	{0xb8db, 0x6e2f}, //港
	{0xb8dc, 0x6760}, //杠
	{0xb8dd, 0x7bd9}, //篙
	{0xb8de, 0x768b}, //皋
	{0xb8df, 0x9ad8}, //高
	{0xb8e0, 0x818f}, //膏
	{0xb8e1, 0x7f94}, //羔
	{0xb8e2, 0x7cd5}, //糕
	{0xb8e3, 0x641e}, //搞
	{0xb8e4, 0x9550}, //镐
	{0xb8e5, 0x7a3f}, //稿
	{0xb8e6, 0x544a}, //告
	{0xb8e7, 0x54e5}, //哥
	{0xb8e8, 0x6b4c}, //歌
	{0xb8e9, 0x6401}, //搁
	{0xb8ea, 0x6208}, //戈
	{0xb8eb, 0x9e3d}, //鸽
	{0xb8ec, 0x80f3}, //胳
	{0xb8ed, 0x7599}, //疙
	{0xb8ee, 0x5272}, //割
	{0xb8ef, 0x9769}, //革
	{0xb8f0, 0x845b}, //葛
	{0xb8f1, 0x683c}, //格
	{0xb8f2, 0x86e4}, //蛤
	{0xb8f3, 0x9601}, //阁
	{0xb8f4, 0x9694}, //隔
	{0xb8f5, 0x94ec}, //铬
	{0xb8f6, 0x4e2a}, //个
	{0xb8f7, 0x5404}, //各
	{0xb8f8, 0x7ed9}, //给
	{0xb8f9, 0x6839}, //根
	{0xb8fa, 0x8ddf}, //跟
	{0xb8fb, 0x8015}, //耕
	{0xb8fc, 0x66f4}, //更
	{0xb8fd, 0x5e9a}, //庚
	{0xb8fe, 0x7fb9}, //羹
	{0xb9a1, 0x57c2}, //埂
	{0xb9a2, 0x803f}, //耿
	{0xb9a3, 0x6897}, //梗
	{0xb9a4, 0x5de5}, //工
	{0xb9a5, 0x653b}, //攻
	{0xb9a6, 0x529f}, //功
	{0xb9a7, 0x606d}, //恭
	{0xb9a8, 0x9f9a}, //龚
	{0xb9a9, 0x4f9b}, //供
	{0xb9aa, 0x8eac}, //躬
	{0xb9ab, 0x516c}, //公
	{0xb9ac, 0x5bab}, //宫
	{0xb9ad, 0x5f13}, //弓
	{0xb9ae, 0x5de9}, //巩
	{0xb9af, 0x6c5e}, //汞
	{0xb9b0, 0x62f1}, //拱
	{0xb9b1, 0x8d21}, //贡
	{0xb9b2, 0x5171}, //共
	{0xb9b3, 0x94a9}, //钩
	{0xb9b4, 0x52fe}, //勾
	{0xb9b5, 0x6c9f}, //沟
	{0xb9b6, 0x82df}, //苟
	{0xb9b7, 0x72d7}, //狗
	{0xb9b8, 0x57a2}, //垢
	{0xb9b9, 0x6784}, //构
	{0xb9ba, 0x8d2d}, //购
	{0xb9bb, 0x591f}, //够
	{0xb9bc, 0x8f9c}, //辜
	{0xb9bd, 0x83c7}, //菇
	{0xb9be, 0x5495}, //咕
	{0xb9bf, 0x7b8d}, //箍
	{0xb9c0, 0x4f30}, //估
	{0xb9c1, 0x6cbd}, //沽
	{0xb9c2, 0x5b64}, //孤
	{0xb9c3, 0x59d1}, //姑
	{0xb9c4, 0x9f13}, //鼓
	{0xb9c5, 0x53e4}, //古
	{0xb9c6, 0x86ca}, //蛊
	{0xb9c7, 0x9aa8}, //骨
	{0xb9c8, 0x8c37}, //谷
	{0xb9c9, 0x80a1}, //股
	{0xb9ca, 0x6545}, //故
	{0xb9cb, 0x987e}, //顾
	{0xb9cc, 0x56fa}, //固
	{0xb9cd, 0x96c7}, //雇
	{0xb9ce, 0x522e}, //刮
	{0xb9cf, 0x74dc}, //瓜
	{0xb9d0, 0x5250}, //剐
	{0xb9d1, 0x5be1}, //寡
	{0xb9d2, 0x6302}, //挂
	{0xb9d3, 0x8902}, //褂
	{0xb9d4, 0x4e56}, //乖
	{0xb9d5, 0x62d0}, //拐
	{0xb9d6, 0x602a}, //怪
	{0xb9d7, 0x68fa}, //棺
	{0xb9d8, 0x5173}, //关
	{0xb9d9, 0x5b98}, //官
	{0xb9da, 0x51a0}, //冠
	{0xb9db, 0x89c2}, //观
	{0xb9dc, 0x7ba1}, //管
	{0xb9dd, 0x9986}, //馆
	{0xb9de, 0x7f50}, //罐
	{0xb9df, 0x60ef}, //惯
	{0xb9e0, 0x704c}, //灌
	{0xb9e1, 0x8d2f}, //贯
	{0xb9e2, 0x5149}, //光
	{0xb9e3, 0x5e7f}, //广
	{0xb9e4, 0x901b}, //逛
	{0xb9e5, 0x7470}, //瑰
	{0xb9e6, 0x89c4}, //规
	{0xb9e7, 0x572d}, //圭
	{0xb9e8, 0x7845}, //硅
	{0xb9e9, 0x5f52}, //归
	{0xb9ea, 0x9f9f}, //龟
	{0xb9eb, 0x95fa}, //闺
	{0xb9ec, 0x8f68}, //轨
	{0xb9ed, 0x9b3c}, //鬼
	{0xb9ee, 0x8be1}, //诡
	{0xb9ef, 0x7678}, //癸
	{0xb9f0, 0x6842}, //桂
	{0xb9f1, 0x67dc}, //柜
	{0xb9f2, 0x8dea}, //跪
	{0xb9f3, 0x8d35}, //贵
	{0xb9f4, 0x523d}, //刽
	{0xb9f5, 0x8f8a}, //辊
	{0xb9f6, 0x6eda}, //滚
	{0xb9f7, 0x68cd}, //棍
	{0xb9f8, 0x9505}, //锅
	{0xb9f9, 0x90ed}, //郭
	{0xb9fa, 0x56fd}, //国
	{0xb9fb, 0x679c}, //果
	{0xb9fc, 0x88f9}, //裹
	{0xb9fd, 0x8fc7}, //过
	{0xb9fe, 0x54c8}, //哈
	{0xbaa1, 0x9ab8}, //骸
	{0xbaa2, 0x5b69}, //孩
	{0xbaa3, 0x6d77}, //海
	{0xbaa4, 0x6c26}, //氦
	{0xbaa5, 0x4ea5}, //亥
	{0xbaa6, 0x5bb3}, //害
	{0xbaa7, 0x9a87}, //骇
	{0xbaa8, 0x9163}, //酣
	{0xbaa9, 0x61a8}, //憨
	{0xbaaa, 0x90af}, //邯
	{0xbaab, 0x97e9}, //韩
	{0xbaac, 0x542b}, //含
	{0xbaad, 0x6db5}, //涵
	{0xbaae, 0x5bd2}, //寒
	{0xbaaf, 0x51fd}, //函
	{0xbab0, 0x558a}, //喊
	{0xbab1, 0x7f55}, //罕
	{0xbab2, 0x7ff0}, //翰
	{0xbab3, 0x64bc}, //撼
	{0xbab4, 0x634d}, //捍
	{0xbab5, 0x65f1}, //旱
	{0xbab6, 0x61be}, //憾
	{0xbab7, 0x608d}, //悍
	{0xbab8, 0x710a}, //焊
	{0xbab9, 0x6c57}, //汗
	{0xbaba, 0x6c49}, //汉
	{0xbabb, 0x592f}, //夯
	{0xbabc, 0x676d}, //杭
	{0xbabd, 0x822a}, //航
	{0xbabe, 0x58d5}, //壕
	{0xbabf, 0x568e}, //嚎
	{0xbac0, 0x8c6a}, //豪
	{0xbac1, 0x6beb}, //毫
	{0xbac2, 0x90dd}, //郝
	{0xbac3, 0x597d}, //好
	{0xbac4, 0x8017}, //耗
	{0xbac5, 0x53f7}, //号
	{0xbac6, 0x6d69}, //浩
	{0xbac7, 0x5475}, //呵
	{0xbac8, 0x559d}, //喝
	{0xbac9, 0x8377}, //荷
	{0xbaca, 0x83cf}, //菏
	{0xbacb, 0x6838}, //核
	{0xbacc, 0x79be}, //禾
	{0xbacd, 0x548c}, //和
	{0xbace, 0x4f55}, //何
	{0xbacf, 0x5408}, //合
	{0xbad0, 0x76d2}, //盒
	{0xbad1, 0x8c89}, //貉
	{0xbad2, 0x9602}, //阂
	{0xbad3, 0x6cb3}, //河
	{0xbad4, 0x6db8}, //涸
	{0xbad5, 0x8d6b}, //赫
	{0xbad6, 0x8910}, //褐
	{0xbad7, 0x9e64}, //鹤
	{0xbad8, 0x8d3a}, //贺
	{0xbad9, 0x563f}, //嘿
	{0xbada, 0x9ed1}, //黑
	{0xbadb, 0x75d5}, //痕
	{0xbadc, 0x5f88}, //很
	{0xbadd, 0x72e0}, //狠
	{0xbade, 0x6068}, //恨
	{0xbadf, 0x54fc}, //哼
	{0xbae0, 0x4ea8}, //亨
	{0xbae1, 0x6a2a}, //横
	{0xbae2, 0x8861}, //衡
	{0xbae3, 0x6052}, //恒
	{0xbae4, 0x8f70}, //轰
	{0xbae5, 0x54c4}, //哄
	{0xbae6, 0x70d8}, //烘
	{0xbae7, 0x8679}, //虹
	{0xbae8, 0x9e3f}, //鸿
	{0xbae9, 0x6d2a}, //洪
	{0xbaea, 0x5b8f}, //宏
	{0xbaeb, 0x5f18}, //弘
	{0xbaec, 0x7ea2}, //红
	{0xbaed, 0x5589}, //喉
	{0xbaee, 0x4faf}, //侯
	{0xbaef, 0x7334}, //猴
	{0xbaf0, 0x543c}, //吼
	{0xbaf1, 0x539a}, //厚
	{0xbaf2, 0x5019}, //候
	{0xbaf3, 0x540e}, //后
	{0xbaf4, 0x547c}, //呼
	{0xbaf5, 0x4e4e}, //乎
	{0xbaf6, 0x5ffd}, //忽
	{0xbaf7, 0x745a}, //瑚
	{0xbaf8, 0x58f6}, //壶
	{0xbaf9, 0x846b}, //葫
	{0xbafa, 0x80e1}, //胡
	{0xbafb, 0x8774}, //蝴
	{0xbafc, 0x72d0}, //狐
	{0xbafd, 0x7cca}, //糊
	{0xbafe, 0x6e56}, //湖
	{0xbba1, 0x5f27}, //弧
	{0xbba2, 0x864e}, //虎
	{0xbba3, 0x552c}, //唬
	{0xbba4, 0x62a4}, //护
	{0xbba5, 0x4e92}, //互
	{0xbba6, 0x6caa}, //沪
	{0xbba7, 0x6237}, //户
	{0xbba8, 0x82b1}, //花
	{0xbba9, 0x54d7}, //哗
	{0xbbaa, 0x534e}, //华
	{0xbbab, 0x733e}, //猾
	{0xbbac, 0x6ed1}, //滑
	{0xbbad, 0x753b}, //画
	{0xbbae, 0x5212}, //划
	{0xbbaf, 0x5316}, //化
	{0xbbb0, 0x8bdd}, //话
	{0xbbb1, 0x69d0}, //槐
	{0xbbb2, 0x5f8a}, //徊
	{0xbbb3, 0x6000}, //怀
	{0xbbb4, 0x6dee}, //淮
	{0xbbb5, 0x574f}, //坏
	{0xbbb6, 0x6b22}, //欢
	{0xbbb7, 0x73af}, //环
	{0xbbb8, 0x6853}, //桓
	{0xbbb9, 0x8fd8}, //还
	{0xbbba, 0x7f13}, //缓
	{0xbbbb, 0x6362}, //换
	{0xbbbc, 0x60a3}, //患
	{0xbbbd, 0x5524}, //唤
	{0xbbbe, 0x75ea}, //痪
	{0xbbbf, 0x8c62}, //豢
	{0xbbc0, 0x7115}, //焕
	{0xbbc1, 0x6da3}, //涣
	{0xbbc2, 0x5ba6}, //宦
	{0xbbc3, 0x5e7b}, //幻
	{0xbbc4, 0x8352}, //荒
	{0xbbc5, 0x614c}, //慌
	{0xbbc6, 0x9ec4}, //黄
	{0xbbc7, 0x78fa}, //磺
	{0xbbc8, 0x8757}, //蝗
	{0xbbc9, 0x7c27}, //簧
	{0xbbca, 0x7687}, //皇
	{0xbbcb, 0x51f0}, //凰
	{0xbbcc, 0x60f6}, //惶
	{0xbbcd, 0x714c}, //煌
	{0xbbce, 0x6643}, //晃
	{0xbbcf, 0x5e4c}, //幌
	{0xbbd0, 0x604d}, //恍
	{0xbbd1, 0x8c0e}, //谎
	{0xbbd2, 0x7070}, //灰
	{0xbbd3, 0x6325}, //挥
	{0xbbd4, 0x8f89}, //辉
	{0xbbd5, 0x5fbd}, //徽
	{0xbbd6, 0x6062}, //恢
	{0xbbd7, 0x86d4}, //蛔
	{0xbbd8, 0x56de}, //回
	{0xbbd9, 0x6bc1}, //毁
	{0xbbda, 0x6094}, //悔
	{0xbbdb, 0x6167}, //慧
	{0xbbdc, 0x5349}, //卉
	{0xbbdd, 0x60e0}, //惠
	{0xbbde, 0x6666}, //晦
	{0xbbdf, 0x8d3f}, //贿
	{0xbbe0, 0x79fd}, //秽
	{0xbbe1, 0x4f1a}, //会
	{0xbbe2, 0x70e9}, //烩
	{0xbbe3, 0x6c47}, //汇
	{0xbbe4, 0x8bb3}, //讳
	{0xbbe5, 0x8bf2}, //诲
	{0xbbe6, 0x7ed8}, //绘
	{0xbbe7, 0x8364}, //荤
	{0xbbe8, 0x660f}, //昏
	{0xbbe9, 0x5a5a}, //婚
	{0xbbea, 0x9b42}, //魂
	{0xbbeb, 0x6d51}, //浑
	{0xbbec, 0x6df7}, //混
	{0xbbed, 0x8c41}, //豁
	{0xbbee, 0x6d3b}, //活
	{0xbbef, 0x4f19}, //伙
	{0xbbf0, 0x706b}, //火
	{0xbbf1, 0x83b7}, //获
	{0xbbf2, 0x6216}, //或
	{0xbbf3, 0x60d1}, //惑
	{0xbbf4, 0x970d}, //霍
	{0xbbf5, 0x8d27}, //货
	{0xbbf6, 0x7978}, //祸
	{0xbbf7, 0x51fb}, //击
	{0xbbf8, 0x573e}, //圾
	{0xbbf9, 0x57fa}, //基
	{0xbbfa, 0x673a}, //机
	{0xbbfb, 0x7578}, //畸
	{0xbbfc, 0x7a3d}, //稽
	{0xbbfd, 0x79ef}, //积
	{0xbbfe, 0x7b95}, //箕
	{0xbca1, 0x808c}, //肌
	{0xbca2, 0x9965}, //饥
	{0xbca3, 0x8ff9}, //迹
	{0xbca4, 0x6fc0}, //激
	{0xbca5, 0x8ba5}, //讥
	{0xbca6, 0x9e21}, //鸡
	{0xbca7, 0x59ec}, //姬
	{0xbca8, 0x7ee9}, //绩
	{0xbca9, 0x7f09}, //缉
	{0xbcaa, 0x5409}, //吉
	{0xbcab, 0x6781}, //极
	{0xbcac, 0x68d8}, //棘
	{0xbcad, 0x8f91}, //辑
	{0xbcae, 0x7c4d}, //籍
	{0xbcaf, 0x96c6}, //集
	{0xbcb0, 0x53ca}, //及
	{0xbcb1, 0x6025}, //急
	{0xbcb2, 0x75be}, //疾
	{0xbcb3, 0x6c72}, //汲
	{0xbcb4, 0x5373}, //即
	{0xbcb5, 0x5ac9}, //嫉
	{0xbcb6, 0x7ea7}, //级
	{0xbcb7, 0x6324}, //挤
	{0xbcb8, 0x51e0}, //几
	{0xbcb9, 0x810a}, //脊
	{0xbcba, 0x5df1}, //己
	{0xbcbb, 0x84df}, //蓟
	{0xbcbc, 0x6280}, //技
	{0xbcbd, 0x5180}, //冀
	{0xbcbe, 0x5b63}, //季
	{0xbcbf, 0x4f0e}, //伎
	{0xbcc0, 0x796d}, //祭
	{0xbcc1, 0x5242}, //剂
	{0xbcc2, 0x60b8}, //悸
	{0xbcc3, 0x6d4e}, //济
	{0xbcc4, 0x5bc4}, //寄
	{0xbcc5, 0x5bc2}, //寂
	{0xbcc6, 0x8ba1}, //计
	{0xbcc7, 0x8bb0}, //记
	{0xbcc8, 0x65e2}, //既
	{0xbcc9, 0x5fcc}, //忌
	{0xbcca, 0x9645}, //际
	{0xbccb, 0x5993}, //妓
	{0xbccc, 0x7ee7}, //继
	{0xbccd, 0x7eaa}, //纪
	{0xbcce, 0x5609}, //嘉
	{0xbccf, 0x67b7}, //枷
	{0xbcd0, 0x5939}, //夹
	{0xbcd1, 0x4f73}, //佳
	{0xbcd2, 0x5bb6}, //家
	{0xbcd3, 0x52a0}, //加
	{0xbcd4, 0x835a}, //荚
	{0xbcd5, 0x988a}, //颊
	{0xbcd6, 0x8d3e}, //贾
	{0xbcd7, 0x7532}, //甲
	{0xbcd8, 0x94be}, //钾
	{0xbcd9, 0x5047}, //假
	{0xbcda, 0x7a3c}, //稼
	{0xbcdb, 0x4ef7}, //价
	{0xbcdc, 0x67b6}, //架
	{0xbcdd, 0x9a7e}, //驾
	{0xbcde, 0x5ac1}, //嫁
	{0xbcdf, 0x6b7c}, //歼
	{0xbce0, 0x76d1}, //监
	{0xbce1, 0x575a}, //坚
	{0xbce2, 0x5c16}, //尖
	{0xbce3, 0x7b3a}, //笺
	{0xbce4, 0x95f4}, //间
	{0xbce5, 0x714e}, //煎
	{0xbce6, 0x517c}, //兼
	{0xbce7, 0x80a9}, //肩
	{0xbce8, 0x8270}, //艰
	{0xbce9, 0x5978}, //奸
	{0xbcea, 0x7f04}, //缄
	{0xbceb, 0x8327}, //茧
	{0xbcec, 0x68c0}, //检
	{0xbced, 0x67ec}, //柬
	{0xbcee, 0x78b1}, //碱
	{0xbcef, 0x7877}, //硷
	{0xbcf0, 0x62e3}, //拣
	{0xbcf1, 0x6361}, //捡
	{0xbcf2, 0x7b80}, //简
	{0xbcf3, 0x4fed}, //俭
	{0xbcf4, 0x526a}, //剪
	{0xbcf5, 0x51cf}, //减
	{0xbcf6, 0x8350}, //荐
	{0xbcf7, 0x69db}, //槛
	{0xbcf8, 0x9274}, //鉴
	{0xbcf9, 0x8df5}, //践
	{0xbcfa, 0x8d31}, //贱
	{0xbcfb, 0x89c1}, //见
	{0xbcfc, 0x952e}, //键
	{0xbcfd, 0x7bad}, //箭
	{0xbcfe, 0x4ef6}, //件
	{0xbda1, 0x5065}, //健
	{0xbda2, 0x8230}, //舰
	{0xbda3, 0x5251}, //剑
	{0xbda4, 0x996f}, //饯
	{0xbda5, 0x6e10}, //渐
	{0xbda6, 0x6e85}, //溅
	{0xbda7, 0x6da7}, //涧
	{0xbda8, 0x5efa}, //建
	{0xbda9, 0x50f5}, //僵
	{0xbdaa, 0x59dc}, //姜
	{0xbdab, 0x5c06}, //将
	{0xbdac, 0x6d46}, //浆
	{0xbdad, 0x6c5f}, //江
	{0xbdae, 0x7586}, //疆
	{0xbdaf, 0x848b}, //蒋
	{0xbdb0, 0x6868}, //桨
	{0xbdb1, 0x5956}, //奖
	{0xbdb2, 0x8bb2}, //讲
	{0xbdb3, 0x5320}, //匠
	{0xbdb4, 0x9171}, //酱
	{0xbdb5, 0x964d}, //降
	{0xbdb6, 0x8549}, //蕉
	{0xbdb7, 0x6912}, //椒
	{0xbdb8, 0x7901}, //礁
	{0xbdb9, 0x7126}, //焦
	{0xbdba, 0x80f6}, //胶
	{0xbdbb, 0x4ea4}, //交
	{0xbdbc, 0x90ca}, //郊
	{0xbdbd, 0x6d47}, //浇
	{0xbdbe, 0x9a84}, //骄
	{0xbdbf, 0x5a07}, //娇
	{0xbdc0, 0x56bc}, //嚼
	{0xbdc1, 0x6405}, //搅
	{0xbdc2, 0x94f0}, //铰
	{0xbdc3, 0x77eb}, //矫
	{0xbdc4, 0x4fa5}, //侥
	{0xbdc5, 0x811a}, //脚
	{0xbdc6, 0x72e1}, //狡
	{0xbdc7, 0x89d2}, //角
	{0xbdc8, 0x997a}, //饺
	{0xbdc9, 0x7f34}, //缴
	{0xbdca, 0x7ede}, //绞
	{0xbdcb, 0x527f}, //剿
	{0xbdcc, 0x6559}, //教
	{0xbdcd, 0x9175}, //酵
	{0xbdce, 0x8f7f}, //轿
	{0xbdcf, 0x8f83}, //较
	{0xbdd0, 0x53eb}, //叫
	{0xbdd1, 0x7a96}, //窖
	{0xbdd2, 0x63ed}, //揭
	{0xbdd3, 0x63a5}, //接
	{0xbdd4, 0x7686}, //皆
	{0xbdd5, 0x79f8}, //秸
	{0xbdd6, 0x8857}, //街
	{0xbdd7, 0x9636}, //阶
	{0xbdd8, 0x622a}, //截
	{0xbdd9, 0x52ab}, //劫
	{0xbdda, 0x8282}, //节
	{0xbddb, 0x6854}, //桔
	{0xbddc, 0x6770}, //杰
	{0xbddd, 0x6377}, //捷
	{0xbdde, 0x776b}, //睫
	{0xbddf, 0x7aed}, //竭
	{0xbde0, 0x6d01}, //洁
	{0xbde1, 0x7ed3}, //结
	{0xbde2, 0x89e3}, //解
	{0xbde3, 0x59d0}, //姐
	{0xbde4, 0x6212}, //戒
	{0xbde5, 0x85c9}, //藉
	{0xbde6, 0x82a5}, //芥
	{0xbde7, 0x754c}, //界
	{0xbde8, 0x501f}, //借
	{0xbde9, 0x4ecb}, //介
	{0xbdea, 0x75a5}, //疥
	{0xbdeb, 0x8beb}, //诫
	{0xbdec, 0x5c4a}, //届
	{0xbded, 0x5dfe}, //巾
	{0xbdee, 0x7b4b}, //筋
	{0xbdef, 0x65a4}, //斤
	{0xbdf0, 0x91d1}, //金
	{0xbdf1, 0x4eca}, //今
	{0xbdf2, 0x6d25}, //津
	{0xbdf3, 0x895f}, //襟
	{0xbdf4, 0x7d27}, //紧
	{0xbdf5, 0x9526}, //锦
	{0xbdf6, 0x4ec5}, //仅
	{0xbdf7, 0x8c28}, //谨
	{0xbdf8, 0x8fdb}, //进
	{0xbdf9, 0x9773}, //靳
	{0xbdfa, 0x664b}, //晋
	{0xbdfb, 0x7981}, //禁
	{0xbdfc, 0x8fd1}, //近
	{0xbdfd, 0x70ec}, //烬
	{0xbdfe, 0x6d78}, //浸
	{0xbea1, 0x5c3d}, //尽
	{0xbea2, 0x52b2}, //劲
	{0xbea3, 0x8346}, //荆
	{0xbea4, 0x5162}, //兢
	{0xbea5, 0x830e}, //茎
	{0xbea6, 0x775b}, //睛
	{0xbea7, 0x6676}, //晶
	{0xbea8, 0x9cb8}, //鲸
	{0xbea9, 0x4eac}, //京
	{0xbeaa, 0x60ca}, //惊
	{0xbeab, 0x7cbe}, //精
	{0xbeac, 0x7cb3}, //粳
	{0xbead, 0x7ecf}, //经
	{0xbeae, 0x4e95}, //井
	{0xbeaf, 0x8b66}, //警
	{0xbeb0, 0x666f}, //景
	{0xbeb1, 0x9888}, //颈
	{0xbeb2, 0x9759}, //静
	{0xbeb3, 0x5883}, //境
	{0xbeb4, 0x656c}, //敬
	{0xbeb5, 0x955c}, //镜
	{0xbeb6, 0x5f84}, //径
	{0xbeb7, 0x75c9}, //痉
	{0xbeb8, 0x9756}, //靖
	{0xbeb9, 0x7adf}, //竟
	{0xbeba, 0x7ade}, //竞
	{0xbebb, 0x51c0}, //净
	{0xbebc, 0x70af}, //炯
	{0xbebd, 0x7a98}, //窘
	{0xbebe, 0x63ea}, //揪
	{0xbebf, 0x7a76}, //究
	{0xbec0, 0x7ea0}, //纠
	{0xbec1, 0x7396}, //玖
	{0xbec2, 0x97ed}, //韭
	{0xbec3, 0x4e45}, //久
	{0xbec4, 0x7078}, //灸
	{0xbec5, 0x4e5d}, //九
	{0xbec6, 0x9152}, //酒
	{0xbec7, 0x53a9}, //厩
	{0xbec8, 0x6551}, //救
	{0xbec9, 0x65e7}, //旧
	{0xbeca, 0x81fc}, //臼
	{0xbecb, 0x8205}, //舅
	{0xbecc, 0x548e}, //咎
	{0xbecd, 0x5c31}, //就
	{0xbece, 0x759a}, //疚
	{0xbecf, 0x97a0}, //鞠
	{0xbed0, 0x62d8}, //拘
	{0xbed1, 0x72d9}, //狙
	{0xbed2, 0x75bd}, //疽
	{0xbed3, 0x5c45}, //居
	{0xbed4, 0x9a79}, //驹
	{0xbed5, 0x83ca}, //菊
	{0xbed6, 0x5c40}, //局
	{0xbed7, 0x5480}, //咀
	{0xbed8, 0x77e9}, //矩
	{0xbed9, 0x4e3e}, //举
	{0xbeda, 0x6cae}, //沮
	{0xbedb, 0x805a}, //聚
	{0xbedc, 0x62d2}, //拒
	{0xbedd, 0x636e}, //据
	{0xbede, 0x5de8}, //巨
	{0xbedf, 0x5177}, //具
	{0xbee0, 0x8ddd}, //距
	{0xbee1, 0x8e1e}, //踞
	{0xbee2, 0x952f}, //锯
	{0xbee3, 0x4ff1}, //俱
	{0xbee4, 0x53e5}, //句
	{0xbee5, 0x60e7}, //惧
	{0xbee6, 0x70ac}, //炬
	{0xbee7, 0x5267}, //剧
	{0xbee8, 0x6350}, //捐
	{0xbee9, 0x9e43}, //鹃
	{0xbeea, 0x5a1f}, //娟
	{0xbeeb, 0x5026}, //倦
	{0xbeec, 0x7737}, //眷
	{0xbeed, 0x5377}, //卷
	{0xbeee, 0x7ee2}, //绢
	{0xbeef, 0x6485}, //撅
	{0xbef0, 0x652b}, //攫
	{0xbef1, 0x6289}, //抉
	{0xbef2, 0x6398}, //掘
	{0xbef3, 0x5014}, //倔
	{0xbef4, 0x7235}, //爵
	{0xbef5, 0x89c9}, //觉
	{0xbef6, 0x51b3}, //决
	{0xbef7, 0x8bc0}, //诀
	{0xbef8, 0x7edd}, //绝
	{0xbef9, 0x5747}, //均
	{0xbefa, 0x83cc}, //菌
	{0xbefb, 0x94a7}, //钧
	{0xbefc, 0x519b}, //军
	{0xbefd, 0x541b}, //君
	{0xbefe, 0x5cfb}, //峻
	{0xbfa1, 0x4fca}, //俊
	{0xbfa2, 0x7ae3}, //竣
	{0xbfa3, 0x6d5a}, //浚
	{0xbfa4, 0x90e1}, //郡
	{0xbfa5, 0x9a8f}, //骏
	{0xbfa6, 0x5580}, //喀
	{0xbfa7, 0x5496}, //咖
	{0xbfa8, 0x5361}, //卡
	{0xbfa9, 0x54af}, //咯
	{0xbfaa, 0x5f00}, //开
	{0xbfab, 0x63e9}, //揩
	{0xbfac, 0x6977}, //楷
	{0xbfad, 0x51ef}, //凯
	{0xbfae, 0x6168}, //慨
	{0xbfaf, 0x520a}, //刊
	{0xbfb0, 0x582a}, //堪
	{0xbfb1, 0x52d8}, //勘
	{0xbfb2, 0x574e}, //坎
	{0xbfb3, 0x780d}, //砍
	{0xbfb4, 0x770b}, //看
	{0xbfb5, 0x5eb7}, //康
	{0xbfb6, 0x6177}, //慷
	{0xbfb7, 0x7ce0}, //糠
	{0xbfb8, 0x625b}, //扛
	{0xbfb9, 0x6297}, //抗
	{0xbfba, 0x4ea2}, //亢
	{0xbfbb, 0x7095}, //炕
	{0xbfbc, 0x8003}, //考
	{0xbfbd, 0x62f7}, //拷
	{0xbfbe, 0x70e4}, //烤
	{0xbfbf, 0x9760}, //靠
	{0xbfc0, 0x5777}, //坷
	{0xbfc1, 0x82db}, //苛
	{0xbfc2, 0x67ef}, //柯
	{0xbfc3, 0x68f5}, //棵
	{0xbfc4, 0x78d5}, //磕
	{0xbfc5, 0x9897}, //颗
	{0xbfc6, 0x79d1}, //科
	{0xbfc7, 0x58f3}, //壳
	{0xbfc8, 0x54b3}, //咳
	{0xbfc9, 0x53ef}, //可
	{0xbfca, 0x6e34}, //渴
	{0xbfcb, 0x514b}, //克
	{0xbfcc, 0x523b}, //刻
	{0xbfcd, 0x5ba2}, //客
	{0xbfce, 0x8bfe}, //课
	{0xbfcf, 0x80af}, //肯
	{0xbfd0, 0x5543}, //啃
	{0xbfd1, 0x57a6}, //垦
	{0xbfd2, 0x6073}, //恳
	{0xbfd3, 0x5751}, //坑
	{0xbfd4, 0x542d}, //吭
	{0xbfd5, 0x7a7a}, //空
	{0xbfd6, 0x6050}, //恐
	{0xbfd7, 0x5b54}, //孔
	{0xbfd8, 0x63a7}, //控
	{0xbfd9, 0x62a0}, //抠
	{0xbfda, 0x53e3}, //口
	{0xbfdb, 0x6263}, //扣
	{0xbfdc, 0x5bc7}, //寇
	{0xbfdd, 0x67af}, //枯
	{0xbfde, 0x54ed}, //哭
	{0xbfdf, 0x7a9f}, //窟
	{0xbfe0, 0x82e6}, //苦
	{0xbfe1, 0x9177}, //酷
	{0xbfe2, 0x5e93}, //库
	{0xbfe3, 0x88e4}, //裤
	{0xbfe4, 0x5938}, //夸
	{0xbfe5, 0x57ae}, //垮
	{0xbfe6, 0x630e}, //挎
	{0xbfe7, 0x8de8}, //跨
	{0xbfe8, 0x80ef}, //胯
	{0xbfe9, 0x5757}, //块
	{0xbfea, 0x7b77}, //筷
	{0xbfeb, 0x4fa9}, //侩
	{0xbfec, 0x5feb}, //快
	{0xbfed, 0x5bbd}, //宽
	{0xbfee, 0x6b3e}, //款
	{0xbfef, 0x5321}, //匡
	{0xbff0, 0x7b50}, //筐
	{0xbff1, 0x72c2}, //狂
	{0xbff2, 0x6846}, //框
	{0xbff3, 0x77ff}, //矿
	{0xbff4, 0x7736}, //眶
	{0xbff5, 0x65f7}, //旷
	{0xbff6, 0x51b5}, //况
	{0xbff7, 0x4e8f}, //亏
	{0xbff8, 0x76d4}, //盔
	{0xbff9, 0x5cbf}, //岿
	{0xbffa, 0x7aa5}, //窥
	{0xbffb, 0x8475}, //葵
	{0xbffc, 0x594e}, //奎
	{0xbffd, 0x9b41}, //魁
	{0xbffe, 0x5080}, //傀
	{0xc0a1, 0x9988}, //馈
	{0xc0a2, 0x6127}, //愧
	{0xc0a3, 0x6e83}, //溃
	{0xc0a4, 0x5764}, //坤
	{0xc0a5, 0x6606}, //昆
	{0xc0a6, 0x6346}, //捆
	{0xc0a7, 0x56f0}, //困
	{0xc0a8, 0x62ec}, //括
	{0xc0a9, 0x6269}, //扩
	{0xc0aa, 0x5ed3}, //廓
	{0xc0ab, 0x9614}, //阔
	{0xc0ac, 0x5783}, //垃
	{0xc0ad, 0x62c9}, //拉
	{0xc0ae, 0x5587}, //喇
	{0xc0af, 0x8721}, //蜡
	{0xc0b0, 0x814a}, //腊
	{0xc0b1, 0x8fa3}, //辣
	{0xc0b2, 0x5566}, //啦
	{0xc0b3, 0x83b1}, //莱
	{0xc0b4, 0x6765}, //来
	{0xc0b5, 0x8d56}, //赖
	{0xc0b6, 0x84dd}, //蓝
	{0xc0b7, 0x5a6a}, //婪
	{0xc0b8, 0x680f}, //栏
	{0xc0b9, 0x62e6}, //拦
	{0xc0ba, 0x7bee}, //篮
	{0xc0bb, 0x9611}, //阑
	{0xc0bc, 0x5170}, //兰
	{0xc0bd, 0x6f9c}, //澜
	{0xc0be, 0x8c30}, //谰
	{0xc0bf, 0x63fd}, //揽
	{0xc0c0, 0x89c8}, //览
	{0xc0c1, 0x61d2}, //懒
	{0xc0c2, 0x7f06}, //缆
	{0xc0c3, 0x70c2}, //烂
	{0xc0c4, 0x6ee5}, //滥
	{0xc0c5, 0x7405}, //琅
	{0xc0c6, 0x6994}, //榔
	{0xc0c7, 0x72fc}, //狼
	{0xc0c8, 0x5eca}, //廊
	{0xc0c9, 0x90ce}, //郎
	{0xc0ca, 0x6717}, //朗
	{0xc0cb, 0x6d6a}, //浪
	{0xc0cc, 0x635e}, //捞
	{0xc0cd, 0x52b3}, //劳
	{0xc0ce, 0x7262}, //牢
	{0xc0cf, 0x8001}, //老
	{0xc0d0, 0x4f6c}, //佬
	{0xc0d1, 0x59e5}, //姥
	{0xc0d2, 0x916a}, //酪
	{0xc0d3, 0x70d9}, //烙
	{0xc0d4, 0x6d9d}, //涝
	{0xc0d5, 0x52d2}, //勒
	{0xc0d6, 0x4e50}, //乐
	{0xc0d7, 0x96f7}, //雷
	{0xc0d8, 0x956d}, //镭
	{0xc0d9, 0x857e}, //蕾
	{0xc0da, 0x78ca}, //磊
	{0xc0db, 0x7d2f}, //累
	{0xc0dc, 0x5121}, //儡
	{0xc0dd, 0x5792}, //垒
	{0xc0de, 0x64c2}, //擂
	{0xc0df, 0x808b}, //肋
	{0xc0e0, 0x7c7b}, //类
	{0xc0e1, 0x6cea}, //泪
	{0xc0e2, 0x68f1}, //棱
	{0xc0e3, 0x695e}, //楞
	{0xc0e4, 0x51b7}, //冷
	{0xc0e5, 0x5398}, //厘
	{0xc0e6, 0x68a8}, //梨
	{0xc0e7, 0x7281}, //犁
	{0xc0e8, 0x9ece}, //黎
	{0xc0e9, 0x7bf1}, //篱
	{0xc0ea, 0x72f8}, //狸
	{0xc0eb, 0x79bb}, //离
	{0xc0ec, 0x6f13}, //漓
	{0xc0ed, 0x7406}, //理
	{0xc0ee, 0x674e}, //李
	{0xc0ef, 0x91cc}, //里
	{0xc0f0, 0x9ca4}, //鲤
	{0xc0f1, 0x793c}, //礼
	{0xc0f2, 0x8389}, //莉
	{0xc0f3, 0x8354}, //荔
	{0xc0f4, 0x540f}, //吏
	{0xc0f5, 0x6817}, //栗
	{0xc0f6, 0x4e3d}, //丽
	{0xc0f7, 0x5389}, //厉
	{0xc0f8, 0x52b1}, //励
	{0xc0f9, 0x783e}, //砾
	{0xc0fa, 0x5386}, //历
	{0xc0fb, 0x5229}, //利
	{0xc0fc, 0x5088}, //傈
	{0xc0fd, 0x4f8b}, //例
	{0xc0fe, 0x4fd0}, //俐
	{0xc1a1, 0x75e2}, //痢
	{0xc1a2, 0x7acb}, //立
	{0xc1a3, 0x7c92}, //粒
	{0xc1a4, 0x6ca5}, //沥
	{0xc1a5, 0x96b6}, //隶
	{0xc1a6, 0x529b}, //力
	{0xc1a7, 0x7483}, //璃
	{0xc1a8, 0x54e9}, //哩
	{0xc1a9, 0x4fe9}, //俩
	{0xc1aa, 0x8054}, //联
	{0xc1ab, 0x83b2}, //莲
	{0xc1ac, 0x8fde}, //连
	{0xc1ad, 0x9570}, //镰
	{0xc1ae, 0x5ec9}, //廉
	{0xc1af, 0x601c}, //怜
	{0xc1b0, 0x6d9f}, //涟
	{0xc1b1, 0x5e18}, //帘
	{0xc1b2, 0x655b}, //敛
	{0xc1b3, 0x8138}, //脸
	{0xc1b4, 0x94fe}, //链
	{0xc1b5, 0x604b}, //恋
	{0xc1b6, 0x70bc}, //炼
	{0xc1b7, 0x7ec3}, //练
	{0xc1b8, 0x7cae}, //粮
	{0xc1b9, 0x51c9}, //凉
	{0xc1ba, 0x6881}, //梁
	{0xc1bb, 0x7cb1}, //粱
	{0xc1bc, 0x826f}, //良
	{0xc1bd, 0x4e24}, //两
	{0xc1be, 0x8f86}, //辆
	{0xc1bf, 0x91cf}, //量
	{0xc1c0, 0x667e}, //晾
	{0xc1c1, 0x4eae}, //亮
	{0xc1c2, 0x8c05}, //谅
	{0xc1c3, 0x64a9}, //撩
	{0xc1c4, 0x804a}, //聊
	{0xc1c5, 0x50da}, //僚
	{0xc1c6, 0x7597}, //疗
	{0xc1c7, 0x71ce}, //燎
	{0xc1c8, 0x5be5}, //寥
	{0xc1c9, 0x8fbd}, //辽
	{0xc1ca, 0x6f66}, //潦
	{0xc1cb, 0x4e86}, //了
	{0xc1cc, 0x6482}, //撂
	{0xc1cd, 0x9563}, //镣
	{0xc1ce, 0x5ed6}, //廖
	{0xc1cf, 0x6599}, //料
	{0xc1d0, 0x5217}, //列
	{0xc1d1, 0x88c2}, //裂
	{0xc1d2, 0x70c8}, //烈
	{0xc1d3, 0x52a3}, //劣
	{0xc1d4, 0x730e}, //猎
	{0xc1d5, 0x7433}, //琳
	{0xc1d6, 0x6797}, //林
	{0xc1d7, 0x78f7}, //磷
	{0xc1d8, 0x9716}, //霖
	{0xc1d9, 0x4e34}, //临
	{0xc1da, 0x90bb}, //邻
	{0xc1db, 0x9cde}, //鳞
	{0xc1dc, 0x6dcb}, //淋
	{0xc1dd, 0x51db}, //凛
	{0xc1de, 0x8d41}, //赁
	{0xc1df, 0x541d}, //吝
	{0xc1e0, 0x62ce}, //拎
	{0xc1e1, 0x73b2}, //玲
	{0xc1e2, 0x83f1}, //菱
	{0xc1e3, 0x96f6}, //零
	{0xc1e4, 0x9f84}, //龄
	{0xc1e5, 0x94c3}, //铃
	{0xc1e6, 0x4f36}, //伶
	{0xc1e7, 0x7f9a}, //羚
	{0xc1e8, 0x51cc}, //凌
	{0xc1e9, 0x7075}, //灵
	{0xc1ea, 0x9675}, //陵
	{0xc1eb, 0x5cad}, //岭
	{0xc1ec, 0x9886}, //领
	{0xc1ed, 0x53e6}, //另
	{0xc1ee, 0x4ee4}, //令
	{0xc1ef, 0x6e9c}, //溜
	{0xc1f0, 0x7409}, //琉
	{0xc1f1, 0x69b4}, //榴
	{0xc1f2, 0x786b}, //硫
	{0xc1f3, 0x998f}, //馏
	{0xc1f4, 0x7559}, //留
	{0xc1f5, 0x5218}, //刘
	{0xc1f6, 0x7624}, //瘤
	{0xc1f7, 0x6d41}, //流
	{0xc1f8, 0x67f3}, //柳
	{0xc1f9, 0x516d}, //六
	{0xc1fa, 0x9f99}, //龙
	{0xc1fb, 0x804b}, //聋
	{0xc1fc, 0x5499}, //咙
	{0xc1fd, 0x7b3c}, //笼
	{0xc1fe, 0x7abf}, //窿
	{0xc2a1, 0x9686}, //隆
	{0xc2a2, 0x5784}, //垄
	{0xc2a3, 0x62e2}, //拢
	{0xc2a4, 0x9647}, //陇
	{0xc2a5, 0x697c}, //楼
	{0xc2a6, 0x5a04}, //娄
	{0xc2a7, 0x6402}, //搂
	{0xc2a8, 0x7bd3}, //篓
	{0xc2a9, 0x6f0f}, //漏
	{0xc2aa, 0x964b}, //陋
	{0xc2ab, 0x82a6}, //芦
	{0xc2ac, 0x5362}, //卢
	{0xc2ad, 0x9885}, //颅
	{0xc2ae, 0x5e90}, //庐
	{0xc2af, 0x7089}, //炉
	{0xc2b0, 0x63b3}, //掳
	{0xc2b1, 0x5364}, //卤
	{0xc2b2, 0x864f}, //虏
	{0xc2b3, 0x9c81}, //鲁
	{0xc2b4, 0x9e93}, //麓
	{0xc2b5, 0x788c}, //碌
	{0xc2b6, 0x9732}, //露
	{0xc2b7, 0x8def}, //路
	{0xc2b8, 0x8d42}, //赂
	{0xc2b9, 0x9e7f}, //鹿
	{0xc2ba, 0x6f5e}, //潞
	{0xc2bb, 0x7984}, //禄
	{0xc2bc, 0x5f55}, //录
	{0xc2bd, 0x9646}, //陆
	{0xc2be, 0x622e}, //戮
	{0xc2bf, 0x9a74}, //驴
	{0xc2c0, 0x5415}, //吕
	{0xc2c1, 0x94dd}, //铝
	{0xc2c2, 0x4fa3}, //侣
	{0xc2c3, 0x65c5}, //旅
	{0xc2c4, 0x5c65}, //履
	{0xc2c5, 0x5c61}, //屡
	{0xc2c6, 0x7f15}, //缕
	{0xc2c7, 0x8651}, //虑
	{0xc2c8, 0x6c2f}, //氯
	{0xc2c9, 0x5f8b}, //律
	{0xc2ca, 0x7387}, //率
	{0xc2cb, 0x6ee4}, //滤
	{0xc2cc, 0x7eff}, //绿
	{0xc2cd, 0x5ce6}, //峦
	{0xc2ce, 0x631b}, //挛
	{0xc2cf, 0x5b6a}, //孪
	{0xc2d0, 0x6ee6}, //滦
	{0xc2d1, 0x5375}, //卵
	{0xc2d2, 0x4e71}, //乱
	{0xc2d3, 0x63a0}, //掠
	{0xc2d4, 0x7565}, //略
	{0xc2d5, 0x62a1}, //抡
	{0xc2d6, 0x8f6e}, //轮
	{0xc2d7, 0x4f26}, //伦
	{0xc2d8, 0x4ed1}, //仑
	{0xc2d9, 0x6ca6}, //沦
	{0xc2da, 0x7eb6}, //纶
	{0xc2db, 0x8bba}, //论
	{0xc2dc, 0x841d}, //萝
	{0xc2dd, 0x87ba}, //螺
	{0xc2de, 0x7f57}, //罗
	{0xc2df, 0x903b}, //逻
	{0xc2e0, 0x9523}, //锣
	{0xc2e1, 0x7ba9}, //箩
	{0xc2e2, 0x9aa1}, //骡
	{0xc2e3, 0x88f8}, //裸
	{0xc2e4, 0x843d}, //落
	{0xc2e5, 0x6d1b}, //洛
	{0xc2e6, 0x9a86}, //骆
	{0xc2e7, 0x7edc}, //络
	{0xc2e8, 0x5988}, //妈
	{0xc2e9, 0x9ebb}, //麻
	{0xc2ea, 0x739b}, //玛
	{0xc2eb, 0x7801}, //码
	{0xc2ec, 0x8682}, //蚂
	{0xc2ed, 0x9a6c}, //马
	{0xc2ee, 0x9a82}, //骂
	{0xc2ef, 0x561b}, //嘛
	{0xc2f0, 0x5417}, //吗
	{0xc2f1, 0x57cb}, //埋
	{0xc2f2, 0x4e70}, //买
	{0xc2f3, 0x9ea6}, //麦
	{0xc2f4, 0x5356}, //卖
	{0xc2f5, 0x8fc8}, //迈
	{0xc2f6, 0x8109}, //脉
	{0xc2f7, 0x7792}, //瞒
	{0xc2f8, 0x9992}, //馒
	{0xc2f9, 0x86ee}, //蛮
	{0xc2fa, 0x6ee1}, //满
	{0xc2fb, 0x8513}, //蔓
	{0xc2fc, 0x66fc}, //曼
	{0xc2fd, 0x6162}, //慢
	{0xc2fe, 0x6f2b}, //漫
	{0xc3a1, 0x8c29}, //谩
	{0xc3a2, 0x8292}, //芒
	{0xc3a3, 0x832b}, //茫
	{0xc3a4, 0x76f2}, //盲
	{0xc3a5, 0x6c13}, //氓
	{0xc3a6, 0x5fd9}, //忙
	{0xc3a7, 0x83bd}, //莽
	{0xc3a8, 0x732b}, //猫
	{0xc3a9, 0x8305}, //茅
	{0xc3aa, 0x951a}, //锚
	{0xc3ab, 0x6bdb}, //毛
	{0xc3ac, 0x77db}, //矛
	{0xc3ad, 0x94c6}, //铆
	{0xc3ae, 0x536f}, //卯
	{0xc3af, 0x8302}, //茂
	{0xc3b0, 0x5192}, //冒
	{0xc3b1, 0x5e3d}, //帽
	{0xc3b2, 0x8c8c}, //貌
	{0xc3b3, 0x8d38}, //贸
	{0xc3b4, 0x4e48}, //么
	{0xc3b5, 0x73ab}, //玫
	{0xc3b6, 0x679a}, //枚
	{0xc3b7, 0x6885}, //梅
	{0xc3b8, 0x9176}, //酶
	{0xc3b9, 0x9709}, //霉
	{0xc3ba, 0x7164}, //煤
	{0xc3bb, 0x6ca1}, //没
	{0xc3bc, 0x7709}, //眉
	{0xc3bd, 0x5a92}, //媒
	{0xc3be, 0x9541}, //镁
	{0xc3bf, 0x6bcf}, //每
	{0xc3c0, 0x7f8e}, //美
	{0xc3c1, 0x6627}, //昧
	{0xc3c2, 0x5bd0}, //寐
	{0xc3c3, 0x59b9}, //妹
	{0xc3c4, 0x5a9a}, //媚
	{0xc3c5, 0x95e8}, //门
	{0xc3c6, 0x95f7}, //闷
	{0xc3c7, 0x4eec}, //们
	{0xc3c8, 0x840c}, //萌
	{0xc3c9, 0x8499}, //蒙
	{0xc3ca, 0x6aac}, //檬
	{0xc3cb, 0x76df}, //盟
	{0xc3cc, 0x9530}, //锰
	{0xc3cd, 0x731b}, //猛
	{0xc3ce, 0x68a6}, //梦
	{0xc3cf, 0x5b5f}, //孟
	{0xc3d0, 0x772f}, //眯
	{0xc3d1, 0x919a}, //醚
	{0xc3d2, 0x9761}, //靡
	{0xc3d3, 0x7cdc}, //糜
	{0xc3d4, 0x8ff7}, //迷
	{0xc3d5, 0x8c1c}, //谜
	{0xc3d6, 0x5f25}, //弥
	{0xc3d7, 0x7c73}, //米
	{0xc3d8, 0x79d8}, //秘
	{0xc3d9, 0x89c5}, //觅
	{0xc3da, 0x6ccc}, //泌
	{0xc3db, 0x871c}, //蜜
	{0xc3dc, 0x5bc6}, //密
	{0xc3dd, 0x5e42}, //幂
	{0xc3de, 0x68c9}, //棉
	{0xc3df, 0x7720}, //眠
	{0xc3e0, 0x7ef5}, //绵
	{0xc3e1, 0x5195}, //冕
	{0xc3e2, 0x514d}, //免
	{0xc3e3, 0x52c9}, //勉
	{0xc3e4, 0x5a29}, //娩
	{0xc3e5, 0x7f05}, //缅
	{0xc3e6, 0x9762}, //面
	{0xc3e7, 0x82d7}, //苗
	{0xc3e8, 0x63cf}, //描
	{0xc3e9, 0x7784}, //瞄
	{0xc3ea, 0x85d0}, //藐
	{0xc3eb, 0x79d2}, //秒
	{0xc3ec, 0x6e3a}, //渺
	{0xc3ed, 0x5e99}, //庙
	{0xc3ee, 0x5999}, //妙
	{0xc3ef, 0x8511}, //蔑
	{0xc3f0, 0x706d}, //灭
	{0xc3f1, 0x6c11}, //民
	{0xc3f2, 0x62bf}, //抿
	{0xc3f3, 0x76bf}, //皿
	{0xc3f4, 0x654f}, //敏
	{0xc3f5, 0x60af}, //悯
	{0xc3f6, 0x95fd}, //闽
	{0xc3f7, 0x660e}, //明
	{0xc3f8, 0x879f}, //螟
	{0xc3f9, 0x9e23}, //鸣
	{0xc3fa, 0x94ed}, //铭
	{0xc3fb, 0x540d}, //名
	{0xc3fc, 0x547d}, //命
	{0xc3fd, 0x8c2c}, //谬
	{0xc3fe, 0x6478}, //摸
	{0xc4a1, 0x6479}, //摹
	{0xc4a2, 0x8611}, //蘑
	{0xc4a3, 0x6a21}, //模
	{0xc4a4, 0x819c}, //膜
	{0xc4a5, 0x78e8}, //磨
	{0xc4a6, 0x6469}, //摩
	{0xc4a7, 0x9b54}, //魔
	{0xc4a8, 0x62b9}, //抹
	{0xc4a9, 0x672b}, //末
	{0xc4aa, 0x83ab}, //莫
	{0xc4ab, 0x58a8}, //墨
	{0xc4ac, 0x9ed8}, //默
	{0xc4ad, 0x6cab}, //沫
	{0xc4ae, 0x6f20}, //漠
	{0xc4af, 0x5bde}, //寞
	{0xc4b0, 0x964c}, //陌
	{0xc4b1, 0x8c0b}, //谋
	{0xc4b2, 0x725f}, //牟
	{0xc4b3, 0x67d0}, //某
	{0xc4b4, 0x62c7}, //拇
	{0xc4b5, 0x7261}, //牡
	{0xc4b6, 0x4ea9}, //亩
	{0xc4b7, 0x59c6}, //姆
	{0xc4b8, 0x6bcd}, //母
	{0xc4b9, 0x5893}, //墓
	{0xc4ba, 0x66ae}, //暮
	{0xc4bb, 0x5e55}, //幕
	{0xc4bc, 0x52df}, //募
	{0xc4bd, 0x6155}, //慕
	{0xc4be, 0x6728}, //木
	{0xc4bf, 0x76ee}, //目
	{0xc4c0, 0x7766}, //睦
	{0xc4c1, 0x7267}, //牧
	{0xc4c2, 0x7a46}, //穆
	{0xc4c3, 0x62ff}, //拿
	{0xc4c4, 0x54ea}, //哪
	{0xc4c5, 0x5450}, //呐
	{0xc4c6, 0x94a0}, //钠
	{0xc4c7, 0x90a3}, //那
	{0xc4c8, 0x5a1c}, //娜
	{0xc4c9, 0x7eb3}, //纳
	{0xc4ca, 0x6c16}, //氖
	{0xc4cb, 0x4e43}, //乃
	{0xc4cc, 0x5976}, //奶
	{0xc4cd, 0x8010}, //耐
	{0xc4ce, 0x5948}, //奈
	{0xc4cf, 0x5357}, //南
	{0xc4d0, 0x7537}, //男
	{0xc4d1, 0x96be}, //难
	{0xc4d2, 0x56ca}, //囊
	{0xc4d3, 0x6320}, //挠
	{0xc4d4, 0x8111}, //脑
	{0xc4d5, 0x607c}, //恼
	{0xc4d6, 0x95f9}, //闹
	{0xc4d7, 0x6dd6}, //淖
	{0xc4d8, 0x5462}, //呢
	{0xc4d9, 0x9981}, //馁
	{0xc4da, 0x5185}, //内
	{0xc4db, 0x5ae9}, //嫩
	{0xc4dc, 0x80fd}, //能
	{0xc4dd, 0x59ae}, //妮
	{0xc4de, 0x9713}, //霓
	{0xc4df, 0x502a}, //倪
	{0xc4e0, 0x6ce5}, //泥
	{0xc4e1, 0x5c3c}, //尼
	{0xc4e2, 0x62df}, //拟
	{0xc4e3, 0x4f60}, //你
	{0xc4e4, 0x533f}, //匿
	{0xc4e5, 0x817b}, //腻
	{0xc4e6, 0x9006}, //逆
	{0xc4e7, 0x6eba}, //溺
	{0xc4e8, 0x852b}, //蔫
	{0xc4e9, 0x62c8}, //拈
	{0xc4ea, 0x5e74}, //年
	{0xc4eb, 0x78be}, //碾
	{0xc4ec, 0x64b5}, //撵
	{0xc4ed, 0x637b}, //捻
	{0xc4ee, 0x5ff5}, //念
	{0xc4ef, 0x5a18}, //娘
	{0xc4f0, 0x917f}, //酿
	{0xc4f1, 0x9e1f}, //鸟
	{0xc4f2, 0x5c3f}, //尿
	{0xc4f3, 0x634f}, //捏
	{0xc4f4, 0x8042}, //聂
	{0xc4f5, 0x5b7d}, //孽
	{0xc4f6, 0x556e}, //啮
	{0xc4f7, 0x954a}, //镊
	{0xc4f8, 0x954d}, //镍
	{0xc4f9, 0x6d85}, //涅
	{0xc4fa, 0x60a8}, //您
	{0xc4fb, 0x67e0}, //柠
	{0xc4fc, 0x72de}, //狞
	{0xc4fd, 0x51dd}, //凝
	{0xc4fe, 0x5b81}, //宁
	{0xc5a1, 0x62e7}, //拧
	{0xc5a2, 0x6cde}, //泞
	{0xc5a3, 0x725b}, //牛
	{0xc5a4, 0x626d}, //扭
	{0xc5a5, 0x94ae}, //钮
	{0xc5a6, 0x7ebd}, //纽
	{0xc5a7, 0x8113}, //脓
	{0xc5a8, 0x6d53}, //浓
	{0xc5a9, 0x519c}, //农
	{0xc5aa, 0x5f04}, //弄
	{0xc5ab, 0x5974}, //奴
	{0xc5ac, 0x52aa}, //努
	{0xc5ad, 0x6012}, //怒
	{0xc5ae, 0x5973}, //女
	{0xc5af, 0x6696}, //暖
	{0xc5b0, 0x8650}, //虐
	{0xc5b1, 0x759f}, //疟
	{0xc5b2, 0x632a}, //挪
	{0xc5b3, 0x61e6}, //懦
	{0xc5b4, 0x7cef}, //糯
	{0xc5b5, 0x8bfa}, //诺
	{0xc5b6, 0x54e6}, //哦
	{0xc5b7, 0x6b27}, //欧
	{0xc5b8, 0x9e25}, //鸥
	{0xc5b9, 0x6bb4}, //殴
	{0xc5ba, 0x85d5}, //藕
	{0xc5bb, 0x5455}, //呕
	{0xc5bc, 0x5076}, //偶
	{0xc5bd, 0x6ca4}, //沤
	{0xc5be, 0x556a}, //啪
	{0xc5bf, 0x8db4}, //趴
	{0xc5c0, 0x722c}, //爬
	{0xc5c1, 0x5e15}, //帕
	{0xc5c2, 0x6015}, //怕
	{0xc5c3, 0x7436}, //琶
	{0xc5c4, 0x62cd}, //拍
	{0xc5c5, 0x6392}, //排
	{0xc5c6, 0x724c}, //牌
	{0xc5c7, 0x5f98}, //徘
	{0xc5c8, 0x6e43}, //湃
	{0xc5c9, 0x6d3e}, //派
	{0xc5ca, 0x6500}, //攀
	{0xc5cb, 0x6f58}, //潘
	{0xc5cc, 0x76d8}, //盘
	{0xc5cd, 0x78d0}, //磐
	{0xc5ce, 0x76fc}, //盼
	{0xc5cf, 0x7554}, //畔
	{0xc5d0, 0x5224}, //判
	{0xc5d1, 0x53db}, //叛
	{0xc5d2, 0x4e53}, //乓
	{0xc5d3, 0x5e9e}, //庞
	{0xc5d4, 0x65c1}, //旁
	{0xc5d5, 0x802a}, //耪
	{0xc5d6, 0x80d6}, //胖
	{0xc5d7, 0x629b}, //抛
	{0xc5d8, 0x5486}, //咆
	{0xc5d9, 0x5228}, //刨
	{0xc5da, 0x70ae}, //炮
	{0xc5db, 0x888d}, //袍
	{0xc5dc, 0x8dd1}, //跑
	{0xc5dd, 0x6ce1}, //泡
	{0xc5de, 0x5478}, //呸
	{0xc5df, 0x80da}, //胚
	{0xc5e0, 0x57f9}, //培
	{0xc5e1, 0x88f4}, //裴
	{0xc5e2, 0x8d54}, //赔
	{0xc5e3, 0x966a}, //陪
	{0xc5e4, 0x914d}, //配
	{0xc5e5, 0x4f69}, //佩
	{0xc5e6, 0x6c9b}, //沛
	{0xc5e7, 0x55b7}, //喷
	{0xc5e8, 0x76c6}, //盆
	{0xc5e9, 0x7830}, //砰
	{0xc5ea, 0x62a8}, //抨
	{0xc5eb, 0x70f9}, //烹
	{0xc5ec, 0x6f8e}, //澎
	{0xc5ed, 0x5f6d}, //彭
	{0xc5ee, 0x84ec}, //蓬
	{0xc5ef, 0x68da}, //棚
	{0xc5f0, 0x787c}, //硼
	{0xc5f1, 0x7bf7}, //篷
	{0xc5f2, 0x81a8}, //膨
	{0xc5f3, 0x670b}, //朋
	{0xc5f4, 0x9e4f}, //鹏
	{0xc5f5, 0x6367}, //捧
	{0xc5f6, 0x78b0}, //碰
	{0xc5f7, 0x576f}, //坯
	{0xc5f8, 0x7812}, //砒
	{0xc5f9, 0x9739}, //霹
	{0xc5fa, 0x6279}, //批
	{0xc5fb, 0x62ab}, //披
	{0xc5fc, 0x5288}, //劈
	{0xc5fd, 0x7435}, //琵
	{0xc5fe, 0x6bd7}, //毗
	{0xc6a1, 0x5564}, //啤
	{0xc6a2, 0x813e}, //脾
	{0xc6a3, 0x75b2}, //疲
	{0xc6a4, 0x76ae}, //皮
	{0xc6a5, 0x5339}, //匹
	{0xc6a6, 0x75de}, //痞
	{0xc6a7, 0x50fb}, //僻
	{0xc6a8, 0x5c41}, //屁
	{0xc6a9, 0x8b6c}, //譬
	{0xc6aa, 0x7bc7}, //篇
	{0xc6ab, 0x504f}, //偏
	{0xc6ac, 0x7247}, //片
	{0xc6ad, 0x9a97}, //骗
	{0xc6ae, 0x98d8}, //飘
	{0xc6af, 0x6f02}, //漂
	{0xc6b0, 0x74e2}, //瓢
	{0xc6b1, 0x7968}, //票
	{0xc6b2, 0x6487}, //撇
	{0xc6b3, 0x77a5}, //瞥
	{0xc6b4, 0x62fc}, //拼
	{0xc6b5, 0x9891}, //频
	{0xc6b6, 0x8d2b}, //贫
	{0xc6b7, 0x54c1}, //品
	{0xc6b8, 0x8058}, //聘
	{0xc6b9, 0x4e52}, //乒
	{0xc6ba, 0x576a}, //坪
	{0xc6bb, 0x82f9}, //苹
	{0xc6bc, 0x840d}, //萍
	{0xc6bd, 0x5e73}, //平
	{0xc6be, 0x51ed}, //凭
	{0xc6bf, 0x74f6}, //瓶
	{0xc6c0, 0x8bc4}, //评
	{0xc6c1, 0x5c4f}, //屏
	{0xc6c2, 0x5761}, //坡
	{0xc6c3, 0x6cfc}, //泼
	{0xc6c4, 0x9887}, //颇
	{0xc6c5, 0x5a46}, //婆
	{0xc6c6, 0x7834}, //破
	{0xc6c7, 0x9b44}, //魄
	{0xc6c8, 0x8feb}, //迫
	{0xc6c9, 0x7c95}, //粕
	{0xc6ca, 0x5256}, //剖
	{0xc6cb, 0x6251}, //扑
	{0xc6cc, 0x94fa}, //铺
	{0xc6cd, 0x4ec6}, //仆
	{0xc6ce, 0x8386}, //莆
	{0xc6cf, 0x8461}, //葡
	{0xc6d0, 0x83e9}, //菩
	{0xc6d1, 0x84b2}, //蒲
	{0xc6d2, 0x57d4}, //埔
	{0xc6d3, 0x6734}, //朴
	{0xc6d4, 0x5703}, //圃
	{0xc6d5, 0x666e}, //普
	{0xc6d6, 0x6d66}, //浦
	{0xc6d7, 0x8c31}, //谱
	{0xc6d8, 0x66dd}, //曝
	{0xc6d9, 0x7011}, //瀑
	{0xc6da, 0x671f}, //期
	{0xc6db, 0x6b3a}, //欺
	{0xc6dc, 0x6816}, //栖
	{0xc6dd, 0x621a}, //戚
	{0xc6de, 0x59bb}, //妻
	{0xc6df, 0x4e03}, //七
	{0xc6e0, 0x51c4}, //凄
	{0xc6e1, 0x6f06}, //漆
	{0xc6e2, 0x67d2}, //柒
	{0xc6e3, 0x6c8f}, //沏
	{0xc6e4, 0x5176}, //其
	{0xc6e5, 0x68cb}, //棋
	{0xc6e6, 0x5947}, //奇
	{0xc6e7, 0x6b67}, //歧
	{0xc6e8, 0x7566}, //畦
	{0xc6e9, 0x5d0e}, //崎
	{0xc6ea, 0x8110}, //脐
	{0xc6eb, 0x9f50}, //齐
	{0xc6ec, 0x65d7}, //旗
	{0xc6ed, 0x7948}, //祈
	{0xc6ee, 0x7941}, //祁
	{0xc6ef, 0x9a91}, //骑
	{0xc6f0, 0x8d77}, //起
	{0xc6f1, 0x5c82}, //岂
	{0xc6f2, 0x4e5e}, //乞
	{0xc6f3, 0x4f01}, //企
	{0xc6f4, 0x542f}, //启
	{0xc6f5, 0x5951}, //契
	{0xc6f6, 0x780c}, //砌
	{0xc6f7, 0x5668}, //器
	{0xc6f8, 0x6c14}, //气
	{0xc6f9, 0x8fc4}, //迄
	{0xc6fa, 0x5f03}, //弃
	{0xc6fb, 0x6c7d}, //汽
	{0xc6fc, 0x6ce3}, //泣
	{0xc6fd, 0x8bab}, //讫
	{0xc6fe, 0x6390}, //掐
	{0xc7a1, 0x6070}, //恰
	{0xc7a2, 0x6d3d}, //洽
	{0xc7a3, 0x7275}, //牵
	{0xc7a4, 0x6266}, //扦
	{0xc7a5, 0x948e}, //钎
	{0xc7a6, 0x94c5}, //铅
	{0xc7a7, 0x5343}, //千
	{0xc7a8, 0x8fc1}, //迁
	{0xc7a9, 0x7b7e}, //签
	{0xc7aa, 0x4edf}, //仟
	{0xc7ab, 0x8c26}, //谦
	{0xc7ac, 0x4e7e}, //乾
	{0xc7ad, 0x9ed4}, //黔
	{0xc7ae, 0x94b1}, //钱
	{0xc7af, 0x94b3}, //钳
	{0xc7b0, 0x524d}, //前
	{0xc7b1, 0x6f5c}, //潜
	{0xc7b2, 0x9063}, //遣
	{0xc7b3, 0x6d45}, //浅
	{0xc7b4, 0x8c34}, //谴
	{0xc7b5, 0x5811}, //堑
	{0xc7b6, 0x5d4c}, //嵌
	{0xc7b7, 0x6b20}, //欠
	{0xc7b8, 0x6b49}, //歉
	{0xc7b9, 0x67aa}, //枪
	{0xc7ba, 0x545b}, //呛
	{0xc7bb, 0x8154}, //腔
	{0xc7bc, 0x7f8c}, //羌
	{0xc7bd, 0x5899}, //墙
	{0xc7be, 0x8537}, //蔷
	{0xc7bf, 0x5f3a}, //强
	{0xc7c0, 0x62a2}, //抢
	{0xc7c1, 0x6a47}, //橇
	{0xc7c2, 0x9539}, //锹
	{0xc7c3, 0x6572}, //敲
	{0xc7c4, 0x6084}, //悄
	{0xc7c5, 0x6865}, //桥
	{0xc7c6, 0x77a7}, //瞧
	{0xc7c7, 0x4e54}, //乔
	{0xc7c8, 0x4fa8}, //侨
	{0xc7c9, 0x5de7}, //巧
	{0xc7ca, 0x9798}, //鞘
	{0xc7cb, 0x64ac}, //撬
	{0xc7cc, 0x7fd8}, //翘
	{0xc7cd, 0x5ced}, //峭
	{0xc7ce, 0x4fcf}, //俏
	{0xc7cf, 0x7a8d}, //窍
	{0xc7d0, 0x5207}, //切
	{0xc7d1, 0x8304}, //茄
	{0xc7d2, 0x4e14}, //且
	{0xc7d3, 0x602f}, //怯
	{0xc7d4, 0x7a83}, //窃
	{0xc7d5, 0x94a6}, //钦
	{0xc7d6, 0x4fb5}, //侵
	{0xc7d7, 0x4eb2}, //亲
	{0xc7d8, 0x79e6}, //秦
	{0xc7d9, 0x7434}, //琴
	{0xc7da, 0x52e4}, //勤
	{0xc7db, 0x82b9}, //芹
	{0xc7dc, 0x64d2}, //擒
	{0xc7dd, 0x79bd}, //禽
	{0xc7de, 0x5bdd}, //寝
	{0xc7df, 0x6c81}, //沁
	{0xc7e0, 0x9752}, //青
	{0xc7e1, 0x8f7b}, //轻
	{0xc7e2, 0x6c22}, //氢
	{0xc7e3, 0x503e}, //倾
	{0xc7e4, 0x537f}, //卿
	{0xc7e5, 0x6e05}, //清
	{0xc7e6, 0x64ce}, //擎
	{0xc7e7, 0x6674}, //晴
	{0xc7e8, 0x6c30}, //氰
	{0xc7e9, 0x60c5}, //情
	{0xc7ea, 0x9877}, //顷
	{0xc7eb, 0x8bf7}, //请
	{0xc7ec, 0x5e86}, //庆
	{0xc7ed, 0x743c}, //琼
	{0xc7ee, 0x7a77}, //穷
	{0xc7ef, 0x79cb}, //秋
	{0xc7f0, 0x4e18}, //丘
	{0xc7f1, 0x90b1}, //邱
	{0xc7f2, 0x7403}, //球
	{0xc7f3, 0x6c42}, //求
	{0xc7f4, 0x56da}, //囚
	{0xc7f5, 0x914b}, //酋
	{0xc7f6, 0x6cc5}, //泅
	{0xc7f7, 0x8d8b}, //趋
	{0xc7f8, 0x533a}, //区
	{0xc7f9, 0x86c6}, //蛆
	{0xc7fa, 0x66f2}, //曲
	{0xc7fb, 0x8eaf}, //躯
	{0xc7fc, 0x5c48}, //屈
	{0xc7fd, 0x9a71}, //驱
	{0xc7fe, 0x6e20}, //渠
	{0xc8a1, 0x53d6}, //取
	{0xc8a2, 0x5a36}, //娶
	{0xc8a3, 0x9f8b}, //龋
	{0xc8a4, 0x8da3}, //趣
	{0xc8a5, 0x53bb}, //去
	{0xc8a6, 0x5708}, //圈
	{0xc8a7, 0x98a7}, //颧
	{0xc8a8, 0x6743}, //权
	{0xc8a9, 0x919b}, //醛
	{0xc8aa, 0x6cc9}, //泉
	{0xc8ab, 0x5168}, //全
	{0xc8ac, 0x75ca}, //痊
	{0xc8ad, 0x62f3}, //拳
	{0xc8ae, 0x72ac}, //犬
	{0xc8af, 0x5238}, //券
	{0xc8b0, 0x529d}, //劝
	{0xc8b1, 0x7f3a}, //缺
	{0xc8b2, 0x7094}, //炔
	{0xc8b3, 0x7638}, //瘸
	{0xc8b4, 0x5374}, //却
	{0xc8b5, 0x9e4a}, //鹊
	{0xc8b6, 0x69b7}, //榷
	{0xc8b7, 0x786e}, //确
	{0xc8b8, 0x96c0}, //雀
	{0xc8b9, 0x88d9}, //裙
	{0xc8ba, 0x7fa4}, //群
	{0xc8bb, 0x7136}, //然
	{0xc8bc, 0x71c3}, //燃
	{0xc8bd, 0x5189}, //冉
	{0xc8be, 0x67d3}, //染
	{0xc8bf, 0x74e4}, //瓤
	{0xc8c0, 0x58e4}, //壤
	{0xc8c1, 0x6518}, //攘
	{0xc8c2, 0x56b7}, //嚷
	{0xc8c3, 0x8ba9}, //让
	{0xc8c4, 0x9976}, //饶
	{0xc8c5, 0x6270}, //扰
	{0xc8c6, 0x7ed5}, //绕
	{0xc8c7, 0x60f9}, //惹
	{0xc8c8, 0x70ed}, //热
	{0xc8c9, 0x58ec}, //壬
	{0xc8ca, 0x4ec1}, //仁
	{0xc8cb, 0x4eba}, //人
	{0xc8cc, 0x5fcd}, //忍
	{0xc8cd, 0x97e7}, //韧
	{0xc8ce, 0x4efb}, //任
	{0xc8cf, 0x8ba4}, //认
	{0xc8d0, 0x5203}, //刃
	{0xc8d1, 0x598a}, //妊
	{0xc8d2, 0x7eab}, //纫
	{0xc8d3, 0x6254}, //扔
	{0xc8d4, 0x4ecd}, //仍
	{0xc8d5, 0x65e5}, //日
	{0xc8d6, 0x620e}, //戎
	{0xc8d7, 0x8338}, //茸
	{0xc8d8, 0x84c9}, //蓉
	{0xc8d9, 0x8363}, //荣
	{0xc8da, 0x878d}, //融
	{0xc8db, 0x7194}, //熔
	{0xc8dc, 0x6eb6}, //溶
	{0xc8dd, 0x5bb9}, //容
	{0xc8de, 0x7ed2}, //绒
	{0xc8df, 0x5197}, //冗
	{0xc8e0, 0x63c9}, //揉
	{0xc8e1, 0x67d4}, //柔
	{0xc8e2, 0x8089}, //肉
	{0xc8e3, 0x8339}, //茹
	{0xc8e4, 0x8815}, //蠕
	{0xc8e5, 0x5112}, //儒
	{0xc8e6, 0x5b7a}, //孺
	{0xc8e7, 0x5982}, //如
	{0xc8e8, 0x8fb1}, //辱
	{0xc8e9, 0x4e73}, //乳
	{0xc8ea, 0x6c5d}, //汝
	{0xc8eb, 0x5165}, //入
	{0xc8ec, 0x8925}, //褥
	{0xc8ed, 0x8f6f}, //软
	{0xc8ee, 0x962e}, //阮
	{0xc8ef, 0x854a}, //蕊
	{0xc8f0, 0x745e}, //瑞
	{0xc8f1, 0x9510}, //锐
	{0xc8f2, 0x95f0}, //闰
	{0xc8f3, 0x6da6}, //润
	{0xc8f4, 0x82e5}, //若
	{0xc8f5, 0x5f31}, //弱
	{0xc8f6, 0x6492}, //撒
	{0xc8f7, 0x6d12}, //洒
	{0xc8f8, 0x8428}, //萨
	{0xc8f9, 0x816e}, //腮
	{0xc8fa, 0x9cc3}, //鳃
	{0xc8fb, 0x585e}, //塞
	{0xc8fc, 0x8d5b}, //赛
	{0xc8fd, 0x4e09}, //三
	{0xc8fe, 0x53c1}, //叁
	{0xc9a1, 0x4f1e}, //伞
	{0xc9a2, 0x6563}, //散
	{0xc9a3, 0x6851}, //桑
	{0xc9a4, 0x55d3}, //嗓
	{0xc9a5, 0x4e27}, //丧
	{0xc9a6, 0x6414}, //搔
	{0xc9a7, 0x9a9a}, //骚
	{0xc9a8, 0x626b}, //扫
	{0xc9a9, 0x5ac2}, //嫂
	{0xc9aa, 0x745f}, //瑟
	{0xc9ab, 0x8272}, //色
	{0xc9ac, 0x6da9}, //涩
	{0xc9ad, 0x68ee}, //森
	{0xc9ae, 0x50e7}, //僧
	{0xc9af, 0x838e}, //莎
	{0xc9b0, 0x7802}, //砂
	{0xc9b1, 0x6740}, //杀
	{0xc9b2, 0x5239}, //刹
	{0xc9b3, 0x6c99}, //沙
	{0xc9b4, 0x7eb1}, //纱
	{0xc9b5, 0x50bb}, //傻
	{0xc9b6, 0x5565}, //啥
	{0xc9b7, 0x715e}, //煞
	{0xc9b8, 0x7b5b}, //筛
	{0xc9b9, 0x6652}, //晒
	{0xc9ba, 0x73ca}, //珊
	{0xc9bb, 0x82eb}, //苫
	{0xc9bc, 0x6749}, //杉
	{0xc9bd, 0x5c71}, //山
	{0xc9be, 0x5220}, //删
	{0xc9bf, 0x717d}, //煽
	{0xc9c0, 0x886b}, //衫
	{0xc9c1, 0x95ea}, //闪
	{0xc9c2, 0x9655}, //陕
	{0xc9c3, 0x64c5}, //擅
	{0xc9c4, 0x8d61}, //赡
	{0xc9c5, 0x81b3}, //膳
	{0xc9c6, 0x5584}, //善
	{0xc9c7, 0x6c55}, //汕
	{0xc9c8, 0x6247}, //扇
	{0xc9c9, 0x7f2e}, //缮
	{0xc9ca, 0x5892}, //墒
	{0xc9cb, 0x4f24}, //伤
	{0xc9cc, 0x5546}, //商
	{0xc9cd, 0x8d4f}, //赏
	{0xc9ce, 0x664c}, //晌
	{0xc9cf, 0x4e0a}, //上
	{0xc9d0, 0x5c1a}, //尚
	{0xc9d1, 0x88f3}, //裳
	{0xc9d2, 0x68a2}, //梢
	{0xc9d3, 0x634e}, //捎
	{0xc9d4, 0x7a0d}, //稍
	{0xc9d5, 0x70e7}, //烧
	{0xc9d6, 0x828d}, //芍
	{0xc9d7, 0x52fa}, //勺
	{0xc9d8, 0x97f6}, //韶
	{0xc9d9, 0x5c11}, //少
	{0xc9da, 0x54e8}, //哨
	{0xc9db, 0x90b5}, //邵
	{0xc9dc, 0x7ecd}, //绍
	{0xc9dd, 0x5962}, //奢
	{0xc9de, 0x8d4a}, //赊
	{0xc9df, 0x86c7}, //蛇
	{0xc9e0, 0x820c}, //舌
	{0xc9e1, 0x820d}, //舍
	{0xc9e2, 0x8d66}, //赦
	{0xc9e3, 0x6444}, //摄
	{0xc9e4, 0x5c04}, //射
	{0xc9e5, 0x6151}, //慑
	{0xc9e6, 0x6d89}, //涉
	{0xc9e7, 0x793e}, //社
	{0xc9e8, 0x8bbe}, //设
	{0xc9e9, 0x7837}, //砷
	{0xc9ea, 0x7533}, //申
	{0xc9eb, 0x547b}, //呻
	{0xc9ec, 0x4f38}, //伸
	{0xc9ed, 0x8eab}, //身
	{0xc9ee, 0x6df1}, //深
	{0xc9ef, 0x5a20}, //娠
	{0xc9f0, 0x7ec5}, //绅
	{0xc9f1, 0x795e}, //神
	{0xc9f2, 0x6c88}, //沈
	{0xc9f3, 0x5ba1}, //审
	{0xc9f4, 0x5a76}, //婶
	{0xc9f5, 0x751a}, //甚
	{0xc9f6, 0x80be}, //肾
	{0xc9f7, 0x614e}, //慎
	{0xc9f8, 0x6e17}, //渗
	{0xc9f9, 0x58f0}, //声
	{0xc9fa, 0x751f}, //生
	{0xc9fb, 0x7525}, //甥
	{0xc9fc, 0x7272}, //牲
	{0xc9fd, 0x5347}, //升
	{0xc9fe, 0x7ef3}, //绳
	{0xcaa1, 0x7701}, //省
	{0xcaa2, 0x76db}, //盛
	{0xcaa3, 0x5269}, //剩
	{0xcaa4, 0x80dc}, //胜
	{0xcaa5, 0x5723}, //圣
	{0xcaa6, 0x5e08}, //师
	{0xcaa7, 0x5931}, //失
	{0xcaa8, 0x72ee}, //狮
	{0xcaa9, 0x65bd}, //施
	{0xcaaa, 0x6e7f}, //湿
	{0xcaab, 0x8bd7}, //诗
	{0xcaac, 0x5c38}, //尸
	{0xcaad, 0x8671}, //虱
	{0xcaae, 0x5341}, //十
	{0xcaaf, 0x77f3}, //石
	{0xcab0, 0x62fe}, //拾
	{0xcab1, 0x65f6}, //时
	{0xcab2, 0x4ec0}, //什
	{0xcab3, 0x98df}, //食
	{0xcab4, 0x8680}, //蚀
	{0xcab5, 0x5b9e}, //实
	{0xcab6, 0x8bc6}, //识
	{0xcab7, 0x53f2}, //史
	{0xcab8, 0x77e2}, //矢
	{0xcab9, 0x4f7f}, //使
	{0xcaba, 0x5c4e}, //屎
	{0xcabb, 0x9a76}, //驶
	{0xcabc, 0x59cb}, //始
	{0xcabd, 0x5f0f}, //式
	{0xcabe, 0x793a}, //示
	{0xcabf, 0x58eb}, //士
	{0xcac0, 0x4e16}, //世
	{0xcac1, 0x67ff}, //柿
	{0xcac2, 0x4e8b}, //事
	{0xcac3, 0x62ed}, //拭
	{0xcac4, 0x8a93}, //誓
	{0xcac5, 0x901d}, //逝
	{0xcac6, 0x52bf}, //势
	{0xcac7, 0x662f}, //是
	{0xcac8, 0x55dc}, //嗜
	{0xcac9, 0x566c}, //噬
	{0xcaca, 0x9002}, //适
	{0xcacb, 0x4ed5}, //仕
	{0xcacc, 0x4f8d}, //侍
	{0xcacd, 0x91ca}, //释
	{0xcace, 0x9970}, //饰
	{0xcacf, 0x6c0f}, //氏
	{0xcad0, 0x5e02}, //市
	{0xcad1, 0x6043}, //恃
	{0xcad2, 0x5ba4}, //室
	{0xcad3, 0x89c6}, //视
	{0xcad4, 0x8bd5}, //试
	{0xcad5, 0x6536}, //收
	{0xcad6, 0x624b}, //手
	{0xcad7, 0x9996}, //首
	{0xcad8, 0x5b88}, //守
	{0xcad9, 0x5bff}, //寿
	{0xcada, 0x6388}, //授
	{0xcadb, 0x552e}, //售
	{0xcadc, 0x53d7}, //受
	{0xcadd, 0x7626}, //瘦
	{0xcade, 0x517d}, //兽
	{0xcadf, 0x852c}, //蔬
	{0xcae0, 0x67a2}, //枢
	{0xcae1, 0x68b3}, //梳
	{0xcae2, 0x6b8a}, //殊
	{0xcae3, 0x6292}, //抒
	{0xcae4, 0x8f93}, //输
	{0xcae5, 0x53d4}, //叔
	{0xcae6, 0x8212}, //舒
	{0xcae7, 0x6dd1}, //淑
	{0xcae8, 0x758f}, //疏
	{0xcae9, 0x4e66}, //书
	{0xcaea, 0x8d4e}, //赎
	{0xcaeb, 0x5b70}, //孰
	{0xcaec, 0x719f}, //熟
	{0xcaed, 0x85af}, //薯
	{0xcaee, 0x6691}, //暑
	{0xcaef, 0x66d9}, //曙
	{0xcaf0, 0x7f72}, //署
	{0xcaf1, 0x8700}, //蜀
	{0xcaf2, 0x9ecd}, //黍
	{0xcaf3, 0x9f20}, //鼠
	{0xcaf4, 0x5c5e}, //属
	{0xcaf5, 0x672f}, //术
	{0xcaf6, 0x8ff0}, //述
	{0xcaf7, 0x6811}, //树
	{0xcaf8, 0x675f}, //束
	{0xcaf9, 0x620d}, //戍
	{0xcafa, 0x7ad6}, //竖
	{0xcafb, 0x5885}, //墅
	{0xcafc, 0x5eb6}, //庶
	{0xcafd, 0x6570}, //数
	{0xcafe, 0x6f31}, //漱
	{0xcba1, 0x6055}, //恕
	{0xcba2, 0x5237}, //刷
	{0xcba3, 0x800d}, //耍
	{0xcba4, 0x6454}, //摔
	{0xcba5, 0x8870}, //衰
	{0xcba6, 0x7529}, //甩
	{0xcba7, 0x5e05}, //帅
	{0xcba8, 0x6813}, //栓
	{0xcba9, 0x62f4}, //拴
	{0xcbaa, 0x971c}, //霜
	{0xcbab, 0x53cc}, //双
	{0xcbac, 0x723d}, //爽
	{0xcbad, 0x8c01}, //谁
	{0xcbae, 0x6c34}, //水
	{0xcbaf, 0x7761}, //睡
	{0xcbb0, 0x7a0e}, //税
	{0xcbb1, 0x542e}, //吮
	{0xcbb2, 0x77ac}, //瞬
	{0xcbb3, 0x987a}, //顺
	{0xcbb4, 0x821c}, //舜
	{0xcbb5, 0x8bf4}, //说
	{0xcbb6, 0x7855}, //硕
	{0xcbb7, 0x6714}, //朔
	{0xcbb8, 0x70c1}, //烁
	{0xcbb9, 0x65af}, //斯
	{0xcbba, 0x6495}, //撕
	{0xcbbb, 0x5636}, //嘶
	{0xcbbc, 0x601d}, //思
	{0xcbbd, 0x79c1}, //私
	{0xcbbe, 0x53f8}, //司
	{0xcbbf, 0x4e1d}, //丝
	{0xcbc0, 0x6b7b}, //死
	{0xcbc1, 0x8086}, //肆
	{0xcbc2, 0x5bfa}, //寺
	{0xcbc3, 0x55e3}, //嗣
	{0xcbc4, 0x56db}, //四
	{0xcbc5, 0x4f3a}, //伺
	{0xcbc6, 0x4f3c}, //似
	{0xcbc7, 0x9972}, //饲
	{0xcbc8, 0x5df3}, //巳
	{0xcbc9, 0x677e}, //松
	{0xcbca, 0x8038}, //耸
	{0xcbcb, 0x6002}, //怂
	{0xcbcc, 0x9882}, //颂
	{0xcbcd, 0x9001}, //送
	{0xcbce, 0x5b8b}, //宋
	{0xcbcf, 0x8bbc}, //讼
	{0xcbd0, 0x8bf5}, //诵
	{0xcbd1, 0x641c}, //搜
	{0xcbd2, 0x8258}, //艘
	{0xcbd3, 0x64de}, //擞
	{0xcbd4, 0x55fd}, //嗽
	{0xcbd5, 0x82cf}, //苏
	{0xcbd6, 0x9165}, //酥
	{0xcbd7, 0x4fd7}, //俗
	{0xcbd8, 0x7d20}, //素
	{0xcbd9, 0x901f}, //速
	{0xcbda, 0x7c9f}, //粟
	{0xcbdb, 0x50f3}, //僳
	{0xcbdc, 0x5851}, //塑
	{0xcbdd, 0x6eaf}, //溯
	{0xcbde, 0x5bbf}, //宿
	{0xcbdf, 0x8bc9}, //诉
	{0xcbe0, 0x8083}, //肃
	{0xcbe1, 0x9178}, //酸
	{0xcbe2, 0x849c}, //蒜
	{0xcbe3, 0x7b97}, //算
	{0xcbe4, 0x867d}, //虽
	{0xcbe5, 0x968b}, //隋
	{0xcbe6, 0x968f}, //随
	{0xcbe7, 0x7ee5}, //绥
	{0xcbe8, 0x9ad3}, //髓
	{0xcbe9, 0x788e}, //碎
	{0xcbea, 0x5c81}, //岁
	{0xcbeb, 0x7a57}, //穗
	{0xcbec, 0x9042}, //遂
	{0xcbed, 0x96a7}, //隧
	{0xcbee, 0x795f}, //祟
	{0xcbef, 0x5b59}, //孙
	{0xcbf0, 0x635f}, //损
	{0xcbf1, 0x7b0b}, //笋
	{0xcbf2, 0x84d1}, //蓑
	{0xcbf3, 0x68ad}, //梭
	{0xcbf4, 0x5506}, //唆
	{0xcbf5, 0x7f29}, //缩
	{0xcbf6, 0x7410}, //琐
	{0xcbf7, 0x7d22}, //索
	{0xcbf8, 0x9501}, //锁
	{0xcbf9, 0x6240}, //所
	{0xcbfa, 0x584c}, //塌
	{0xcbfb, 0x4ed6}, //他
	{0xcbfc, 0x5b83}, //它
	{0xcbfd, 0x5979}, //她
	{0xcbfe, 0x5854}, //塔
	{0xcca1, 0x736d}, //獭
	{0xcca2, 0x631e}, //挞
	{0xcca3, 0x8e4b}, //蹋
	{0xcca4, 0x8e0f}, //踏
	{0xcca5, 0x80ce}, //胎
	{0xcca6, 0x82d4}, //苔
	{0xcca7, 0x62ac}, //抬
	{0xcca8, 0x53f0}, //台
	{0xcca9, 0x6cf0}, //泰
	{0xccaa, 0x915e}, //酞
	{0xccab, 0x592a}, //太
	{0xccac, 0x6001}, //态
	{0xccad, 0x6c70}, //汰
	{0xccae, 0x574d}, //坍
	{0xccaf, 0x644a}, //摊
	{0xccb0, 0x8d2a}, //贪
	{0xccb1, 0x762b}, //瘫
	{0xccb2, 0x6ee9}, //滩
	{0xccb3, 0x575b}, //坛
	{0xccb4, 0x6a80}, //檀
	{0xccb5, 0x75f0}, //痰
	{0xccb6, 0x6f6d}, //潭
	{0xccb7, 0x8c2d}, //谭
	{0xccb8, 0x8c08}, //谈
	{0xccb9, 0x5766}, //坦
	{0xccba, 0x6bef}, //毯
	{0xccbb, 0x8892}, //袒
	{0xccbc, 0x78b3}, //碳
	{0xccbd, 0x63a2}, //探
	{0xccbe, 0x53f9}, //叹
	{0xccbf, 0x70ad}, //炭
	{0xccc0, 0x6c64}, //汤
	{0xccc1, 0x5858}, //塘
	{0xccc2, 0x642a}, //搪
	{0xccc3, 0x5802}, //堂
	{0xccc4, 0x68e0}, //棠
	{0xccc5, 0x819b}, //膛
	{0xccc6, 0x5510}, //唐
	{0xccc7, 0x7cd6}, //糖
	{0xccc8, 0x5018}, //倘
	{0xccc9, 0x8eba}, //躺
	{0xccca, 0x6dcc}, //淌
	{0xcccb, 0x8d9f}, //趟
	{0xcccc, 0x70eb}, //烫
	{0xcccd, 0x638f}, //掏
	{0xccce, 0x6d9b}, //涛
	{0xcccf, 0x6ed4}, //滔
	{0xccd0, 0x7ee6}, //绦
	{0xccd1, 0x8404}, //萄
	{0xccd2, 0x6843}, //桃
	{0xccd3, 0x9003}, //逃
	{0xccd4, 0x6dd8}, //淘
	{0xccd5, 0x9676}, //陶
	{0xccd6, 0x8ba8}, //讨
	{0xccd7, 0x5957}, //套
	{0xccd8, 0x7279}, //特
	{0xccd9, 0x85e4}, //藤
	{0xccda, 0x817e}, //腾
	{0xccdb, 0x75bc}, //疼
	{0xccdc, 0x8a8a}, //誊
	{0xccdd, 0x68af}, //梯
	{0xccde, 0x5254}, //剔
	{0xccdf, 0x8e22}, //踢
	{0xcce0, 0x9511}, //锑
	{0xcce1, 0x63d0}, //提
	{0xcce2, 0x9898}, //题
	{0xcce3, 0x8e44}, //蹄
	{0xcce4, 0x557c}, //啼
	{0xcce5, 0x4f53}, //体
	{0xcce6, 0x66ff}, //替
	{0xcce7, 0x568f}, //嚏
	{0xcce8, 0x60d5}, //惕
	{0xcce9, 0x6d95}, //涕
	{0xccea, 0x5243}, //剃
	{0xcceb, 0x5c49}, //屉
	{0xccec, 0x5929}, //天
	{0xcced, 0x6dfb}, //添
	{0xccee, 0x586b}, //填
	{0xccef, 0x7530}, //田
	{0xccf0, 0x751c}, //甜
	{0xccf1, 0x606c}, //恬
	{0xccf2, 0x8214}, //舔
	{0xccf3, 0x8146}, //腆
	{0xccf4, 0x6311}, //挑
	{0xccf5, 0x6761}, //条
	{0xccf6, 0x8fe2}, //迢
	{0xccf7, 0x773a}, //眺
	{0xccf8, 0x8df3}, //跳
	{0xccf9, 0x8d34}, //贴
	{0xccfa, 0x94c1}, //铁
	{0xccfb, 0x5e16}, //帖
	{0xccfc, 0x5385}, //厅
	{0xccfd, 0x542c}, //听
	{0xccfe, 0x70c3}, //烃
	{0xcda1, 0x6c40}, //汀
	{0xcda2, 0x5ef7}, //廷
	{0xcda3, 0x505c}, //停
	{0xcda4, 0x4ead}, //亭
	{0xcda5, 0x5ead}, //庭
	{0xcda6, 0x633a}, //挺
	{0xcda7, 0x8247}, //艇
	{0xcda8, 0x901a}, //通
	{0xcda9, 0x6850}, //桐
	{0xcdaa, 0x916e}, //酮
	{0xcdab, 0x77b3}, //瞳
	{0xcdac, 0x540c}, //同
	{0xcdad, 0x94dc}, //铜
	{0xcdae, 0x5f64}, //彤
	{0xcdaf, 0x7ae5}, //童
	{0xcdb0, 0x6876}, //桶
	{0xcdb1, 0x6345}, //捅
	{0xcdb2, 0x7b52}, //筒
	{0xcdb3, 0x7edf}, //统
	{0xcdb4, 0x75db}, //痛
	{0xcdb5, 0x5077}, //偷
	{0xcdb6, 0x6295}, //投
	{0xcdb7, 0x5934}, //头
	{0xcdb8, 0x900f}, //透
	{0xcdb9, 0x51f8}, //凸
	{0xcdba, 0x79c3}, //秃
	{0xcdbb, 0x7a81}, //突
	{0xcdbc, 0x56fe}, //图
	{0xcdbd, 0x5f92}, //徒
	{0xcdbe, 0x9014}, //途
	{0xcdbf, 0x6d82}, //涂
	{0xcdc0, 0x5c60}, //屠
	{0xcdc1, 0x571f}, //土
	{0xcdc2, 0x5410}, //吐
	{0xcdc3, 0x5154}, //兔
	{0xcdc4, 0x6e4d}, //湍
	{0xcdc5, 0x56e2}, //团
	{0xcdc6, 0x63a8}, //推
	{0xcdc7, 0x9893}, //颓
	{0xcdc8, 0x817f}, //腿
	{0xcdc9, 0x8715}, //蜕
	{0xcdca, 0x892a}, //褪
	{0xcdcb, 0x9000}, //退
	{0xcdcc, 0x541e}, //吞
	{0xcdcd, 0x5c6f}, //屯
	{0xcdce, 0x81c0}, //臀
	{0xcdcf, 0x62d6}, //拖
	{0xcdd0, 0x6258}, //托
	{0xcdd1, 0x8131}, //脱
	{0xcdd2, 0x9e35}, //鸵
	{0xcdd3, 0x9640}, //陀
	{0xcdd4, 0x9a6e}, //驮
	{0xcdd5, 0x9a7c}, //驼
	{0xcdd6, 0x692d}, //椭
	{0xcdd7, 0x59a5}, //妥
	{0xcdd8, 0x62d3}, //拓
	{0xcdd9, 0x553e}, //唾
	{0xcdda, 0x6316}, //挖
	{0xcddb, 0x54c7}, //哇
	{0xcddc, 0x86d9}, //蛙
	{0xcddd, 0x6d3c}, //洼
	{0xcdde, 0x5a03}, //娃
	{0xcddf, 0x74e6}, //瓦
	{0xcde0, 0x889c}, //袜
	{0xcde1, 0x6b6a}, //歪
	{0xcde2, 0x5916}, //外
	{0xcde3, 0x8c4c}, //豌
	{0xcde4, 0x5f2f}, //弯
	{0xcde5, 0x6e7e}, //湾
	{0xcde6, 0x73a9}, //玩
	{0xcde7, 0x987d}, //顽
	{0xcde8, 0x4e38}, //丸
	{0xcde9, 0x70f7}, //烷
	{0xcdea, 0x5b8c}, //完
	{0xcdeb, 0x7897}, //碗
	{0xcdec, 0x633d}, //挽
	{0xcded, 0x665a}, //晚
	{0xcdee, 0x7696}, //皖
	{0xcdef, 0x60cb}, //惋
	{0xcdf0, 0x5b9b}, //宛
	{0xcdf1, 0x5a49}, //婉
	{0xcdf2, 0x4e07}, //万
	{0xcdf3, 0x8155}, //腕
	{0xcdf4, 0x6c6a}, //汪
	{0xcdf5, 0x738b}, //王
	{0xcdf6, 0x4ea1}, //亡
	{0xcdf7, 0x6789}, //枉
	{0xcdf8, 0x7f51}, //网
	{0xcdf9, 0x5f80}, //往
	{0xcdfa, 0x65fa}, //旺
	{0xcdfb, 0x671b}, //望
	{0xcdfc, 0x5fd8}, //忘
	{0xcdfd, 0x5984}, //妄
	{0xcdfe, 0x5a01}, //威
	{0xcea1, 0x5dcd}, //巍
	{0xcea2, 0x5fae}, //微
	{0xcea3, 0x5371}, //危
	{0xcea4, 0x97e6}, //韦
	{0xcea5, 0x8fdd}, //违
	{0xcea6, 0x6845}, //桅
	{0xcea7, 0x56f4}, //围
	{0xcea8, 0x552f}, //唯
	{0xcea9, 0x60df}, //惟
	{0xceaa, 0x4e3a}, //为
	{0xceab, 0x6f4d}, //潍
	{0xceac, 0x7ef4}, //维
	{0xcead, 0x82c7}, //苇
	{0xceae, 0x840e}, //萎
	{0xceaf, 0x59d4}, //委
	{0xceb0, 0x4f1f}, //伟
	{0xceb1, 0x4f2a}, //伪
	{0xceb2, 0x5c3e}, //尾
	{0xceb3, 0x7eac}, //纬
	{0xceb4, 0x672a}, //未
	{0xceb5, 0x851a}, //蔚
	{0xceb6, 0x5473}, //味
	{0xceb7, 0x754f}, //畏
	{0xceb8, 0x80c3}, //胃
	{0xceb9, 0x5582}, //喂
	{0xceba, 0x9b4f}, //魏
	{0xcebb, 0x4f4d}, //位
	{0xcebc, 0x6e2d}, //渭
	{0xcebd, 0x8c13}, //谓
	{0xcebe, 0x5c09}, //尉
	{0xcebf, 0x6170}, //慰
	{0xcec0, 0x536b}, //卫
	{0xcec1, 0x761f}, //瘟
	{0xcec2, 0x6e29}, //温
	{0xcec3, 0x868a}, //蚊
	{0xcec4, 0x6587}, //文
	{0xcec5, 0x95fb}, //闻
	{0xcec6, 0x7eb9}, //纹
	{0xcec7, 0x543b}, //吻
	{0xcec8, 0x7a33}, //稳
	{0xcec9, 0x7d0a}, //紊
	{0xceca, 0x95ee}, //问
	{0xcecb, 0x55e1}, //嗡
	{0xcecc, 0x7fc1}, //翁
	{0xcecd, 0x74ee}, //瓮
	{0xcece, 0x631d}, //挝
	{0xcecf, 0x8717}, //蜗
	{0xced0, 0x6da1}, //涡
	{0xced1, 0x7a9d}, //窝
	{0xced2, 0x6211}, //我
	{0xced3, 0x65a1}, //斡
	{0xced4, 0x5367}, //卧
	{0xced5, 0x63e1}, //握
	{0xced6, 0x6c83}, //沃
	{0xced7, 0x5deb}, //巫
	{0xced8, 0x545c}, //呜
	{0xced9, 0x94a8}, //钨
	{0xceda, 0x4e4c}, //乌
	{0xcedb, 0x6c61}, //污
	{0xcedc, 0x8bec}, //诬
	{0xcedd, 0x5c4b}, //屋
	{0xcede, 0x65e0}, //无
	{0xcedf, 0x829c}, //芜
	{0xcee0, 0x68a7}, //梧
	{0xcee1, 0x543e}, //吾
	{0xcee2, 0x5434}, //吴
	{0xcee3, 0x6bcb}, //毋
	{0xcee4, 0x6b66}, //武
	{0xcee5, 0x4e94}, //五
	{0xcee6, 0x6342}, //捂
	{0xcee7, 0x5348}, //午
	{0xcee8, 0x821e}, //舞
	{0xcee9, 0x4f0d}, //伍
	{0xceea, 0x4fae}, //侮
	{0xceeb, 0x575e}, //坞
	{0xceec, 0x620a}, //戊
	{0xceed, 0x96fe}, //雾
	{0xceee, 0x6664}, //晤
	{0xceef, 0x7269}, //物
	{0xcef0, 0x52ff}, //勿
	{0xcef1, 0x52a1}, //务
	{0xcef2, 0x609f}, //悟
	{0xcef3, 0x8bef}, //误
	{0xcef4, 0x6614}, //昔
	{0xcef5, 0x7199}, //熙
	{0xcef6, 0x6790}, //析
	{0xcef7, 0x897f}, //西
	{0xcef8, 0x7852}, //硒
	{0xcef9, 0x77fd}, //矽
	{0xcefa, 0x6670}, //晰
	{0xcefb, 0x563b}, //嘻
	{0xcefc, 0x5438}, //吸
	{0xcefd, 0x9521}, //锡
	{0xcefe, 0x727a}, //牺
	{0xcfa1, 0x7a00}, //稀
	{0xcfa2, 0x606f}, //息
	{0xcfa3, 0x5e0c}, //希
	{0xcfa4, 0x6089}, //悉
	{0xcfa5, 0x819d}, //膝
	{0xcfa6, 0x5915}, //夕
	{0xcfa7, 0x60dc}, //惜
	{0xcfa8, 0x7184}, //熄
	{0xcfa9, 0x70ef}, //烯
	{0xcfaa, 0x6eaa}, //溪
	{0xcfab, 0x6c50}, //汐
	{0xcfac, 0x7280}, //犀
	{0xcfad, 0x6a84}, //檄
	{0xcfae, 0x88ad}, //袭
	{0xcfaf, 0x5e2d}, //席
	{0xcfb0, 0x4e60}, //习
	{0xcfb1, 0x5ab3}, //媳
	{0xcfb2, 0x559c}, //喜
	{0xcfb3, 0x94e3}, //铣
	{0xcfb4, 0x6d17}, //洗
	{0xcfb5, 0x7cfb}, //系
	{0xcfb6, 0x9699}, //隙
	{0xcfb7, 0x620f}, //戏
	{0xcfb8, 0x7ec6}, //细
	{0xcfb9, 0x778e}, //瞎
	{0xcfba, 0x867e}, //虾
	{0xcfbb, 0x5323}, //匣
	{0xcfbc, 0x971e}, //霞
	{0xcfbd, 0x8f96}, //辖
	{0xcfbe, 0x6687}, //暇
	{0xcfbf, 0x5ce1}, //峡
	{0xcfc0, 0x4fa0}, //侠
	{0xcfc1, 0x72ed}, //狭
	{0xcfc2, 0x4e0b}, //下
	{0xcfc3, 0x53a6}, //厦
	{0xcfc4, 0x590f}, //夏
	{0xcfc5, 0x5413}, //吓
	{0xcfc6, 0x6380}, //掀
	{0xcfc7, 0x9528}, //锨
	{0xcfc8, 0x5148}, //先
	{0xcfc9, 0x4ed9}, //仙
	{0xcfca, 0x9c9c}, //鲜
	{0xcfcb, 0x7ea4}, //纤
	{0xcfcc, 0x54b8}, //咸
	{0xcfcd, 0x8d24}, //贤
	{0xcfce, 0x8854}, //衔
	{0xcfcf, 0x8237}, //舷
	{0xcfd0, 0x95f2}, //闲
	{0xcfd1, 0x6d8e}, //涎
	{0xcfd2, 0x5f26}, //弦
	{0xcfd3, 0x5acc}, //嫌
	{0xcfd4, 0x663e}, //显
	{0xcfd5, 0x9669}, //险
	{0xcfd6, 0x73b0}, //现
	{0xcfd7, 0x732e}, //献
	{0xcfd8, 0x53bf}, //县
	{0xcfd9, 0x817a}, //腺
	{0xcfda, 0x9985}, //馅
	{0xcfdb, 0x7fa1}, //羡
	{0xcfdc, 0x5baa}, //宪
	{0xcfdd, 0x9677}, //陷
	{0xcfde, 0x9650}, //限
	{0xcfdf, 0x7ebf}, //线
	{0xcfe0, 0x76f8}, //相
	{0xcfe1, 0x53a2}, //厢
	{0xcfe2, 0x9576}, //镶
	{0xcfe3, 0x9999}, //香
	{0xcfe4, 0x7bb1}, //箱
	{0xcfe5, 0x8944}, //襄
	{0xcfe6, 0x6e58}, //湘
	{0xcfe7, 0x4e61}, //乡
	{0xcfe8, 0x7fd4}, //翔
	{0xcfe9, 0x7965}, //祥
	{0xcfea, 0x8be6}, //详
	{0xcfeb, 0x60f3}, //想
	{0xcfec, 0x54cd}, //响
	{0xcfed, 0x4eab}, //享
	{0xcfee, 0x9879}, //项
	{0xcfef, 0x5df7}, //巷
	{0xcff0, 0x6a61}, //橡
	{0xcff1, 0x50cf}, //像
	{0xcff2, 0x5411}, //向
	{0xcff3, 0x8c61}, //象
	{0xcff4, 0x8427}, //萧
	{0xcff5, 0x785d}, //硝
	{0xcff6, 0x9704}, //霄
	{0xcff7, 0x524a}, //削
	{0xcff8, 0x54ee}, //哮
	{0xcff9, 0x56a3}, //嚣
	{0xcffa, 0x9500}, //销
	{0xcffb, 0x6d88}, //消
	{0xcffc, 0x5bb5}, //宵
	{0xcffd, 0x6dc6}, //淆
	{0xcffe, 0x6653}, //晓
	{0xd0a1, 0x5c0f}, //小
	{0xd0a2, 0x5b5d}, //孝
	{0xd0a3, 0x6821}, //校
	{0xd0a4, 0x8096}, //肖
	{0xd0a5, 0x5578}, //啸
	{0xd0a6, 0x7b11}, //笑
	{0xd0a7, 0x6548}, //效
	{0xd0a8, 0x6954}, //楔
	{0xd0a9, 0x4e9b}, //些
	{0xd0aa, 0x6b47}, //歇
	{0xd0ab, 0x874e}, //蝎
	{0xd0ac, 0x978b}, //鞋
	{0xd0ad, 0x534f}, //协
	{0xd0ae, 0x631f}, //挟
	{0xd0af, 0x643a}, //携
	{0xd0b0, 0x90aa}, //邪
	{0xd0b1, 0x659c}, //斜
	{0xd0b2, 0x80c1}, //胁
	{0xd0b3, 0x8c10}, //谐
	{0xd0b4, 0x5199}, //写
	{0xd0b5, 0x68b0}, //械
	{0xd0b6, 0x5378}, //卸
	{0xd0b7, 0x87f9}, //蟹
	{0xd0b8, 0x61c8}, //懈
	{0xd0b9, 0x6cc4}, //泄
	{0xd0ba, 0x6cfb}, //泻
	{0xd0bb, 0x8c22}, //谢
	{0xd0bc, 0x5c51}, //屑
	{0xd0bd, 0x85aa}, //薪
	{0xd0be, 0x82af}, //芯
	{0xd0bf, 0x950c}, //锌
	{0xd0c0, 0x6b23}, //欣
	{0xd0c1, 0x8f9b}, //辛
	{0xd0c2, 0x65b0}, //新
	{0xd0c3, 0x5ffb}, //忻
	{0xd0c4, 0x5fc3}, //心
	{0xd0c5, 0x4fe1}, //信
	{0xd0c6, 0x8845}, //衅
	{0xd0c7, 0x661f}, //星
	{0xd0c8, 0x8165}, //腥
	{0xd0c9, 0x7329}, //猩
	{0xd0ca, 0x60fa}, //惺
	{0xd0cb, 0x5174}, //兴
	{0xd0cc, 0x5211}, //刑
	{0xd0cd, 0x578b}, //型
	{0xd0ce, 0x5f62}, //形
	{0xd0cf, 0x90a2}, //邢
	{0xd0d0, 0x884c}, //行
	{0xd0d1, 0x9192}, //醒
	{0xd0d2, 0x5e78}, //幸
	{0xd0d3, 0x674f}, //杏
	{0xd0d4, 0x6027}, //性
	{0xd0d5, 0x59d3}, //姓
	{0xd0d6, 0x5144}, //兄
	{0xd0d7, 0x51f6}, //凶
	{0xd0d8, 0x80f8}, //胸
	{0xd0d9, 0x5308}, //匈
	{0xd0da, 0x6c79}, //汹
	{0xd0db, 0x96c4}, //雄
	{0xd0dc, 0x718a}, //熊
	{0xd0dd, 0x4f11}, //休
	{0xd0de, 0x4fee}, //修
	{0xd0df, 0x7f9e}, //羞
	{0xd0e0, 0x673d}, //朽
	{0xd0e1, 0x55c5}, //嗅
	{0xd0e2, 0x9508}, //锈
	{0xd0e3, 0x79c0}, //秀
	{0xd0e4, 0x8896}, //袖
	{0xd0e5, 0x7ee3}, //绣
	{0xd0e6, 0x589f}, //墟
	{0xd0e7, 0x620c}, //戌
	{0xd0e8, 0x9700}, //需
	{0xd0e9, 0x865a}, //虚
	{0xd0ea, 0x5618}, //嘘
	{0xd0eb, 0x987b}, //须
	{0xd0ec, 0x5f90}, //徐
	{0xd0ed, 0x8bb8}, //许
	{0xd0ee, 0x84c4}, //蓄
	{0xd0ef, 0x9157}, //酗
	{0xd0f0, 0x53d9}, //叙
	{0xd0f1, 0x65ed}, //旭
	{0xd0f2, 0x5e8f}, //序
	{0xd0f3, 0x755c}, //畜
	{0xd0f4, 0x6064}, //恤
	{0xd0f5, 0x7d6e}, //絮
	{0xd0f6, 0x5a7f}, //婿
	{0xd0f7, 0x7eea}, //绪
	{0xd0f8, 0x7eed}, //续
	{0xd0f9, 0x8f69}, //轩
	{0xd0fa, 0x55a7}, //喧
	{0xd0fb, 0x5ba3}, //宣
	{0xd0fc, 0x60ac}, //悬
	{0xd0fd, 0x65cb}, //旋
	{0xd0fe, 0x7384}, //玄
	{0xd1a1, 0x9009}, //选
	{0xd1a2, 0x7663}, //癣
	{0xd1a3, 0x7729}, //眩
	{0xd1a4, 0x7eda}, //绚
	{0xd1a5, 0x9774}, //靴
	{0xd1a6, 0x859b}, //薛
	{0xd1a7, 0x5b66}, //学
	{0xd1a8, 0x7a74}, //穴
	{0xd1a9, 0x96ea}, //雪
	{0xd1aa, 0x8840}, //血
	{0xd1ab, 0x52cb}, //勋
	{0xd1ac, 0x718f}, //熏
	{0xd1ad, 0x5faa}, //循
	{0xd1ae, 0x65ec}, //旬
	{0xd1af, 0x8be2}, //询
	{0xd1b0, 0x5bfb}, //寻
	{0xd1b1, 0x9a6f}, //驯
	{0xd1b2, 0x5de1}, //巡
	{0xd1b3, 0x6b89}, //殉
	{0xd1b4, 0x6c5b}, //汛
	{0xd1b5, 0x8bad}, //训
	{0xd1b6, 0x8baf}, //讯
	{0xd1b7, 0x900a}, //逊
	{0xd1b8, 0x8fc5}, //迅
	{0xd1b9, 0x538b}, //压
	{0xd1ba, 0x62bc}, //押
	{0xd1bb, 0x9e26}, //鸦
	{0xd1bc, 0x9e2d}, //鸭
	{0xd1bd, 0x5440}, //呀
	{0xd1be, 0x4e2b}, //丫
	{0xd1bf, 0x82bd}, //芽
	{0xd1c0, 0x7259}, //牙
	{0xd1c1, 0x869c}, //蚜
	{0xd1c2, 0x5d16}, //崖
	{0xd1c3, 0x8859}, //衙
	{0xd1c4, 0x6daf}, //涯
	{0xd1c5, 0x96c5}, //雅
	{0xd1c6, 0x54d1}, //哑
	{0xd1c7, 0x4e9a}, //亚
	{0xd1c8, 0x8bb6}, //讶
	{0xd1c9, 0x7109}, //焉
	{0xd1ca, 0x54bd}, //咽
	{0xd1cb, 0x9609}, //阉
	{0xd1cc, 0x70df}, //烟
	{0xd1cd, 0x6df9}, //淹
	{0xd1ce, 0x76d0}, //盐
	{0xd1cf, 0x4e25}, //严
	{0xd1d0, 0x7814}, //研
	{0xd1d1, 0x8712}, //蜒
	{0xd1d2, 0x5ca9}, //岩
	{0xd1d3, 0x5ef6}, //延
	{0xd1d4, 0x8a00}, //言
	{0xd1d5, 0x989c}, //颜
	{0xd1d6, 0x960e}, //阎
	{0xd1d7, 0x708e}, //炎
	{0xd1d8, 0x6cbf}, //沿
	{0xd1d9, 0x5944}, //奄
	{0xd1da, 0x63a9}, //掩
	{0xd1db, 0x773c}, //眼
	{0xd1dc, 0x884d}, //衍
	{0xd1dd, 0x6f14}, //演
	{0xd1de, 0x8273}, //艳
	{0xd1df, 0x5830}, //堰
	{0xd1e0, 0x71d5}, //燕
	{0xd1e1, 0x538c}, //厌
	{0xd1e2, 0x781a}, //砚
	{0xd1e3, 0x96c1}, //雁
	{0xd1e4, 0x5501}, //唁
	{0xd1e5, 0x5f66}, //彦
	{0xd1e6, 0x7130}, //焰
	{0xd1e7, 0x5bb4}, //宴
	{0xd1e8, 0x8c1a}, //谚
	{0xd1e9, 0x9a8c}, //验
	{0xd1ea, 0x6b83}, //殃
	{0xd1eb, 0x592e}, //央
	{0xd1ec, 0x9e2f}, //鸯
	{0xd1ed, 0x79e7}, //秧
	{0xd1ee, 0x6768}, //杨
	{0xd1ef, 0x626c}, //扬
	{0xd1f0, 0x4f6f}, //佯
	{0xd1f1, 0x75a1}, //疡
	{0xd1f2, 0x7f8a}, //羊
	{0xd1f3, 0x6d0b}, //洋
	{0xd1f4, 0x9633}, //阳
	{0xd1f5, 0x6c27}, //氧
	{0xd1f6, 0x4ef0}, //仰
	{0xd1f7, 0x75d2}, //痒
	{0xd1f8, 0x517b}, //养
	{0xd1f9, 0x6837}, //样
	{0xd1fa, 0x6f3e}, //漾
	{0xd1fb, 0x9080}, //邀
	{0xd1fc, 0x8170}, //腰
	{0xd1fd, 0x5996}, //妖
	{0xd1fe, 0x7476}, //瑶
	{0xd2a1, 0x6447}, //摇
	{0xd2a2, 0x5c27}, //尧
	{0xd2a3, 0x9065}, //遥
	{0xd2a4, 0x7a91}, //窑
	{0xd2a5, 0x8c23}, //谣
	{0xd2a6, 0x59da}, //姚
	{0xd2a7, 0x54ac}, //咬
	{0xd2a8, 0x8200}, //舀
	{0xd2a9, 0x836f}, //药
	{0xd2aa, 0x8981}, //要
	{0xd2ab, 0x8000}, //耀
	{0xd2ac, 0x6930}, //椰
	{0xd2ad, 0x564e}, //噎
	{0xd2ae, 0x8036}, //耶
	{0xd2af, 0x7237}, //爷
	{0xd2b0, 0x91ce}, //野
	{0xd2b1, 0x51b6}, //冶
	{0xd2b2, 0x4e5f}, //也
	{0xd2b3, 0x9875}, //页
	{0xd2b4, 0x6396}, //掖
	{0xd2b5, 0x4e1a}, //业
	{0xd2b6, 0x53f6}, //叶
	{0xd2b7, 0x66f3}, //曳
	{0xd2b8, 0x814b}, //腋
	{0xd2b9, 0x591c}, //夜
	{0xd2ba, 0x6db2}, //液
	{0xd2bb, 0x4e00}, //一
	{0xd2bc, 0x58f9}, //壹
	{0xd2bd, 0x533b}, //医
	{0xd2be, 0x63d6}, //揖
	{0xd2bf, 0x94f1}, //铱
	{0xd2c0, 0x4f9d}, //依
	{0xd2c1, 0x4f0a}, //伊
	{0xd2c2, 0x8863}, //衣
	{0xd2c3, 0x9890}, //颐
	{0xd2c4, 0x5937}, //夷
	{0xd2c5, 0x9057}, //遗
	{0xd2c6, 0x79fb}, //移
	{0xd2c7, 0x4eea}, //仪
	{0xd2c8, 0x80f0}, //胰
	{0xd2c9, 0x7591}, //疑
	{0xd2ca, 0x6c82}, //沂
	{0xd2cb, 0x5b9c}, //宜
	{0xd2cc, 0x59e8}, //姨
	{0xd2cd, 0x5f5d}, //彝
	{0xd2ce, 0x6905}, //椅
	{0xd2cf, 0x8681}, //蚁
	{0xd2d0, 0x501a}, //倚
	{0xd2d1, 0x5df2}, //已
	{0xd2d2, 0x4e59}, //乙
	{0xd2d3, 0x77e3}, //矣
	{0xd2d4, 0x4ee5}, //以
	{0xd2d5, 0x827a}, //艺
	{0xd2d6, 0x6291}, //抑
	{0xd2d7, 0x6613}, //易
	{0xd2d8, 0x9091}, //邑
	{0xd2d9, 0x5c79}, //屹
	{0xd2da, 0x4ebf}, //亿
	{0xd2db, 0x5f79}, //役
	{0xd2dc, 0x81c6}, //臆
	{0xd2dd, 0x9038}, //逸
	{0xd2de, 0x8084}, //肄
	{0xd2df, 0x75ab}, //疫
	{0xd2e0, 0x4ea6}, //亦
	{0xd2e1, 0x88d4}, //裔
	{0xd2e2, 0x610f}, //意
	{0xd2e3, 0x6bc5}, //毅
	{0xd2e4, 0x5fc6}, //忆
	{0xd2e5, 0x4e49}, //义
	{0xd2e6, 0x76ca}, //益
	{0xd2e7, 0x6ea2}, //溢
	{0xd2e8, 0x8be3}, //诣
	{0xd2e9, 0x8bae}, //议
	{0xd2ea, 0x8c0a}, //谊
	{0xd2eb, 0x8bd1}, //译
	{0xd2ec, 0x5f02}, //异
	{0xd2ed, 0x7ffc}, //翼
	{0xd2ee, 0x7fcc}, //翌
	{0xd2ef, 0x7ece}, //绎
	{0xd2f0, 0x8335}, //茵
	{0xd2f1, 0x836b}, //荫
	{0xd2f2, 0x56e0}, //因
	{0xd2f3, 0x6bb7}, //殷
	{0xd2f4, 0x97f3}, //音
	{0xd2f5, 0x9634}, //阴
	{0xd2f6, 0x59fb}, //姻
	{0xd2f7, 0x541f}, //吟
	{0xd2f8, 0x94f6}, //银
	{0xd2f9, 0x6deb}, //淫
	{0xd2fa, 0x5bc5}, //寅
	{0xd2fb, 0x996e}, //饮
	{0xd2fc, 0x5c39}, //尹
	{0xd2fd, 0x5f15}, //引
	{0xd2fe, 0x9690}, //隐
	{0xd3a1, 0x5370}, //印
	{0xd3a2, 0x82f1}, //英
	{0xd3a3, 0x6a31}, //樱
	{0xd3a4, 0x5a74}, //婴
	{0xd3a5, 0x9e70}, //鹰
	{0xd3a6, 0x5e94}, //应
	{0xd3a7, 0x7f28}, //缨
	{0xd3a8, 0x83b9}, //莹
	{0xd3a9, 0x8424}, //萤
	{0xd3aa, 0x8425}, //营
	{0xd3ab, 0x8367}, //荧
	{0xd3ac, 0x8747}, //蝇
	{0xd3ad, 0x8fce}, //迎
	{0xd3ae, 0x8d62}, //赢
	{0xd3af, 0x76c8}, //盈
	{0xd3b0, 0x5f71}, //影
	{0xd3b1, 0x9896}, //颖
	{0xd3b2, 0x786c}, //硬
	{0xd3b3, 0x6620}, //映
	{0xd3b4, 0x54df}, //哟
	{0xd3b5, 0x62e5}, //拥
	{0xd3b6, 0x4f63}, //佣
	{0xd3b7, 0x81c3}, //臃
	{0xd3b8, 0x75c8}, //痈
	{0xd3b9, 0x5eb8}, //庸
	{0xd3ba, 0x96cd}, //雍
	{0xd3bb, 0x8e0a}, //踊
	{0xd3bc, 0x86f9}, //蛹
	{0xd3bd, 0x548f}, //咏
	{0xd3be, 0x6cf3}, //泳
	{0xd3bf, 0x6d8c}, //涌
	{0xd3c0, 0x6c38}, //永
	{0xd3c1, 0x607f}, //恿
	{0xd3c2, 0x52c7}, //勇
	{0xd3c3, 0x7528}, //用
	{0xd3c4, 0x5e7d}, //幽
	{0xd3c5, 0x4f18}, //优
	{0xd3c6, 0x60a0}, //悠
	{0xd3c7, 0x5fe7}, //忧
	{0xd3c8, 0x5c24}, //尤
	{0xd3c9, 0x7531}, //由
	{0xd3ca, 0x90ae}, //邮
	{0xd3cb, 0x94c0}, //铀
	{0xd3cc, 0x72b9}, //犹
	{0xd3cd, 0x6cb9}, //油
	{0xd3ce, 0x6e38}, //游
	{0xd3cf, 0x9149}, //酉
	{0xd3d0, 0x6709}, //有
	{0xd3d1, 0x53cb}, //友
	{0xd3d2, 0x53f3}, //右
	{0xd3d3, 0x4f51}, //佑
	{0xd3d4, 0x91c9}, //釉
	{0xd3d5, 0x8bf1}, //诱
	{0xd3d6, 0x53c8}, //又
	{0xd3d7, 0x5e7c}, //幼
	{0xd3d8, 0x8fc2}, //迂
	{0xd3d9, 0x6de4}, //淤
	{0xd3da, 0x4e8e}, //于
	{0xd3db, 0x76c2}, //盂
	{0xd3dc, 0x6986}, //榆
	{0xd3dd, 0x865e}, //虞
	{0xd3de, 0x611a}, //愚
	{0xd3df, 0x8206}, //舆
	{0xd3e0, 0x4f59}, //余
	{0xd3e1, 0x4fde}, //俞
	{0xd3e2, 0x903e}, //逾
	{0xd3e3, 0x9c7c}, //鱼
	{0xd3e4, 0x6109}, //愉
	{0xd3e5, 0x6e1d}, //渝
	{0xd3e6, 0x6e14}, //渔
	{0xd3e7, 0x9685}, //隅
	{0xd3e8, 0x4e88}, //予
	{0xd3e9, 0x5a31}, //娱
	{0xd3ea, 0x96e8}, //雨
	{0xd3eb, 0x4e0e}, //与
	{0xd3ec, 0x5c7f}, //屿
	{0xd3ed, 0x79b9}, //禹
	{0xd3ee, 0x5b87}, //宇
	{0xd3ef, 0x8bed}, //语
	{0xd3f0, 0x7fbd}, //羽
	{0xd3f1, 0x7389}, //玉
	{0xd3f2, 0x57df}, //域
	{0xd3f3, 0x828b}, //芋
	{0xd3f4, 0x90c1}, //郁
	{0xd3f5, 0x5401}, //吁
	{0xd3f6, 0x9047}, //遇
	{0xd3f7, 0x55bb}, //喻
	{0xd3f8, 0x5cea}, //峪
	{0xd3f9, 0x5fa1}, //御
	{0xd3fa, 0x6108}, //愈
	{0xd3fb, 0x6b32}, //欲
	{0xd3fc, 0x72f1}, //狱
	{0xd3fd, 0x80b2}, //育
	{0xd3fe, 0x8a89}, //誉
	{0xd4a1, 0x6d74}, //浴
	{0xd4a2, 0x5bd3}, //寓
	{0xd4a3, 0x88d5}, //裕
	{0xd4a4, 0x9884}, //预
	{0xd4a5, 0x8c6b}, //豫
	{0xd4a6, 0x9a6d}, //驭
	{0xd4a7, 0x9e33}, //鸳
	{0xd4a8, 0x6e0a}, //渊
	{0xd4a9, 0x51a4}, //冤
	{0xd4aa, 0x5143}, //元
	{0xd4ab, 0x57a3}, //垣
	{0xd4ac, 0x8881}, //袁
	{0xd4ad, 0x539f}, //原
	{0xd4ae, 0x63f4}, //援
	{0xd4af, 0x8f95}, //辕
	{0xd4b0, 0x56ed}, //园
	{0xd4b1, 0x5458}, //员
	{0xd4b2, 0x5706}, //圆
	{0xd4b3, 0x733f}, //猿
	{0xd4b4, 0x6e90}, //源
	{0xd4b5, 0x7f18}, //缘
	{0xd4b6, 0x8fdc}, //远
	{0xd4b7, 0x82d1}, //苑
	{0xd4b8, 0x613f}, //愿
	{0xd4b9, 0x6028}, //怨
	{0xd4ba, 0x9662}, //院
	{0xd4bb, 0x66f0}, //曰
	{0xd4bc, 0x7ea6}, //约
	{0xd4bd, 0x8d8a}, //越
	{0xd4be, 0x8dc3}, //跃
	{0xd4bf, 0x94a5}, //钥
	{0xd4c0, 0x5cb3}, //岳
	{0xd4c1, 0x7ca4}, //粤
	{0xd4c2, 0x6708}, //月
	{0xd4c3, 0x60a6}, //悦
	{0xd4c4, 0x9605}, //阅
	{0xd4c5, 0x8018}, //耘
	{0xd4c6, 0x4e91}, //云
	{0xd4c7, 0x90e7}, //郧
	{0xd4c8, 0x5300}, //匀
	{0xd4c9, 0x9668}, //陨
	{0xd4ca, 0x5141}, //允
	{0xd4cb, 0x8fd0}, //运
	{0xd4cc, 0x8574}, //蕴
	{0xd4cd, 0x915d}, //酝
	{0xd4ce, 0x6655}, //晕
	{0xd4cf, 0x97f5}, //韵
	{0xd4d0, 0x5b55}, //孕
	{0xd4d1, 0x531d}, //匝
	{0xd4d2, 0x7838}, //砸
	{0xd4d3, 0x6742}, //杂
	{0xd4d4, 0x683d}, //栽
	{0xd4d5, 0x54c9}, //哉
	{0xd4d6, 0x707e}, //灾
	{0xd4d7, 0x5bb0}, //宰
	{0xd4d8, 0x8f7d}, //载
	{0xd4d9, 0x518d}, //再
	{0xd4da, 0x5728}, //在
	{0xd4db, 0x54b1}, //咱
	{0xd4dc, 0x6512}, //攒
	{0xd4dd, 0x6682}, //暂
	{0xd4de, 0x8d5e}, //赞
	{0xd4df, 0x8d43}, //赃
	{0xd4e0, 0x810f}, //脏
	{0xd4e1, 0x846c}, //葬
	{0xd4e2, 0x906d}, //遭
	{0xd4e3, 0x7cdf}, //糟
	{0xd4e4, 0x51ff}, //凿
	{0xd4e5, 0x85fb}, //藻
	{0xd4e6, 0x67a3}, //枣
	{0xd4e7, 0x65e9}, //早
	{0xd4e8, 0x6fa1}, //澡
	{0xd4e9, 0x86a4}, //蚤
	{0xd4ea, 0x8e81}, //躁
	{0xd4eb, 0x566a}, //噪
	{0xd4ec, 0x9020}, //造
	{0xd4ed, 0x7682}, //皂
	{0xd4ee, 0x7076}, //灶
	{0xd4ef, 0x71e5}, //燥
	{0xd4f0, 0x8d23}, //责
	{0xd4f1, 0x62e9}, //择
	{0xd4f2, 0x5219}, //则
	{0xd4f3, 0x6cfd}, //泽
	{0xd4f4, 0x8d3c}, //贼
	{0xd4f5, 0x600e}, //怎
	{0xd4f6, 0x589e}, //增
	{0xd4f7, 0x618e}, //憎
	{0xd4f8, 0x66fe}, //曾
	{0xd4f9, 0x8d60}, //赠
	{0xd4fa, 0x624e}, //扎
	{0xd4fb, 0x55b3}, //喳
	{0xd4fc, 0x6e23}, //渣
	{0xd4fd, 0x672d}, //札
	{0xd4fe, 0x8f67}, //轧
	{0xd5a1, 0x94e1}, //铡
	{0xd5a2, 0x95f8}, //闸
	{0xd5a3, 0x7728}, //眨
	{0xd5a4, 0x6805}, //栅
	{0xd5a5, 0x69a8}, //榨
	{0xd5a6, 0x548b}, //咋
	{0xd5a7, 0x4e4d}, //乍
	{0xd5a8, 0x70b8}, //炸
	{0xd5a9, 0x8bc8}, //诈
	{0xd5aa, 0x6458}, //摘
	{0xd5ab, 0x658b}, //斋
	{0xd5ac, 0x5b85}, //宅
	{0xd5ad, 0x7a84}, //窄
	{0xd5ae, 0x503a}, //债
	{0xd5af, 0x5be8}, //寨
	{0xd5b0, 0x77bb}, //瞻
	{0xd5b1, 0x6be1}, //毡
	{0xd5b2, 0x8a79}, //詹
	{0xd5b3, 0x7c98}, //粘
	{0xd5b4, 0x6cbe}, //沾
	{0xd5b5, 0x76cf}, //盏
	{0xd5b6, 0x65a9}, //斩
	{0xd5b7, 0x8f97}, //辗
	{0xd5b8, 0x5d2d}, //崭
	{0xd5b9, 0x5c55}, //展
	{0xd5ba, 0x8638}, //蘸
	{0xd5bb, 0x6808}, //栈
	{0xd5bc, 0x5360}, //占
	{0xd5bd, 0x6218}, //战
	{0xd5be, 0x7ad9}, //站
	{0xd5bf, 0x6e5b}, //湛
	{0xd5c0, 0x7efd}, //绽
	{0xd5c1, 0x6a1f}, //樟
	{0xd5c2, 0x7ae0}, //章
	{0xd5c3, 0x5f70}, //彰
	{0xd5c4, 0x6f33}, //漳
	{0xd5c5, 0x5f20}, //张
	{0xd5c6, 0x638c}, //掌
	{0xd5c7, 0x6da8}, //涨
	{0xd5c8, 0x6756}, //杖
	{0xd5c9, 0x4e08}, //丈
	{0xd5ca, 0x5e10}, //帐
	{0xd5cb, 0x8d26}, //账
	{0xd5cc, 0x4ed7}, //仗
	{0xd5cd, 0x80c0}, //胀
	{0xd5ce, 0x7634}, //瘴
	{0xd5cf, 0x969c}, //障
	{0xd5d0, 0x62db}, //招
	{0xd5d1, 0x662d}, //昭
	{0xd5d2, 0x627e}, //找
	{0xd5d3, 0x6cbc}, //沼
	{0xd5d4, 0x8d75}, //赵
	{0xd5d5, 0x7167}, //照
	{0xd5d6, 0x7f69}, //罩
	{0xd5d7, 0x5146}, //兆
	{0xd5d8, 0x8087}, //肇
	{0xd5d9, 0x53ec}, //召
	{0xd5da, 0x906e}, //遮
	{0xd5db, 0x6298}, //折
	{0xd5dc, 0x54f2}, //哲
	{0xd5dd, 0x86f0}, //蛰
	{0xd5de, 0x8f99}, //辙
	{0xd5df, 0x8005}, //者
	{0xd5e0, 0x9517}, //锗
	{0xd5e1, 0x8517}, //蔗
	{0xd5e2, 0x8fd9}, //这
	{0xd5e3, 0x6d59}, //浙
	{0xd5e4, 0x73cd}, //珍
	{0xd5e5, 0x659f}, //斟
	{0xd5e6, 0x771f}, //真
	{0xd5e7, 0x7504}, //甄
	{0xd5e8, 0x7827}, //砧
	{0xd5e9, 0x81fb}, //臻
	{0xd5ea, 0x8d1e}, //贞
	{0xd5eb, 0x9488}, //针
	{0xd5ec, 0x4fa6}, //侦
	{0xd5ed, 0x6795}, //枕
	{0xd5ee, 0x75b9}, //疹
	{0xd5ef, 0x8bca}, //诊
	{0xd5f0, 0x9707}, //震
	{0xd5f1, 0x632f}, //振
	{0xd5f2, 0x9547}, //镇
	{0xd5f3, 0x9635}, //阵
	{0xd5f4, 0x84b8}, //蒸
	{0xd5f5, 0x6323}, //挣
	{0xd5f6, 0x7741}, //睁
	{0xd5f7, 0x5f81}, //征
	{0xd5f8, 0x72f0}, //狰
	{0xd5f9, 0x4e89}, //争
	{0xd5fa, 0x6014}, //怔
	{0xd5fb, 0x6574}, //整
	{0xd5fc, 0x62ef}, //拯
	{0xd5fd, 0x6b63}, //正
	{0xd5fe, 0x653f}, //政
	{0xd6a1, 0x5e27}, //帧
	{0xd6a2, 0x75c7}, //症
	{0xd6a3, 0x90d1}, //郑
	{0xd6a4, 0x8bc1}, //证
	{0xd6a5, 0x829d}, //芝
	{0xd6a6, 0x679d}, //枝
	{0xd6a7, 0x652f}, //支
	{0xd6a8, 0x5431}, //吱
	{0xd6a9, 0x8718}, //蜘
	{0xd6aa, 0x77e5}, //知
	{0xd6ab, 0x80a2}, //肢
	{0xd6ac, 0x8102}, //脂
	{0xd6ad, 0x6c41}, //汁
	{0xd6ae, 0x4e4b}, //之
	{0xd6af, 0x7ec7}, //织
	{0xd6b0, 0x804c}, //职
	{0xd6b1, 0x76f4}, //直
	{0xd6b2, 0x690d}, //植
	{0xd6b3, 0x6b96}, //殖
	{0xd6b4, 0x6267}, //执
	{0xd6b5, 0x503c}, //值
	{0xd6b6, 0x4f84}, //侄
	{0xd6b7, 0x5740}, //址
	{0xd6b8, 0x6307}, //指
	{0xd6b9, 0x6b62}, //止
	{0xd6ba, 0x8dbe}, //趾
	{0xd6bb, 0x53ea}, //只
	{0xd6bc, 0x65e8}, //旨
	{0xd6bd, 0x7eb8}, //纸
	{0xd6be, 0x5fd7}, //志
	{0xd6bf, 0x631a}, //挚
	{0xd6c0, 0x63b7}, //掷
	{0xd6c1, 0x81f3}, //至
	{0xd6c2, 0x81f4}, //致
	{0xd6c3, 0x7f6e}, //置
	{0xd6c4, 0x5e1c}, //帜
	{0xd6c5, 0x5cd9}, //峙
	{0xd6c6, 0x5236}, //制
	{0xd6c7, 0x667a}, //智
	{0xd6c8, 0x79e9}, //秩
	{0xd6c9, 0x7a1a}, //稚
	{0xd6ca, 0x8d28}, //质
	{0xd6cb, 0x7099}, //炙
	{0xd6cc, 0x75d4}, //痔
	{0xd6cd, 0x6ede}, //滞
	{0xd6ce, 0x6cbb}, //治
	{0xd6cf, 0x7a92}, //窒
	{0xd6d0, 0x4e2d}, //中
	{0xd6d1, 0x76c5}, //盅
	{0xd6d2, 0x5fe0}, //忠
	{0xd6d3, 0x949f}, //钟
	{0xd6d4, 0x8877}, //衷
	{0xd6d5, 0x7ec8}, //终
	{0xd6d6, 0x79cd}, //种
	{0xd6d7, 0x80bf}, //肿
	{0xd6d8, 0x91cd}, //重
	{0xd6d9, 0x4ef2}, //仲
	{0xd6da, 0x4f17}, //众
	{0xd6db, 0x821f}, //舟
	{0xd6dc, 0x5468}, //周
	{0xd6dd, 0x5dde}, //州
	{0xd6de, 0x6d32}, //洲
	{0xd6df, 0x8bcc}, //诌
	{0xd6e0, 0x7ca5}, //粥
	{0xd6e1, 0x8f74}, //轴
	{0xd6e2, 0x8098}, //肘
	{0xd6e3, 0x5e1a}, //帚
	{0xd6e4, 0x5492}, //咒
	{0xd6e5, 0x76b1}, //皱
	{0xd6e6, 0x5b99}, //宙
	{0xd6e7, 0x663c}, //昼
	{0xd6e8, 0x9aa4}, //骤
	{0xd6e9, 0x73e0}, //珠
	{0xd6ea, 0x682a}, //株
	{0xd6eb, 0x86db}, //蛛
	{0xd6ec, 0x6731}, //朱
	{0xd6ed, 0x732a}, //猪
	{0xd6ee, 0x8bf8}, //诸
	{0xd6ef, 0x8bdb}, //诛
	{0xd6f0, 0x9010}, //逐
	{0xd6f1, 0x7af9}, //竹
	{0xd6f2, 0x70db}, //烛
	{0xd6f3, 0x716e}, //煮
	{0xd6f4, 0x62c4}, //拄
	{0xd6f5, 0x77a9}, //瞩
	{0xd6f6, 0x5631}, //嘱
	{0xd6f7, 0x4e3b}, //主
	{0xd6f8, 0x8457}, //著
	{0xd6f9, 0x67f1}, //柱
	{0xd6fa, 0x52a9}, //助
	{0xd6fb, 0x86c0}, //蛀
	{0xd6fc, 0x8d2e}, //贮
	{0xd6fd, 0x94f8}, //铸
	{0xd6fe, 0x7b51}, //筑
	{0xd7a1, 0x4f4f}, //住
	{0xd7a2, 0x6ce8}, //注
	{0xd7a3, 0x795d}, //祝
	{0xd7a4, 0x9a7b}, //驻
	{0xd7a5, 0x6293}, //抓
	{0xd7a6, 0x722a}, //爪
	{0xd7a7, 0x62fd}, //拽
	{0xd7a8, 0x4e13}, //专
	{0xd7a9, 0x7816}, //砖
	{0xd7aa, 0x8f6c}, //转
	{0xd7ab, 0x64b0}, //撰
	{0xd7ac, 0x8d5a}, //赚
	{0xd7ad, 0x7bc6}, //篆
	{0xd7ae, 0x6869}, //桩
	{0xd7af, 0x5e84}, //庄
	{0xd7b0, 0x88c5}, //装
	{0xd7b1, 0x5986}, //妆
	{0xd7b2, 0x649e}, //撞
	{0xd7b3, 0x58ee}, //壮
	{0xd7b4, 0x72b6}, //状
	{0xd7b5, 0x690e}, //椎
	{0xd7b6, 0x9525}, //锥
	{0xd7b7, 0x8ffd}, //追
	{0xd7b8, 0x8d58}, //赘
	{0xd7b9, 0x5760}, //坠
	{0xd7ba, 0x7f00}, //缀
	{0xd7bb, 0x8c06}, //谆
	{0xd7bc, 0x51c6}, //准
	{0xd7bd, 0x6349}, //捉
	{0xd7be, 0x62d9}, //拙
	{0xd7bf, 0x5353}, //卓
	{0xd7c0, 0x684c}, //桌
	{0xd7c1, 0x7422}, //琢
	{0xd7c2, 0x8301}, //茁
	{0xd7c3, 0x914c}, //酌
	{0xd7c4, 0x5544}, //啄
	{0xd7c5, 0x7740}, //着
	{0xd7c6, 0x707c}, //灼
	{0xd7c7, 0x6d4a}, //浊
	{0xd7c8, 0x5179}, //兹
	{0xd7c9, 0x54a8}, //咨
	{0xd7ca, 0x8d44}, //资
	{0xd7cb, 0x59ff}, //姿
	{0xd7cc, 0x6ecb}, //滋
	{0xd7cd, 0x6dc4}, //淄
	{0xd7ce, 0x5b5c}, //孜
	{0xd7cf, 0x7d2b}, //紫
	{0xd7d0, 0x4ed4}, //仔
	{0xd7d1, 0x7c7d}, //籽
	{0xd7d2, 0x6ed3}, //滓
	{0xd7d3, 0x5b50}, //子
	{0xd7d4, 0x81ea}, //自
	{0xd7d5, 0x6e0d}, //渍
	{0xd7d6, 0x5b57}, //字
	{0xd7d7, 0x9b03}, //鬃
	{0xd7d8, 0x68d5}, //棕
	{0xd7d9, 0x8e2a}, //踪
	{0xd7da, 0x5b97}, //宗
	{0xd7db, 0x7efc}, //综
	{0xd7dc, 0x603b}, //总
	{0xd7dd, 0x7eb5}, //纵
	{0xd7de, 0x90b9}, //邹
	{0xd7df, 0x8d70}, //走
	{0xd7e0, 0x594f}, //奏
	{0xd7e1, 0x63cd}, //揍
	{0xd7e2, 0x79df}, //租
	{0xd7e3, 0x8db3}, //足
	{0xd7e4, 0x5352}, //卒
	{0xd7e5, 0x65cf}, //族
	{0xd7e6, 0x7956}, //祖
	{0xd7e7, 0x8bc5}, //诅
	{0xd7e8, 0x963b}, //阻
	{0xd7e9, 0x7ec4}, //组
	{0xd7ea, 0x94bb}, //钻
	{0xd7eb, 0x7e82}, //纂
	{0xd7ec, 0x5634}, //嘴
	{0xd7ed, 0x9189}, //醉
	{0xd7ee, 0x6700}, //最
	{0xd7ef, 0x7f6a}, //罪
	{0xd7f0, 0x5c0a}, //尊
	{0xd7f1, 0x9075}, //遵
	{0xd7f2, 0x6628}, //昨
	{0xd7f3, 0x5de6}, //左
	{0xd7f4, 0x4f50}, //佐
	{0xd7f5, 0x67de}, //柞
	{0xd7f6, 0x505a}, //做
	{0xd7f7, 0x4f5c}, //作
	{0xd7f8, 0x5750}, //坐
	{0xd7f9, 0x5ea7}, //座
	{0xd8a1, 0x4e8d}, //亍
	{0xd8a2, 0x4e0c}, //丌
	{0xd8a3, 0x5140}, //兀
	{0xd8a4, 0x4e10}, //丐
	{0xd8a5, 0x5eff}, //廿
	{0xd8a6, 0x5345}, //卅
	{0xd8a7, 0x4e15}, //丕
	{0xd8a8, 0x4e98}, //亘
	{0xd8a9, 0x4e1e}, //丞
	{0xd8aa, 0x9b32}, //鬲
	{0xd8ab, 0x5b6c}, //孬
	{0xd8ac, 0x5669}, //噩
	{0xd8ad, 0x4e28}, //丨
	{0xd8ae, 0x79ba}, //禺
	{0xd8af, 0x4e3f}, //丿
	{0xd8b0, 0x5315}, //匕
	{0xd8b1, 0x4e47}, //乇
	{0xd8b2, 0x592d}, //夭
	{0xd8b3, 0x723b}, //爻
	{0xd8b4, 0x536e}, //卮
	{0xd8b5, 0x6c10}, //氐
	{0xd8b6, 0x56df}, //囟
	{0xd8b7, 0x80e4}, //胤
	{0xd8b8, 0x9997}, //馗
	{0xd8b9, 0x6bd3}, //毓
	{0xd8ba, 0x777e}, //睾
	{0xd8bb, 0x9f17}, //鼗
	{0xd8bc, 0x4e36}, //丶
	{0xd8bd, 0x4e9f}, //亟
	{0xd8be, 0x9f10}, //鼐
	{0xd8bf, 0x4e5c}, //乜
	{0xd8c0, 0x4e69}, //乩
	{0xd8c1, 0x4e93}, //亓
	{0xd8c2, 0x8288}, //芈
	{0xd8c3, 0x5b5b}, //孛
	{0xd8c4, 0x556c}, //啬
	{0xd8c5, 0x560f}, //嘏
	{0xd8c6, 0x4ec4}, //仄
	{0xd8c7, 0x538d}, //厍
	{0xd8c8, 0x539d}, //厝
	{0xd8c9, 0x53a3}, //厣
	{0xd8ca, 0x53a5}, //厥
	{0xd8cb, 0x53ae}, //厮
	{0xd8cc, 0x9765}, //靥
	{0xd8cd, 0x8d5d}, //赝
	{0xd8ce, 0x531a}, //匚
	{0xd8cf, 0x53f5}, //叵
	{0xd8d0, 0x5326}, //匦
	{0xd8d1, 0x532e}, //匮
	{0xd8d2, 0x533e}, //匾
	{0xd8d3, 0x8d5c}, //赜
	{0xd8d4, 0x5366}, //卦
	{0xd8d5, 0x5363}, //卣
	{0xd8d6, 0x5202}, //刂
	{0xd8d7, 0x5208}, //刈
	{0xd8d8, 0x520e}, //刎
	{0xd8d9, 0x522d}, //刭
	{0xd8da, 0x5233}, //刳
	{0xd8db, 0x523f}, //刿
	{0xd8dc, 0x5240}, //剀
	{0xd8dd, 0x524c}, //剌
	{0xd8de, 0x525e}, //剞
	{0xd8df, 0x5261}, //剡
	{0xd8e0, 0x525c}, //剜
	{0xd8e1, 0x84af}, //蒯
	{0xd8e2, 0x527d}, //剽
	{0xd8e3, 0x5282}, //劂
	{0xd8e4, 0x5281}, //劁
	{0xd8e5, 0x5290}, //劐
	{0xd8e6, 0x5293}, //劓
	{0xd8e7, 0x5182}, //冂
	{0xd8e8, 0x7f54}, //罔
	{0xd8e9, 0x4ebb}, //亻
	{0xd8ea, 0x4ec3}, //仃
	{0xd8eb, 0x4ec9}, //仉
	{0xd8ec, 0x4ec2}, //仂
	{0xd8ed, 0x4ee8}, //仨
	{0xd8ee, 0x4ee1}, //仡
	{0xd8ef, 0x4eeb}, //仫
	{0xd8f0, 0x4ede}, //仞
	{0xd8f1, 0x4f1b}, //伛
	{0xd8f2, 0x4ef3}, //仳
	{0xd8f3, 0x4f22}, //伢
	{0xd8f4, 0x4f64}, //佤
	{0xd8f5, 0x4ef5}, //仵
	{0xd8f6, 0x4f25}, //伥
	{0xd8f7, 0x4f27}, //伧
	{0xd8f8, 0x4f09}, //伉
	{0xd8f9, 0x4f2b}, //伫
	{0xd8fa, 0x4f5e}, //佞
	{0xd8fb, 0x4f67}, //佧
	{0xd8fc, 0x6538}, //攸
	{0xd8fd, 0x4f5a}, //佚
	{0xd8fe, 0x4f5d}, //佝
	{0xd9a1, 0x4f5f}, //佟
	{0xd9a2, 0x4f57}, //佗
	{0xd9a3, 0x4f32}, //伲
	{0xd9a4, 0x4f3d}, //伽
	{0xd9a5, 0x4f76}, //佶
	{0xd9a6, 0x4f74}, //佴
	{0xd9a7, 0x4f91}, //侑
	{0xd9a8, 0x4f89}, //侉
	{0xd9a9, 0x4f83}, //侃
	{0xd9aa, 0x4f8f}, //侏
	{0xd9ab, 0x4f7e}, //佾
	{0xd9ac, 0x4f7b}, //佻
	{0xd9ad, 0x4faa}, //侪
	{0xd9ae, 0x4f7c}, //佼
	{0xd9af, 0x4fac}, //侬
	{0xd9b0, 0x4f94}, //侔
	{0xd9b1, 0x4fe6}, //俦
	{0xd9b2, 0x4fe8}, //俨
	{0xd9b3, 0x4fea}, //俪
	{0xd9b4, 0x4fc5}, //俅
	{0xd9b5, 0x4fda}, //俚
	{0xd9b6, 0x4fe3}, //俣
	{0xd9b7, 0x4fdc}, //俜
	{0xd9b8, 0x4fd1}, //俑
	{0xd9b9, 0x4fdf}, //俟
	{0xd9ba, 0x4ff8}, //俸
	{0xd9bb, 0x5029}, //倩
	{0xd9bc, 0x504c}, //偌
	{0xd9bd, 0x4ff3}, //俳
	{0xd9be, 0x502c}, //倬
	{0xd9bf, 0x500f}, //倏
	{0xd9c0, 0x502e}, //倮
	{0xd9c1, 0x502d}, //倭
	{0xd9c2, 0x4ffe}, //俾
	{0xd9c3, 0x501c}, //倜
	{0xd9c4, 0x500c}, //倌
	{0xd9c5, 0x5025}, //倥
	{0xd9c6, 0x5028}, //倨
	{0xd9c7, 0x507e}, //偾
	{0xd9c8, 0x5043}, //偃
	{0xd9c9, 0x5055}, //偕
	{0xd9ca, 0x5048}, //偈
	{0xd9cb, 0x504e}, //偎
	{0xd9cc, 0x506c}, //偬
	{0xd9cd, 0x507b}, //偻
	{0xd9ce, 0x50a5}, //傥
	{0xd9cf, 0x50a7}, //傧
	{0xd9d0, 0x50a9}, //傩
	{0xd9d1, 0x50ba}, //傺
	{0xd9d2, 0x50d6}, //僖
	{0xd9d3, 0x5106}, //儆
	{0xd9d4, 0x50ed}, //僭
	{0xd9d5, 0x50ec}, //僬
	{0xd9d6, 0x50e6}, //僦
	{0xd9d7, 0x50ee}, //僮
	{0xd9d8, 0x5107}, //儇
	{0xd9d9, 0x510b}, //儋
	{0xd9da, 0x4edd}, //仝
	{0xd9db, 0x6c3d}, //氽
	{0xd9dc, 0x4f58}, //佘
	{0xd9dd, 0x4f65}, //佥
	{0xd9de, 0x4fce}, //俎
	{0xd9df, 0x9fa0}, //龠
	{0xd9e0, 0x6c46}, //汆
	{0xd9e1, 0x7c74}, //籴
	{0xd9e2, 0x516e}, //兮
	{0xd9e3, 0x5dfd}, //巽
	{0xd9e4, 0x9ec9}, //黉
	{0xd9e5, 0x9998}, //馘
	{0xd9e6, 0x5181}, //冁
	{0xd9e7, 0x5914}, //夔
	{0xd9e8, 0x52f9}, //勹
	{0xd9e9, 0x530d}, //匍
	{0xd9ea, 0x8a07}, //訇
	{0xd9eb, 0x5310}, //匐
	{0xd9ec, 0x51eb}, //凫
	{0xd9ed, 0x5919}, //夙
	{0xd9ee, 0x5155}, //兕
	{0xd9ef, 0x4ea0}, //亠
	{0xd9f0, 0x5156}, //兖
	{0xd9f1, 0x4eb3}, //亳
	{0xd9f2, 0x886e}, //衮
	{0xd9f3, 0x88a4}, //袤
	{0xd9f4, 0x4eb5}, //亵
	{0xd9f5, 0x8114}, //脔
	{0xd9f6, 0x88d2}, //裒
	{0xd9f7, 0x7980}, //禀
	{0xd9f8, 0x5b34}, //嬴
	{0xd9f9, 0x8803}, //蠃
	{0xd9fa, 0x7fb8}, //羸
	{0xd9fb, 0x51ab}, //冫
	{0xd9fc, 0x51b1}, //冱
	{0xd9fd, 0x51bd}, //冽
	{0xd9fe, 0x51bc}, //冼
	{0xdaa1, 0x51c7}, //凇
	{0xdaa2, 0x5196}, //冖
	{0xdaa3, 0x51a2}, //冢
	{0xdaa4, 0x51a5}, //冥
	{0xdaa5, 0x8ba0}, //讠
	{0xdaa6, 0x8ba6}, //讦
	{0xdaa7, 0x8ba7}, //讧
	{0xdaa8, 0x8baa}, //讪
	{0xdaa9, 0x8bb4}, //讴
	{0xdaaa, 0x8bb5}, //讵
	{0xdaab, 0x8bb7}, //讷
	{0xdaac, 0x8bc2}, //诂
	{0xdaad, 0x8bc3}, //诃
	{0xdaae, 0x8bcb}, //诋
	{0xdaaf, 0x8bcf}, //诏
	{0xdab0, 0x8bce}, //诎
	{0xdab1, 0x8bd2}, //诒
	{0xdab2, 0x8bd3}, //诓
	{0xdab3, 0x8bd4}, //诔
	{0xdab4, 0x8bd6}, //诖
	{0xdab5, 0x8bd8}, //诘
	{0xdab6, 0x8bd9}, //诙
	{0xdab7, 0x8bdc}, //诜
	{0xdab8, 0x8bdf}, //诟
	{0xdab9, 0x8be0}, //诠
	{0xdaba, 0x8be4}, //诤
	{0xdabb, 0x8be8}, //诨
	{0xdabc, 0x8be9}, //诩
	{0xdabd, 0x8bee}, //诮
	{0xdabe, 0x8bf0}, //诰
	{0xdabf, 0x8bf3}, //诳
	{0xdac0, 0x8bf6}, //诶
	{0xdac1, 0x8bf9}, //诹
	{0xdac2, 0x8bfc}, //诼
	{0xdac3, 0x8bff}, //诿
	{0xdac4, 0x8c00}, //谀
	{0xdac5, 0x8c02}, //谂
	{0xdac6, 0x8c04}, //谄
	{0xdac7, 0x8c07}, //谇
	{0xdac8, 0x8c0c}, //谌
	{0xdac9, 0x8c0f}, //谏
	{0xdaca, 0x8c11}, //谑
	{0xdacb, 0x8c12}, //谒
	{0xdacc, 0x8c14}, //谔
	{0xdacd, 0x8c15}, //谕
	{0xdace, 0x8c16}, //谖
	{0xdacf, 0x8c19}, //谙
	{0xdad0, 0x8c1b}, //谛
	{0xdad1, 0x8c18}, //谘
	{0xdad2, 0x8c1d}, //谝
	{0xdad3, 0x8c1f}, //谟
	{0xdad4, 0x8c20}, //谠
	{0xdad5, 0x8c21}, //谡
	{0xdad6, 0x8c25}, //谥
	{0xdad7, 0x8c27}, //谧
	{0xdad8, 0x8c2a}, //谪
	{0xdad9, 0x8c2b}, //谫
	{0xdada, 0x8c2e}, //谮
	{0xdadb, 0x8c2f}, //谯
	{0xdadc, 0x8c32}, //谲
	{0xdadd, 0x8c33}, //谳
	{0xdade, 0x8c35}, //谵
	{0xdadf, 0x8c36}, //谶
	{0xdae0, 0x5369}, //卩
	{0xdae1, 0x537a}, //卺
	{0xdae2, 0x961d}, //阝
	{0xdae3, 0x9622}, //阢
	{0xdae4, 0x9621}, //阡
	{0xdae5, 0x9631}, //阱
	{0xdae6, 0x962a}, //阪
	{0xdae7, 0x963d}, //阽
	{0xdae8, 0x963c}, //阼
	{0xdae9, 0x9642}, //陂
	{0xdaea, 0x9649}, //陉
	{0xdaeb, 0x9654}, //陔
	{0xdaec, 0x965f}, //陟
	{0xdaed, 0x9667}, //陧
	{0xdaee, 0x966c}, //陬
	{0xdaef, 0x9672}, //陲
	{0xdaf0, 0x9674}, //陴
	{0xdaf1, 0x9688}, //隈
	{0xdaf2, 0x968d}, //隍
	{0xdaf3, 0x9697}, //隗
	{0xdaf4, 0x96b0}, //隰
	{0xdaf5, 0x9097}, //邗
	{0xdaf6, 0x909b}, //邛
	{0xdaf7, 0x909d}, //邝
	{0xdaf8, 0x9099}, //邙
	{0xdaf9, 0x90ac}, //邬
	{0xdafa, 0x90a1}, //邡
	{0xdafb, 0x90b4}, //邴
	{0xdafc, 0x90b3}, //邳
	{0xdafd, 0x90b6}, //邶
	{0xdafe, 0x90ba}, //邺
	{0xdba1, 0x90b8}, //邸
	{0xdba2, 0x90b0}, //邰
	{0xdba3, 0x90cf}, //郏
	{0xdba4, 0x90c5}, //郅
	{0xdba5, 0x90be}, //邾
	{0xdba6, 0x90d0}, //郐
	{0xdba7, 0x90c4}, //郄
	{0xdba8, 0x90c7}, //郇
	{0xdba9, 0x90d3}, //郓
	{0xdbaa, 0x90e6}, //郦
	{0xdbab, 0x90e2}, //郢
	{0xdbac, 0x90dc}, //郜
	{0xdbad, 0x90d7}, //郗
	{0xdbae, 0x90db}, //郛
	{0xdbaf, 0x90eb}, //郫
	{0xdbb0, 0x90ef}, //郯
	{0xdbb1, 0x90fe}, //郾
	{0xdbb2, 0x9104}, //鄄
	{0xdbb3, 0x9122}, //鄢
	{0xdbb4, 0x911e}, //鄞
	{0xdbb5, 0x9123}, //鄣
	{0xdbb6, 0x9131}, //鄱
	{0xdbb7, 0x912f}, //鄯
	{0xdbb8, 0x9139}, //鄹
	{0xdbb9, 0x9143}, //酃
	{0xdbba, 0x9146}, //酆
	{0xdbbb, 0x520d}, //刍
	{0xdbbc, 0x5942}, //奂
	{0xdbbd, 0x52a2}, //劢
	{0xdbbe, 0x52ac}, //劬
	{0xdbbf, 0x52ad}, //劭
	{0xdbc0, 0x52be}, //劾
	{0xdbc1, 0x54ff}, //哿
	{0xdbc2, 0x52d0}, //勐
	{0xdbc3, 0x52d6}, //勖
	{0xdbc4, 0x52f0}, //勰
	{0xdbc5, 0x53df}, //叟
	{0xdbc6, 0x71ee}, //燮
	{0xdbc7, 0x77cd}, //矍
	{0xdbc8, 0x5ef4}, //廴
	{0xdbc9, 0x51f5}, //凵
	{0xdbca, 0x51fc}, //凼
	{0xdbcb, 0x9b2f}, //鬯
	{0xdbcc, 0x53b6}, //厶
	{0xdbcd, 0x5f01}, //弁
	{0xdbce, 0x755a}, //畚
	{0xdbcf, 0x5def}, //巯
	{0xdbd0, 0x574c}, //坌
	{0xdbd1, 0x57a9}, //垩
	{0xdbd2, 0x57a1}, //垡
	{0xdbd3, 0x587e}, //塾
	{0xdbd4, 0x58bc}, //墼
	{0xdbd5, 0x58c5}, //壅
	{0xdbd6, 0x58d1}, //壑
	{0xdbd7, 0x5729}, //圩
	{0xdbd8, 0x572c}, //圬
	{0xdbd9, 0x572a}, //圪
	{0xdbda, 0x5733}, //圳
	{0xdbdb, 0x5739}, //圹
	{0xdbdc, 0x572e}, //圮
	{0xdbdd, 0x572f}, //圯
	{0xdbde, 0x575c}, //坜
	{0xdbdf, 0x573b}, //圻
	{0xdbe0, 0x5742}, //坂
	{0xdbe1, 0x5769}, //坩
	{0xdbe2, 0x5785}, //垅
	{0xdbe3, 0x576b}, //坫
	{0xdbe4, 0x5786}, //垆
	{0xdbe5, 0x577c}, //坼
	{0xdbe6, 0x577b}, //坻
	{0xdbe7, 0x5768}, //坨
	{0xdbe8, 0x576d}, //坭
	{0xdbe9, 0x5776}, //坶
	{0xdbea, 0x5773}, //坳
	{0xdbeb, 0x57ad}, //垭
	{0xdbec, 0x57a4}, //垤
	{0xdbed, 0x578c}, //垌
	{0xdbee, 0x57b2}, //垲
	{0xdbef, 0x57cf}, //埏
	{0xdbf0, 0x57a7}, //垧
	{0xdbf1, 0x57b4}, //垴
	{0xdbf2, 0x5793}, //垓
	{0xdbf3, 0x57a0}, //垠
	{0xdbf4, 0x57d5}, //埕
	{0xdbf5, 0x57d8}, //埘
	{0xdbf6, 0x57da}, //埚
	{0xdbf7, 0x57d9}, //埙
	{0xdbf8, 0x57d2}, //埒
	{0xdbf9, 0x57b8}, //垸
	{0xdbfa, 0x57f4}, //埴
	{0xdbfb, 0x57ef}, //埯
	{0xdbfc, 0x57f8}, //埸
	{0xdbfd, 0x57e4}, //埤
	{0xdbfe, 0x57dd}, //埝
	{0xdca1, 0x580b}, //堋
	{0xdca2, 0x580d}, //堍
	{0xdca3, 0x57fd}, //埽
	{0xdca4, 0x57ed}, //埭
	{0xdca5, 0x5800}, //堀
	{0xdca6, 0x581e}, //堞
	{0xdca7, 0x5819}, //堙
	{0xdca8, 0x5844}, //塄
	{0xdca9, 0x5820}, //堠
	{0xdcaa, 0x5865}, //塥
	{0xdcab, 0x586c}, //塬
	{0xdcac, 0x5881}, //墁
	{0xdcad, 0x5889}, //墉
	{0xdcae, 0x589a}, //墚
	{0xdcaf, 0x5880}, //墀
	{0xdcb0, 0x99a8}, //馨
	{0xdcb1, 0x9f19}, //鼙
	{0xdcb2, 0x61ff}, //懿
	{0xdcb3, 0x8279}, //艹
	{0xdcb4, 0x827d}, //艽
	{0xdcb5, 0x827f}, //艿
	{0xdcb6, 0x828f}, //芏
	{0xdcb7, 0x828a}, //芊
	{0xdcb8, 0x82a8}, //芨
	{0xdcb9, 0x8284}, //芄
	{0xdcba, 0x828e}, //芎
	{0xdcbb, 0x8291}, //芑
	{0xdcbc, 0x8297}, //芗
	{0xdcbd, 0x8299}, //芙
	{0xdcbe, 0x82ab}, //芫
	{0xdcbf, 0x82b8}, //芸
	{0xdcc0, 0x82be}, //芾
	{0xdcc1, 0x82b0}, //芰
	{0xdcc2, 0x82c8}, //苈
	{0xdcc3, 0x82ca}, //苊
	{0xdcc4, 0x82e3}, //苣
	{0xdcc5, 0x8298}, //芘
	{0xdcc6, 0x82b7}, //芷
	{0xdcc7, 0x82ae}, //芮
	{0xdcc8, 0x82cb}, //苋
	{0xdcc9, 0x82cc}, //苌
	{0xdcca, 0x82c1}, //苁
	{0xdccb, 0x82a9}, //芩
	{0xdccc, 0x82b4}, //芴
	{0xdccd, 0x82a1}, //芡
	{0xdcce, 0x82aa}, //芪
	{0xdccf, 0x829f}, //芟
	{0xdcd0, 0x82c4}, //苄
	{0xdcd1, 0x82ce}, //苎
	{0xdcd2, 0x82a4}, //芤
	{0xdcd3, 0x82e1}, //苡
	{0xdcd4, 0x8309}, //茉
	{0xdcd5, 0x82f7}, //苷
	{0xdcd6, 0x82e4}, //苤
	{0xdcd7, 0x830f}, //茏
	{0xdcd8, 0x8307}, //茇
	{0xdcd9, 0x82dc}, //苜
	{0xdcda, 0x82f4}, //苴
	{0xdcdb, 0x82d2}, //苒
	{0xdcdc, 0x82d8}, //苘
	{0xdcdd, 0x830c}, //茌
	{0xdcde, 0x82fb}, //苻
	{0xdcdf, 0x82d3}, //苓
	{0xdce0, 0x8311}, //茑
	{0xdce1, 0x831a}, //茚
	{0xdce2, 0x8306}, //茆
	{0xdce3, 0x8314}, //茔
	{0xdce4, 0x8315}, //茕
	{0xdce5, 0x82e0}, //苠
	{0xdce6, 0x82d5}, //苕
	{0xdce7, 0x831c}, //茜
	{0xdce8, 0x8351}, //荑
	{0xdce9, 0x835b}, //荛
	{0xdcea, 0x835c}, //荜
	{0xdceb, 0x8308}, //茈
	{0xdcec, 0x8392}, //莒
	{0xdced, 0x833c}, //茼
	{0xdcee, 0x8334}, //茴
	{0xdcef, 0x8331}, //茱
	{0xdcf0, 0x839b}, //莛
	{0xdcf1, 0x835e}, //荞
	{0xdcf2, 0x832f}, //茯
	{0xdcf3, 0x834f}, //荏
	{0xdcf4, 0x8347}, //荇
	{0xdcf5, 0x8343}, //荃
	{0xdcf6, 0x835f}, //荟
	{0xdcf7, 0x8340}, //荀
	{0xdcf8, 0x8317}, //茗
	{0xdcf9, 0x8360}, //荠
	{0xdcfa, 0x832d}, //茭
	{0xdcfb, 0x833a}, //茺
	{0xdcfc, 0x8333}, //茳
	{0xdcfd, 0x8366}, //荦
	{0xdcfe, 0x8365}, //荥
	{0xdda1, 0x8368}, //荨
	{0xdda2, 0x831b}, //茛
	{0xdda3, 0x8369}, //荩
	{0xdda4, 0x836c}, //荬
	{0xdda5, 0x836a}, //荪
	{0xdda6, 0x836d}, //荭
	{0xdda7, 0x836e}, //荮
	{0xdda8, 0x83b0}, //莰
	{0xdda9, 0x8378}, //荸
	{0xddaa, 0x83b3}, //莳
	{0xddab, 0x83b4}, //莴
	{0xddac, 0x83a0}, //莠
	{0xddad, 0x83aa}, //莪
	{0xddae, 0x8393}, //莓
	{0xddaf, 0x839c}, //莜
	{0xddb0, 0x8385}, //莅
	{0xddb1, 0x837c}, //荼
	{0xddb2, 0x83b6}, //莶
	{0xddb3, 0x83a9}, //莩
	{0xddb4, 0x837d}, //荽
	{0xddb5, 0x83b8}, //莸
	{0xddb6, 0x837b}, //荻
	{0xddb7, 0x8398}, //莘
	{0xddb8, 0x839e}, //莞
	{0xddb9, 0x83a8}, //莨
	{0xddba, 0x83ba}, //莺
	{0xddbb, 0x83bc}, //莼
	{0xddbc, 0x83c1}, //菁
	{0xddbd, 0x8401}, //萁
	{0xddbe, 0x83e5}, //菥
	{0xddbf, 0x83d8}, //菘
	{0xddc0, 0x5807}, //堇
	{0xddc1, 0x8418}, //萘
	{0xddc2, 0x840b}, //萋
	{0xddc3, 0x83dd}, //菝
	{0xddc4, 0x83fd}, //菽
	{0xddc5, 0x83d6}, //菖
	{0xddc6, 0x841c}, //萜
	{0xddc7, 0x8438}, //萸
	{0xddc8, 0x8411}, //萑
	{0xddc9, 0x8406}, //萆
	{0xddca, 0x83d4}, //菔
	{0xddcb, 0x83df}, //菟
	{0xddcc, 0x840f}, //萏
	{0xddcd, 0x8403}, //萃
	{0xddce, 0x83f8}, //菸
	{0xddcf, 0x83f9}, //菹
	{0xddd0, 0x83ea}, //菪
	{0xddd1, 0x83c5}, //菅
	{0xddd2, 0x83c0}, //菀
	{0xddd3, 0x8426}, //萦
	{0xddd4, 0x83f0}, //菰
	{0xddd5, 0x83e1}, //菡
	{0xddd6, 0x845c}, //葜
	{0xddd7, 0x8451}, //葑
	{0xddd8, 0x845a}, //葚
	{0xddd9, 0x8459}, //葙
	{0xddda, 0x8473}, //葳
	{0xdddb, 0x8487}, //蒇
	{0xdddc, 0x8488}, //蒈
	{0xdddd, 0x847a}, //葺
	{0xddde, 0x8489}, //蒉
	{0xdddf, 0x8478}, //葸
	{0xdde0, 0x843c}, //萼
	{0xdde1, 0x8446}, //葆
	{0xdde2, 0x8469}, //葩
	{0xdde3, 0x8476}, //葶
	{0xdde4, 0x848c}, //蒌
	{0xdde5, 0x848e}, //蒎
	{0xdde6, 0x8431}, //萱
	{0xdde7, 0x846d}, //葭
	{0xdde8, 0x84c1}, //蓁
	{0xdde9, 0x84cd}, //蓍
	{0xddea, 0x84d0}, //蓐
	{0xddeb, 0x84e6}, //蓦
	{0xddec, 0x84bd}, //蒽
	{0xdded, 0x84d3}, //蓓
	{0xddee, 0x84ca}, //蓊
	{0xddef, 0x84bf}, //蒿
	{0xddf0, 0x84ba}, //蒺
	{0xddf1, 0x84e0}, //蓠
	{0xddf2, 0x84a1}, //蒡
	{0xddf3, 0x84b9}, //蒹
	{0xddf4, 0x84b4}, //蒴
	{0xddf5, 0x8497}, //蒗
	{0xddf6, 0x84e5}, //蓥
	{0xddf7, 0x84e3}, //蓣
	{0xddf8, 0x850c}, //蔌
	{0xddf9, 0x750d}, //甍
	{0xddfa, 0x8538}, //蔸
	{0xddfb, 0x84f0}, //蓰
	{0xddfc, 0x8539}, //蔹
	{0xddfd, 0x851f}, //蔟
	{0xddfe, 0x853a}, //蔺
	{0xdea1, 0x8556}, //蕖
	{0xdea2, 0x853b}, //蔻
	{0xdea3, 0x84ff}, //蓿
	{0xdea4, 0x84fc}, //蓼
	{0xdea5, 0x8559}, //蕙
	{0xdea6, 0x8548}, //蕈
	{0xdea7, 0x8568}, //蕨
	{0xdea8, 0x8564}, //蕤
	{0xdea9, 0x855e}, //蕞
	{0xdeaa, 0x857a}, //蕺
	{0xdeab, 0x77a2}, //瞢
	{0xdeac, 0x8543}, //蕃
	{0xdead, 0x8572}, //蕲
	{0xdeae, 0x857b}, //蕻
	{0xdeaf, 0x85a4}, //薤
	{0xdeb0, 0x85a8}, //薨
	{0xdeb1, 0x8587}, //薇
	{0xdeb2, 0x858f}, //薏
	{0xdeb3, 0x8579}, //蕹
	{0xdeb4, 0x85ae}, //薮
	{0xdeb5, 0x859c}, //薜
	{0xdeb6, 0x8585}, //薅
	{0xdeb7, 0x85b9}, //薹
	{0xdeb8, 0x85b7}, //薷
	{0xdeb9, 0x85b0}, //薰
	{0xdeba, 0x85d3}, //藓
	{0xdebb, 0x85c1}, //藁
	{0xdebc, 0x85dc}, //藜
	{0xdebd, 0x85ff}, //藿
	{0xdebe, 0x8627}, //蘧
	{0xdebf, 0x8605}, //蘅
	{0xdec0, 0x8629}, //蘩
	{0xdec1, 0x8616}, //蘖
	{0xdec2, 0x863c}, //蘼
	{0xdec3, 0x5efe}, //廾
	{0xdec4, 0x5f08}, //弈
	{0xdec5, 0x593c}, //夼
	{0xdec6, 0x5941}, //奁
	{0xdec7, 0x8037}, //耷
	{0xdec8, 0x5955}, //奕
	{0xdec9, 0x595a}, //奚
	{0xdeca, 0x5958}, //奘
	{0xdecb, 0x530f}, //匏
	{0xdecc, 0x5c22}, //尢
	{0xdecd, 0x5c25}, //尥
	{0xdece, 0x5c2c}, //尬
	{0xdecf, 0x5c34}, //尴
	{0xded0, 0x624c}, //扌
	{0xded1, 0x626a}, //扪
	{0xded2, 0x629f}, //抟
	{0xded3, 0x62bb}, //抻
	{0xded4, 0x62ca}, //拊
	{0xded5, 0x62da}, //拚
	{0xded6, 0x62d7}, //拗
	{0xded7, 0x62ee}, //拮
	{0xded8, 0x6322}, //挢
	{0xded9, 0x62f6}, //拶
	{0xdeda, 0x6339}, //挹
	{0xdedb, 0x634b}, //捋
	{0xdedc, 0x6343}, //捃
	{0xdedd, 0x63ad}, //掭
	{0xdede, 0x63f6}, //揶
	{0xdedf, 0x6371}, //捱
	{0xdee0, 0x637a}, //捺
	{0xdee1, 0x638e}, //掎
	{0xdee2, 0x63b4}, //掴
	{0xdee3, 0x636d}, //捭
	{0xdee4, 0x63ac}, //掬
	{0xdee5, 0x638a}, //掊
	{0xdee6, 0x6369}, //捩
	{0xdee7, 0x63ae}, //掮
	{0xdee8, 0x63bc}, //掼
	{0xdee9, 0x63f2}, //揲
	{0xdeea, 0x63f8}, //揸
	{0xdeeb, 0x63e0}, //揠
	{0xdeec, 0x63ff}, //揿
	{0xdeed, 0x63c4}, //揄
	{0xdeee, 0x63de}, //揞
	{0xdeef, 0x63ce}, //揎
	{0xdef0, 0x6452}, //摒
	{0xdef1, 0x63c6}, //揆
	{0xdef2, 0x63be}, //掾
	{0xdef3, 0x6445}, //摅
	{0xdef4, 0x6441}, //摁
	{0xdef5, 0x640b}, //搋
	{0xdef6, 0x641b}, //搛
	{0xdef7, 0x6420}, //搠
	{0xdef8, 0x640c}, //搌
	{0xdef9, 0x6426}, //搦
	{0xdefa, 0x6421}, //搡
	{0xdefb, 0x645e}, //摞
	{0xdefc, 0x6484}, //撄
	{0xdefd, 0x646d}, //摭
	{0xdefe, 0x6496}, //撖
	{0xdfa1, 0x647a}, //摺
	{0xdfa2, 0x64b7}, //撷
	{0xdfa3, 0x64b8}, //撸
	{0xdfa4, 0x6499}, //撙
	{0xdfa5, 0x64ba}, //撺
	{0xdfa6, 0x64c0}, //擀
	{0xdfa7, 0x64d0}, //擐
	{0xdfa8, 0x64d7}, //擗
	{0xdfa9, 0x64e4}, //擤
	{0xdfaa, 0x64e2}, //擢
	{0xdfab, 0x6509}, //攉
	{0xdfac, 0x6525}, //攥
	{0xdfad, 0x652e}, //攮
	{0xdfae, 0x5f0b}, //弋
	{0xdfaf, 0x5fd2}, //忒
	{0xdfb0, 0x7519}, //甙
	{0xdfb1, 0x5f11}, //弑
	{0xdfb2, 0x535f}, //卟
	{0xdfb3, 0x53f1}, //叱
	{0xdfb4, 0x53fd}, //叽
	{0xdfb5, 0x53e9}, //叩
	{0xdfb6, 0x53e8}, //叨
	{0xdfb7, 0x53fb}, //叻
	{0xdfb8, 0x5412}, //吒
	{0xdfb9, 0x5416}, //吖
	{0xdfba, 0x5406}, //吆
	{0xdfbb, 0x544b}, //呋
	{0xdfbc, 0x5452}, //呒
	{0xdfbd, 0x5453}, //呓
	{0xdfbe, 0x5454}, //呔
	{0xdfbf, 0x5456}, //呖
	{0xdfc0, 0x5443}, //呃
	{0xdfc1, 0x5421}, //吡
	{0xdfc2, 0x5457}, //呗
	{0xdfc3, 0x5459}, //呙
	{0xdfc4, 0x5423}, //吣
	{0xdfc5, 0x5432}, //吲
	{0xdfc6, 0x5482}, //咂
	{0xdfc7, 0x5494}, //咔
	{0xdfc8, 0x5477}, //呷
	{0xdfc9, 0x5471}, //呱
	{0xdfca, 0x5464}, //呤
	{0xdfcb, 0x549a}, //咚
	{0xdfcc, 0x549b}, //咛
	{0xdfcd, 0x5484}, //咄
	{0xdfce, 0x5476}, //呶
	{0xdfcf, 0x5466}, //呦
	{0xdfd0, 0x549d}, //咝
	{0xdfd1, 0x54d0}, //哐
	{0xdfd2, 0x54ad}, //咭
	{0xdfd3, 0x54c2}, //哂
	{0xdfd4, 0x54b4}, //咴
	{0xdfd5, 0x54d2}, //哒
	{0xdfd6, 0x54a7}, //咧
	{0xdfd7, 0x54a6}, //咦
	{0xdfd8, 0x54d3}, //哓
	{0xdfd9, 0x54d4}, //哔
	{0xdfda, 0x5472}, //呲
	{0xdfdb, 0x54a3}, //咣
	{0xdfdc, 0x54d5}, //哕
	{0xdfdd, 0x54bb}, //咻
	{0xdfde, 0x54bf}, //咿
	{0xdfdf, 0x54cc}, //哌
	{0xdfe0, 0x54d9}, //哙
	{0xdfe1, 0x54da}, //哚
	{0xdfe2, 0x54dc}, //哜
	{0xdfe3, 0x54a9}, //咩
	{0xdfe4, 0x54aa}, //咪
	{0xdfe5, 0x54a4}, //咤
	{0xdfe6, 0x54dd}, //哝
	{0xdfe7, 0x54cf}, //哏
	{0xdfe8, 0x54de}, //哞
	{0xdfe9, 0x551b}, //唛
	{0xdfea, 0x54e7}, //哧
	{0xdfeb, 0x5520}, //唠
	{0xdfec, 0x54fd}, //哽
	{0xdfed, 0x5514}, //唔
	{0xdfee, 0x54f3}, //哳
	{0xdfef, 0x5522}, //唢
	{0xdff0, 0x5523}, //唣
	{0xdff1, 0x550f}, //唏
	{0xdff2, 0x5511}, //唑
	{0xdff3, 0x5527}, //唧
	{0xdff4, 0x552a}, //唪
	{0xdff5, 0x5567}, //啧
	{0xdff6, 0x558f}, //喏
	{0xdff7, 0x55b5}, //喵
	{0xdff8, 0x5549}, //啉
	{0xdff9, 0x556d}, //啭
	{0xdffa, 0x5541}, //啁
	{0xdffb, 0x5555}, //啕
	{0xdffc, 0x553f}, //唿
	{0xdffd, 0x5550}, //啐
	{0xdffe, 0x553c}, //唼
	{0xe0a1, 0x5537}, //唷
	{0xe0a2, 0x5556}, //啖
	{0xe0a3, 0x5575}, //啵
	{0xe0a4, 0x5576}, //啶
	{0xe0a5, 0x5577}, //啷
	{0xe0a6, 0x5533}, //唳
	{0xe0a7, 0x5530}, //唰
	{0xe0a8, 0x555c}, //啜
	{0xe0a9, 0x558b}, //喋
	{0xe0aa, 0x55d2}, //嗒
	{0xe0ab, 0x5583}, //喃
	{0xe0ac, 0x55b1}, //喱
	{0xe0ad, 0x55b9}, //喹
	{0xe0ae, 0x5588}, //喈
	{0xe0af, 0x5581}, //喁
	{0xe0b0, 0x559f}, //喟
	{0xe0b1, 0x557e}, //啾
	{0xe0b2, 0x55d6}, //嗖
	{0xe0b3, 0x5591}, //喑
	{0xe0b4, 0x557b}, //啻
	{0xe0b5, 0x55df}, //嗟
	{0xe0b6, 0x55bd}, //喽
	{0xe0b7, 0x55be}, //喾
	{0xe0b8, 0x5594}, //喔
	{0xe0b9, 0x5599}, //喙
	{0xe0ba, 0x55ea}, //嗪
	{0xe0bb, 0x55f7}, //嗷
	{0xe0bc, 0x55c9}, //嗉
	{0xe0bd, 0x561f}, //嘟
	{0xe0be, 0x55d1}, //嗑
	{0xe0bf, 0x55eb}, //嗫
	{0xe0c0, 0x55ec}, //嗬
	{0xe0c1, 0x55d4}, //嗔
	{0xe0c2, 0x55e6}, //嗦
	{0xe0c3, 0x55dd}, //嗝
	{0xe0c4, 0x55c4}, //嗄
	{0xe0c5, 0x55ef}, //嗯
	{0xe0c6, 0x55e5}, //嗥
	{0xe0c7, 0x55f2}, //嗲
	{0xe0c8, 0x55f3}, //嗳
	{0xe0c9, 0x55cc}, //嗌
	{0xe0ca, 0x55cd}, //嗍
	{0xe0cb, 0x55e8}, //嗨
	{0xe0cc, 0x55f5}, //嗵
	{0xe0cd, 0x55e4}, //嗤
	{0xe0ce, 0x8f94}, //辔
	{0xe0cf, 0x561e}, //嘞
	{0xe0d0, 0x5608}, //嘈
	{0xe0d1, 0x560c}, //嘌
	{0xe0d2, 0x5601}, //嘁
	{0xe0d3, 0x5624}, //嘤
	{0xe0d4, 0x5623}, //嘣
	{0xe0d5, 0x55fe}, //嗾
	{0xe0d6, 0x5600}, //嘀
	{0xe0d7, 0x5627}, //嘧
	{0xe0d8, 0x562d}, //嘭
	{0xe0d9, 0x5658}, //噘
	{0xe0da, 0x5639}, //嘹
	{0xe0db, 0x5657}, //噗
	{0xe0dc, 0x562c}, //嘬
	{0xe0dd, 0x564d}, //噍
	{0xe0de, 0x5662}, //噢
	{0xe0df, 0x5659}, //噙
	{0xe0e0, 0x565c}, //噜
	{0xe0e1, 0x564c}, //噌
	{0xe0e2, 0x5654}, //噔
	{0xe0e3, 0x5686}, //嚆
	{0xe0e4, 0x5664}, //噤
	{0xe0e5, 0x5671}, //噱
	{0xe0e6, 0x566b}, //噫
	{0xe0e7, 0x567b}, //噻
	{0xe0e8, 0x567c}, //噼
	{0xe0e9, 0x5685}, //嚅
	{0xe0ea, 0x5693}, //嚓
	{0xe0eb, 0x56af}, //嚯
	{0xe0ec, 0x56d4}, //囔
	{0xe0ed, 0x56d7}, //囗
	{0xe0ee, 0x56dd}, //囝
	{0xe0ef, 0x56e1}, //囡
	{0xe0f0, 0x56f5}, //囵
	{0xe0f1, 0x56eb}, //囫
	{0xe0f2, 0x56f9}, //囹
	{0xe0f3, 0x56ff}, //囿
	{0xe0f4, 0x5704}, //圄
	{0xe0f5, 0x570a}, //圊
	{0xe0f6, 0x5709}, //圉
	{0xe0f7, 0x571c}, //圜
	{0xe0f8, 0x5e0f}, //帏
	{0xe0f9, 0x5e19}, //帙
	{0xe0fa, 0x5e14}, //帔
	{0xe0fb, 0x5e11}, //帑
	{0xe0fc, 0x5e31}, //帱
	{0xe0fd, 0x5e3b}, //帻
	{0xe0fe, 0x5e3c}, //帼
	{0xe1a1, 0x5e37}, //帷
	{0xe1a2, 0x5e44}, //幄
	{0xe1a3, 0x5e54}, //幔
	{0xe1a4, 0x5e5b}, //幛
	{0xe1a5, 0x5e5e}, //幞
	{0xe1a6, 0x5e61}, //幡
	{0xe1a7, 0x5c8c}, //岌
	{0xe1a8, 0x5c7a}, //屺
	{0xe1a9, 0x5c8d}, //岍
	{0xe1aa, 0x5c90}, //岐
	{0xe1ab, 0x5c96}, //岖
	{0xe1ac, 0x5c88}, //岈
	{0xe1ad, 0x5c98}, //岘
	{0xe1ae, 0x5c99}, //岙
	{0xe1af, 0x5c91}, //岑
	{0xe1b0, 0x5c9a}, //岚
	{0xe1b1, 0x5c9c}, //岜
	{0xe1b2, 0x5cb5}, //岵
	{0xe1b3, 0x5ca2}, //岢
	{0xe1b4, 0x5cbd}, //岽
	{0xe1b5, 0x5cac}, //岬
	{0xe1b6, 0x5cab}, //岫
	{0xe1b7, 0x5cb1}, //岱
	{0xe1b8, 0x5ca3}, //岣
	{0xe1b9, 0x5cc1}, //峁
	{0xe1ba, 0x5cb7}, //岷
	{0xe1bb, 0x5cc4}, //峄
	{0xe1bc, 0x5cd2}, //峒
	{0xe1bd, 0x5ce4}, //峤
	{0xe1be, 0x5ccb}, //峋
	{0xe1bf, 0x5ce5}, //峥
	{0xe1c0, 0x5d02}, //崂
	{0xe1c1, 0x5d03}, //崃
	{0xe1c2, 0x5d27}, //崧
	{0xe1c3, 0x5d26}, //崦
	{0xe1c4, 0x5d2e}, //崮
	{0xe1c5, 0x5d24}, //崤
	{0xe1c6, 0x5d1e}, //崞
	{0xe1c7, 0x5d06}, //崆
	{0xe1c8, 0x5d1b}, //崛
	{0xe1c9, 0x5d58}, //嵘
	{0xe1ca, 0x5d3e}, //崾
	{0xe1cb, 0x5d34}, //崴
	{0xe1cc, 0x5d3d}, //崽
	{0xe1cd, 0x5d6c}, //嵬
	{0xe1ce, 0x5d5b}, //嵛
	{0xe1cf, 0x5d6f}, //嵯
	{0xe1d0, 0x5d5d}, //嵝
	{0xe1d1, 0x5d6b}, //嵫
	{0xe1d2, 0x5d4b}, //嵋
	{0xe1d3, 0x5d4a}, //嵊
	{0xe1d4, 0x5d69}, //嵩
	{0xe1d5, 0x5d74}, //嵴
	{0xe1d6, 0x5d82}, //嶂
	{0xe1d7, 0x5d99}, //嶙
	{0xe1d8, 0x5d9d}, //嶝
	{0xe1d9, 0x8c73}, //豳
	{0xe1da, 0x5db7}, //嶷
	{0xe1db, 0x5dc5}, //巅
	{0xe1dc, 0x5f73}, //彳
	{0xe1dd, 0x5f77}, //彷
	{0xe1de, 0x5f82}, //徂
	{0xe1df, 0x5f87}, //徇
	{0xe1e0, 0x5f89}, //徉
	{0xe1e1, 0x5f8c}, //後
	{0xe1e2, 0x5f95}, //徕
	{0xe1e3, 0x5f99}, //徙
	{0xe1e4, 0x5f9c}, //徜
	{0xe1e5, 0x5fa8}, //徨
	{0xe1e6, 0x5fad}, //徭
	{0xe1e7, 0x5fb5}, //徵
	{0xe1e8, 0x5fbc}, //徼
	{0xe1e9, 0x8862}, //衢
	{0xe1ea, 0x5f61}, //彡
	{0xe1eb, 0x72ad}, //犭
	{0xe1ec, 0x72b0}, //犰
	{0xe1ed, 0x72b4}, //犴
	{0xe1ee, 0x72b7}, //犷
	{0xe1ef, 0x72b8}, //犸
	{0xe1f0, 0x72c3}, //狃
	{0xe1f1, 0x72c1}, //狁
	{0xe1f2, 0x72ce}, //狎
	{0xe1f3, 0x72cd}, //狍
	{0xe1f4, 0x72d2}, //狒
	{0xe1f5, 0x72e8}, //狨
	{0xe1f6, 0x72ef}, //狯
	{0xe1f7, 0x72e9}, //狩
	{0xe1f8, 0x72f2}, //狲
	{0xe1f9, 0x72f4}, //狴
	{0xe1fa, 0x72f7}, //狷
	{0xe1fb, 0x7301}, //猁
	{0xe1fc, 0x72f3}, //狳
	{0xe1fd, 0x7303}, //猃
	{0xe1fe, 0x72fa}, //狺
	{0xe2a1, 0x72fb}, //狻
	{0xe2a2, 0x7317}, //猗
	{0xe2a3, 0x7313}, //猓
	{0xe2a4, 0x7321}, //猡
	{0xe2a5, 0x730a}, //猊
	{0xe2a6, 0x731e}, //猞
	{0xe2a7, 0x731d}, //猝
	{0xe2a8, 0x7315}, //猕
	{0xe2a9, 0x7322}, //猢
	{0xe2aa, 0x7339}, //猹
	{0xe2ab, 0x7325}, //猥
	{0xe2ac, 0x732c}, //猬
	{0xe2ad, 0x7338}, //猸
	{0xe2ae, 0x7331}, //猱
	{0xe2af, 0x7350}, //獐
	{0xe2b0, 0x734d}, //獍
	{0xe2b1, 0x7357}, //獗
	{0xe2b2, 0x7360}, //獠
	{0xe2b3, 0x736c}, //獬
	{0xe2b4, 0x736f}, //獯
	{0xe2b5, 0x737e}, //獾
	{0xe2b6, 0x821b}, //舛
	{0xe2b7, 0x5925}, //夥
	{0xe2b8, 0x98e7}, //飧
	{0xe2b9, 0x5924}, //夤
	{0xe2ba, 0x5902}, //夂
	{0xe2bb, 0x9963}, //饣
	{0xe2bc, 0x9967}, //饧
	{0xe2bd, 0x9968}, //饨
	{0xe2be, 0x9969}, //饩
	{0xe2bf, 0x996a}, //饪
	{0xe2c0, 0x996b}, //饫
	{0xe2c1, 0x996c}, //饬
	{0xe2c2, 0x9974}, //饴
	{0xe2c3, 0x9977}, //饷
	{0xe2c4, 0x997d}, //饽
	{0xe2c5, 0x9980}, //馀
	{0xe2c6, 0x9984}, //馄
	{0xe2c7, 0x9987}, //馇
	{0xe2c8, 0x998a}, //馊
	{0xe2c9, 0x998d}, //馍
	{0xe2ca, 0x9990}, //馐
	{0xe2cb, 0x9991}, //馑
	{0xe2cc, 0x9993}, //馓
	{0xe2cd, 0x9994}, //馔
	{0xe2ce, 0x9995}, //馕
	{0xe2cf, 0x5e80}, //庀
	{0xe2d0, 0x5e91}, //庑
	{0xe2d1, 0x5e8b}, //庋
	{0xe2d2, 0x5e96}, //庖
	{0xe2d3, 0x5ea5}, //庥
	{0xe2d4, 0x5ea0}, //庠
	{0xe2d5, 0x5eb9}, //庹
	{0xe2d6, 0x5eb5}, //庵
	{0xe2d7, 0x5ebe}, //庾
	{0xe2d8, 0x5eb3}, //庳
	{0xe2d9, 0x8d53}, //赓
	{0xe2da, 0x5ed2}, //廒
	{0xe2db, 0x5ed1}, //廑
	{0xe2dc, 0x5edb}, //廛
	{0xe2dd, 0x5ee8}, //廨
	{0xe2de, 0x5eea}, //廪
	{0xe2df, 0x81ba}, //膺
	{0xe2e0, 0x5fc4}, //忄
	{0xe2e1, 0x5fc9}, //忉
	{0xe2e2, 0x5fd6}, //忖
	{0xe2e3, 0x5fcf}, //忏
	{0xe2e4, 0x6003}, //怃
	{0xe2e5, 0x5fee}, //忮
	{0xe2e6, 0x6004}, //怄
	{0xe2e7, 0x5fe1}, //忡
	{0xe2e8, 0x5fe4}, //忤
	{0xe2e9, 0x5ffe}, //忾
	{0xe2ea, 0x6005}, //怅
	{0xe2eb, 0x6006}, //怆
	{0xe2ec, 0x5fea}, //忪
	{0xe2ed, 0x5fed}, //忭
	{0xe2ee, 0x5ff8}, //忸
	{0xe2ef, 0x6019}, //怙
	{0xe2f0, 0x6035}, //怵
	{0xe2f1, 0x6026}, //怦
	{0xe2f2, 0x601b}, //怛
	{0xe2f3, 0x600f}, //怏
	{0xe2f4, 0x600d}, //怍
	{0xe2f5, 0x6029}, //怩
	{0xe2f6, 0x602b}, //怫
	{0xe2f7, 0x600a}, //怊
	{0xe2f8, 0x603f}, //怿
	{0xe2f9, 0x6021}, //怡
	{0xe2fa, 0x6078}, //恸
	{0xe2fb, 0x6079}, //恹
	{0xe2fc, 0x607b}, //恻
	{0xe2fd, 0x607a}, //恺
	{0xe2fe, 0x6042}, //恂
	{0xe3a1, 0x606a}, //恪
	{0xe3a2, 0x607d}, //恽
	{0xe3a3, 0x6096}, //悖
	{0xe3a4, 0x609a}, //悚
	{0xe3a5, 0x60ad}, //悭
	{0xe3a6, 0x609d}, //悝
	{0xe3a7, 0x6083}, //悃
	{0xe3a8, 0x6092}, //悒
	{0xe3a9, 0x608c}, //悌
	{0xe3aa, 0x609b}, //悛
	{0xe3ab, 0x60ec}, //惬
	{0xe3ac, 0x60bb}, //悻
	{0xe3ad, 0x60b1}, //悱
	{0xe3ae, 0x60dd}, //惝
	{0xe3af, 0x60d8}, //惘
	{0xe3b0, 0x60c6}, //惆
	{0xe3b1, 0x60da}, //惚
	{0xe3b2, 0x60b4}, //悴
	{0xe3b3, 0x6120}, //愠
	{0xe3b4, 0x6126}, //愦
	{0xe3b5, 0x6115}, //愕
	{0xe3b6, 0x6123}, //愣
	{0xe3b7, 0x60f4}, //惴
	{0xe3b8, 0x6100}, //愀
	{0xe3b9, 0x610e}, //愎
	{0xe3ba, 0x612b}, //愫
	{0xe3bb, 0x614a}, //慊
	{0xe3bc, 0x6175}, //慵
	{0xe3bd, 0x61ac}, //憬
	{0xe3be, 0x6194}, //憔
	{0xe3bf, 0x61a7}, //憧
	{0xe3c0, 0x61b7}, //憷
	{0xe3c1, 0x61d4}, //懔
	{0xe3c2, 0x61f5}, //懵
	{0xe3c3, 0x5fdd}, //忝
	{0xe3c4, 0x96b3}, //隳
	{0xe3c5, 0x95e9}, //闩
	{0xe3c6, 0x95eb}, //闫
	{0xe3c7, 0x95f1}, //闱
	{0xe3c8, 0x95f3}, //闳
	{0xe3c9, 0x95f5}, //闵
	{0xe3ca, 0x95f6}, //闶
	{0xe3cb, 0x95fc}, //闼
	{0xe3cc, 0x95fe}, //闾
	{0xe3cd, 0x9603}, //阃
	{0xe3ce, 0x9604}, //阄
	{0xe3cf, 0x9606}, //阆
	{0xe3d0, 0x9608}, //阈
	{0xe3d1, 0x960a}, //阊
	{0xe3d2, 0x960b}, //阋
	{0xe3d3, 0x960c}, //阌
	{0xe3d4, 0x960d}, //阍
	{0xe3d5, 0x960f}, //阏
	{0xe3d6, 0x9612}, //阒
	{0xe3d7, 0x9615}, //阕
	{0xe3d8, 0x9616}, //阖
	{0xe3d9, 0x9617}, //阗
	{0xe3da, 0x9619}, //阙
	{0xe3db, 0x961a}, //阚
	{0xe3dc, 0x4e2c}, //丬
	{0xe3dd, 0x723f}, //爿
	{0xe3de, 0x6215}, //戕
	{0xe3df, 0x6c35}, //氵
	{0xe3e0, 0x6c54}, //汔
	{0xe3e1, 0x6c5c}, //汜
	{0xe3e2, 0x6c4a}, //汊
	{0xe3e3, 0x6ca3}, //沣
	{0xe3e4, 0x6c85}, //沅
	{0xe3e5, 0x6c90}, //沐
	{0xe3e6, 0x6c94}, //沔
	{0xe3e7, 0x6c8c}, //沌
	{0xe3e8, 0x6c68}, //汨
	{0xe3e9, 0x6c69}, //汩
	{0xe3ea, 0x6c74}, //汴
	{0xe3eb, 0x6c76}, //汶
	{0xe3ec, 0x6c86}, //沆
	{0xe3ed, 0x6ca9}, //沩
	{0xe3ee, 0x6cd0}, //泐
	{0xe3ef, 0x6cd4}, //泔
	{0xe3f0, 0x6cad}, //沭
	{0xe3f1, 0x6cf7}, //泷
	{0xe3f2, 0x6cf8}, //泸
	{0xe3f3, 0x6cf1}, //泱
	{0xe3f4, 0x6cd7}, //泗
	{0xe3f5, 0x6cb2}, //沲
	{0xe3f6, 0x6ce0}, //泠
	{0xe3f7, 0x6cd6}, //泖
	{0xe3f8, 0x6cfa}, //泺
	{0xe3f9, 0x6ceb}, //泫
	{0xe3fa, 0x6cee}, //泮
	{0xe3fb, 0x6cb1}, //沱
	{0xe3fc, 0x6cd3}, //泓
	{0xe3fd, 0x6cef}, //泯
	{0xe3fe, 0x6cfe}, //泾
	{0xe4a1, 0x6d39}, //洹
	{0xe4a2, 0x6d27}, //洧
	{0xe4a3, 0x6d0c}, //洌
	{0xe4a4, 0x6d43}, //浃
	{0xe4a5, 0x6d48}, //浈
	{0xe4a6, 0x6d07}, //洇
	{0xe4a7, 0x6d04}, //洄
	{0xe4a8, 0x6d19}, //洙
	{0xe4a9, 0x6d0e}, //洎
	{0xe4aa, 0x6d2b}, //洫
	{0xe4ab, 0x6d4d}, //浍
	{0xe4ac, 0x6d2e}, //洮
	{0xe4ad, 0x6d35}, //洵
	{0xe4ae, 0x6d1a}, //洚
	{0xe4af, 0x6d4f}, //浏
	{0xe4b0, 0x6d52}, //浒
	{0xe4b1, 0x6d54}, //浔
	{0xe4b2, 0x6d33}, //洳
	{0xe4b3, 0x6d91}, //涑
	{0xe4b4, 0x6d6f}, //浯
	{0xe4b5, 0x6d9e}, //涞
	{0xe4b6, 0x6da0}, //涠
	{0xe4b7, 0x6d5e}, //浞
	{0xe4b8, 0x6d93}, //涓
	{0xe4b9, 0x6d94}, //涔
	{0xe4ba, 0x6d5c}, //浜
	{0xe4bb, 0x6d60}, //浠
	{0xe4bc, 0x6d7c}, //浼
	{0xe4bd, 0x6d63}, //浣
	{0xe4be, 0x6e1a}, //渚
	{0xe4bf, 0x6dc7}, //淇
	{0xe4c0, 0x6dc5}, //淅
	{0xe4c1, 0x6dde}, //淞
	{0xe4c2, 0x6e0e}, //渎
	{0xe4c3, 0x6dbf}, //涿
	{0xe4c4, 0x6de0}, //淠
	{0xe4c5, 0x6e11}, //渑
	{0xe4c6, 0x6de6}, //淦
	{0xe4c7, 0x6ddd}, //淝
	{0xe4c8, 0x6dd9}, //淙
	{0xe4c9, 0x6e16}, //渖
	{0xe4ca, 0x6dab}, //涫
	{0xe4cb, 0x6e0c}, //渌
	{0xe4cc, 0x6dae}, //涮
	{0xe4cd, 0x6e2b}, //渫
	{0xe4ce, 0x6e6e}, //湮
	{0xe4cf, 0x6e4e}, //湎
	{0xe4d0, 0x6e6b}, //湫
	{0xe4d1, 0x6eb2}, //溲
	{0xe4d2, 0x6e5f}, //湟
	{0xe4d3, 0x6e86}, //溆
	{0xe4d4, 0x6e53}, //湓
	{0xe4d5, 0x6e54}, //湔
	{0xe4d6, 0x6e32}, //渲
	{0xe4d7, 0x6e25}, //渥
	{0xe4d8, 0x6e44}, //湄
	{0xe4d9, 0x6edf}, //滟
	{0xe4da, 0x6eb1}, //溱
	{0xe4db, 0x6e98}, //溘
	{0xe4dc, 0x6ee0}, //滠
	{0xe4dd, 0x6f2d}, //漭
	{0xe4de, 0x6ee2}, //滢
	{0xe4df, 0x6ea5}, //溥
	{0xe4e0, 0x6ea7}, //溧
	{0xe4e1, 0x6ebd}, //溽
	{0xe4e2, 0x6ebb}, //溻
	{0xe4e3, 0x6eb7}, //溷
	{0xe4e4, 0x6ed7}, //滗
	{0xe4e5, 0x6eb4}, //溴
	{0xe4e6, 0x6ecf}, //滏
	{0xe4e7, 0x6e8f}, //溏
	{0xe4e8, 0x6ec2}, //滂
	{0xe4e9, 0x6e9f}, //溟
	{0xe4ea, 0x6f62}, //潢
	{0xe4eb, 0x6f46}, //潆
	{0xe4ec, 0x6f47}, //潇
	{0xe4ed, 0x6f24}, //漤
	{0xe4ee, 0x6f15}, //漕
	{0xe4ef, 0x6ef9}, //滹
	{0xe4f0, 0x6f2f}, //漯
	{0xe4f1, 0x6f36}, //漶
	{0xe4f2, 0x6f4b}, //潋
	{0xe4f3, 0x6f74}, //潴
	{0xe4f4, 0x6f2a}, //漪
	{0xe4f5, 0x6f09}, //漉
	{0xe4f6, 0x6f29}, //漩
	{0xe4f7, 0x6f89}, //澉
	{0xe4f8, 0x6f8d}, //澍
	{0xe4f9, 0x6f8c}, //澌
	{0xe4fa, 0x6f78}, //潸
	{0xe4fb, 0x6f72}, //潲
	{0xe4fc, 0x6f7c}, //潼
	{0xe4fd, 0x6f7a}, //潺
	{0xe4fe, 0x6fd1}, //濑
	{0xe5a1, 0x6fc9}, //濉
	{0xe5a2, 0x6fa7}, //澧
	{0xe5a3, 0x6fb9}, //澹
	{0xe5a4, 0x6fb6}, //澶
	{0xe5a5, 0x6fc2}, //濂
	{0xe5a6, 0x6fe1}, //濡
	{0xe5a7, 0x6fee}, //濮
	{0xe5a8, 0x6fde}, //濞
	{0xe5a9, 0x6fe0}, //濠
	{0xe5aa, 0x6fef}, //濯
	{0xe5ab, 0x701a}, //瀚
	{0xe5ac, 0x7023}, //瀣
	{0xe5ad, 0x701b}, //瀛
	{0xe5ae, 0x7039}, //瀹
	{0xe5af, 0x7035}, //瀵
	{0xe5b0, 0x704f}, //灏
	{0xe5b1, 0x705e}, //灞
	{0xe5b2, 0x5b80}, //宀
	{0xe5b3, 0x5b84}, //宄
	{0xe5b4, 0x5b95}, //宕
	{0xe5b5, 0x5b93}, //宓
	{0xe5b6, 0x5ba5}, //宥
	{0xe5b7, 0x5bb8}, //宸
	{0xe5b8, 0x752f}, //甯
	{0xe5b9, 0x9a9e}, //骞
	{0xe5ba, 0x6434}, //搴
	{0xe5bb, 0x5be4}, //寤
	{0xe5bc, 0x5bee}, //寮
	{0xe5bd, 0x8930}, //褰
	{0xe5be, 0x5bf0}, //寰
	{0xe5bf, 0x8e47}, //蹇
	{0xe5c0, 0x8b07}, //謇
	{0xe5c1, 0x8fb6}, //辶
	{0xe5c2, 0x8fd3}, //迓
	{0xe5c3, 0x8fd5}, //迕
	{0xe5c4, 0x8fe5}, //迥
	{0xe5c5, 0x8fee}, //迮
	{0xe5c6, 0x8fe4}, //迤
	{0xe5c7, 0x8fe9}, //迩
	{0xe5c8, 0x8fe6}, //迦
	{0xe5c9, 0x8ff3}, //迳
	{0xe5ca, 0x8fe8}, //迨
	{0xe5cb, 0x9005}, //逅
	{0xe5cc, 0x9004}, //逄
	{0xe5cd, 0x900b}, //逋
	{0xe5ce, 0x9026}, //逦
	{0xe5cf, 0x9011}, //逑
	{0xe5d0, 0x900d}, //逍
	{0xe5d1, 0x9016}, //逖
	{0xe5d2, 0x9021}, //逡
	{0xe5d3, 0x9035}, //逵
	{0xe5d4, 0x9036}, //逶
	{0xe5d5, 0x902d}, //逭
	{0xe5d6, 0x902f}, //逯
	{0xe5d7, 0x9044}, //遄
	{0xe5d8, 0x9051}, //遑
	{0xe5d9, 0x9052}, //遒
	{0xe5da, 0x9050}, //遐
	{0xe5db, 0x9068}, //遨
	{0xe5dc, 0x9058}, //遘
	{0xe5dd, 0x9062}, //遢
	{0xe5de, 0x905b}, //遛
	{0xe5df, 0x66b9}, //暹
	{0xe5e0, 0x9074}, //遴
	{0xe5e1, 0x907d}, //遽
	{0xe5e2, 0x9082}, //邂
	{0xe5e3, 0x9088}, //邈
	{0xe5e4, 0x9083}, //邃
	{0xe5e5, 0x908b}, //邋
	{0xe5e6, 0x5f50}, //彐
	{0xe5e7, 0x5f57}, //彗
	{0xe5e8, 0x5f56}, //彖
	{0xe5e9, 0x5f58}, //彘
	{0xe5ea, 0x5c3b}, //尻
	{0xe5eb, 0x54ab}, //咫
	{0xe5ec, 0x5c50}, //屐
	{0xe5ed, 0x5c59}, //屙
	{0xe5ee, 0x5b71}, //孱
	{0xe5ef, 0x5c63}, //屣
	{0xe5f0, 0x5c66}, //屦
	{0xe5f1, 0x7fbc}, //羼
	{0xe5f2, 0x5f2a}, //弪
	{0xe5f3, 0x5f29}, //弩
	{0xe5f4, 0x5f2d}, //弭
	{0xe5f5, 0x8274}, //艴
	{0xe5f6, 0x5f3c}, //弼
	{0xe5f7, 0x9b3b}, //鬻
	{0xe5f8, 0x5c6e}, //屮
	{0xe5f9, 0x5981}, //妁
	{0xe5fa, 0x5983}, //妃
	{0xe5fb, 0x598d}, //妍
	{0xe5fc, 0x59a9}, //妩
	{0xe5fd, 0x59aa}, //妪
	{0xe5fe, 0x59a3}, //妣
	{0xe6a1, 0x5997}, //妗
	{0xe6a2, 0x59ca}, //姊
	{0xe6a3, 0x59ab}, //妫
	{0xe6a4, 0x599e}, //妞
	{0xe6a5, 0x59a4}, //妤
	{0xe6a6, 0x59d2}, //姒
	{0xe6a7, 0x59b2}, //妲
	{0xe6a8, 0x59af}, //妯
	{0xe6a9, 0x59d7}, //姗
	{0xe6aa, 0x59be}, //妾
	{0xe6ab, 0x5a05}, //娅
	{0xe6ac, 0x5a06}, //娆
	{0xe6ad, 0x59dd}, //姝
	{0xe6ae, 0x5a08}, //娈
	{0xe6af, 0x59e3}, //姣
	{0xe6b0, 0x59d8}, //姘
	{0xe6b1, 0x59f9}, //姹
	{0xe6b2, 0x5a0c}, //娌
	{0xe6b3, 0x5a09}, //娉
	{0xe6b4, 0x5a32}, //娲
	{0xe6b5, 0x5a34}, //娴
	{0xe6b6, 0x5a11}, //娑
	{0xe6b7, 0x5a23}, //娣
	{0xe6b8, 0x5a13}, //娓
	{0xe6b9, 0x5a40}, //婀
	{0xe6ba, 0x5a67}, //婧
	{0xe6bb, 0x5a4a}, //婊
	{0xe6bc, 0x5a55}, //婕
	{0xe6bd, 0x5a3c}, //娼
	{0xe6be, 0x5a62}, //婢
	{0xe6bf, 0x5a75}, //婵
	{0xe6c0, 0x80ec}, //胬
	{0xe6c1, 0x5aaa}, //媪
	{0xe6c2, 0x5a9b}, //媛
	{0xe6c3, 0x5a77}, //婷
	{0xe6c4, 0x5a7a}, //婺
	{0xe6c5, 0x5abe}, //媾
	{0xe6c6, 0x5aeb}, //嫫
	{0xe6c7, 0x5ab2}, //媲
	{0xe6c8, 0x5ad2}, //嫒
	{0xe6c9, 0x5ad4}, //嫔
	{0xe6ca, 0x5ab8}, //媸
	{0xe6cb, 0x5ae0}, //嫠
	{0xe6cc, 0x5ae3}, //嫣
	{0xe6cd, 0x5af1}, //嫱
	{0xe6ce, 0x5ad6}, //嫖
	{0xe6cf, 0x5ae6}, //嫦
	{0xe6d0, 0x5ad8}, //嫘
	{0xe6d1, 0x5adc}, //嫜
	{0xe6d2, 0x5b09}, //嬉
	{0xe6d3, 0x5b17}, //嬗
	{0xe6d4, 0x5b16}, //嬖
	{0xe6d5, 0x5b32}, //嬲
	{0xe6d6, 0x5b37}, //嬷
	{0xe6d7, 0x5b40}, //孀
	{0xe6d8, 0x5c15}, //尕
	{0xe6d9, 0x5c1c}, //尜
	{0xe6da, 0x5b5a}, //孚
	{0xe6db, 0x5b65}, //孥
	{0xe6dc, 0x5b73}, //孳
	{0xe6dd, 0x5b51}, //孑
	{0xe6de, 0x5b53}, //孓
	{0xe6df, 0x5b62}, //孢
	{0xe6e0, 0x9a75}, //驵
	{0xe6e1, 0x9a77}, //驷
	{0xe6e2, 0x9a78}, //驸
	{0xe6e3, 0x9a7a}, //驺
	{0xe6e4, 0x9a7f}, //驿
	{0xe6e5, 0x9a7d}, //驽
	{0xe6e6, 0x9a80}, //骀
	{0xe6e7, 0x9a81}, //骁
	{0xe6e8, 0x9a85}, //骅
	{0xe6e9, 0x9a88}, //骈
	{0xe6ea, 0x9a8a}, //骊
	{0xe6eb, 0x9a90}, //骐
	{0xe6ec, 0x9a92}, //骒
	{0xe6ed, 0x9a93}, //骓
	{0xe6ee, 0x9a96}, //骖
	{0xe6ef, 0x9a98}, //骘
	{0xe6f0, 0x9a9b}, //骛
	{0xe6f1, 0x9a9c}, //骜
	{0xe6f2, 0x9a9d}, //骝
	{0xe6f3, 0x9a9f}, //骟
	{0xe6f4, 0x9aa0}, //骠
	{0xe6f5, 0x9aa2}, //骢
	{0xe6f6, 0x9aa3}, //骣
	{0xe6f7, 0x9aa5}, //骥
	{0xe6f8, 0x9aa7}, //骧
	{0xe6f9, 0x7e9f}, //纟
	{0xe6fa, 0x7ea1}, //纡
	{0xe6fb, 0x7ea3}, //纣
	{0xe6fc, 0x7ea5}, //纥
	{0xe6fd, 0x7ea8}, //纨
	{0xe6fe, 0x7ea9}, //纩
	{0xe7a1, 0x7ead}, //纭
	{0xe7a2, 0x7eb0}, //纰
	{0xe7a3, 0x7ebe}, //纾
	{0xe7a4, 0x7ec0}, //绀
	{0xe7a5, 0x7ec1}, //绁
	{0xe7a6, 0x7ec2}, //绂
	{0xe7a7, 0x7ec9}, //绉
	{0xe7a8, 0x7ecb}, //绋
	{0xe7a9, 0x7ecc}, //绌
	{0xe7aa, 0x7ed0}, //绐
	{0xe7ab, 0x7ed4}, //绔
	{0xe7ac, 0x7ed7}, //绗
	{0xe7ad, 0x7edb}, //绛
	{0xe7ae, 0x7ee0}, //绠
	{0xe7af, 0x7ee1}, //绡
	{0xe7b0, 0x7ee8}, //绨
	{0xe7b1, 0x7eeb}, //绫
	{0xe7b2, 0x7eee}, //绮
	{0xe7b3, 0x7eef}, //绯
	{0xe7b4, 0x7ef1}, //绱
	{0xe7b5, 0x7ef2}, //绲
	{0xe7b6, 0x7f0d}, //缍
	{0xe7b7, 0x7ef6}, //绶
	{0xe7b8, 0x7efa}, //绺
	{0xe7b9, 0x7efb}, //绻
	{0xe7ba, 0x7efe}, //绾
	{0xe7bb, 0x7f01}, //缁
	{0xe7bc, 0x7f02}, //缂
	{0xe7bd, 0x7f03}, //缃
	{0xe7be, 0x7f07}, //缇
	{0xe7bf, 0x7f08}, //缈
	{0xe7c0, 0x7f0b}, //缋
	{0xe7c1, 0x7f0c}, //缌
	{0xe7c2, 0x7f0f}, //缏
	{0xe7c3, 0x7f11}, //缑
	{0xe7c4, 0x7f12}, //缒
	{0xe7c5, 0x7f17}, //缗
	{0xe7c6, 0x7f19}, //缙
	{0xe7c7, 0x7f1c}, //缜
	{0xe7c8, 0x7f1b}, //缛
	{0xe7c9, 0x7f1f}, //缟
	{0xe7ca, 0x7f21}, //缡
	{0xe7cb, 0x7f22}, //缢
	{0xe7cc, 0x7f23}, //缣
	{0xe7cd, 0x7f24}, //缤
	{0xe7ce, 0x7f25}, //缥
	{0xe7cf, 0x7f26}, //缦
	{0xe7d0, 0x7f27}, //缧
	{0xe7d1, 0x7f2a}, //缪
	{0xe7d2, 0x7f2b}, //缫
	{0xe7d3, 0x7f2c}, //缬
	{0xe7d4, 0x7f2d}, //缭
	{0xe7d5, 0x7f2f}, //缯
	{0xe7d6, 0x7f30}, //缰
	{0xe7d7, 0x7f31}, //缱
	{0xe7d8, 0x7f32}, //缲
	{0xe7d9, 0x7f33}, //缳
	{0xe7da, 0x7f35}, //缵
	{0xe7db, 0x5e7a}, //幺
	{0xe7dc, 0x757f}, //畿
	{0xe7dd, 0x5ddb}, //巛
	{0xe7de, 0x753e}, //甾
	{0xe7df, 0x9095}, //邕
	{0xe7e0, 0x738e}, //玎
	{0xe7e1, 0x7391}, //玑
	{0xe7e2, 0x73ae}, //玮
	{0xe7e3, 0x73a2}, //玢
	{0xe7e4, 0x739f}, //玟
	{0xe7e5, 0x73cf}, //珏
	{0xe7e6, 0x73c2}, //珂
	{0xe7e7, 0x73d1}, //珑
	{0xe7e8, 0x73b7}, //玷
	{0xe7e9, 0x73b3}, //玳
	{0xe7ea, 0x73c0}, //珀
	{0xe7eb, 0x73c9}, //珉
	{0xe7ec, 0x73c8}, //珈
	{0xe7ed, 0x73e5}, //珥
	{0xe7ee, 0x73d9}, //珙
	{0xe7ef, 0x987c}, //顼
	{0xe7f0, 0x740a}, //琊
	{0xe7f1, 0x73e9}, //珩
	{0xe7f2, 0x73e7}, //珧
	{0xe7f3, 0x73de}, //珞
	{0xe7f4, 0x73ba}, //玺
	{0xe7f5, 0x73f2}, //珲
	{0xe7f6, 0x740f}, //琏
	{0xe7f7, 0x742a}, //琪
	{0xe7f8, 0x745b}, //瑛
	{0xe7f9, 0x7426}, //琦
	{0xe7fa, 0x7425}, //琥
	{0xe7fb, 0x7428}, //琨
	{0xe7fc, 0x7430}, //琰
	{0xe7fd, 0x742e}, //琮
	{0xe7fe, 0x742c}, //琬
	{0xe8a1, 0x741b}, //琛
	{0xe8a2, 0x741a}, //琚
	{0xe8a3, 0x7441}, //瑁
	{0xe8a4, 0x745c}, //瑜
	{0xe8a5, 0x7457}, //瑗
	{0xe8a6, 0x7455}, //瑕
	{0xe8a7, 0x7459}, //瑙
	{0xe8a8, 0x7477}, //瑷
	{0xe8a9, 0x746d}, //瑭
	{0xe8aa, 0x747e}, //瑾
	{0xe8ab, 0x749c}, //璜
	{0xe8ac, 0x748e}, //璎
	{0xe8ad, 0x7480}, //璀
	{0xe8ae, 0x7481}, //璁
	{0xe8af, 0x7487}, //璇
	{0xe8b0, 0x748b}, //璋
	{0xe8b1, 0x749e}, //璞
	{0xe8b2, 0x74a8}, //璨
	{0xe8b3, 0x74a9}, //璩
	{0xe8b4, 0x7490}, //璐
	{0xe8b5, 0x74a7}, //璧
	{0xe8b6, 0x74d2}, //瓒
	{0xe8b7, 0x74ba}, //璺
	{0xe8b8, 0x97ea}, //韪
	{0xe8b9, 0x97eb}, //韫
	{0xe8ba, 0x97ec}, //韬
	{0xe8bb, 0x674c}, //杌
	{0xe8bc, 0x6753}, //杓
	{0xe8bd, 0x675e}, //杞
	{0xe8be, 0x6748}, //杈
	{0xe8bf, 0x6769}, //杩
	{0xe8c0, 0x67a5}, //枥
	{0xe8c1, 0x6787}, //枇
	{0xe8c2, 0x676a}, //杪
	{0xe8c3, 0x6773}, //杳
	{0xe8c4, 0x6798}, //枘
	{0xe8c5, 0x67a7}, //枧
	{0xe8c6, 0x6775}, //杵
	{0xe8c7, 0x67a8}, //枨
	{0xe8c8, 0x679e}, //枞
	{0xe8c9, 0x67ad}, //枭
	{0xe8ca, 0x678b}, //枋
	{0xe8cb, 0x6777}, //杷
	{0xe8cc, 0x677c}, //杼
	{0xe8cd, 0x67f0}, //柰
	{0xe8ce, 0x6809}, //栉
	{0xe8cf, 0x67d8}, //柘
	{0xe8d0, 0x680a}, //栊
	{0xe8d1, 0x67e9}, //柩
	{0xe8d2, 0x67b0}, //枰
	{0xe8d3, 0x680c}, //栌
	{0xe8d4, 0x67d9}, //柙
	{0xe8d5, 0x67b5}, //枵
	{0xe8d6, 0x67da}, //柚
	{0xe8d7, 0x67b3}, //枳
	{0xe8d8, 0x67dd}, //柝
	{0xe8d9, 0x6800}, //栀
	{0xe8da, 0x67c3}, //柃
	{0xe8db, 0x67b8}, //枸
	{0xe8dc, 0x67e2}, //柢
	{0xe8dd, 0x680e}, //栎
	{0xe8de, 0x67c1}, //柁
	{0xe8df, 0x67fd}, //柽
	{0xe8e0, 0x6832}, //栲
	{0xe8e1, 0x6833}, //栳
	{0xe8e2, 0x6860}, //桠
	{0xe8e3, 0x6861}, //桡
	{0xe8e4, 0x684e}, //桎
	{0xe8e5, 0x6862}, //桢
	{0xe8e6, 0x6844}, //桄
	{0xe8e7, 0x6864}, //桤
	{0xe8e8, 0x6883}, //梃
	{0xe8e9, 0x681d}, //栝
	{0xe8ea, 0x6855}, //桕
	{0xe8eb, 0x6866}, //桦
	{0xe8ec, 0x6841}, //桁
	{0xe8ed, 0x6867}, //桧
	{0xe8ee, 0x6840}, //桀
	{0xe8ef, 0x683e}, //栾
	{0xe8f0, 0x684a}, //桊
	{0xe8f1, 0x6849}, //桉
	{0xe8f2, 0x6829}, //栩
	{0xe8f3, 0x68b5}, //梵
	{0xe8f4, 0x688f}, //梏
	{0xe8f5, 0x6874}, //桴
	{0xe8f6, 0x6877}, //桷
	{0xe8f7, 0x6893}, //梓
	{0xe8f8, 0x686b}, //桫
	{0xe8f9, 0x68c2}, //棂
	{0xe8fa, 0x696e}, //楮
	{0xe8fb, 0x68fc}, //棼
	{0xe8fc, 0x691f}, //椟
	{0xe8fd, 0x6920}, //椠
	{0xe8fe, 0x68f9}, //棹
	{0xe9a1, 0x6924}, //椤
	{0xe9a2, 0x68f0}, //棰
	{0xe9a3, 0x690b}, //椋
	{0xe9a4, 0x6901}, //椁
	{0xe9a5, 0x6957}, //楗
	{0xe9a6, 0x68e3}, //棣
	{0xe9a7, 0x6910}, //椐
	{0xe9a8, 0x6971}, //楱
	{0xe9a9, 0x6939}, //椹
	{0xe9aa, 0x6960}, //楠
	{0xe9ab, 0x6942}, //楂
	{0xe9ac, 0x695d}, //楝
	{0xe9ad, 0x6984}, //榄
	{0xe9ae, 0x696b}, //楫
	{0xe9af, 0x6980}, //榀
	{0xe9b0, 0x6998}, //榘
	{0xe9b1, 0x6978}, //楸
	{0xe9b2, 0x6934}, //椴
	{0xe9b3, 0x69cc}, //槌
	{0xe9b4, 0x6987}, //榇
	{0xe9b5, 0x6988}, //榈
	{0xe9b6, 0x69ce}, //槎
	{0xe9b7, 0x6989}, //榉
	{0xe9b8, 0x6966}, //楦
	{0xe9b9, 0x6963}, //楣
	{0xe9ba, 0x6979}, //楹
	{0xe9bb, 0x699b}, //榛
	{0xe9bc, 0x69a7}, //榧
	{0xe9bd, 0x69bb}, //榻
	{0xe9be, 0x69ab}, //榫
	{0xe9bf, 0x69ad}, //榭
	{0xe9c0, 0x69d4}, //槔
	{0xe9c1, 0x69b1}, //榱
	{0xe9c2, 0x69c1}, //槁
	{0xe9c3, 0x69ca}, //槊
	{0xe9c4, 0x69df}, //槟
	{0xe9c5, 0x6995}, //榕
	{0xe9c6, 0x69e0}, //槠
	{0xe9c7, 0x698d}, //榍
	{0xe9c8, 0x69ff}, //槿
	{0xe9c9, 0x6a2f}, //樯
	{0xe9ca, 0x69ed}, //槭
	{0xe9cb, 0x6a17}, //樗
	{0xe9cc, 0x6a18}, //樘
	{0xe9cd, 0x6a65}, //橥
	{0xe9ce, 0x69f2}, //槲
	{0xe9cf, 0x6a44}, //橄
	{0xe9d0, 0x6a3e}, //樾
	{0xe9d1, 0x6aa0}, //檠
	{0xe9d2, 0x6a50}, //橐
	{0xe9d3, 0x6a5b}, //橛
	{0xe9d4, 0x6a35}, //樵
	{0xe9d5, 0x6a8e}, //檎
	{0xe9d6, 0x6a79}, //橹
	{0xe9d7, 0x6a3d}, //樽
	{0xe9d8, 0x6a28}, //樨
	{0xe9d9, 0x6a58}, //橘
	{0xe9da, 0x6a7c}, //橼
	{0xe9db, 0x6a91}, //檑
	{0xe9dc, 0x6a90}, //檐
	{0xe9dd, 0x6aa9}, //檩
	{0xe9de, 0x6a97}, //檗
	{0xe9df, 0x6aab}, //檫
	{0xe9e0, 0x7337}, //猷
	{0xe9e1, 0x7352}, //獒
	{0xe9e2, 0x6b81}, //殁
	{0xe9e3, 0x6b82}, //殂
	{0xe9e4, 0x6b87}, //殇
	{0xe9e5, 0x6b84}, //殄
	{0xe9e6, 0x6b92}, //殒
	{0xe9e7, 0x6b93}, //殓
	{0xe9e8, 0x6b8d}, //殍
	{0xe9e9, 0x6b9a}, //殚
	{0xe9ea, 0x6b9b}, //殛
	{0xe9eb, 0x6ba1}, //殡
	{0xe9ec, 0x6baa}, //殪
	{0xe9ed, 0x8f6b}, //轫
	{0xe9ee, 0x8f6d}, //轭
	{0xe9ef, 0x8f71}, //轱
	{0xe9f0, 0x8f72}, //轲
	{0xe9f1, 0x8f73}, //轳
	{0xe9f2, 0x8f75}, //轵
	{0xe9f3, 0x8f76}, //轶
	{0xe9f4, 0x8f78}, //轸
	{0xe9f5, 0x8f77}, //轷
	{0xe9f6, 0x8f79}, //轹
	{0xe9f7, 0x8f7a}, //轺
	{0xe9f8, 0x8f7c}, //轼
	{0xe9f9, 0x8f7e}, //轾
	{0xe9fa, 0x8f81}, //辁
	{0xe9fb, 0x8f82}, //辂
	{0xe9fc, 0x8f84}, //辄
	{0xe9fd, 0x8f87}, //辇
	{0xe9fe, 0x8f8b}, //辋
	{0xeaa1, 0x8f8d}, //辍
	{0xeaa2, 0x8f8e}, //辎
	{0xeaa3, 0x8f8f}, //辏
	{0xeaa4, 0x8f98}, //辘
	{0xeaa5, 0x8f9a}, //辚
	{0xeaa6, 0x8ece}, //軎
	{0xeaa7, 0x620b}, //戋
	{0xeaa8, 0x6217}, //戗
	{0xeaa9, 0x621b}, //戛
	{0xeaaa, 0x621f}, //戟
	{0xeaab, 0x6222}, //戢
	{0xeaac, 0x6221}, //戡
	{0xeaad, 0x6225}, //戥
	{0xeaae, 0x6224}, //戤
	{0xeaaf, 0x622c}, //戬
	{0xeab0, 0x81e7}, //臧
	{0xeab1, 0x74ef}, //瓯
	{0xeab2, 0x74f4}, //瓴
	{0xeab3, 0x74ff}, //瓿
	{0xeab4, 0x750f}, //甏
	{0xeab5, 0x7511}, //甑
	{0xeab6, 0x7513}, //甓
	{0xeab7, 0x6534}, //攴
	{0xeab8, 0x65ee}, //旮
	{0xeab9, 0x65ef}, //旯
	{0xeaba, 0x65f0}, //旰
	{0xeabb, 0x660a}, //昊
	{0xeabc, 0x6619}, //昙
	{0xeabd, 0x6772}, //杲
	{0xeabe, 0x6603}, //昃
	{0xeabf, 0x6615}, //昕
	{0xeac0, 0x6600}, //昀
	{0xeac1, 0x7085}, //炅
	{0xeac2, 0x66f7}, //曷
	{0xeac3, 0x661d}, //昝
	{0xeac4, 0x6634}, //昴
	{0xeac5, 0x6631}, //昱
	{0xeac6, 0x6636}, //昶
	{0xeac7, 0x6635}, //昵
	{0xeac8, 0x8006}, //耆
	{0xeac9, 0x665f}, //晟
	{0xeaca, 0x6654}, //晔
	{0xeacb, 0x6641}, //晁
	{0xeacc, 0x664f}, //晏
	{0xeacd, 0x6656}, //晖
	{0xeace, 0x6661}, //晡
	{0xeacf, 0x6657}, //晗
	{0xead0, 0x6677}, //晷
	{0xead1, 0x6684}, //暄
	{0xead2, 0x668c}, //暌
	{0xead3, 0x66a7}, //暧
	{0xead4, 0x669d}, //暝
	{0xead5, 0x66be}, //暾
	{0xead6, 0x66db}, //曛
	{0xead7, 0x66dc}, //曜
	{0xead8, 0x66e6}, //曦
	{0xead9, 0x66e9}, //曩
	{0xeada, 0x8d32}, //贲
	{0xeadb, 0x8d33}, //贳
	{0xeadc, 0x8d36}, //贶
	{0xeadd, 0x8d3b}, //贻
	{0xeade, 0x8d3d}, //贽
	{0xeadf, 0x8d40}, //赀
	{0xeae0, 0x8d45}, //赅
	{0xeae1, 0x8d46}, //赆
	{0xeae2, 0x8d48}, //赈
	{0xeae3, 0x8d49}, //赉
	{0xeae4, 0x8d47}, //赇
	{0xeae5, 0x8d4d}, //赍
	{0xeae6, 0x8d55}, //赕
	{0xeae7, 0x8d59}, //赙
	{0xeae8, 0x89c7}, //觇
	{0xeae9, 0x89ca}, //觊
	{0xeaea, 0x89cb}, //觋
	{0xeaeb, 0x89cc}, //觌
	{0xeaec, 0x89ce}, //觎
	{0xeaed, 0x89cf}, //觏
	{0xeaee, 0x89d0}, //觐
	{0xeaef, 0x89d1}, //觑
	{0xeaf0, 0x726e}, //牮
	{0xeaf1, 0x729f}, //犟
	{0xeaf2, 0x725d}, //牝
	{0xeaf3, 0x7266}, //牦
	{0xeaf4, 0x726f}, //牯
	{0xeaf5, 0x727e}, //牾
	{0xeaf6, 0x727f}, //牿
	{0xeaf7, 0x7284}, //犄
	{0xeaf8, 0x728b}, //犋
	{0xeaf9, 0x728d}, //犍
	{0xeafa, 0x728f}, //犏
	{0xeafb, 0x7292}, //犒
	{0xeafc, 0x6308}, //挈
	{0xeafd, 0x6332}, //挲
	{0xeafe, 0x63b0}, //掰
	{0xeba1, 0x643f}, //搿
	{0xeba2, 0x64d8}, //擘
	{0xeba3, 0x8004}, //耄
	{0xeba4, 0x6bea}, //毪
	{0xeba5, 0x6bf3}, //毳
	{0xeba6, 0x6bfd}, //毽
	{0xeba7, 0x6bf5}, //毵
	{0xeba8, 0x6bf9}, //毹
	{0xeba9, 0x6c05}, //氅
	{0xebaa, 0x6c07}, //氇
	{0xebab, 0x6c06}, //氆
	{0xebac, 0x6c0d}, //氍
	{0xebad, 0x6c15}, //氕
	{0xebae, 0x6c18}, //氘
	{0xebaf, 0x6c19}, //氙
	{0xebb0, 0x6c1a}, //氚
	{0xebb1, 0x6c21}, //氡
	{0xebb2, 0x6c29}, //氩
	{0xebb3, 0x6c24}, //氤
	{0xebb4, 0x6c2a}, //氪
	{0xebb5, 0x6c32}, //氲
	{0xebb6, 0x6535}, //攵
	{0xebb7, 0x6555}, //敕
	{0xebb8, 0x656b}, //敫
	{0xebb9, 0x724d}, //牍
	{0xebba, 0x7252}, //牒
	{0xebbb, 0x7256}, //牖
	{0xebbc, 0x7230}, //爰
	{0xebbd, 0x8662}, //虢
	{0xebbe, 0x5216}, //刖
	{0xebbf, 0x809f}, //肟
	{0xebc0, 0x809c}, //肜
	{0xebc1, 0x8093}, //肓
	{0xebc2, 0x80bc}, //肼
	{0xebc3, 0x670a}, //朊
	{0xebc4, 0x80bd}, //肽
	{0xebc5, 0x80b1}, //肱
	{0xebc6, 0x80ab}, //肫
	{0xebc7, 0x80ad}, //肭
	{0xebc8, 0x80b4}, //肴
	{0xebc9, 0x80b7}, //肷
	{0xebca, 0x80e7}, //胧
	{0xebcb, 0x80e8}, //胨
	{0xebcc, 0x80e9}, //胩
	{0xebcd, 0x80ea}, //胪
	{0xebce, 0x80db}, //胛
	{0xebcf, 0x80c2}, //胂
	{0xebd0, 0x80c4}, //胄
	{0xebd1, 0x80d9}, //胙
	{0xebd2, 0x80cd}, //胍
	{0xebd3, 0x80d7}, //胗
	{0xebd4, 0x6710}, //朐
	{0xebd5, 0x80dd}, //胝
	{0xebd6, 0x80eb}, //胫
	{0xebd7, 0x80f1}, //胱
	{0xebd8, 0x80f4}, //胴
	{0xebd9, 0x80ed}, //胭
	{0xebda, 0x810d}, //脍
	{0xebdb, 0x810e}, //脎
	{0xebdc, 0x80f2}, //胲
	{0xebdd, 0x80fc}, //胼
	{0xebde, 0x6715}, //朕
	{0xebdf, 0x8112}, //脒
	{0xebe0, 0x8c5a}, //豚
	{0xebe1, 0x8136}, //脶
	{0xebe2, 0x811e}, //脞
	{0xebe3, 0x812c}, //脬
	{0xebe4, 0x8118}, //脘
	{0xebe5, 0x8132}, //脲
	{0xebe6, 0x8148}, //腈
	{0xebe7, 0x814c}, //腌
	{0xebe8, 0x8153}, //腓
	{0xebe9, 0x8174}, //腴
	{0xebea, 0x8159}, //腙
	{0xebeb, 0x815a}, //腚
	{0xebec, 0x8171}, //腱
	{0xebed, 0x8160}, //腠
	{0xebee, 0x8169}, //腩
	{0xebef, 0x817c}, //腼
	{0xebf0, 0x817d}, //腽
	{0xebf1, 0x816d}, //腭
	{0xebf2, 0x8167}, //腧
	{0xebf3, 0x584d}, //塍
	{0xebf4, 0x5ab5}, //媵
	{0xebf5, 0x8188}, //膈
	{0xebf6, 0x8182}, //膂
	{0xebf7, 0x8191}, //膑
	{0xebf8, 0x6ed5}, //滕
	{0xebf9, 0x81a3}, //膣
	{0xebfa, 0x81aa}, //膪
	{0xebfb, 0x81cc}, //臌
	{0xebfc, 0x6726}, //朦
	{0xebfd, 0x81ca}, //臊
	{0xebfe, 0x81bb}, //膻
	{0xeca1, 0x81c1}, //臁
	{0xeca2, 0x81a6}, //膦
	{0xeca3, 0x6b24}, //欤
	{0xeca4, 0x6b37}, //欷
	{0xeca5, 0x6b39}, //欹
	{0xeca6, 0x6b43}, //歃
	{0xeca7, 0x6b46}, //歆
	{0xeca8, 0x6b59}, //歙
	{0xeca9, 0x98d1}, //飑
	{0xecaa, 0x98d2}, //飒
	{0xecab, 0x98d3}, //飓
	{0xecac, 0x98d5}, //飕
	{0xecad, 0x98d9}, //飙
	{0xecae, 0x98da}, //飚
	{0xecaf, 0x6bb3}, //殳
	{0xecb0, 0x5f40}, //彀
	{0xecb1, 0x6bc2}, //毂
	{0xecb2, 0x89f3}, //觳
	{0xecb3, 0x6590}, //斐
	{0xecb4, 0x9f51}, //齑
	{0xecb5, 0x6593}, //斓
	{0xecb6, 0x65bc}, //於
	{0xecb7, 0x65c6}, //旆
	{0xecb8, 0x65c4}, //旄
	{0xecb9, 0x65c3}, //旃
	{0xecba, 0x65cc}, //旌
	{0xecbb, 0x65ce}, //旎
	{0xecbc, 0x65d2}, //旒
	{0xecbd, 0x65d6}, //旖
	{0xecbe, 0x7080}, //炀
	{0xecbf, 0x709c}, //炜
	{0xecc0, 0x7096}, //炖
	{0xecc1, 0x709d}, //炝
	{0xecc2, 0x70bb}, //炻
	{0xecc3, 0x70c0}, //烀
	{0xecc4, 0x70b7}, //炷
	{0xecc5, 0x70ab}, //炫
	{0xecc6, 0x70b1}, //炱
	{0xecc7, 0x70e8}, //烨
	{0xecc8, 0x70ca}, //烊
	{0xecc9, 0x7110}, //焐
	{0xecca, 0x7113}, //焓
	{0xeccb, 0x7116}, //焖
	{0xeccc, 0x712f}, //焯
	{0xeccd, 0x7131}, //焱
	{0xecce, 0x7173}, //煳
	{0xeccf, 0x715c}, //煜
	{0xecd0, 0x7168}, //煨
	{0xecd1, 0x7145}, //煅
	{0xecd2, 0x7172}, //煲
	{0xecd3, 0x714a}, //煊
	{0xecd4, 0x7178}, //煸
	{0xecd5, 0x717a}, //煺
	{0xecd6, 0x7198}, //熘
	{0xecd7, 0x71b3}, //熳
	{0xecd8, 0x71b5}, //熵
	{0xecd9, 0x71a8}, //熨
	{0xecda, 0x71a0}, //熠
	{0xecdb, 0x71e0}, //燠
	{0xecdc, 0x71d4}, //燔
	{0xecdd, 0x71e7}, //燧
	{0xecde, 0x71f9}, //燹
	{0xecdf, 0x721d}, //爝
	{0xece0, 0x7228}, //爨
	{0xece1, 0x706c}, //灬
	{0xece2, 0x7118}, //焘
	{0xece3, 0x7166}, //煦
	{0xece4, 0x71b9}, //熹
	{0xece5, 0x623e}, //戾
	{0xece6, 0x623d}, //戽
	{0xece7, 0x6243}, //扃
	{0xece8, 0x6248}, //扈
	{0xece9, 0x6249}, //扉
	{0xecea, 0x793b}, //礻
	{0xeceb, 0x7940}, //祀
	{0xecec, 0x7946}, //祆
	{0xeced, 0x7949}, //祉
	{0xecee, 0x795b}, //祛
	{0xecef, 0x795c}, //祜
	{0xecf0, 0x7953}, //祓
	{0xecf1, 0x795a}, //祚
	{0xecf2, 0x7962}, //祢
	{0xecf3, 0x7957}, //祗
	{0xecf4, 0x7960}, //祠
	{0xecf5, 0x796f}, //祯
	{0xecf6, 0x7967}, //祧
	{0xecf7, 0x797a}, //祺
	{0xecf8, 0x7985}, //禅
	{0xecf9, 0x798a}, //禊
	{0xecfa, 0x799a}, //禚
	{0xecfb, 0x79a7}, //禧
	{0xecfc, 0x79b3}, //禳
	{0xecfd, 0x5fd1}, //忑
	{0xecfe, 0x5fd0}, //忐
	{0xeda1, 0x603c}, //怼
	{0xeda2, 0x605d}, //恝
	{0xeda3, 0x605a}, //恚
	{0xeda4, 0x6067}, //恧
	{0xeda5, 0x6041}, //恁
	{0xeda6, 0x6059}, //恙
	{0xeda7, 0x6063}, //恣
	{0xeda8, 0x60ab}, //悫
	{0xeda9, 0x6106}, //愆
	{0xedaa, 0x610d}, //愍
	{0xedab, 0x615d}, //慝
	{0xedac, 0x61a9}, //憩
	{0xedad, 0x619d}, //憝
	{0xedae, 0x61cb}, //懋
	{0xedaf, 0x61d1}, //懑
	{0xedb0, 0x6206}, //戆
	{0xedb1, 0x8080}, //肀
	{0xedb2, 0x807f}, //聿
	{0xedb3, 0x6c93}, //沓
	{0xedb4, 0x6cf6}, //泶
	{0xedb5, 0x6dfc}, //淼
	{0xedb6, 0x77f6}, //矶
	{0xedb7, 0x77f8}, //矸
	{0xedb8, 0x7800}, //砀
	{0xedb9, 0x7809}, //砉
	{0xedba, 0x7817}, //砗
	{0xedbb, 0x7818}, //砘
	{0xedbc, 0x7811}, //砑
	{0xedbd, 0x65ab}, //斫
	{0xedbe, 0x782d}, //砭
	{0xedbf, 0x781c}, //砜
	{0xedc0, 0x781d}, //砝
	{0xedc1, 0x7839}, //砹
	{0xedc2, 0x783a}, //砺
	{0xedc3, 0x783b}, //砻
	{0xedc4, 0x781f}, //砟
	{0xedc5, 0x783c}, //砼
	{0xedc6, 0x7825}, //砥
	{0xedc7, 0x782c}, //砬
	{0xedc8, 0x7823}, //砣
	{0xedc9, 0x7829}, //砩
	{0xedca, 0x784e}, //硎
	{0xedcb, 0x786d}, //硭
	{0xedcc, 0x7856}, //硖
	{0xedcd, 0x7857}, //硗
	{0xedce, 0x7826}, //砦
	{0xedcf, 0x7850}, //硐
	{0xedd0, 0x7847}, //硇
	{0xedd1, 0x784c}, //硌
	{0xedd2, 0x786a}, //硪
	{0xedd3, 0x789b}, //碛
	{0xedd4, 0x7893}, //碓
	{0xedd5, 0x789a}, //碚
	{0xedd6, 0x7887}, //碇
	{0xedd7, 0x789c}, //碜
	{0xedd8, 0x78a1}, //碡
	{0xedd9, 0x78a3}, //碣
	{0xedda, 0x78b2}, //碲
	{0xeddb, 0x78b9}, //碹
	{0xeddc, 0x78a5}, //碥
	{0xeddd, 0x78d4}, //磔
	{0xedde, 0x78d9}, //磙
	{0xeddf, 0x78c9}, //磉
	{0xede0, 0x78ec}, //磬
	{0xede1, 0x78f2}, //磲
	{0xede2, 0x7905}, //礅
	{0xede3, 0x78f4}, //磴
	{0xede4, 0x7913}, //礓
	{0xede5, 0x7924}, //礤
	{0xede6, 0x791e}, //礞
	{0xede7, 0x7934}, //礴
	{0xede8, 0x9f9b}, //龛
	{0xede9, 0x9ef9}, //黹
	{0xedea, 0x9efb}, //黻
	{0xedeb, 0x9efc}, //黼
	{0xedec, 0x76f1}, //盱
	{0xeded, 0x7704}, //眄
	{0xedee, 0x770d}, //眍
	{0xedef, 0x76f9}, //盹
	{0xedf0, 0x7707}, //眇
	{0xedf1, 0x7708}, //眈
	{0xedf2, 0x771a}, //眚
	{0xedf3, 0x7722}, //眢
	{0xedf4, 0x7719}, //眙
	{0xedf5, 0x772d}, //眭
	{0xedf6, 0x7726}, //眦
	{0xedf7, 0x7735}, //眵
	{0xedf8, 0x7738}, //眸
	{0xedf9, 0x7750}, //睐
	{0xedfa, 0x7751}, //睑
	{0xedfb, 0x7747}, //睇
	{0xedfc, 0x7743}, //睃
	{0xedfd, 0x775a}, //睚
	{0xedfe, 0x7768}, //睨
	{0xeea1, 0x7762}, //睢
	{0xeea2, 0x7765}, //睥
	{0xeea3, 0x777f}, //睿
	{0xeea4, 0x778d}, //瞍
	{0xeea5, 0x777d}, //睽
	{0xeea6, 0x7780}, //瞀
	{0xeea7, 0x778c}, //瞌
	{0xeea8, 0x7791}, //瞑
	{0xeea9, 0x779f}, //瞟
	{0xeeaa, 0x77a0}, //瞠
	{0xeeab, 0x77b0}, //瞰
	{0xeeac, 0x77b5}, //瞵
	{0xeead, 0x77bd}, //瞽
	{0xeeae, 0x753a}, //町
	{0xeeaf, 0x7540}, //畀
	{0xeeb0, 0x754e}, //畎
	{0xeeb1, 0x754b}, //畋
	{0xeeb2, 0x7548}, //畈
	{0xeeb3, 0x755b}, //畛
	{0xeeb4, 0x7572}, //畲
	{0xeeb5, 0x7579}, //畹
	{0xeeb6, 0x7583}, //疃
	{0xeeb7, 0x7f58}, //罘
	{0xeeb8, 0x7f61}, //罡
	{0xeeb9, 0x7f5f}, //罟
	{0xeeba, 0x8a48}, //詈
	{0xeebb, 0x7f68}, //罨
	{0xeebc, 0x7f74}, //罴
	{0xeebd, 0x7f71}, //罱
	{0xeebe, 0x7f79}, //罹
	{0xeebf, 0x7f81}, //羁
	{0xeec0, 0x7f7e}, //罾
	{0xeec1, 0x76cd}, //盍
	{0xeec2, 0x76e5}, //盥
	{0xeec3, 0x8832}, //蠲
	{0xeec4, 0x9485}, //钅
	{0xeec5, 0x9486}, //钆
	{0xeec6, 0x9487}, //钇
	{0xeec7, 0x948b}, //钋
	{0xeec8, 0x948a}, //钊
	{0xeec9, 0x948c}, //钌
	{0xeeca, 0x948d}, //钍
	{0xeecb, 0x948f}, //钏
	{0xeecc, 0x9490}, //钐
	{0xeecd, 0x9494}, //钔
	{0xeece, 0x9497}, //钗
	{0xeecf, 0x9495}, //钕
	{0xeed0, 0x949a}, //钚
	{0xeed1, 0x949b}, //钛
	{0xeed2, 0x949c}, //钜
	{0xeed3, 0x94a3}, //钣
	{0xeed4, 0x94a4}, //钤
	{0xeed5, 0x94ab}, //钫
	{0xeed6, 0x94aa}, //钪
	{0xeed7, 0x94ad}, //钭
	{0xeed8, 0x94ac}, //钬
	{0xeed9, 0x94af}, //钯
	{0xeeda, 0x94b0}, //钰
	{0xeedb, 0x94b2}, //钲
	{0xeedc, 0x94b4}, //钴
	{0xeedd, 0x94b6}, //钶
	{0xeede, 0x94b7}, //钷
	{0xeedf, 0x94b8}, //钸
	{0xeee0, 0x94b9}, //钹
	{0xeee1, 0x94ba}, //钺
	{0xeee2, 0x94bc}, //钼
	{0xeee3, 0x94bd}, //钽
	{0xeee4, 0x94bf}, //钿
	{0xeee5, 0x94c4}, //铄
	{0xeee6, 0x94c8}, //铈
	{0xeee7, 0x94c9}, //铉
	{0xeee8, 0x94ca}, //铊
	{0xeee9, 0x94cb}, //铋
	{0xeeea, 0x94cc}, //铌
	{0xeeeb, 0x94cd}, //铍
	{0xeeec, 0x94ce}, //铎
	{0xeeed, 0x94d0}, //铐
	{0xeeee, 0x94d1}, //铑
	{0xeeef, 0x94d2}, //铒
	{0xeef0, 0x94d5}, //铕
	{0xeef1, 0x94d6}, //铖
	{0xeef2, 0x94d7}, //铗
	{0xeef3, 0x94d9}, //铙
	{0xeef4, 0x94d8}, //铘
	{0xeef5, 0x94db}, //铛
	{0xeef6, 0x94de}, //铞
	{0xeef7, 0x94df}, //铟
	{0xeef8, 0x94e0}, //铠
	{0xeef9, 0x94e2}, //铢
	{0xeefa, 0x94e4}, //铤
	{0xeefb, 0x94e5}, //铥
	{0xeefc, 0x94e7}, //铧
	{0xeefd, 0x94e8}, //铨
	{0xeefe, 0x94ea}, //铪
	{0xefa1, 0x94e9}, //铩
	{0xefa2, 0x94eb}, //铫
	{0xefa3, 0x94ee}, //铮
	{0xefa4, 0x94ef}, //铯
	{0xefa5, 0x94f3}, //铳
	{0xefa6, 0x94f4}, //铴
	{0xefa7, 0x94f5}, //铵
	{0xefa8, 0x94f7}, //铷
	{0xefa9, 0x94f9}, //铹
	{0xefaa, 0x94fc}, //铼
	{0xefab, 0x94fd}, //铽
	{0xefac, 0x94ff}, //铿
	{0xefad, 0x9503}, //锃
	{0xefae, 0x9502}, //锂
	{0xefaf, 0x9506}, //锆
	{0xefb0, 0x9507}, //锇
	{0xefb1, 0x9509}, //锉
	{0xefb2, 0x950a}, //锊
	{0xefb3, 0x950d}, //锍
	{0xefb4, 0x950e}, //锎
	{0xefb5, 0x950f}, //锏
	{0xefb6, 0x9512}, //锒
	{0xefb7, 0x9513}, //锓
	{0xefb8, 0x9514}, //锔
	{0xefb9, 0x9515}, //锕
	{0xefba, 0x9516}, //锖
	{0xefbb, 0x9518}, //锘
	{0xefbc, 0x951b}, //锛
	{0xefbd, 0x951d}, //锝
	{0xefbe, 0x951e}, //锞
	{0xefbf, 0x951f}, //锟
	{0xefc0, 0x9522}, //锢
	{0xefc1, 0x952a}, //锪
	{0xefc2, 0x952b}, //锫
	{0xefc3, 0x9529}, //锩
	{0xefc4, 0x952c}, //锬
	{0xefc5, 0x9531}, //锱
	{0xefc6, 0x9532}, //锲
	{0xefc7, 0x9534}, //锴
	{0xefc8, 0x9536}, //锶
	{0xefc9, 0x9537}, //锷
	{0xefca, 0x9538}, //锸
	{0xefcb, 0x953c}, //锼
	{0xefcc, 0x953e}, //锾
	{0xefcd, 0x953f}, //锿
	{0xefce, 0x9542}, //镂
	{0xefcf, 0x9535}, //锵
	{0xefd0, 0x9544}, //镄
	{0xefd1, 0x9545}, //镅
	{0xefd2, 0x9546}, //镆
	{0xefd3, 0x9549}, //镉
	{0xefd4, 0x954c}, //镌
	{0xefd5, 0x954e}, //镎
	{0xefd6, 0x954f}, //镏
	{0xefd7, 0x9552}, //镒
	{0xefd8, 0x9553}, //镓
	{0xefd9, 0x9554}, //镔
	{0xefda, 0x9556}, //镖
	{0xefdb, 0x9557}, //镗
	{0xefdc, 0x9558}, //镘
	{0xefdd, 0x9559}, //镙
	{0xefde, 0x955b}, //镛
	{0xefdf, 0x955e}, //镞
	{0xefe0, 0x955f}, //镟
	{0xefe1, 0x955d}, //镝
	{0xefe2, 0x9561}, //镡
	{0xefe3, 0x9562}, //镢
	{0xefe4, 0x9564}, //镤
	{0xefe5, 0x9565}, //镥
	{0xefe6, 0x9566}, //镦
	{0xefe7, 0x9567}, //镧
	{0xefe8, 0x9568}, //镨
	{0xefe9, 0x9569}, //镩
	{0xefea, 0x956a}, //镪
	{0xefeb, 0x956b}, //镫
	{0xefec, 0x956c}, //镬
	{0xefed, 0x956f}, //镯
	{0xefee, 0x9571}, //镱
	{0xefef, 0x9572}, //镲
	{0xeff0, 0x9573}, //镳
	{0xeff1, 0x953a}, //锺
	{0xeff2, 0x77e7}, //矧
	{0xeff3, 0x77ec}, //矬
	{0xeff4, 0x96c9}, //雉
	{0xeff5, 0x79d5}, //秕
	{0xeff6, 0x79ed}, //秭
	{0xeff7, 0x79e3}, //秣
	{0xeff8, 0x79eb}, //秫
	{0xeff9, 0x7a06}, //稆
	{0xeffa, 0x5d47}, //嵇
	{0xeffb, 0x7a03}, //稃
	{0xeffc, 0x7a02}, //稂
	{0xeffd, 0x7a1e}, //稞
	{0xeffe, 0x7a14}, //稔
	{0xf0a1, 0x7a39}, //稹
	{0xf0a2, 0x7a37}, //稷
	{0xf0a3, 0x7a51}, //穑
	{0xf0a4, 0x9ecf}, //黏
	{0xf0a5, 0x99a5}, //馥
	{0xf0a6, 0x7a70}, //穰
	{0xf0a7, 0x7688}, //皈
	{0xf0a8, 0x768e}, //皎
	{0xf0a9, 0x7693}, //皓
	{0xf0aa, 0x7699}, //皙
	{0xf0ab, 0x76a4}, //皤
	{0xf0ac, 0x74de}, //瓞
	{0xf0ad, 0x74e0}, //瓠
	{0xf0ae, 0x752c}, //甬
	{0xf0af, 0x9e20}, //鸠
	{0xf0b0, 0x9e22}, //鸢
	{0xf0b1, 0x9e28}, //鸨
	{0xf0b2, 0x9e29}, //鸩
	{0xf0b3, 0x9e2a}, //鸪
	{0xf0b4, 0x9e2b}, //鸫
	{0xf0b5, 0x9e2c}, //鸬
	{0xf0b6, 0x9e32}, //鸲
	{0xf0b7, 0x9e31}, //鸱
	{0xf0b8, 0x9e36}, //鸶
	{0xf0b9, 0x9e38}, //鸸
	{0xf0ba, 0x9e37}, //鸷
	{0xf0bb, 0x9e39}, //鸹
	{0xf0bc, 0x9e3a}, //鸺
	{0xf0bd, 0x9e3e}, //鸾
	{0xf0be, 0x9e41}, //鹁
	{0xf0bf, 0x9e42}, //鹂
	{0xf0c0, 0x9e44}, //鹄
	{0xf0c1, 0x9e46}, //鹆
	{0xf0c2, 0x9e47}, //鹇
	{0xf0c3, 0x9e48}, //鹈
	{0xf0c4, 0x9e49}, //鹉
	{0xf0c5, 0x9e4b}, //鹋
	{0xf0c6, 0x9e4c}, //鹌
	{0xf0c7, 0x9e4e}, //鹎
	{0xf0c8, 0x9e51}, //鹑
	{0xf0c9, 0x9e55}, //鹕
	{0xf0ca, 0x9e57}, //鹗
	{0xf0cb, 0x9e5a}, //鹚
	{0xf0cc, 0x9e5b}, //鹛
	{0xf0cd, 0x9e5c}, //鹜
	{0xf0ce, 0x9e5e}, //鹞
	{0xf0cf, 0x9e63}, //鹣
	{0xf0d0, 0x9e66}, //鹦
	{0xf0d1, 0x9e67}, //鹧
	{0xf0d2, 0x9e68}, //鹨
	{0xf0d3, 0x9e69}, //鹩
	{0xf0d4, 0x9e6a}, //鹪
	{0xf0d5, 0x9e6b}, //鹫
	{0xf0d6, 0x9e6c}, //鹬
	{0xf0d7, 0x9e71}, //鹱
	{0xf0d8, 0x9e6d}, //鹭
	{0xf0d9, 0x9e73}, //鹳
	{0xf0da, 0x7592}, //疒
	{0xf0db, 0x7594}, //疔
	{0xf0dc, 0x7596}, //疖
	{0xf0dd, 0x75a0}, //疠
	{0xf0de, 0x759d}, //疝
	{0xf0df, 0x75ac}, //疬
	{0xf0e0, 0x75a3}, //疣
	{0xf0e1, 0x75b3}, //疳
	{0xf0e2, 0x75b4}, //疴
	{0xf0e3, 0x75b8}, //疸
	{0xf0e4, 0x75c4}, //痄
	{0xf0e5, 0x75b1}, //疱
	{0xf0e6, 0x75b0}, //疰
	{0xf0e7, 0x75c3}, //痃
	{0xf0e8, 0x75c2}, //痂
	{0xf0e9, 0x75d6}, //痖
	{0xf0ea, 0x75cd}, //痍
	{0xf0eb, 0x75e3}, //痣
	{0xf0ec, 0x75e8}, //痨
	{0xf0ed, 0x75e6}, //痦
	{0xf0ee, 0x75e4}, //痤
	{0xf0ef, 0x75eb}, //痫
	{0xf0f0, 0x75e7}, //痧
	{0xf0f1, 0x7603}, //瘃
	{0xf0f2, 0x75f1}, //痱
	{0xf0f3, 0x75fc}, //痼
	{0xf0f4, 0x75ff}, //痿
	{0xf0f5, 0x7610}, //瘐
	{0xf0f6, 0x7600}, //瘀
	{0xf0f7, 0x7605}, //瘅
	{0xf0f8, 0x760c}, //瘌
	{0xf0f9, 0x7617}, //瘗
	{0xf0fa, 0x760a}, //瘊
	{0xf0fb, 0x7625}, //瘥
	{0xf0fc, 0x7618}, //瘘
	{0xf0fd, 0x7615}, //瘕
	{0xf0fe, 0x7619}, //瘙
	{0xf1a1, 0x761b}, //瘛
	{0xf1a2, 0x763c}, //瘼
	{0xf1a3, 0x7622}, //瘢
	{0xf1a4, 0x7620}, //瘠
	{0xf1a5, 0x7640}, //癀
	{0xf1a6, 0x762d}, //瘭
	{0xf1a7, 0x7630}, //瘰
	{0xf1a8, 0x763f}, //瘿
	{0xf1a9, 0x7635}, //瘵
	{0xf1aa, 0x7643}, //癃
	{0xf1ab, 0x763e}, //瘾
	{0xf1ac, 0x7633}, //瘳
	{0xf1ad, 0x764d}, //癍
	{0xf1ae, 0x765e}, //癞
	{0xf1af, 0x7654}, //癔
	{0xf1b0, 0x765c}, //癜
	{0xf1b1, 0x7656}, //癖
	{0xf1b2, 0x766b}, //癫
	{0xf1b3, 0x766f}, //癯
	{0xf1b4, 0x7fca}, //翊
	{0xf1b5, 0x7ae6}, //竦
	{0xf1b6, 0x7a78}, //穸
	{0xf1b7, 0x7a79}, //穹
	{0xf1b8, 0x7a80}, //窀
	{0xf1b9, 0x7a86}, //窆
	{0xf1ba, 0x7a88}, //窈
	{0xf1bb, 0x7a95}, //窕
	{0xf1bc, 0x7aa6}, //窦
	{0xf1bd, 0x7aa0}, //窠
	{0xf1be, 0x7aac}, //窬
	{0xf1bf, 0x7aa8}, //窨
	{0xf1c0, 0x7aad}, //窭
	{0xf1c1, 0x7ab3}, //窳
	{0xf1c2, 0x8864}, //衤
	{0xf1c3, 0x8869}, //衩
	{0xf1c4, 0x8872}, //衲
	{0xf1c5, 0x887d}, //衽
	{0xf1c6, 0x887f}, //衿
	{0xf1c7, 0x8882}, //袂
	{0xf1c8, 0x88a2}, //袢
	{0xf1c9, 0x88c6}, //裆
	{0xf1ca, 0x88b7}, //袷
	{0xf1cb, 0x88bc}, //袼
	{0xf1cc, 0x88c9}, //裉
	{0xf1cd, 0x88e2}, //裢
	{0xf1ce, 0x88ce}, //裎
	{0xf1cf, 0x88e3}, //裣
	{0xf1d0, 0x88e5}, //裥
	{0xf1d1, 0x88f1}, //裱
	{0xf1d2, 0x891a}, //褚
	{0xf1d3, 0x88fc}, //裼
	{0xf1d4, 0x88e8}, //裨
	{0xf1d5, 0x88fe}, //裾
	{0xf1d6, 0x88f0}, //裰
	{0xf1d7, 0x8921}, //褡
	{0xf1d8, 0x8919}, //褙
	{0xf1d9, 0x8913}, //褓
	{0xf1da, 0x891b}, //褛
	{0xf1db, 0x890a}, //褊
	{0xf1dc, 0x8934}, //褴
	{0xf1dd, 0x892b}, //褫
	{0xf1de, 0x8936}, //褶
	{0xf1df, 0x8941}, //襁
	{0xf1e0, 0x8966}, //襦
	{0xf1e1, 0x897b}, //襻
	{0xf1e2, 0x758b}, //疋
	{0xf1e3, 0x80e5}, //胥
	{0xf1e4, 0x76b2}, //皲
	{0xf1e5, 0x76b4}, //皴
	{0xf1e6, 0x77dc}, //矜
	{0xf1e7, 0x8012}, //耒
	{0xf1e8, 0x8014}, //耔
	{0xf1e9, 0x8016}, //耖
	{0xf1ea, 0x801c}, //耜
	{0xf1eb, 0x8020}, //耠
	{0xf1ec, 0x8022}, //耢
	{0xf1ed, 0x8025}, //耥
	{0xf1ee, 0x8026}, //耦
	{0xf1ef, 0x8027}, //耧
	{0xf1f0, 0x8029}, //耩
	{0xf1f1, 0x8028}, //耨
	{0xf1f2, 0x8031}, //耱
	{0xf1f3, 0x800b}, //耋
	{0xf1f4, 0x8035}, //耵
	{0xf1f5, 0x8043}, //聃
	{0xf1f6, 0x8046}, //聆
	{0xf1f7, 0x804d}, //聍
	{0xf1f8, 0x8052}, //聒
	{0xf1f9, 0x8069}, //聩
	{0xf1fa, 0x8071}, //聱
	{0xf1fb, 0x8983}, //覃
	{0xf1fc, 0x9878}, //顸
	{0xf1fd, 0x9880}, //颀
	{0xf1fe, 0x9883}, //颃
	{0xf2a1, 0x9889}, //颉
	{0xf2a2, 0x988c}, //颌
	{0xf2a3, 0x988d}, //颍
	{0xf2a4, 0x988f}, //颏
	{0xf2a5, 0x9894}, //颔
	{0xf2a6, 0x989a}, //颚
	{0xf2a7, 0x989b}, //颛
	{0xf2a8, 0x989e}, //颞
	{0xf2a9, 0x989f}, //颟
	{0xf2aa, 0x98a1}, //颡
	{0xf2ab, 0x98a2}, //颢
	{0xf2ac, 0x98a5}, //颥
	{0xf2ad, 0x98a6}, //颦
	{0xf2ae, 0x864d}, //虍
	{0xf2af, 0x8654}, //虔
	{0xf2b0, 0x866c}, //虬
	{0xf2b1, 0x866e}, //虮
	{0xf2b2, 0x867f}, //虿
	{0xf2b3, 0x867a}, //虺
	{0xf2b4, 0x867c}, //虼
	{0xf2b5, 0x867b}, //虻
	{0xf2b6, 0x86a8}, //蚨
	{0xf2b7, 0x868d}, //蚍
	{0xf2b8, 0x868b}, //蚋
	{0xf2b9, 0x86ac}, //蚬
	{0xf2ba, 0x869d}, //蚝
	{0xf2bb, 0x86a7}, //蚧
	{0xf2bc, 0x86a3}, //蚣
	{0xf2bd, 0x86aa}, //蚪
	{0xf2be, 0x8693}, //蚓
	{0xf2bf, 0x86a9}, //蚩
	{0xf2c0, 0x86b6}, //蚶
	{0xf2c1, 0x86c4}, //蛄
	{0xf2c2, 0x86b5}, //蚵
	{0xf2c3, 0x86ce}, //蛎
	{0xf2c4, 0x86b0}, //蚰
	{0xf2c5, 0x86ba}, //蚺
	{0xf2c6, 0x86b1}, //蚱
	{0xf2c7, 0x86af}, //蚯
	{0xf2c8, 0x86c9}, //蛉
	{0xf2c9, 0x86cf}, //蛏
	{0xf2ca, 0x86b4}, //蚴
	{0xf2cb, 0x86e9}, //蛩
	{0xf2cc, 0x86f1}, //蛱
	{0xf2cd, 0x86f2}, //蛲
	{0xf2ce, 0x86ed}, //蛭
	{0xf2cf, 0x86f3}, //蛳
	{0xf2d0, 0x86d0}, //蛐
	{0xf2d1, 0x8713}, //蜓
	{0xf2d2, 0x86de}, //蛞
	{0xf2d3, 0x86f4}, //蛴
	{0xf2d4, 0x86df}, //蛟
	{0xf2d5, 0x86d8}, //蛘
	{0xf2d6, 0x86d1}, //蛑
	{0xf2d7, 0x8703}, //蜃
	{0xf2d8, 0x8707}, //蜇
	{0xf2d9, 0x86f8}, //蛸
	{0xf2da, 0x8708}, //蜈
	{0xf2db, 0x870a}, //蜊
	{0xf2dc, 0x870d}, //蜍
	{0xf2dd, 0x8709}, //蜉
	{0xf2de, 0x8723}, //蜣
	{0xf2df, 0x873b}, //蜻
	{0xf2e0, 0x871e}, //蜞
	{0xf2e1, 0x8725}, //蜥
	{0xf2e2, 0x872e}, //蜮
	{0xf2e3, 0x871a}, //蜚
	{0xf2e4, 0x873e}, //蜾
	{0xf2e5, 0x8748}, //蝈
	{0xf2e6, 0x8734}, //蜴
	{0xf2e7, 0x8731}, //蜱
	{0xf2e8, 0x8729}, //蜩
	{0xf2e9, 0x8737}, //蜷
	{0xf2ea, 0x873f}, //蜿
	{0xf2eb, 0x8782}, //螂
	{0xf2ec, 0x8722}, //蜢
	{0xf2ed, 0x877d}, //蝽
	{0xf2ee, 0x877e}, //蝾
	{0xf2ef, 0x877b}, //蝻
	{0xf2f0, 0x8760}, //蝠
	{0xf2f1, 0x8770}, //蝰
	{0xf2f2, 0x874c}, //蝌
	{0xf2f3, 0x876e}, //蝮
	{0xf2f4, 0x878b}, //螋
	{0xf2f5, 0x8753}, //蝓
	{0xf2f6, 0x8763}, //蝣
	{0xf2f7, 0x877c}, //蝼
	{0xf2f8, 0x8764}, //蝤
	{0xf2f9, 0x8759}, //蝙
	{0xf2fa, 0x8765}, //蝥
	{0xf2fb, 0x8793}, //螓
	{0xf2fc, 0x87af}, //螯
	{0xf2fd, 0x87a8}, //螨
	{0xf2fe, 0x87d2}, //蟒
	{0xf3a1, 0x87c6}, //蟆
	{0xf3a2, 0x8788}, //螈
	{0xf3a3, 0x8785}, //螅
	{0xf3a4, 0x87ad}, //螭
	{0xf3a5, 0x8797}, //螗
	{0xf3a6, 0x8783}, //螃
	{0xf3a7, 0x87ab}, //螫
	{0xf3a8, 0x87e5}, //蟥
	{0xf3a9, 0x87ac}, //螬
	{0xf3aa, 0x87b5}, //螵
	{0xf3ab, 0x87b3}, //螳
	{0xf3ac, 0x87cb}, //蟋
	{0xf3ad, 0x87d3}, //蟓
	{0xf3ae, 0x87bd}, //螽
	{0xf3af, 0x87d1}, //蟑
	{0xf3b0, 0x87c0}, //蟀
	{0xf3b1, 0x87ca}, //蟊
	{0xf3b2, 0x87db}, //蟛
	{0xf3b3, 0x87ea}, //蟪
	{0xf3b4, 0x87e0}, //蟠
	{0xf3b5, 0x87ee}, //蟮
	{0xf3b6, 0x8816}, //蠖
	{0xf3b7, 0x8813}, //蠓
	{0xf3b8, 0x87fe}, //蟾
	{0xf3b9, 0x880a}, //蠊
	{0xf3ba, 0x881b}, //蠛
	{0xf3bb, 0x8821}, //蠡
	{0xf3bc, 0x8839}, //蠹
	{0xf3bd, 0x883c}, //蠼
	{0xf3be, 0x7f36}, //缶
	{0xf3bf, 0x7f42}, //罂
	{0xf3c0, 0x7f44}, //罄
	{0xf3c1, 0x7f45}, //罅
	{0xf3c2, 0x8210}, //舐
	{0xf3c3, 0x7afa}, //竺
	{0xf3c4, 0x7afd}, //竽
	{0xf3c5, 0x7b08}, //笈
	{0xf3c6, 0x7b03}, //笃
	{0xf3c7, 0x7b04}, //笄
	{0xf3c8, 0x7b15}, //笕
	{0xf3c9, 0x7b0a}, //笊
	{0xf3ca, 0x7b2b}, //笫
	{0xf3cb, 0x7b0f}, //笏
	{0xf3cc, 0x7b47}, //筇
	{0xf3cd, 0x7b38}, //笸
	{0xf3ce, 0x7b2a}, //笪
	{0xf3cf, 0x7b19}, //笙
	{0xf3d0, 0x7b2e}, //笮
	{0xf3d1, 0x7b31}, //笱
	{0xf3d2, 0x7b20}, //笠
	{0xf3d3, 0x7b25}, //笥
	{0xf3d4, 0x7b24}, //笤
	{0xf3d5, 0x7b33}, //笳
	{0xf3d6, 0x7b3e}, //笾
	{0xf3d7, 0x7b1e}, //笞
	{0xf3d8, 0x7b58}, //筘
	{0xf3d9, 0x7b5a}, //筚
	{0xf3da, 0x7b45}, //筅
	{0xf3db, 0x7b75}, //筵
	{0xf3dc, 0x7b4c}, //筌
	{0xf3dd, 0x7b5d}, //筝
	{0xf3de, 0x7b60}, //筠
	{0xf3df, 0x7b6e}, //筮
	{0xf3e0, 0x7b7b}, //筻
	{0xf3e1, 0x7b62}, //筢
	{0xf3e2, 0x7b72}, //筲
	{0xf3e3, 0x7b71}, //筱
	{0xf3e4, 0x7b90}, //箐
	{0xf3e5, 0x7ba6}, //箦
	{0xf3e6, 0x7ba7}, //箧
	{0xf3e7, 0x7bb8}, //箸
	{0xf3e8, 0x7bac}, //箬
	{0xf3e9, 0x7b9d}, //箝
	{0xf3ea, 0x7ba8}, //箨
	{0xf3eb, 0x7b85}, //箅
	{0xf3ec, 0x7baa}, //箪
	{0xf3ed, 0x7b9c}, //箜
	{0xf3ee, 0x7ba2}, //箢
	{0xf3ef, 0x7bab}, //箫
	{0xf3f0, 0x7bb4}, //箴
	{0xf3f1, 0x7bd1}, //篑
	{0xf3f2, 0x7bc1}, //篁
	{0xf3f3, 0x7bcc}, //篌
	{0xf3f4, 0x7bdd}, //篝
	{0xf3f5, 0x7bda}, //篚
	{0xf3f6, 0x7be5}, //篥
	{0xf3f7, 0x7be6}, //篦
	{0xf3f8, 0x7bea}, //篪
	{0xf3f9, 0x7c0c}, //簌
	{0xf3fa, 0x7bfe}, //篾
	{0xf3fb, 0x7bfc}, //篼
	{0xf3fc, 0x7c0f}, //簏
	{0xf3fd, 0x7c16}, //簖
	{0xf3fe, 0x7c0b}, //簋
	{0xf4a1, 0x7c1f}, //簟
	{0xf4a2, 0x7c2a}, //簪
	{0xf4a3, 0x7c26}, //簦
	{0xf4a4, 0x7c38}, //簸
	{0xf4a5, 0x7c41}, //籁
	{0xf4a6, 0x7c40}, //籀
	{0xf4a7, 0x81fe}, //臾
	{0xf4a8, 0x8201}, //舁
	{0xf4a9, 0x8202}, //舂
	{0xf4aa, 0x8204}, //舄
	{0xf4ab, 0x81ec}, //臬
	{0xf4ac, 0x8844}, //衄
	{0xf4ad, 0x8221}, //舡
	{0xf4ae, 0x8222}, //舢
	{0xf4af, 0x8223}, //舣
	{0xf4b0, 0x822d}, //舭
	{0xf4b1, 0x822f}, //舯
	{0xf4b2, 0x8228}, //舨
	{0xf4b3, 0x822b}, //舫
	{0xf4b4, 0x8238}, //舸
	{0xf4b5, 0x823b}, //舻
	{0xf4b6, 0x8233}, //舳
	{0xf4b7, 0x8234}, //舴
	{0xf4b8, 0x823e}, //舾
	{0xf4b9, 0x8244}, //艄
	{0xf4ba, 0x8249}, //艉
	{0xf4bb, 0x824b}, //艋
	{0xf4bc, 0x824f}, //艏
	{0xf4bd, 0x825a}, //艚
	{0xf4be, 0x825f}, //艟
	{0xf4bf, 0x8268}, //艨
	{0xf4c0, 0x887e}, //衾
	{0xf4c1, 0x8885}, //袅
	{0xf4c2, 0x8888}, //袈
	{0xf4c3, 0x88d8}, //裘
	{0xf4c4, 0x88df}, //裟
	{0xf4c5, 0x895e}, //襞
	{0xf4c6, 0x7f9d}, //羝
	{0xf4c7, 0x7f9f}, //羟
	{0xf4c8, 0x7fa7}, //羧
	{0xf4c9, 0x7faf}, //羯
	{0xf4ca, 0x7fb0}, //羰
	{0xf4cb, 0x7fb2}, //羲
	{0xf4cc, 0x7c7c}, //籼
	{0xf4cd, 0x6549}, //敉
	{0xf4ce, 0x7c91}, //粑
	{0xf4cf, 0x7c9d}, //粝
	{0xf4d0, 0x7c9c}, //粜
	{0xf4d1, 0x7c9e}, //粞
	{0xf4d2, 0x7ca2}, //粢
	{0xf4d3, 0x7cb2}, //粲
	{0xf4d4, 0x7cbc}, //粼
	{0xf4d5, 0x7cbd}, //粽
	{0xf4d6, 0x7cc1}, //糁
	{0xf4d7, 0x7cc7}, //糇
	{0xf4d8, 0x7ccc}, //糌
	{0xf4d9, 0x7ccd}, //糍
	{0xf4da, 0x7cc8}, //糈
	{0xf4db, 0x7cc5}, //糅
	{0xf4dc, 0x7cd7}, //糗
	{0xf4dd, 0x7ce8}, //糨
	{0xf4de, 0x826e}, //艮
	{0xf4df, 0x66a8}, //暨
	{0xf4e0, 0x7fbf}, //羿
	{0xf4e1, 0x7fce}, //翎
	{0xf4e2, 0x7fd5}, //翕
	{0xf4e3, 0x7fe5}, //翥
	{0xf4e4, 0x7fe1}, //翡
	{0xf4e5, 0x7fe6}, //翦
	{0xf4e6, 0x7fe9}, //翩
	{0xf4e7, 0x7fee}, //翮
	{0xf4e8, 0x7ff3}, //翳
	{0xf4e9, 0x7cf8}, //糸
	{0xf4ea, 0x7d77}, //絷
	{0xf4eb, 0x7da6}, //綦
	{0xf4ec, 0x7dae}, //綮
	{0xf4ed, 0x7e47}, //繇
	{0xf4ee, 0x7e9b}, //纛
	{0xf4ef, 0x9eb8}, //麸
	{0xf4f0, 0x9eb4}, //麴
	{0xf4f1, 0x8d73}, //赳
	{0xf4f2, 0x8d84}, //趄
	{0xf4f3, 0x8d94}, //趔
	{0xf4f4, 0x8d91}, //趑
	{0xf4f5, 0x8db1}, //趱
	{0xf4f6, 0x8d67}, //赧
	{0xf4f7, 0x8d6d}, //赭
	{0xf4f8, 0x8c47}, //豇
	{0xf4f9, 0x8c49}, //豉
	{0xf4fa, 0x914a}, //酊
	{0xf4fb, 0x9150}, //酐
	{0xf4fc, 0x914e}, //酎
	{0xf4fd, 0x914f}, //酏
	{0xf4fe, 0x9164}, //酤
	{0xf5a1, 0x9162}, //酢
	{0xf5a2, 0x9161}, //酡
	{0xf5a3, 0x9170}, //酰
	{0xf5a4, 0x9169}, //酩
	{0xf5a5, 0x916f}, //酯
	{0xf5a6, 0x917d}, //酽
	{0xf5a7, 0x917e}, //酾
	{0xf5a8, 0x9172}, //酲
	{0xf5a9, 0x9174}, //酴
	{0xf5aa, 0x9179}, //酹
	{0xf5ab, 0x918c}, //醌
	{0xf5ac, 0x9185}, //醅
	{0xf5ad, 0x9190}, //醐
	{0xf5ae, 0x918d}, //醍
	{0xf5af, 0x9191}, //醑
	{0xf5b0, 0x91a2}, //醢
	{0xf5b1, 0x91a3}, //醣
	{0xf5b2, 0x91aa}, //醪
	{0xf5b3, 0x91ad}, //醭
	{0xf5b4, 0x91ae}, //醮
	{0xf5b5, 0x91af}, //醯
	{0xf5b6, 0x91b5}, //醵
	{0xf5b7, 0x91b4}, //醴
	{0xf5b8, 0x91ba}, //醺
	{0xf5b9, 0x8c55}, //豕
	{0xf5ba, 0x9e7e}, //鹾
	{0xf5bb, 0x8db8}, //趸
	{0xf5bc, 0x8deb}, //跫
	{0xf5bd, 0x8e05}, //踅
	{0xf5be, 0x8e59}, //蹙
	{0xf5bf, 0x8e69}, //蹩
	{0xf5c0, 0x8db5}, //趵
	{0xf5c1, 0x8dbf}, //趿
	{0xf5c2, 0x8dbc}, //趼
	{0xf5c3, 0x8dba}, //趺
	{0xf5c4, 0x8dc4}, //跄
	{0xf5c5, 0x8dd6}, //跖
	{0xf5c6, 0x8dd7}, //跗
	{0xf5c7, 0x8dda}, //跚
	{0xf5c8, 0x8dde}, //跞
	{0xf5c9, 0x8dce}, //跎
	{0xf5ca, 0x8dcf}, //跏
	{0xf5cb, 0x8ddb}, //跛
	{0xf5cc, 0x8dc6}, //跆
	{0xf5cd, 0x8dec}, //跬
	{0xf5ce, 0x8df7}, //跷
	{0xf5cf, 0x8df8}, //跸
	{0xf5d0, 0x8de3}, //跣
	{0xf5d1, 0x8df9}, //跹
	{0xf5d2, 0x8dfb}, //跻
	{0xf5d3, 0x8de4}, //跤
	{0xf5d4, 0x8e09}, //踉
	{0xf5d5, 0x8dfd}, //跽
	{0xf5d6, 0x8e14}, //踔
	{0xf5d7, 0x8e1d}, //踝
	{0xf5d8, 0x8e1f}, //踟
	{0xf5d9, 0x8e2c}, //踬
	{0xf5da, 0x8e2e}, //踮
	{0xf5db, 0x8e23}, //踣
	{0xf5dc, 0x8e2f}, //踯
	{0xf5dd, 0x8e3a}, //踺
	{0xf5de, 0x8e40}, //蹀
	{0xf5df, 0x8e39}, //踹
	{0xf5e0, 0x8e35}, //踵
	{0xf5e1, 0x8e3d}, //踽
	{0xf5e2, 0x8e31}, //踱
	{0xf5e3, 0x8e49}, //蹉
	{0xf5e4, 0x8e41}, //蹁
	{0xf5e5, 0x8e42}, //蹂
	{0xf5e6, 0x8e51}, //蹑
	{0xf5e7, 0x8e52}, //蹒
	{0xf5e8, 0x8e4a}, //蹊
	{0xf5e9, 0x8e70}, //蹰
	{0xf5ea, 0x8e76}, //蹶
	{0xf5eb, 0x8e7c}, //蹼
	{0xf5ec, 0x8e6f}, //蹯
	{0xf5ed, 0x8e74}, //蹴
	{0xf5ee, 0x8e85}, //躅
	{0xf5ef, 0x8e8f}, //躏
	{0xf5f0, 0x8e94}, //躔
	{0xf5f1, 0x8e90}, //躐
	{0xf5f2, 0x8e9c}, //躜
	{0xf5f3, 0x8e9e}, //躞
	{0xf5f4, 0x8c78}, //豸
	{0xf5f5, 0x8c82}, //貂
	{0xf5f6, 0x8c8a}, //貊
	{0xf5f7, 0x8c85}, //貅
	{0xf5f8, 0x8c98}, //貘
	{0xf5f9, 0x8c94}, //貔
	{0xf5fa, 0x659b}, //斛
	{0xf5fb, 0x89d6}, //觖
	{0xf5fc, 0x89de}, //觞
	{0xf5fd, 0x89da}, //觚
	{0xf5fe, 0x89dc}, //觜
	{0xf6a1, 0x89e5}, //觥
	{0xf6a2, 0x89eb}, //觫
	{0xf6a3, 0x89ef}, //觯
	{0xf6a4, 0x8a3e}, //訾
	{0xf6a5, 0x8b26}, //謦
	{0xf6a6, 0x9753}, //靓
	{0xf6a7, 0x96e9}, //雩
	{0xf6a8, 0x96f3}, //雳
	{0xf6a9, 0x96ef}, //雯
	{0xf6aa, 0x9706}, //霆
	{0xf6ab, 0x9701}, //霁
	{0xf6ac, 0x9708}, //霈
	{0xf6ad, 0x970f}, //霏
	{0xf6ae, 0x970e}, //霎
	{0xf6af, 0x972a}, //霪
	{0xf6b0, 0x972d}, //霭
	{0xf6b1, 0x9730}, //霰
	{0xf6b2, 0x973e}, //霾
	{0xf6b3, 0x9f80}, //龀
	{0xf6b4, 0x9f83}, //龃
	{0xf6b5, 0x9f85}, //龅
	{0xf6b6, 0x9f86}, //龆
	{0xf6b7, 0x9f87}, //龇
	{0xf6b8, 0x9f88}, //龈
	{0xf6b9, 0x9f89}, //龉
	{0xf6ba, 0x9f8a}, //龊
	{0xf6bb, 0x9f8c}, //龌
	{0xf6bc, 0x9efe}, //黾
	{0xf6bd, 0x9f0b}, //鼋
	{0xf6be, 0x9f0d}, //鼍
	{0xf6bf, 0x96b9}, //隹
	{0xf6c0, 0x96bc}, //隼
	{0xf6c1, 0x96bd}, //隽
	{0xf6c2, 0x96ce}, //雎
	{0xf6c3, 0x96d2}, //雒
	{0xf6c4, 0x77bf}, //瞿
	{0xf6c5, 0x96e0}, //雠
	{0xf6c6, 0x928e}, //銎
	{0xf6c7, 0x92ae}, //銮
	{0xf6c8, 0x92c8}, //鋈
	{0xf6c9, 0x933e}, //錾
	{0xf6ca, 0x936a}, //鍪
	{0xf6cb, 0x93ca}, //鏊
	{0xf6cc, 0x938f}, //鎏
	{0xf6cd, 0x943e}, //鐾
	{0xf6ce, 0x946b}, //鑫
	{0xf6cf, 0x9c7f}, //鱿
	{0xf6d0, 0x9c82}, //鲂
	{0xf6d1, 0x9c85}, //鲅
	{0xf6d2, 0x9c86}, //鲆
	{0xf6d3, 0x9c87}, //鲇
	{0xf6d4, 0x9c88}, //鲈
	{0xf6d5, 0x7a23}, //稣
	{0xf6d6, 0x9c8b}, //鲋
	{0xf6d7, 0x9c8e}, //鲎
	{0xf6d8, 0x9c90}, //鲐
	{0xf6d9, 0x9c91}, //鲑
	{0xf6da, 0x9c92}, //鲒
	{0xf6db, 0x9c94}, //鲔
	{0xf6dc, 0x9c95}, //鲕
	{0xf6dd, 0x9c9a}, //鲚
	{0xf6de, 0x9c9b}, //鲛
	{0xf6df, 0x9c9e}, //鲞
	{0xf6e0, 0x9c9f}, //鲟
	{0xf6e1, 0x9ca0}, //鲠
	{0xf6e2, 0x9ca1}, //鲡
	{0xf6e3, 0x9ca2}, //鲢
	{0xf6e4, 0x9ca3}, //鲣
	{0xf6e5, 0x9ca5}, //鲥
	{0xf6e6, 0x9ca6}, //鲦
	{0xf6e7, 0x9ca7}, //鲧
	{0xf6e8, 0x9ca8}, //鲨
	{0xf6e9, 0x9ca9}, //鲩
	{0xf6ea, 0x9cab}, //鲫
	{0xf6eb, 0x9cad}, //鲭
	{0xf6ec, 0x9cae}, //鲮
	{0xf6ed, 0x9cb0}, //鲰
	{0xf6ee, 0x9cb1}, //鲱
	{0xf6ef, 0x9cb2}, //鲲
	{0xf6f0, 0x9cb3}, //鲳
	{0xf6f1, 0x9cb4}, //鲴
	{0xf6f2, 0x9cb5}, //鲵
	{0xf6f3, 0x9cb6}, //鲶
	{0xf6f4, 0x9cb7}, //鲷
	{0xf6f5, 0x9cba}, //鲺
	{0xf6f6, 0x9cbb}, //鲻
	{0xf6f7, 0x9cbc}, //鲼
	{0xf6f8, 0x9cbd}, //鲽
	{0xf6f9, 0x9cc4}, //鳄
	{0xf6fa, 0x9cc5}, //鳅
	{0xf6fb, 0x9cc6}, //鳆
	{0xf6fc, 0x9cc7}, //鳇
	{0xf6fd, 0x9cca}, //鳊
	{0xf6fe, 0x9ccb}, //鳋
	{0xf7a1, 0x9ccc}, //鳌
	{0xf7a2, 0x9ccd}, //鳍
	{0xf7a3, 0x9cce}, //鳎
	{0xf7a4, 0x9ccf}, //鳏
	{0xf7a5, 0x9cd0}, //鳐
	{0xf7a6, 0x9cd3}, //鳓
	{0xf7a7, 0x9cd4}, //鳔
	{0xf7a8, 0x9cd5}, //鳕
	{0xf7a9, 0x9cd7}, //鳗
	{0xf7aa, 0x9cd8}, //鳘
	{0xf7ab, 0x9cd9}, //鳙
	{0xf7ac, 0x9cdc}, //鳜
	{0xf7ad, 0x9cdd}, //鳝
	{0xf7ae, 0x9cdf}, //鳟
	{0xf7af, 0x9ce2}, //鳢
	{0xf7b0, 0x977c}, //靼
	{0xf7b1, 0x9785}, //鞅
	{0xf7b2, 0x9791}, //鞑
	{0xf7b3, 0x9792}, //鞒
	{0xf7b4, 0x9794}, //鞔
	{0xf7b5, 0x97af}, //鞯
	{0xf7b6, 0x97ab}, //鞫
	{0xf7b7, 0x97a3}, //鞣
	{0xf7b8, 0x97b2}, //鞲
	{0xf7b9, 0x97b4}, //鞴
	{0xf7ba, 0x9ab1}, //骱
	{0xf7bb, 0x9ab0}, //骰
	{0xf7bc, 0x9ab7}, //骷
	{0xf7bd, 0x9e58}, //鹘
	{0xf7be, 0x9ab6}, //骶
	{0xf7bf, 0x9aba}, //骺
	{0xf7c0, 0x9abc}, //骼
	{0xf7c1, 0x9ac1}, //髁
	{0xf7c2, 0x9ac0}, //髀
	{0xf7c3, 0x9ac5}, //髅
	{0xf7c4, 0x9ac2}, //髂
	{0xf7c5, 0x9acb}, //髋
	{0xf7c6, 0x9acc}, //髌
	{0xf7c7, 0x9ad1}, //髑
	{0xf7c8, 0x9b45}, //魅
	{0xf7c9, 0x9b43}, //魃
	{0xf7ca, 0x9b47}, //魇
	{0xf7cb, 0x9b49}, //魉
	{0xf7cc, 0x9b48}, //魈
	{0xf7cd, 0x9b4d}, //魍
	{0xf7ce, 0x9b51}, //魑
	{0xf7cf, 0x98e8}, //飨
	{0xf7d0, 0x990d}, //餍
	{0xf7d1, 0x992e}, //餮
	{0xf7d2, 0x9955}, //饕
	{0xf7d3, 0x9954}, //饔
	{0xf7d4, 0x9adf}, //髟
	{0xf7d5, 0x9ae1}, //髡
	{0xf7d6, 0x9ae6}, //髦
	{0xf7d7, 0x9aef}, //髯
	{0xf7d8, 0x9aeb}, //髫
	{0xf7d9, 0x9afb}, //髻
	{0xf7da, 0x9aed}, //髭
	{0xf7db, 0x9af9}, //髹
	{0xf7dc, 0x9b08}, //鬈
	{0xf7dd, 0x9b0f}, //鬏
	{0xf7de, 0x9b13}, //鬓
	{0xf7df, 0x9b1f}, //鬟
	{0xf7e0, 0x9b23}, //鬣
	{0xf7e1, 0x9ebd}, //麽
	{0xf7e2, 0x9ebe}, //麾
	{0xf7e3, 0x7e3b}, //縻
	{0xf7e4, 0x9e82}, //麂
	{0xf7e5, 0x9e87}, //麇
	{0xf7e6, 0x9e88}, //麈
	{0xf7e7, 0x9e8b}, //麋
	{0xf7e8, 0x9e92}, //麒
	{0xf7e9, 0x93d6}, //鏖
	{0xf7ea, 0x9e9d}, //麝
	{0xf7eb, 0x9e9f}, //麟
	{0xf7ec, 0x9edb}, //黛
	{0xf7ed, 0x9edc}, //黜
	{0xf7ee, 0x9edd}, //黝
	{0xf7ef, 0x9ee0}, //黠
	{0xf7f0, 0x9edf}, //黟
	{0xf7f1, 0x9ee2}, //黢
	{0xf7f2, 0x9ee9}, //黩
	{0xf7f3, 0x9ee7}, //黧
	{0xf7f4, 0x9ee5}, //黥
	{0xf7f5, 0x9eea}, //黪
	{0xf7f6, 0x9eef}, //黯
	{0xf7f7, 0x9f22}, //鼢
	{0xf7f8, 0x9f2c}, //鼬
	{0xf7f9, 0x9f2f}, //鼯
	{0xf7fa, 0x9f39}, //鼹
	{0xf7fb, 0x9f37}, //鼷
	{0xf7fc, 0x9f3d}, //鼽
	{0xf7fd, 0x9f3e}, //鼾
	{0xf7fe, 0x9f44}, //齄
};


/* BASE 64 encode table */
static const char base64en[] = {
	'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
	'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
	'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
	'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
	'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
	'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
	'w', 'x', 'y', 'z', '0', '1', '2', '3',
	'4', '5', '6', '7', '8', '9', '+', '/',
};

/* ASCII order for BASE 64 decode, 255 in unused character */
static const unsigned char base64de[] = {
	/* nul, soh, stx, etx, eot, enq, ack, bel, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/*  bs,  ht,  nl,  vt,  np,  cr,  so,  si, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/* dle, dc1, dc2, dc3, dc4, nak, syn, etb, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/* can,  em, sub, esc,  fs,  gs,  rs,  us, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/*  sp, '!', '"', '#', '$', '%', '&', ''', */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/* '(', ')', '*', '+', ',', '-', '.', '/', */
	   255, 255, 255,  62, 255, 255, 255,  63,

	/* '0', '1', '2', '3', '4', '5', '6', '7', */
	    52,  53,  54,  55,  56,  57,  58,  59,

	/* '8', '9', ':', ';', '<', '=', '>', '?', */
	    60,  61, 255, 255, 255, 255, 255, 255,

	/* '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', */
	   255,   0,   1,  2,   3,   4,   5,    6,

	/* 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', */
	     7,   8,   9,  10,  11,  12,  13,  14,

	/* 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', */
	    15,  16,  17,  18,  19,  20,  21,  22,

	/* 'X', 'Y', 'Z', '[', '\', ']', '^', '_', */
	    23,  24,  25, 255, 255, 255, 255, 255,

	/* '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', */
	   255,  26,  27,  28,  29,  30,  31,  32,

	/* 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', */
	    33,  34,  35,  36,  37,  38,  39,  40,

	/* 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', */
	    41,  42,  43,  44,  45,  46,  47,  48,

	/* 'x', 'y', 'z', '{', '|', '}', '~', del, */
	    49,  50,  51, 255, 255, 255, 255, 255
};

unsigned int
base64_encode(const unsigned char *in, unsigned int inlen, char *out)
{
	int s;
	unsigned int i;
	unsigned int j;
	unsigned char c;
	unsigned char l;

	s = 0;
	l = 0;
	for (i = j = 0; i < inlen; i++) {
		c = in[i];

		switch (s) {
		case 0:
			s = 1;
			out[j++] = base64en[(c >> 2) & 0x3F];
			break;
		case 1:
			s = 2;
			out[j++] = base64en[((l & 0x3) << 4) | ((c >> 4) & 0xF)];
			break;
		case 2:
			s = 0;
			out[j++] = base64en[((l & 0xF) << 2) | ((c >> 6) & 0x3)];
			out[j++] = base64en[c & 0x3F];
			break;
		}
		l = c;
	}

	switch (s) {
	case 1:
		out[j++] = base64en[(l & 0x3) << 4];
		out[j++] = BASE64_PAD;
		out[j++] = BASE64_PAD;
		break;
	case 2:
		out[j++] = base64en[(l & 0xF) << 2];
		out[j++] = BASE64_PAD;
		break;
	}

	out[j] = 0;

	return j;
}




unsigned int
base64_decode(const char *in, unsigned int inlen, unsigned char *out)
{
	unsigned int i;
	unsigned int j;
	unsigned char c;

	if (inlen & 0x3) {
		return 0;
	}

	for (i = j = 0; i < inlen; i++) {
		if (in[i] == BASE64_PAD) {
			break;
		}
		if (in[i] < BASE64DE_FIRST || in[i] > BASE64DE_LAST) {
			return 0;
		}

		c = base64de[(unsigned char)in[i]];
		if (c == 255) {
			return 0;
		}

		switch (i & 0x3) {
		case 0:
			out[j] = (c << 2) & 0xFF;
			break;
		case 1:
			out[j++] |= (c >> 4) & 0x3;
			out[j] = (c & 0xF) << 4;
			break;
		case 2:
			out[j++] |= (c >> 2) & 0xF;
			out[j] = (c & 0x3) << 6;
			break;
		case 3:
			out[j++] |= c;
			break;
		}
	}

	return j;
}

unsigned int
base64_decode_strip_newline(const char *in, unsigned int inlen, unsigned char *out)
{
    unsigned int r = 0;
	unsigned int i;
	unsigned int j;
	unsigned char c;

	for (i = j = 0; i < inlen; i++) {
		if (in[i] == BASE64_PAD) {
			break;
		}

        if(in[i] == '\\' && i + 1 != inlen && in[i+1] == 'n')
        {
            i += 1;
            continue;
        }


		c = base64de[(unsigned char)in[i]];
		if (c == 255) {
			return j;
		}

		switch ((r++) & 0x3) {
		case 0:
			out[j] = (c << 2) & 0xFF;
			break;
		case 1:
			out[j++] |= (c >> 4) & 0x3;
			out[j] = (c & 0xF) << 4;
			break;
		case 2:
			out[j++] |= (c >> 2) & 0xF;
			out[j] = (c & 0x3) << 6;
			break;
		case 3:
			out[j++] |= c;
			break;
		}
	}

	return j;
}

unsigned int
base64_decode_strip(const char *in, unsigned int inlen, unsigned char *out)
{
    unsigned int r = 0;
	unsigned int i;
	unsigned int j;
	unsigned char c;

	for (i = j = 0; i < inlen; i++) {
		if (in[i] == BASE64_PAD) {
			break;
		}

        if(in[i] == 0x0D && i + 1 != inlen && in[i+1] == 0x0A)
        {
            i += 1;
            continue;
        }


		c = base64de[(unsigned char)in[i]];
		if (c == 255) {
			return j;
		}

		switch ((r++) & 0x3) {
		case 0:
			out[j] = (c << 2) & 0xFF;
			break;
		case 1:
			out[j++] |= (c >> 4) & 0x3;
			out[j] = (c & 0xF) << 4;
			break;
		case 2:
			out[j++] |= (c >> 2) & 0xF;
			out[j] = (c & 0x3) << 6;
			break;
		case 3:
			out[j++] |= c;
			break;
		}
	}

	return j;
}


static unsigned short gbk_to_ucs2(unsigned short gbk)
{

    int low = 0, high = sizeof(gbkUcs2Tab)/4 - 1;
    while(low <= high){
        int mid = (high - low) / 2 + low;
        int num = gbkUcs2Tab[mid][0];
        if (num == gbk) {
            return gbkUcs2Tab[mid][1];
        } else if (num > gbk) {
            high = mid - 1;
        } else {
            low = mid + 1;
        }
    }
    return 0;
};

static int enc_ucs2_to_utf8_one(unsigned short unic, unsigned char *pOutput)
{
    if ( unic <= 0x007F )
    {
        // * U-00000000 - U-0000007F:  0xxxxxxx
        *pOutput     = (unic & 0x7F);
        return 1;
    }
    else if ( unic >= 0x0080 && unic <= 0x07FF )
    {
        // * U-00000080 - U-000007FF:  110xxxxx 10xxxxxx
        *(pOutput+1) = (unic & 0x3F) | 0x80;
        *pOutput     = ((unic >> 6) & 0x1F) | 0xC0;
        return 2;
    }
    else if ( unic >= 0x0800  )
    {
        // * U-00000800 - U-0000FFFF:  1110xxxx 10xxxxxx 10xxxxxx
        *(pOutput+2) = (unic & 0x3F) | 0x80;
        *(pOutput+1) = ((unic >>  6) & 0x3F) | 0x80;
        *pOutput     = ((unic >> 12) & 0x0F) | 0xE0;
        return 3;
    }

    return 0;
}

int gbk_convert_to_utf8(unsigned char* gbk, unsigned char* utf8, int utf8_size)
{
    int i, gbk_len, gbk_val, ucs2_val, utf8_len, ret;
    unsigned char tmp[4];

    gbk_len = strlen((char *)gbk);
    utf8_len = 0;

    for(i = 0; i < gbk_len; i++)
    {
        ret = 0;
        if(gbk[i] <= 0xA0) {
            tmp[0] = gbk[i];
            ret = 1;
        }
        else if ((i+1 < gbk_len) && gbk[i+1] > 0xA0)
        {
            gbk_val = (gbk[i] << 8) + gbk[i+1];
            i++;
            ucs2_val = gbk_to_ucs2(gbk_val);
            ret = enc_ucs2_to_utf8_one(ucs2_val, tmp);
        }
        else
        {
            //only one continuous byte biger than 0xA0, Discard the character
            continue;
        }
        if(utf8_len + ret > utf8_size) {
            return -2;
        }
        memcpy(utf8+utf8_len, tmp, ret);
        utf8_len += ret;
    }
    return utf8_len;
}


int utf8_to_gbk(char *inbuf, int inlen, char *outbuf, int outlen)
{
    iconv_t cd;

//    char local_in[256]={0};
//    char local_out[256]={0};
    char *ptr_in  = inbuf;
    char *ptr_out = outbuf;

    size_t local_inlen=inlen;
    size_t local_outlen=outlen;

    //cd = iconv_open("gbk//IGNORE", "utf-8"); //gb2312
    cd = iconv_open("gbk", "utf-8"); //gb2312
    if (0 == cd) {
        printf("iconv open failed!\n");
        return -1;
    }
    
    if(-1==(int )iconv(cd, (char **)&ptr_in, (size_t *)&local_inlen, 
                           (char **)&ptr_out, (size_t *)&local_outlen))
    {
        printf("iconv failed !, errno %d\n",errno);
        snprintf((char *)outbuf, (unsigned int )outlen, "%s", inbuf);
        iconv_close(cd);
        return -1;
    }

    iconv_close(cd);
    return 0;
}




/**
 * dpdk接口封装文件, 不同版本的dpdk接口差异在此解决
 */ 
#ifndef DPI_DPDK_WRAPPER_H_
#define DPI_DPDK_WRAPPER_H_

#include <stdio.h>

#include <rte_version.h>
#include <rte_mbuf.h>
#include <rte_mbuf_dyn.h>
#include <rte_eal.h>
#include <rte_lcore.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <rte_ip.h>
#include <rte_tcp.h>
#include <rte_udp.h>
#include <rte_net.h>
#include <rte_malloc.h>
#include <rte_config.h>
#include <rte_ring.h>

#if RTE_VERSION > RTE_VERSION_NUM(19, 0xff, 0xff, 0xff)

#else // RTE_VERSION <= 19

#define RTE_ETH_MQ_TX_NONE      ETH_MQ_TX_NONE
#define RTE_ETH_MQ_RX_RSS       ETH_MQ_RX_RSS

#endif // RTE_VERSION


#ifndef RTE_ETH_SPEED_NUM_NONE
#define RTE_ETH_SPEED_NUM_NONE  ETH_SPEED_NUM_NONE
#endif


#ifndef RTE_ETH_TX_OFFLOAD_MBUF_FAST_FREE
#define RTE_ETH_TX_OFFLOAD_MBUF_FAST_FREE   DEV_TX_OFFLOAD_MBUF_FAST_FREE
#endif


#ifndef RTE_ETH_RSS_IPV4
#define RTE_ETH_RSS_IPV4    ETH_RSS_IPV4
#define RTE_ETH_RSS_IP      ETH_RSS_IP
#define RTE_ETH_RSS_TCP     ETH_RSS_TCP
#define RTE_ETH_RSS_UDP     ETH_RSS_UDP
#define RTE_ETH_RSS_SCTP    ETH_RSS_SCTP
#endif


#ifndef RTE_LCORE_FOREACH_WORKER
#define RTE_LCORE_FOREACH_WORKER  RTE_LCORE_FOREACH_SLAVE
#endif


#ifndef RTE_MBUF_DYNFIELD_TIMESTAMP_NAME
#define RTE_MBUF_DYNFIELD_TIMESTAMP_NAME        "rte_dynfield_timestamp"
typedef uint64_t rte_mbuf_timestamp_t;
#endif

#ifndef RTE_MBUF_DYNFLAG_RX_TIMESTAMP_NAME
#define RTE_MBUF_DYNFLAG_RX_TIMESTAMP_NAME      "rte_dynflag_rx_timestamp"
#endif

#ifndef RTE_MBUF_DYNFIELD_IP_REASSEMBLY_NAME
#define RTE_MBUF_DYNFIELD_IP_REASSEMBLY_NAME    "rte_dynfield_ip_reassembly"
#endif

#ifndef RTE_MBUF_DYNFLAG_IP_REASSEMBLY_INCOMPLETE_NAME
#define RTE_MBUF_DYNFLAG_IP_REASSEMBLY_INCOMPLETE_NAME  "rte_dynflag_ip_reassembly_incomplete"
#endif

/**
 * 克隆一个mbuf, 引用计数+1
 * 对应的, 调用原生 rte_pktmbuf_free() 释放
 */
static inline struct rte_mbuf*
rte_mbuf_clone_0(struct rte_mbuf *m)
{
    rte_mbuf_refcnt_update(m, 1);
    return m;
}


#endif // DPI_DPDK_WRAPPER_H_

/****************************************************************************************
 * 文 件 名 : dpi_cdp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: xuxn          2019/02/13
编码: xuxn          2019/02/13
修改: zhangsx       2019/10/24
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <glib.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_common.h"
#include "dpi_log.h"
#include "dpi_cdp.h"
#include "dpi_sdt_link.h"
#include "dpi_pschema.h"
extern struct rte_mempool *tbl_log_mempool;

static dpi_field_table  cdp_field_array[] = {
    DPI_FIELD_D(EM_CDP_H_DST,                   EM_F_TYPE_NULL,         "h_dst"),
    DPI_FIELD_D(EM_CDP_H_SRC,                   EM_F_TYPE_NULL,         "h_src"),
    DPI_FIELD_D(EM_CDP_H_PROTO,                 EM_F_TYPE_NULL,         "h_proto"),
    DPI_FIELD_D(EM_CDP_DSAP,                    EM_F_TYPE_NULL,         "dsap"),
    DPI_FIELD_D(EM_CDP_SSAP,                    EM_F_TYPE_NULL,         "ssap"),
    DPI_FIELD_D(EM_CDP_CONTRAL,                 EM_F_TYPE_NULL,         "control"),
    DPI_FIELD_D(EM_CDP_OUI,                     EM_F_TYPE_NULL,         "oui"),
    DPI_FIELD_D(EM_CDP_CISO_PID,                EM_F_TYPE_NULL,         "cisco_pid"),
    DPI_FIELD_D(EM_CDP_VERSION,                 EM_F_TYPE_NULL,         "version"),
    DPI_FIELD_D(EM_CDP_TTL,                     EM_F_TYPE_NULL,         "ttl"),
    DPI_FIELD_D(EM_CDP_CHECKSUM,                EM_F_TYPE_NULL,         "checksum"),
    DPI_FIELD_D(EM_CDP_CHECKSUM_STATUS,         EM_F_TYPE_NULL,         "checksum_status"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE,                EM_F_TYPE_UINT16,         "tlvtype"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH,              EM_F_TYPE_UINT16,         "tlvlength"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_1,              EM_F_TYPE_UINT16,         "tlvtype_1"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_1,            EM_F_TYPE_UINT16,         "tlvlength_1"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_2,              EM_F_TYPE_UINT16,         "tlvtype_2"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_2,            EM_F_TYPE_UINT16,         "tlvlength_2"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_3,              EM_F_TYPE_UINT16,         "tlvtype_3"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_3,            EM_F_TYPE_UINT16,         "tlvlength_3"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_4,              EM_F_TYPE_UINT16,         "tlvtype_4"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_4,            EM_F_TYPE_UINT16,         "tlvlength_4"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_5,              EM_F_TYPE_UINT16,         "tlvtype_5"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_5,            EM_F_TYPE_UINT16,         "tlvlength_5"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_6,              EM_F_TYPE_UINT16,         "tlvtype_6"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_6,            EM_F_TYPE_UINT16,         "tlvlength_6"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_7,              EM_F_TYPE_UINT16,         "tlvtype_7"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_7,            EM_F_TYPE_UINT16,         "tlvlength_7"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_8,              EM_F_TYPE_UINT16,         "tlvtype_8"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_8,            EM_F_TYPE_UINT16,         "tlvlength_8"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_9,              EM_F_TYPE_UINT16,         "tlvtype_9"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_9,            EM_F_TYPE_UINT16,         "tlvlength_9"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_A,              EM_F_TYPE_UINT16,         "tlvtype_a"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_A,            EM_F_TYPE_UINT16,         "tlvlength_a"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_B,              EM_F_TYPE_UINT16,         "tlvtype_b"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_B,            EM_F_TYPE_UINT16,         "tlvlength_b"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_E,              EM_F_TYPE_UINT16,         "tlvtype_e"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_E,            EM_F_TYPE_UINT16,         "tlvlength_e"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_F,              EM_F_TYPE_UINT16,         "tlvtype_f"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_F,            EM_F_TYPE_UINT16,         "tlvlength_f"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_10,             EM_F_TYPE_UINT16,         "tlvtype_10"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_10,           EM_F_TYPE_UINT16,         "tlvlength_10"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_11,             EM_F_TYPE_UINT16,         "tlvtype_11"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_11,           EM_F_TYPE_UINT16,         "tlvlength_11"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_12,             EM_F_TYPE_UINT16,         "tlvtype_12"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_12,           EM_F_TYPE_UINT16,         "tlvlength_12"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_13,             EM_F_TYPE_UINT16,         "tlvtype_13"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_13,           EM_F_TYPE_UINT16,         "tlvlength_13"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_14,             EM_F_TYPE_UINT16,         "tlvtype_14"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_14,           EM_F_TYPE_UINT16,         "tlvlength_14"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_15,             EM_F_TYPE_UINT16,         "tlvtype_15"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_15,           EM_F_TYPE_UINT16,         "tlvlength_15"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_16,             EM_F_TYPE_UINT16,         "tlvtype_16"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_16,           EM_F_TYPE_UINT16,         "tlvlength_16"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_17,             EM_F_TYPE_UINT16,         "tlvtype_17"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_17,           EM_F_TYPE_UINT16,         "tlvlength_17"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_19,             EM_F_TYPE_UINT16,         "tlvtype_19"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_19,           EM_F_TYPE_UINT16,         "tlvlength_19"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_1A,             EM_F_TYPE_NULL,         "tlvtype_1a"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_1A,           EM_F_TYPE_NULL,         "tlvlength_1a"),
    DPI_FIELD_D(EM_CDP_TLV_TYPE_1D,             EM_F_TYPE_NULL,         "tlvtype_1d"),
    DPI_FIELD_D(EM_CDP_TLV_LENGTH_1D,           EM_F_TYPE_NULL,         "tlvlength_1d"),
    DPI_FIELD_D(EM_CDP_DEVICE_ID,               EM_F_TYPE_NULL,         "deviceid"),
    DPI_FIELD_D(EM_CDP_PORT_ID,                 EM_F_TYPE_NULL,         "portid"),
    DPI_FIELD_D(EM_CDP_N_ADDRESS_2,             EM_F_TYPE_NULL,         "naddresses_2"),
    DPI_FIELD_D(EM_CDP_N_ADDRESS_16,            EM_F_TYPE_NULL,         "naddresses_16"),
    DPI_FIELD_D(EM_CDP_PROTO_TYPE,              EM_F_TYPE_NULL,         "proto_type"),
    DPI_FIELD_D(EM_CDP_PROTO_LEN,               EM_F_TYPE_NULL,         "proto_len"),
    DPI_FIELD_D(EM_CDP_PROTOCOL,                EM_F_TYPE_NULL,         "protocol"),
    DPI_FIELD_D(EM_CDP_ADDRESS_LEN,             EM_F_TYPE_NULL,         "address_length"),
    DPI_FIELD_D(EM_CDP_ADDRESS,                 EM_F_TYPE_NULL,         "address"),
    DPI_FIELD_D(EM_CDP_NRGYZ_IP_ADDRESS,        EM_F_TYPE_NULL,         "nrgyz_ip_address"),
    DPI_FIELD_D(EM_CDP_CAPABILITY,              EM_F_TYPE_NULL,         "capabilities"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_ROUTER,       EM_F_TYPE_NULL,         "capabilities_router"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_TRANS,        EM_F_TYPE_NULL,         "capabilities_trans_bridge"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_SRC,          EM_F_TYPE_NULL,         "capabilities_src_bridge"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_SWITCH,       EM_F_TYPE_NULL,         "capabilities_switch"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_HOST,         EM_F_TYPE_NULL,         "capabilities_host"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_IGMP,         EM_F_TYPE_NULL,         "capabilities_igmp_capable"),
    DPI_FIELD_D(EM_CDP_CAPABILITY_REPEATER,     EM_F_TYPE_NULL,         "capabilities_repeater"),
    DPI_FIELD_D(EM_CDP_SOFTWARE_V,              EM_F_TYPE_NULL,         "software_version"),
    DPI_FIELD_D(EM_CDP_PLATFORM,                EM_F_TYPE_NULL,         "platform"),
    DPI_FIELD_D(EM_CDP_ODR_DEFAULT_GATEWAY,     EM_F_TYPE_NULL,         "odr_default_gateway"),
    DPI_FIELD_D(EM_CDP_OUI_8,                   YA_FT_STRING,         "oui_8"),
    DPI_FIELD_D(EM_CDP_PROTO_ID,                EM_F_TYPE_NULL,         "proto_id"),
    DPI_FIELD_D(EM_CDP_CLUSTER_MASTER_IP,       EM_F_TYPE_NULL,         "cluster_master_ip"),
    DPI_FIELD_D(EM_CDP_CLUSTER_IP,              EM_F_TYPE_NULL,         "cluster_ip"),
    DPI_FIELD_D(EM_CDP_CLUSTER_VERSION,         EM_F_TYPE_NULL,         "cluster_version"),
    DPI_FIELD_D(EM_CDP_CLUSTER_SUB_V,           EM_F_TYPE_NULL,         "cluster_sub_version"),
    DPI_FIELD_D(EM_CDP_CLUSTER_STATUS,          EM_F_TYPE_NULL,         "cluster_status"),
    DPI_FIELD_D(EM_CDP_CLUSTER_UNKNOW1,         EM_F_TYPE_NULL,         "cluster_unknown1"),
    DPI_FIELD_D(EM_CDP_CLUSTER_COMMAND_MAC,     EM_F_TYPE_NULL,         "cluster_commander_mac"),
    DPI_FIELD_D(EM_CDP_CLUSTER_SWITCH_MAC,      EM_F_TYPE_NULL,         "cluster_switch_mac"),
    DPI_FIELD_D(EM_CDP_CLUSTER_UNKNOW2,         EM_F_TYPE_NULL,         "cluster_unknown2"),
    DPI_FIELD_D(EM_CDP_CLUSTER_MANAGEMENT_VLAN, EM_F_TYPE_NULL,         "cluster_management_vlan"),
    DPI_FIELD_D(EM_CDP_HELLO_UNKNOE,            EM_F_TYPE_NULL,         "hello_unknown"),
    DPI_FIELD_D(EM_CDP_VTP_M_DOMAIN,            EM_F_TYPE_NULL,         "vtp_management_domain"),
    DPI_FIELD_D(EM_CDP_NATIVE_VLAN,             EM_F_TYPE_NULL,         "native_vlan"),
    DPI_FIELD_D(EM_CDP_DUPLEX,                  EM_F_TYPE_NULL,         "duplex"),
    DPI_FIELD_D(EM_CDP_DATA_E,                  EM_F_TYPE_NULL,         "data_e"),
    DPI_FIELD_D(EM_CDP_VOICE_VLAN_E,            EM_F_TYPE_NULL,         "voice_vlan_e"),
    DPI_FIELD_D(EM_CDP_DATA_F,                  EM_F_TYPE_NULL,         "data_f"),
    DPI_FIELD_D(EM_CDP_VOICE_VLAN_F,            EM_F_TYPE_NULL,         "voice_vlan_f"),
    DPI_FIELD_D(EM_CDP_POWER_CONSUMPTION,       EM_F_TYPE_NULL,         "power_consumption"),
    DPI_FIELD_D(EM_CDP_MTU,                     EM_F_TYPE_NULL,         "mtu"),
    DPI_FIELD_D(EM_CDP_TRUST_BITMAP,            EM_F_TYPE_NULL,         "trust_bitmap"),
    DPI_FIELD_D(EM_CDP_UNTRUST_PORT_COS,        EM_F_TYPE_NULL,         "untrusted_port_cos"),
    DPI_FIELD_D(EM_CDP_SYSTEM_NAME,             EM_F_TYPE_NULL,         "system_name"),
    DPI_FIELD_D(EM_CDP_SYSTEM_OBJ_IDENT,        EM_F_TYPE_NULL,         "system_object_identifier"),
    DPI_FIELD_D(EM_CDP_LOCATION_UNKNOW,         EM_F_TYPE_NULL,         "location_unknown"),
    DPI_FIELD_D(EM_CDP_LOCATION,                EM_F_TYPE_NULL,         "location"),
    DPI_FIELD_D(EM_CDP_REQ_ID19,                EM_F_TYPE_NULL,         "request_id_19"),
    DPI_FIELD_D(EM_CDP_MANAGE_ID_19,            EM_F_TYPE_NULL,         "management_id_19"),
    DPI_FIELD_D(EM_CDP_POWER_REQ,               EM_F_TYPE_NULL,         "power_requested"),
    DPI_FIELD_D(EM_CDP_REQ_ID_1A,               EM_F_TYPE_NULL,         "request_id_1a"),
    DPI_FIELD_D(EM_CDP_MANAGE_ID_1A,            EM_F_TYPE_NULL,         "management_id_1a"),
    DPI_FIELD_D(EM_CDP_POWER_AVAIL,             EM_F_TYPE_NULL,         "power_available"),
    DPI_FIELD_D(EM_CDP_ENCRYPT_DATA,            EM_F_TYPE_NULL,         "encrypted_data"),
    DPI_FIELD_D(EM_CDP_SEEN_SEQUENCE,           EM_F_TYPE_NULL,         "seen_sequence"),
    DPI_FIELD_D(EM_CDP_SEQUENCE_NUM,            EM_F_TYPE_NULL,         "sequence_number"),
    DPI_FIELD_D(EM_CDP_MODEL_NUM,               EM_F_TYPE_NULL,         "model_number"),
    DPI_FIELD_D(EM_CDP_UNKNOW_PAD,              EM_F_TYPE_NULL,         "unknown_pad"),
    DPI_FIELD_D(EM_CDP_HARDWARE_V_ID,           EM_F_TYPE_NULL,         "hardware_version_id"),
    DPI_FIELD_D(EM_CDP_SYSTEM_SERIAL_NUM,       EM_F_TYPE_NULL,         "system_serial_number"),
    DPI_FIELD_D(EM_CDP_NRGYZ_UNKNOW_VAL,        EM_F_TYPE_NULL,         "nrgyz_unknown_values"),
    DPI_FIELD_D(EM_CDP_LEN_TLV_TABLE,           EM_F_TYPE_NULL,         "len_tlv_table"),
    DPI_FIELD_D(EM_CDP_NUM_TLVS_TABLE,          EM_F_TYPE_NULL,         "num_tlvs_table"),

};

void write_cdp_log(CDPINFO* info);

static int compute_offset(struct dpi_pkt_st* pkt, const int offset, uint32_t *offset_ptr)
{
    if (offset >= 0)
    {
        if ((uint32_t) offset <= pkt->payload_len)
            *offset_ptr = offset;
        else
            return 1;
    }
    else
    {
        if ((guint) -offset <= pkt->payload_len)
            *offset_ptr = pkt->payload_len + offset;
        else
            return 2;
    }
    return 0;
}

static uint32_t reported_length_remaining(struct dpi_pkt_st* pkt, const gint offset)
{
    guint abs_offset;
    int   exception;

    exception = compute_offset(pkt, offset, &abs_offset);
    if (exception)
        return 0;
#if 0
    if (tvb->reported_length >= abs_offset)
        return tvb->reported_length - abs_offset;
#endif
    if(pkt->payload_len >= abs_offset)
        return pkt->payload_len - abs_offset;
    else
        return 0;
}

static void dissect_capabilities(CDPINFO* cdp, const uint8_t* payload, uint32_t offset, int length)
{
    if (length < 4)
        return;
    cdp->capabilities = get_uint32_t(payload, offset);
    cdp->capabilities_router = get_uint32_t(payload, offset);
    cdp->capabilities_trans_bridge = get_uint32_t(payload, offset);
    cdp->capabilities_src_bridge = get_uint32_t(payload, offset);
    cdp->capabilities_switch = get_uint32_t(payload, offset);
    cdp->capabilities_host = get_uint32_t(payload, offset);
    cdp->capabilities_igmp_capable = get_uint32_t(payload, offset);
    cdp->capabilities_repeater = get_uint32_t(payload, offset);
}

static void address_malloc(CDPINFO* cdp, int n)
{
    if(0 < n)
    {
        cdp->proto_type            =        (uint8_t *)malloc(n);
        cdp->proto_len            =        (uint8_t *)malloc(n);
        cdp->protocol            =        (uint8_t *)malloc(n);
        cdp->address_length        =        (uint16_t *)malloc(sizeof(uint16_t)*n);
        cdp->address            =        (uint16_t *)malloc(sizeof(uint16_t)*n);
        cdp->nrgyz_ip_address    =        (uint32_t *)malloc(sizeof(uint32_t)*n);
        memset(cdp->proto_type, 0, n);
        memset(cdp->proto_len, 0, n);
        memset(cdp->protocol, 0, n);
        memset(cdp->address_length, 0, sizeof(uint16_t)*n);
        memset(cdp->address, 0, sizeof(uint16_t)*n);
        memset(cdp->nrgyz_ip_address, 0, sizeof(uint32_t)*n);
    }
}

static int dissect_address_tlv(CDPINFO* cdp, const char* payload, int offset, int length, int n)
{
    int nlpid = 0;
    int hf_addr = 0;
    uint16_t etypeid = 0;
    if (length < 1 || n < 0)
        return -1;
    cdp->proto_type[n] = payload[offset++];
    length--;
    cdp->proto_len[n] = payload[offset++];
    length--;
    if(length < cdp->proto_len[n])
    {
        if(0 != length)
            cdp->protocol[n] = payload[offset];
        return -1;
    }

    if(PROTO_TYPE_NLPID == cdp->proto_type[n] && 1 == cdp->proto_len[n])
        nlpid = payload[offset];
    else if(PROTO_TYPE_IEEE_802_2 == cdp->proto_type[n] && 8 == cdp->proto_len[n] && 0xAAAA03000000 == get_uint64_t(payload, offset))
        etypeid = get_uint16_t(payload, offset+6);
    else
    {
        nlpid = -1;
        cdp->protocol[n] = payload[offset];
    }

    offset += cdp->proto_len[n];
    length -= cdp->proto_len[n];
    if(length < 2)
        return -1;
    cdp->address_length[n] = get_uint16_t(payload, offset);
    offset += 2;
    length -= 2;
    if(length < cdp->address_length[n])
    {
        if(0 != length)
            cdp->address[n] = get_uint16_t(payload, offset);
        return -1;
    }
    if(PROTO_TYPE_NLPID == cdp->proto_type[n] && 1 == cdp->proto_len[n])
    {
        if(NLPID_IP == nlpid && 4 == cdp->address_length[n])
        {
            hf_addr = -1;
            cdp->nrgyz_ip_address[n] = get_uint32_t(payload, offset);
        }
    }
    else if(PROTO_TYPE_IEEE_802_2 == cdp->proto_type[n] && 8 == cdp->proto_len[n] && etypeid > 0)
    {
        if(ETHERTYPE_IPv6 == etypeid && 16 == cdp->address_length[n])
        {
            hf_addr = -1;
            cdp->nrgyz_ip_address[n] = get_uint32_t(payload, offset);
        }
    }
    if(-1 == hf_addr)
        cdp->address[n] = get_uint16_t(payload, offset);
    return 2 + cdp->proto_len[n] + 2 + cdp->address_length[n];
}

static void free_buff(CDPINFO* cdp)
{
    if(NULL != cdp->deviceid)
    {
        free(cdp->deviceid);
        cdp->deviceid = NULL;
    }
    if(NULL != cdp->proto_type)
    {
        free(cdp->proto_type);
        cdp->proto_type = NULL;
    }
    if(NULL != cdp->proto_len)
    {
        free(cdp->proto_len);
        cdp->proto_len = NULL;
    }
    if(NULL != cdp->protocol)
    {
        free(cdp->protocol);
        cdp->protocol = NULL;
    }
    if(NULL != cdp->address_length)
    {
        free(cdp->address_length);
        cdp->address_length = NULL;
    }
    if(NULL != cdp->address)
    {
        free(cdp->address);
        cdp->address = NULL;
    }
    if(NULL != cdp->nrgyz_ip_address)
    {
        free(cdp->nrgyz_ip_address);
        cdp->nrgyz_ip_address = NULL;
    }
    if(NULL != cdp->software_version)
    {
        free(cdp->software_version);
        cdp->software_version = NULL;
    }
    if(NULL != cdp->platform)
    {
        free(cdp->platform);
        cdp->platform = NULL;
    }
    if(NULL != cdp->hello_unknown)
    {
        free(cdp->hello_unknown);
        cdp->hello_unknown = NULL;
    }
    if(NULL != cdp->vtp_management_domain)
    {
        free(cdp->vtp_management_domain);
        cdp->vtp_management_domain = NULL;
    }
    if(NULL != cdp->system_name)
    {
        free(cdp->system_name);
        cdp->system_name = NULL;
    }
    if(NULL != cdp->location)
    {
        free(cdp->location);
        cdp->location = NULL;
    }
}

void dissect_cdp(const uint8_t * cdp_payload, uint32_t cdp_len)
{
	if (g_config.protocol_switch[PROTOCOL_CDP] == 0)
		return;

    int type, length, power_len;
    uint32_t cdp_offset = 0;

    CDPINFO info;
    CDPINFO *cdp = &info;
    memset(cdp, 0, sizeof(info));
/*
    //cdp头部
    memcpy(cdp->h_dest, cdp_payload+cdp_offset, 6);
    cdp_offset += 6;
    memcpy(cdp->h_source, cdp_payload+cdp_offset, 6);
    cdp_offset += 6;
    cdp->h_proto = get_uint16_ntohs(cdp_payload, cdp_offset);
    cdp_offset += 2;
    cdp->dsap = cdp_payload[cdp_offset++];
    cdp->ssap = cdp_payload[cdp_offset++];
    cdp->control = cdp_payload[cdp_offset++];
    memcpy(cdp->oui, cdp_payload+cdp_offset, 3);
    cdp_offset += 3;
    cdp->cisco_pid = get_uint16_ntohs(cdp_payload, cdp_offset);
    cdp_offset += 2;
*/

    cdp->version = cdp_payload[cdp_offset++];
    cdp->ttl = cdp_payload[cdp_offset++];
    cdp->checksum = get_uint16_ntohs(cdp_payload, cdp_offset);
    cdp_offset += 2;

    struct dpi_pkt_st pkt;
    pkt.payload = (const uint8_t*)cdp_payload;
    pkt.payload_len = cdp_len;

#if 1
    //由于type的值恒为256,所以此处为死循环,转换字节序试试
    while(cdp_len > cdp_offset)
    {
        type = get_uint16_ntohs(cdp_payload, cdp_offset+TLV_TYPE);
        length = get_uint16_ntohs(cdp_payload, cdp_offset+TLV_LENGTH);
        if(length < 4)
        {
            cdp->tlvtype = type;
            cdp->tlvlength = length;
            break;
        }
        else if(cdp_offset + length > cdp_len){
            break;
        }

        switch(type)
        {
            case TYPE_DEVICE_ID:
                cdp->tlvtype_1 = type;
                cdp->tlvlength_1 = length;
                MALLOC(cdp->deviceid, length-4+1);
                memcpy(cdp->deviceid, cdp_payload+cdp_offset+4, length-4);
                cdp_offset += length;
                break;
            case TYPE_PORT_ID:
                if(cdp_len >= cdp_offset+3 && 0x00 != cdp_payload[cdp_offset + length])
                    length += 3;
                cdp->tlvtype_3 = type;
                cdp->tlvlength_3 = length;
                MALLOC(cdp->portid, length-4+1);
                memcpy(cdp->portid, cdp_payload+cdp_offset+4, length-4);
                cdp_offset += length;
                break;
            case TYPE_ADDRESS:
                cdp->tlvtype_2 = type;
                cdp->tlvlength_2 = length;
                cdp_offset += 4;
                length -= 4;
                cdp->naddresses_2 = get_uint32_ntohl(cdp_payload, cdp_offset);
                cdp_offset += 4;
                length -= 4;
#if 0
                //todo ....待商榷,同：TYPE_MANAGEMENT_ADDR
                address_malloc(cdp, cdp->naddresses_2);
                for(i=0; i < cdp->naddresses_2; i++)
                {
                    int addr_length = dissect_address_tlv(cdp, cdp_payload, cdp_offset, length, i);
                    if (addr_length < 0)
                        break;
                    cdp_offset += addr_length;
                    length -= addr_length;
                }
#endif
                cdp_offset += length;
                break;
            case TYPE_CAPABILITIES:
                cdp->tlvtype_4 = type;
                cdp->tlvlength_4 = length;
                cdp_offset += 4;
                length -= 4;
                dissect_capabilities(cdp, cdp_payload, cdp_offset, length);
                cdp_offset += length;
                break;
            case TYPE_IOS_VERSION:
                cdp->tlvtype_5 = type;
                cdp->tlvlength_5 = length;
                cdp_offset += 4;
                length -= 4;
                MALLOC(cdp->software_version, length+1);
                memcpy(cdp->software_version, cdp_payload+cdp_offset, length);
                cdp_offset += length;
                break;
            case TYPE_PLATFORM:
                cdp->tlvtype_6 = type;
                cdp->tlvlength_6 = length;
                cdp_offset += 4;
                length -= 4;
                MALLOC(cdp->platform, length+1);
                memcpy(cdp->platform, cdp_payload+cdp_offset, length);
                cdp_offset += length;
                break;
            case TYPE_IP_PREFIX:
                cdp->tlvtype_7 = type;
                cdp->tlvlength_7 = length;
                cdp_offset += 4;
                length -= 4;
                if(8 == cdp->tlvlength_7)
                {
                    cdp->odr_default_gateway = get_uint32_t(cdp_payload, cdp_offset);
                    cdp_offset += length;
                }
                else
                {
                    while(length > 0)
                    {
                        cdp_offset += 5;
                        length -= 5;
                    }
                }
                break;
            case TYPE_PROTOCOL_HELLO:
#define OUI_LEN 3
                cdp->tlvtype_8 = type;
                cdp->tlvlength_8 = length;
                memcpy(cdp->oui_8, cdp_payload + cdp_offset + 4, OUI_LEN);
                cdp->proto_id = get_uint16_ntohs(cdp_payload, cdp_offset + 4 + OUI_LEN);
                int tmp_len = 4 + OUI_LEN + 2;
                int index = cdp_offset + tmp_len;
                switch(cdp->proto_id)
                {
                    case TYPE_HELLO_CLUSTER_MGMT:
                        cdp->cluster_master_ip = get_uint32_ntohl(cdp_payload, index);
                        index += 4;
                        cdp->cluster_ip = get_uint32_ntohl(cdp_payload, index);
                        index += 4;
                        cdp->cluster_version = cdp_payload[index++];
                        cdp->cluster_sub_version = cdp_payload[index++];
                        cdp->cluster_status = cdp_payload[index++];
                        cdp->cluster_unknown1 = cdp_payload[index++];
                        memcpy(cdp->cluster_commander_mac, cdp_payload+index, sizeof(cdp->cluster_commander_mac));
                        index += sizeof(cdp->cluster_commander_mac);
                        memcpy(cdp->cluster_switch_mac, cdp_payload+index, sizeof(cdp->cluster_switch_mac));
                        index += sizeof(cdp->cluster_commander_mac);
                        cdp->cluster_unknown2 = cdp_payload[index++];
                        cdp->cluster_management_vlan = get_uint16_ntohs(cdp_payload, index);
                        break;
                    default:
                        MALLOC(cdp->hello_unknown, length - tmp_len + 1);
                        memcpy(cdp->hello_unknown, cdp_payload+index, length - tmp_len);
                        break;
                }
                cdp_offset += length;
                break;
            case TYPE_VTP_MGMT_DOMAIN:
                cdp->tlvtype_9 = type;
                cdp->tlvlength_9 = length;
                MALLOC(cdp->vtp_management_domain, length-4+1);
                memcpy(cdp->vtp_management_domain, cdp_payload+cdp_offset+4, length-4);
                cdp_offset += length;
                break;
            case TYPE_NATIVE_VLAN:
                cdp->tlvtype_a = type;
                cdp->tlvlength_a = length;
                cdp->native_vlan = get_uint16_ntohs(cdp_payload, cdp_offset+4);
                cdp_offset += length;
                break;
            case TYPE_DUPLEX:
                cdp->tlvtype_b = type;
                cdp->tlvlength_b = length;
                cdp->duplex = cdp_payload[cdp_offset+4];
                cdp_offset += length;
                break;
            case TYPE_VOIP_VLAN_REPLY:
                cdp->tlvtype_e = type;
                cdp->tlvlength_e = length;
                if(6 == length)
                    cdp->data_e = get_uint16_ntohs(cdp_payload, cdp_offset+4);
                else
                {
                    cdp->data_e = cdp_payload[cdp_offset+4];
                    cdp->voice_vlan_e = get_uint16_ntohs(cdp_payload, cdp_offset+4+1);
                }
                cdp_offset += length;
                break;
            case TYPE_VOIP_VLAN_QUERY:
                cdp->tlvtype_f = type;
                cdp->tlvlength_f = length;
                if(6 == length)
                    cdp->data_f = get_uint16_ntohs(cdp_payload, cdp_offset+4);
                else
                {
                    cdp->data_f = cdp_payload[cdp_offset+4];
                    cdp->voice_vlan_f = get_uint16_ntohs(cdp_payload, cdp_offset+4+1);
                }
                cdp_offset += length;
                break;
            case TYPE_POWER:
                cdp->tlvtype_10 = type;
                cdp->tlvlength_10 = length;
                cdp->power_consumption = get_uint16_ntohs(cdp_payload, cdp_offset+4);
                cdp_offset += length;
                break;
            case TYPE_MTU:
                cdp->tlvtype_11 = type;
                cdp->tlvlength_11 = length;
                cdp->mtu = get_uint32_ntohl(cdp_payload, cdp_offset+4);
                cdp_offset += length;
                break;
            case TYPE_TRUST_BITMAP:
                cdp->tlvtype_12 = type;
                cdp->tlvlength_12 = length;
                cdp->trust_bitmap = cdp_payload[cdp_offset+4];
                cdp_offset += length;
                break;
            case TYPE_UNTRUSTED_COS:
                cdp->tlvtype_13 = type;
                cdp->tlvlength_13 = length;
                cdp->untrusted_port_cos = cdp_payload[cdp_offset+4];
                cdp_offset += length;
                break;
            case TYPE_SYSTEM_NAME:
                cdp->tlvtype_14 = type;
                cdp->tlvlength_14 = length;
                MALLOC(cdp->system_name, length-4+1);
                memcpy(cdp->system_name, cdp_payload+cdp_offset+4, length-4);
                cdp_offset += length;
                break;
            case TYPE_SYSTEM_OID:
                cdp->tlvtype_15 = type;
                cdp->tlvlength_15 = length;
                MALLOC(cdp->system_object_identifier, length-4+1);
                memcpy(cdp->system_object_identifier, cdp_payload+cdp_offset+4, length-4);
                cdp_offset += length;
                break;
            case TYPE_MANAGEMENT_ADDR:
                cdp->tlvtype_16 = type;
                cdp->tlvlength_16 = length;
                cdp_offset += 4;
                length -= 4;
                cdp->naddresses_16 = get_uint32_ntohl(cdp_payload, cdp_offset);
                cdp_offset += 4;
                length -= 4;
#if 0
                //todo ....待商榷,同：TYPE_ADDRESS
                address_malloc(cdp, cdp->naddresses_16);
                for(i=0; i < cdp->naddresses_16; i++)
                {
                    int addr_length = dissect_address_tlv(cdp, cdp_payload, cdp_offset, length, i);
                    if (addr_length < 0)
                        break;
                    cdp_offset += addr_length;
                    length -= addr_length;
                }
#endif
                cdp_offset += length;
                break;
            case TYPE_LOCATION:
                cdp->tlvtype_17 = type;
                cdp->tlvlength_17 = length;
                cdp->location_unknown = cdp_payload[cdp_offset+4];
                MALLOC(cdp->location, length-5+1);
                memcpy(cdp->location, cdp_payload+cdp_offset+5, length-5);
                cdp_offset += length;
                break;
            case TYPE_POWER_REQUESTED:
                cdp->tlvtype_19 = type;
                cdp->tlvlength_19 = length;
                cdp->request_id_19 = get_uint16_ntohs(cdp_payload, cdp_offset+4);
                cdp->management_id_19 = get_uint16_ntohs(cdp_payload, cdp_offset+4+2);
                power_len = length;
                if (power_len < 8)
                {
                    cdp_offset += power_len;
                    break;
                }
                power_len -= 8;
                cdp_offset += 8;
                //todo .... 待商榷, 暂舍弃cdp->power_requested的值
                while(power_len >= 4)
                {
                    //cdp->power_requested = get_uint32_t(cdp_payload, cdp_offset);
                    power_len -= 4;
                    cdp_offset += 4;
                }
                cdp_offset += power_len;
                break;
            case TYPE_POWER_AVAILABLE:
                cdp->tlvtype_1a = type;
                cdp->tlvlength_1a = length;
                cdp->request_id_1a = get_uint16_ntohs(cdp_payload, cdp_offset+4);
                cdp->management_id_1a = get_uint16_ntohs(cdp_payload, cdp_offset+4+2);
                power_len = length;
                if (power_len < 8)
                {
                    cdp_offset += power_len;
                    break;
                }
                power_len -= 8;
                cdp_offset += 8;
                //todo .... 待商榷, 暂舍弃cdp->power_requested的值
                while(power_len >= 4)
                {
                    //cdp->power_available = get_uint32_t(cdp_payload, cdp_offset);
                    power_len -= 4;
                    cdp_offset += 4;
                }
                cdp_offset += power_len;
                break;
            case TYPE_NRGYZ:
                cdp->tlvtype_1d = type;
                cdp->tlvlength_1d = length;
                memcpy(cdp->encrypted_data, cdp_payload+cdp_offset+4, 20);
                cdp->seen_sequence = get_uint32_ntohl(cdp_payload, cdp_offset+24);
                cdp->sequence_number = get_uint32_ntohl(cdp_payload, cdp_offset+28);
                memcpy(cdp->model_number, cdp_payload+cdp_offset+32, 16);
                cdp->unknown_pad = get_uint16_ntohs(cdp_payload, cdp_offset+48);
                memcpy(cdp->hardware_version_id, cdp_payload+cdp_offset+50, 3);
                memcpy(cdp->system_serial_number, cdp_payload+cdp_offset+53, 11);
                memcpy(cdp->nrgyz_unknown_values, cdp_payload+cdp_offset+64, 8);
                cdp->len_tlv_table = get_uint16_ntohs(cdp_payload, cdp_offset+72);
                cdp->num_tlvs_table = get_uint16_ntohs(cdp_payload, cdp_offset+74);
                //todo....待解析
                //dissect_nrgyz_tlv();
                cdp_offset += length;
                break;

            default:
                cdp_offset += length;
                break;
        }
    }
#endif
    write_cdp_log(cdp);
    free_buff(cdp);
}

void write_cdp_log(CDPINFO* info)
{
    int idx = 0, i;
    char __str[64];
    struct tbl_log* log_ptr;
    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }

    struct flow_info flow;
    memset(&flow, 0, sizeof(struct flow_info));

    init_log_ptr_data(log_ptr, &flow,PROTOCOL_CDP);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    precord_t *record = log_ptr->record;
    player_t  *layer  = precord_layer_put_new_layer(record, "common");
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ENUM_SHARE_HEADER_MAX);         /**<临时措施 */

#ifndef DPI_SDT_ZDY
    idx = 0;
    layer = precord_layer_put_new_layer(record, "link");
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_LINK_MAX);         /**<临时措施 */
#endif

    if(g_config._327_common_switch){
        idx = 0;
        player_t *layer = precord_layer_put_new_layer(log_ptr->record, "327_common");
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ENUM_P327_MAX);         /**<临时措施 */
    }

    precord_layer_put_new_layer(log_ptr->record, "cdp");
    idx = 0;
    for(i = 0; i < EM_CDP_MAX; ++i)
    {
        switch(cdp_field_array[i].index)
        {
            case EM_CDP_H_DST:
            case EM_CDP_H_SRC:
            case EM_CDP_H_PROTO:
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_DSAP:
            case EM_CDP_SSAP:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "0xaa", 4);
                break;
            case EM_CDP_CONTRAL:
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_OUI:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "Cisco", 5);
                break;
            case EM_CDP_CISO_PID:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "CDP", 3);
                break;
            case EM_CDP_VERSION:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->version);
                break;
            case EM_CDP_TTL:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ttl);
                break;
            case EM_CDP_CHECKSUM:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->checksum);
                break;
            case EM_CDP_CHECKSUM_STATUS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->checksum_status);
                break;
            case EM_CDP_TLV_TYPE:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype);
                break;
            case EM_CDP_TLV_LENGTH:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength);
                break;
            case EM_CDP_TLV_TYPE_1:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_1);
                break;
            case EM_CDP_TLV_LENGTH_1:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_1);
                break;
            case EM_CDP_TLV_TYPE_2:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_2);
                break;
            case EM_CDP_TLV_LENGTH_2:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_2);
                break;
            case EM_CDP_TLV_TYPE_3:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_3);
                break;
            case EM_CDP_TLV_LENGTH_3:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_3);
                break;
            case EM_CDP_TLV_TYPE_4:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_4);
                break;
            case EM_CDP_TLV_LENGTH_4:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_4);
                break;
            case EM_CDP_TLV_TYPE_5:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_5);
                break;
            case EM_CDP_TLV_LENGTH_5:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_5);
                break;
            case EM_CDP_TLV_TYPE_6:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_6);
                break;
            case EM_CDP_TLV_LENGTH_6:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_6);
                break;
            case EM_CDP_TLV_TYPE_7:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_7);
                break;
            case EM_CDP_TLV_LENGTH_7:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_7);
                break;
            case EM_CDP_TLV_TYPE_8:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_8);
                break;
            case EM_CDP_TLV_LENGTH_8:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_8);
                break;
            case EM_CDP_TLV_TYPE_9:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_9);
                break;
            case EM_CDP_TLV_LENGTH_9:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_9);
                break;
            case EM_CDP_TLV_TYPE_A:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_a);
                break;
            case EM_CDP_TLV_LENGTH_A:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_a);
                break;
            case EM_CDP_TLV_TYPE_B:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_b);
                break;
            case EM_CDP_TLV_LENGTH_B:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_b);
                break;
            case EM_CDP_TLV_TYPE_E:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_e);
                break;
            case EM_CDP_TLV_LENGTH_E:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_e);
                break;
            case EM_CDP_TLV_TYPE_F:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_f);
                break;
            case EM_CDP_TLV_LENGTH_F:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_f);
                break;
            case EM_CDP_TLV_TYPE_10:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_10);
                break;
            case EM_CDP_TLV_LENGTH_10:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_10);
                break;
            case EM_CDP_TLV_TYPE_11:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_11);
                break;
            case EM_CDP_TLV_LENGTH_11:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_11);
                break;
            case EM_CDP_TLV_TYPE_12:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_12);
                break;
            case EM_CDP_TLV_LENGTH_12:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_12);
                break;
            case EM_CDP_TLV_TYPE_13:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_13);
                break;
            case EM_CDP_TLV_LENGTH_13:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_13);
                break;
            case EM_CDP_TLV_TYPE_14:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_14);
                break;
            case EM_CDP_TLV_LENGTH_14:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_14);
                break;
            case EM_CDP_TLV_TYPE_15:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_15);
                break;
            case EM_CDP_TLV_LENGTH_15:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_15);
                break;
            case EM_CDP_TLV_TYPE_16:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_16);
                break;
            case EM_CDP_TLV_LENGTH_16:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_16);
                break;
            case EM_CDP_TLV_TYPE_17:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_17);
                break;
            case EM_CDP_TLV_LENGTH_17:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_17);
                break;
            case EM_CDP_TLV_TYPE_19:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_19);
                break;
            case EM_CDP_TLV_LENGTH_19:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_19);
                break;
            case EM_CDP_TLV_TYPE_1A:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_1a);
                break;
            case EM_CDP_TLV_LENGTH_1A:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_1a);
                break;
            case EM_CDP_TLV_TYPE_1D:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvtype_1d);
                break;
            case EM_CDP_TLV_LENGTH_1D:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->tlvlength_1d);
                break;
            case EM_CDP_DEVICE_ID:
                write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->deviceid);
                break;
            case EM_CDP_PORT_ID:
                write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->portid);
                break;
            case EM_CDP_N_ADDRESS_2:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->naddresses_2);
                break;
            case EM_CDP_N_ADDRESS_16:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->naddresses_16);
                break;
#if 0
            case EM_CDP_PROTO_TYPE:
                if(NULL != info->proto_type)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->proto_type, strlen(info->proto_type));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_PROTO_LEN:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->proto_len);
                break;
            case EM_CDP_PROTOCOL:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->protocol);
                break;
            case EM_CDP_ADDRESS_LEN:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->address_length);
                break;
            case EM_CDP_ADDRESS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->address);
                break;
            case EM_CDP_NRGYZ_IP_ADDRESS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->nrgyz_ip_address);
                break;
#endif
            case EM_CDP_CAPABILITY:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities);
                break;
            case EM_CDP_CAPABILITY_ROUTER:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_router);
                break;
            case EM_CDP_CAPABILITY_TRANS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_trans_bridge);
                break;
            case EM_CDP_CAPABILITY_SRC:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_src_bridge);
                break;
            case EM_CDP_CAPABILITY_SWITCH:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_switch);
                break;
            case EM_CDP_CAPABILITY_HOST:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_host);
                break;
            case EM_CDP_CAPABILITY_IGMP:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_igmp_capable);
                break;
            case EM_CDP_CAPABILITY_REPEATER:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->capabilities_repeater);
                break;
            case EM_CDP_SOFTWARE_V:
                if(NULL != info->software_version)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->software_version, strlen((const char*)info->software_version));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_PLATFORM:
                if(NULL != info->platform)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)info->platform, strlen((const char*)info->platform));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_ODR_DEFAULT_GATEWAY:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->odr_default_gateway);
                break;
            case EM_CDP_OUI_8:
                write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->oui_8, 3);
                break;
            case EM_CDP_PROTO_ID:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->proto_id);
                break;
            case EM_CDP_CLUSTER_MASTER_IP:
                get_ip4string(__str, sizeof(__str), info->cluster_master_ip);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
                break;
            case EM_CDP_CLUSTER_IP:
                get_ip4string(__str, sizeof(__str), info->cluster_ip);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
                break;
            case EM_CDP_CLUSTER_VERSION:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_version);
                break;
            case EM_CDP_CLUSTER_SUB_V:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_sub_version);
                break;
            case EM_CDP_CLUSTER_STATUS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_status);
                break;
            case EM_CDP_CLUSTER_UNKNOW1:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_unknown1);
                break;
            case EM_CDP_CLUSTER_COMMAND_MAC:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->cluster_commander_mac, 6);
                break;
            case EM_CDP_CLUSTER_SWITCH_MAC:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->cluster_switch_mac, 6);
                break;
            case EM_CDP_CLUSTER_UNKNOW2:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_unknown2);
                break;
            case EM_CDP_CLUSTER_MANAGEMENT_VLAN:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->cluster_management_vlan);
                break;
#if 0
            case EM_CDP_HELLO_UNKNOE:
                if(NULL != info->hello_unknown)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->hello_unknown, strlen(info->hello_unknown));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
#endif
            case EM_CDP_VTP_M_DOMAIN:
                if(NULL != info->vtp_management_domain)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->vtp_management_domain, strlen((const char*)info->vtp_management_domain));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_NATIVE_VLAN:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->native_vlan);
                break;
            case EM_CDP_DUPLEX:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->duplex);
                break;
            case EM_CDP_DATA_E:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->data_e);
                break;
            case EM_CDP_VOICE_VLAN_E:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->voice_vlan_e);
                break;
            case EM_CDP_DATA_F:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->data_f);
                break;
            case EM_CDP_VOICE_VLAN_F:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->voice_vlan_f);
                break;
            case EM_CDP_POWER_CONSUMPTION:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->power_consumption);
                break;
            case EM_CDP_MTU:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->mtu);
                break;
            case EM_CDP_TRUST_BITMAP:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->trust_bitmap);
                break;
            case EM_CDP_UNTRUST_PORT_COS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->untrusted_port_cos);
                break;
            case EM_CDP_SYSTEM_NAME:
                if(NULL != info->system_name)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->system_name, strlen((const char*)info->system_name));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_SYSTEM_OBJ_IDENT:
                if(NULL != info->system_object_identifier)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->system_object_identifier, strlen((const char*)info->system_object_identifier));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_LOCATION_UNKNOW:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->location_unknown);
                break;
            case EM_CDP_LOCATION:
                if(NULL != info->location)
                    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->location, strlen((const char*)info->location));
                else
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_CDP_REQ_ID19:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->request_id_19);
                break;
            case EM_CDP_MANAGE_ID_19:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->management_id_19);
                break;
            case EM_CDP_POWER_REQ:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->power_requested);
                break;
            case EM_CDP_REQ_ID_1A:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->request_id_1a);
                break;
            case EM_CDP_MANAGE_ID_1A:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->management_id_1a);
                break;
            case EM_CDP_POWER_AVAIL:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->power_available);
                break;
            case EM_CDP_ENCRYPT_DATA:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->encrypted_data, 20);
                break;
            case EM_CDP_SEEN_SEQUENCE:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->seen_sequence);
                break;
            case EM_CDP_SEQUENCE_NUM:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->sequence_number);
                break;
            case EM_CDP_MODEL_NUM:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->model_number, 16);
                break;
            case EM_CDP_UNKNOW_PAD:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->unknown_pad);
                break;
            case EM_CDP_HARDWARE_V_ID:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->hardware_version_id, 3);
                break;
            case EM_CDP_SYSTEM_SERIAL_NUM:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->system_serial_number, 11);
                break;
            case EM_CDP_NRGYZ_UNKNOW_VAL:
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)info->nrgyz_unknown_values, 8);
                break;
            case EM_CDP_LEN_TLV_TABLE:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->len_tlv_table);
                break;
            case EM_CDP_NUM_TLVS_TABLE:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->num_tlvs_table);
                break;
            default:
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                break;
        }
    }

    log_ptr->log_type = TBL_LOG_CDP;
    log_ptr->log_len  = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->flow=NULL;
    if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
    return ;
}


#if 0
int dissect_address_tlv(CDPINFO* cdp, const char* payload, int offset, int length)
{
    int nlpid = 0;
    int hf_addr = 0;
    uint16_t etypeid = 0;
    if (length < 1)
        return -1;
    cdp->proto_type = payload[offset++];
    length--;
    cdp->proto_len = payload[offset++];
    length--;
    if(length < cdp->proto_len)
    {
        if(0 != length)
            cdp->protocol = payload[offset];
        return -1;
    }

    if(PROTO_TYPE_NLPID == cdp->proto_type && 1 == cdp->proto_len)
        nlpid = payload[offset];
    else if(PROTO_TYPE_IEEE_802_2 == cdp->proto_type && 8 == cdp->proto_len && 0xAAAA03000000 == get_uint64_t(payload, offset))
        etypeid = get_uint16_t(payload, offset+6);
    else
    {
        nlpid = -1;
        cdp->protocol = payload[offset];
    }

    offset += cdp->proto_len;
    length -= cdp->proto_len;
    if(length < 2)
        return -1;
    cdp->address_length = get_uint16_t(payload, offset);
    offset += 2;
    length -= 2;
    if(length < cdp->address_length)
    {
        if(0 != length)
            cdp->address = get_uint16_t(payload, offset);
        return -1;
    }
    if(PROTO_TYPE_NLPID == cdp->proto_type && 1 == cdp->proto_len)
    {
        if(NLPID_IP == nlpid && 4 == cdp->address_length)
        {
            hf_addr = -1;
            cdp->nrgyz_ip_address = get_uint32_t(payload, offset);
        }
    }
    else if(PROTO_TYPE_IEEE_802_2 == cdp->proto_type && 8 == cdp->proto_len && etypeid > 0)
    {
        if(ETHERTYPE_IPv6 == etypeid && 16 == cdp->address_length)
        {
            hf_addr = -1;
            cdp->nrgyz_ip_address = get_uint32_t(payload, offset);
        }
    }
    if(-1 == hf_addr)
        cdp->address = get_uint16_t(payload, offset);
    return 2 + cdp->proto_len + 2 + cdp->address_length;
}
#endif

static void init_cdp_dissector(void)
{
    dpi_register_proto_schema(cdp_field_array, EM_CDP_MAX, "cdp");

    map_fields_info_register(cdp_field_array,PROTOCOL_CDP, EM_CDP_MAX, "cdp");
    pschema_t *schema = dpi_pschema_get_proto("cdp");
    pschema_register_field(schema, "ip_address", YA_FT_STRING, "desc");
    return;
}


static __attribute((constructor)) void     before_init_smb(void){
    register_tbl_array(TBL_LOG_CDP, 0, "cdp", init_cdp_dissector);
}

#include "dpi_trailer.h"

static uint64_t conver_data(uint64_t    data)
{
    uint8_t array[10]={0};
    int i;
    uint64_t tmp=0;
    uint64_t ret=0;
    uint8_t  h,l;
    uint8_t  octet=0;
    for(i = 0;i < 8; i++){
        tmp = data>>((7-i)*8);
        octet = (uint8_t)tmp;
        h = (octet>>4) & 0x0F;
        l = (octet) & 0x0F;
        array[i] = (l<<4)|h;
    }

    ret=(get_uint64_t(&array[0],0))>>8;
    return ret;
}


/**
 * Convert 64bit integer in BCD encoding to decimal.
 * @param bcd		BCD value to convert.
 * @param[out] dec	Pointer to decimal result.
 * @return TRUE if ok, FALSE if bcd contains a nibble > 9.
 */
static int
convert_bcd_to_dec(uint64_t raw, uint64_t* dec)
{
    int      rok    = 1;
    uint64_t result = 0;
    uint64_t mult   = 1;
    uint64_t bcd    = 0;

    bcd=conver_data(raw);
    while (bcd) {
        if ((bcd & 0x0f) > 9){
            rok = 0;
            bcd >>= 4;
            continue;
        }
        result += (bcd & 0x0f) * mult;
        bcd >>= 4;
        mult *= 10;
    }
    *dec = result;

    return rok;
}

void parse_rt_trailer(struct rt_trailer *trailer, const uint8_t *payload, uint16_t payload_len)
{
	if(payload_len <56)
		return;

	int offset;

	trailer->teid  = get_uint32_ntohl(payload, 0);
	trailer->srcip = get_uint32_t(payload, 8);
	trailer->dstip = get_uint32_t(payload, 12);
	offset = 16;	

	trailer->msisdn = be64toh(get_uint64_t(payload, offset)) >> 8;
	trailer->imei   = be64toh(get_uint64_t(payload, offset+7)) >> 8;
	trailer->imsi   = be64toh(get_uint64_t(payload, offset+14)) >> 8;

    
	offset += 21;

	trailer->tac     = get_uint16_ntohs(payload, offset);
	trailer->plmn_id = get_uint16_ntohs(payload, offset+2);
	trailer->uli     = get_uint32_ntohl(payload, offset+4);
	trailer->base    = payload[offset+8] & 0x0f;
	
	return;
}

void parse_rt_sino_trailer(struct rt_trailer *trailer, const uint8_t *payload, uint16_t payload_len)
{
    if(payload_len <56)
        return;

    int offset;

    trailer->teid  = get_uint32_ntohl(payload, 0);
    trailer->srcip = get_uint32_t(payload, 8);
    trailer->dstip = get_uint32_t(payload, 12);
    offset = 16;

    uint64_t tmp=0;
    tmp=(get_uint64_t(payload, offset));
    convert_bcd_to_dec(tmp,&trailer->msisdn);
  //  trailer->msisdn>>=4;

    tmp=0;
    tmp=(get_uint64_t(payload, offset+7));
    convert_bcd_to_dec(tmp,&trailer->imei);

    tmp=0;
    tmp=(get_uint64_t(payload, offset+14));
    convert_bcd_to_dec(tmp,&trailer->imsi);
    
    offset += 21;

    trailer->tac     = get_uint16_ntohs(payload, offset);
    trailer->plmn_id = get_uint16_ntohs(payload, offset+2);
    trailer->uli     = get_uint32_ntohl(payload, offset+4);
    trailer->base    = payload[offset+8] & 0x0f;

    return;
}


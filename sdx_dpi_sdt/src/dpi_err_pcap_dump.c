/****************************************************************************************
 * 文 件 名 : dpi_detect.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy              2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <sys/time.h>

#include <pcap.h>

#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_err_pcap_dump.h"


extern struct global_config g_config;


int dpi_err_pcap_dump(struct flow_info* flow, const char*dir)
{
    // 输出pcap
    if(flow->err_pcap && 1 == flow->err_pcap_dump) // 默认抓包. 解析成功才不抓包
    {
        int                     fd;
        struct pcap_file_header fh;
        unsigned char  *ipsrc = (unsigned char*)&flow->tuple.inner.ip_src;
        unsigned char  *ipdst = (unsigned char*)&flow->tuple.inner.ip_dst;
        unsigned short  port1 = ntohs(flow->tuple.inner.port_src);
        unsigned short  port2 = ntohs(flow->tuple.inner.port_dst);
        char            name[128];
        memset(&fh, 0, sizeof(struct pcap_file_header));

        snprintf(name, sizeof(name), "%s/ERR_PCAP_%u.%u.%u.%u_%u.%u.%u.%u_%u_%u.pcap",
                dir,
                ipsrc[0], ipsrc[1], ipsrc[2], ipsrc[3],
                ipdst[0], ipdst[1], ipdst[2], ipdst[3],
                port1, port2);

        fh.magic         = 0xA1B2C3D4;
        fh.version_major = PCAP_VERSION_MAJOR;
        fh.version_minor = PCAP_VERSION_MINOR;
        fh.snaplen       = ntohl(1024);
        fh.linktype      = DLT_EN10MB;
        mkdirs(dir);
        fd = creat(name, 0644);
        if(fd > 0)
        {
            write(fd, &fh, sizeof(fh));
            write(fd, flow->err_pcap, flow->err_pcap_hold);
            close(fd);
        }
    }

    if(flow->err_pcap)
    {
        free(flow->err_pcap);
        flow->err_pcap      = NULL;
        flow->err_pcap_hold = 0;
    }

    return 0;
}



int dpi_err_pcap_copy(struct flow_info *flow, const char *p, int l)
{
    const  char mac[] = "\x11\x11\x11\x11\x11\x11\x22\x22\x22\x22\x22\x22\x08\x00";
    int   ETH_MAC_LEN = 14;

    while(g_config.error_pcap_size > 0 && 1 == flow->err_pcap_dump && NULL == flow->err_pcap)
    {
        flow->err_pcap      = malloc(g_config.error_pcap_size);
        flow->err_pcap_hold = 0;
    }

    if(NULL != flow->err_pcap && g_config.error_pcap_size - flow->err_pcap_hold > (int)(sizeof(struct pcappkt_hdr) + ETH_MAC_LEN + l))
    {
        struct timeval tv;
        struct pcappkt_hdr    pckt_header;

        memset(&pckt_header, 0, sizeof(struct pcappkt_hdr));
        gettimeofday(&tv, NULL);

        pckt_header.tv_sec  = tv.tv_sec;
        pckt_header.tv_usec = tv.tv_usec;
        pckt_header.caplen  = ETH_MAC_LEN + l;
        pckt_header.len     = ETH_MAC_LEN + l;

        memcpy(flow->err_pcap + flow->err_pcap_hold, &pckt_header, sizeof(struct pcappkt_hdr));
        flow->err_pcap_hold += sizeof(struct pcappkt_hdr);

        memcpy(flow->err_pcap + flow->err_pcap_hold, mac, ETH_MAC_LEN);
        flow->err_pcap_hold += ETH_MAC_LEN;

        memcpy(flow->err_pcap + flow->err_pcap_hold, p, l);
        flow->err_pcap_hold += l;
    }
    return 0;
}




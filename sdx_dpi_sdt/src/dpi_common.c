/****************************************************************************************
 * 文 件 名 : dpi_common.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include "dpi_common.h"

#include <yaFtypes/ftypes.h>

#include <arpa/inet.h>
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <sys/time.h>
#include <time.h>
#include <stdio.h>
#include <unistd.h>
#include <assert.h>
#include "dpi_pint.h"
#include "dpi_log.h"
#include "sdt_action_out.h"


static const unsigned char left_aligned_bitmask[] = {
    0xff,
    0x80,
    0xc0,
    0xe0,
    0xf0,
    0xf8,
    0xfc,
    0xfe
};

int find_packet_line_end(const uint8_t *data, uint16_t len)
{
    uint16_t i;

    for (i = 0; i < len - 1; i++) {
        if (get_uint16_t(data, i) == ntohs(0x0d0a))
            return i;
    }

    return -1;
}

int match_prefix_str(const u_int8_t *payload, size_t payload_len, const char *str, size_t str_len)
{
    int rc = str_len <= payload_len ? memcmp(payload, str, str_len) == 0 : 0;

    return rc;
}

/*
 * Get the length of the next token in a line, and the beginning of the
 * next token after that (if any).
 * Return 0 if there is no next token.
 */
uint32_t dpi_get_token_len(const uint8_t *linep, const uint8_t *lineend, const uint8_t **next_token)
{
    const uint8_t *tokenp;
    uint32_t token_len;

    tokenp = linep;

    /*
     * Search for a blank, a CR or an LF, or the end of the buffer.
     */
    while (linep < lineend && *linep != ' ' && *linep != '\r' && *linep != '\n')
        linep++;
    token_len = (int) (linep - tokenp);

    /*
     * Skip trailing blanks.
     */
    while (linep < lineend && *linep == ' ')
        linep++;

    *next_token = linep;

    return token_len;
}

void get_parameters_keys_values(char *payload, int payload_len, void *user, parameters_keys_values func) {
    char *p_eq = NULL;
    int   num = 0;
    char *p_start = payload;
    int   key_len = 0;
    int   value_len = 0;

    while (p_start) {
        char *p_next = memchr(p_start, '&', strlen(p_start));
        key_len = 0;
        if (p_next) {
            p_eq = memchr(p_start, '=', p_next - p_start);
            if(p_eq)
            {
                key_len = p_eq - p_start;
                value_len = p_next - p_eq - 1;
            }
        } else {
            //最后一个key
            p_eq = memchr(p_start, '=', strlen(p_start));
            if(p_eq)
            {
                char* p_space = memchr(p_start, ' ', strlen(p_eq));
                if(p_space != NULL){
                   // 最后一个kv可能以空格结尾 使用str操作判断不了真正的长度
                    value_len = p_space  - p_eq -1;
                }else {
                    value_len = strlen(p_eq + 1);
                }
                key_len = p_eq - p_start;
            }else{
                break;
            }
        }
        if (key_len > 0 && value_len >0) {
            func(user, p_start, key_len, p_eq + 1, value_len);
        }
        if (p_next) {
            p_start = p_next + 1;
        } else {
            break;
        }
    }
}

int find_blank_space(const uint8_t *start, int max_len)
{
    int index;

    for (index = 0; index < max_len; index++) {
        if (start[index] == ' ')
            return index;
    }

    return -1;
}

int find_special_char(const uint8_t *start, int max_len, char c)
{
    int index;

    for (index = 0; index < max_len; index++) {
        if (start[index] == c)
            return index;
    }

    return -1;
}


int find_special_colon_space(const uint8_t *start, int max_len)
{
    int index;

    for (index = 0; index <= max_len-2; index++) {
        if (memcmp(start + index, ": ", 2) == 0)
            return index;
    }

    return -1;
}


int _find_imap_fetch_end_line(const uint8_t *payload, uint32_t payload_len)
{
    uint32_t i;
    if (payload_len < 5)
        return -1;
    for (i = 0; i <= payload_len - 5; i++) {
        if (memcmp(payload + i, "\r\n)\r\n", 5) == 0)
            return i;
    }

    return -1;
}


int _find_email_end_line(const uint8_t *payload, uint32_t payload_len)
{
    uint32_t i;
    if (payload_len < 5)
        return -1;
    for (i = 0; i <= payload_len - 5; i++) {
        if (memcmp(payload + i, "\r\n.\r\n", 5) == 0)
            return i;
    }

    return -1;
}

int _find_single_line_end(const uint8_t *payload, uint32_t payload_len)
{
    uint32_t i;
    if (payload_len < 4)
        return -1;
    for (i = 0; i <= payload_len - 4; i++) {
        if (memcmp(payload + i, "\r\n", 2) == 0)
            return i;
    }

    return -1;
}

int _find_empty_line(const uint8_t *payload, uint32_t payload_len)
{
    uint32_t i;
    if (payload_len < 4)
        return -1;
    for (i = 0; i <= payload_len - 4; i++) {
        if (memcmp(payload + i, "\r\n\r\n", 4) == 0)
            return i;
    }

    return -1;
}

/*containes null char*/
int find_str_end_len(const uint8_t *start, uint32_t max_len)
{
    uint32_t i;

    for (i = 0; i < max_len; i++) {
        if (start[i] == 0)
            return i + 1;
    }
    return -1;
}

/* Convert all ASCII letters to lower case, in place. */
char *strdown_inplace(char *str)
{
    char *s;

    for (s = str; *s; s++)
        /* What 'g_ascii_tolower (gchar c)' does, this should be slightly more efficient */
        *s = isupper(*s) ? *s - 'A' + 'a' : *s;

    return (str);
}

/* Tries to match val against each element in the value_string array vs.
   Returns the associated string ptr, and sets "*idx" to the index in
   that table, on a match, and returns NULL, and sets "*idx" to -1,
   on failure. */
const char *val_to_string(const int val, const struct int_to_string *vs)
{
    int i = 0;

    if(vs) {
        while (vs[i].strptr) {
            if (vs[i].value == val) {
                return(vs[i].strptr);
            }
            i++;
        }
    }

    return NULL;
}

/* Tries to match val against each element in the value_string array vs.
   Returns the associated string ptr, and sets "*idx" to the index in
   that table, on a match, and returns NULL, and sets "*idx" to -1,
   on failure. */
const char *val_to_string_ext(const int val, const struct int_to_string *vs, int *idx)
{
    int i = 0;

    if(vs) {
        while (vs[i].strptr) {
            if (vs[i].value == val) {
                *idx = i;
                return(vs[i].strptr);
            }
            i++;
        }
    }

    return NULL;
}

int timet_to_datetime(time_t t, char *time_str, int len)
{
    struct tm tm_tmp;
    //localtime_r(&t, &tm_tmp);
    t += 28800;  //东八区
    gmtime_r(&t, &tm_tmp);

    snprintf(time_str, len, "%04d-%02d-%02d %02d:%02d:%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
    return 0;
}

int timet_to_datetime_local(time_t t, char *time_str, int len)
{
    struct tm tm_tmp;
    localtime_r(&t, &tm_tmp);

    snprintf(time_str, len, "%04d-%02d-%02d %02d:%02d:%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
    return 0;
}

int timet_to_datetime_gm(time_t t, char *time_str, int len)
{
    struct tm tm_tmp;
    gmtime_r(&t, &tm_tmp);

    snprintf(time_str, len, "%04d-%02d-%02d %02d:%02d:%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
    return 0;
}

char * get_owner_path(void)
{
    int i;
    static char path[256];
    int cnt = readlink("/proc/self/exe", path, sizeof(path));
    if (cnt < 0 || (unsigned int)cnt > sizeof(path))
        return NULL;
    for (i = cnt; i >= 0; i--) {
        if (path[i] == '/') {
            path[i + 1] = 0;
            break;
        }
    }
    return path;
}

unsigned long long malloc_num;
unsigned long long free_num;

void *dpi_malloc(size_t size)
{
    malloc_num++;

    char* ret = malloc(size + 1);
    ret[size] = '\0';
    return ret;
}

void dpi_free(void *ptr)
{
    free_num++;
    free(ptr);
}


int dpi_get_uint8(struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *val)
{
    if (offset + 1 > pkt->payload_len)
        return -1;

    *val = *(pkt->payload + offset);
    return 0;
}

int dpi_get_be16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val)
{
    if (offset + 2 > pkt->payload_len)
        return -1;

    *val = pntoh16(pkt->payload + offset);
    return 0;
}

int dpi_get_be24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
    if (offset + 4 > pkt->payload_len)
        return -1;

    *val = pntoh24(pkt->payload + offset);
    return 0;
}

int dpi_get_be32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
    if (offset + 4 > pkt->payload_len)
        return -1;

    *val = pntoh32(pkt->payload + offset);
    return 0;
}


int dpi_get_be64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val)
{
    if (offset + 8 > pkt->payload_len)
        return -1;

    *val = pntoh64(pkt->payload + offset);
    return 0;
}


int dpi_get_le16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val)
{
    if (offset + 2 > pkt->payload_len)
        return -1;

    *val = pletoh16(pkt->payload + offset);
    return 0;
}

int dpi_get_le24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
    if (offset + 3 > pkt->payload_len)
        return -1;

    *val = pletoh24(pkt->payload + offset);
    return 0;
}

int dpi_get_le32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
    if (offset + 4 > pkt->payload_len)
        return -1;

    *val = pletoh32(pkt->payload + offset);
    return 0;
}


int dpi_get_le64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val)
{
    if (offset + 8 > pkt->payload_len)
        return -1;

    *val = pletoh64(pkt->payload + offset);
    return 0;
}

int dpi_get_string_endwith_null(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len)
{
    if (offset + len > pkt->payload_len)
        return -1;
    snprintf(val, max_len, "%s", (const char *)pkt->payload + offset);
    return 0;
}


int dpi_get_string_ascii(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len)
{
    uint32_t copy_len = len >= max_len ? max_len - 1 : len;
    if (offset + len > pkt->payload_len)
        return -1;

    memcpy(val, pkt->payload + offset, copy_len);
    val[copy_len] = 0;
    return 0;
}

int dpi_get_hex_string(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len)
{
    uint32_t i;
    if (offset + len > pkt->payload_len)
        return -1;

    val[0] = '0';
    val[1] = 'x';
    for (i = 0; i < len && i < (max_len - 3) / 2; i++) {
        sprintf(val + i * 2 + 2, "%02x", pkt->payload[offset + i]);
    }
    return 0;
}

int dpi_get_guid(struct dpi_pkt_st *pkt, uint32_t offset, char *val, uint32_t max_len)
{
    int idx;
    uint32_t i;
    if (offset + 16 > pkt->payload_len)
        return -1;

    if (max_len < sizeof("00112233-0011-0011-0011-001122334455"))
        return -1;

    for (i = 0; i < 4; i++) {
        sprintf(val + i * 2, "%02x", pkt->payload[offset + 4 - i - 1]);
    }

    val[8] = '-';
    for (i = 0; i < 2; i++) {
        sprintf(val + 9 + 2 * i, "%02x", pkt->payload[offset + 6 - i - 1]);
    }

    val[13] = '-';
    for (i = 0; i < 2; i++) {
        sprintf(val + 14 + i * 2, "%02x", pkt->payload[offset + 8 - i - 1]);
    }

    val[18] = '-';
    for (i = 0; i < 2; i++) {
        sprintf(val + 19 + i * 2, "%02x", pkt->payload[offset + 8 + i ]);
    }

    val[23] = '-';
    for (i = 0; i < 6; i++) {
        sprintf(val + 24 + i * 2, "%02x", pkt->payload[offset + 10 + i]);
    }

    return 0;
}

int dpi_strneql(struct dpi_pkt_st *pkt, uint32_t offset, const char *str, const size_t size)
{
    if (offset + size > pkt->payload_len)
        return -1;
    int cmp = strncmp((const char *)pkt->payload + offset, str, size);

    return cmp == 0 ? 0 : 1;
}


void dpi_append_octet_aligned(struct dpi_pkt_st *pkt_src, uint32_t bit_offset, int no_of_bits, struct dpi_pkt_op_st *pkt_dst, uint32_t dst_max_len)
{
    uint32_t       byte_offset;
    int        datalen, i;
    uint8_t        left, right, remaining_bits, *buf;
    const uint8_t *data;
    uint32_t copy_len;

    byte_offset = bit_offset >> 3;
    left = bit_offset % 8; /* for left-shifting */
    right = 8 - left; /* for right-shifting */

    if (no_of_bits == -1) {
        datalen = pkt_src->payload_len - byte_offset;
        remaining_bits = 0;
    } else {
        datalen = no_of_bits >> 3;
        remaining_bits = no_of_bits % 8;
        if (remaining_bits) {
            datalen++;
        }
    }

    /* already aligned -> shortcut */
    if ((left == 0) && (remaining_bits == 0)) {
        copy_len = (uint32_t)datalen < (dst_max_len - pkt_dst->payload_len) ? (uint32_t)datalen : (dst_max_len - pkt_dst->payload_len);
        memcpy((void *)(pkt_dst->payload + pkt_dst->payload_len), (const void*)(pkt_src->payload + byte_offset), copy_len);
        pkt_dst->payload_len += copy_len;
        return;
    }

    /* if at least one trailing byte is available, we must use the content
    * of that byte for the last shift (i.e. tvb_get_ptr() must use datalen + 1
    * if non extra byte is available, the last shifted byte requires
    * special treatment
    */
    if (pkt_src->payload_len - byte_offset > (uint32_t)datalen) {
        data = pkt_src->payload + byte_offset;
        buf = (uint8_t *)(pkt_dst->payload + pkt_dst->payload_len);

        /* shift tvb data bit_offset bits to the left */
        for (i = 0; i < datalen; i++) {
            if (pkt_dst->payload_len >= dst_max_len)
                break;
            buf[i] = (data[i] << left) | (data[i + 1] >> right);
            pkt_dst->payload_len++;
        }
    } else {
        data = pkt_src->payload + byte_offset;
        buf = (uint8_t *)pkt_dst->payload + pkt_dst->payload_len;

        /* shift tvb data bit_offset bits to the left */
        for (i = 0; i < (datalen-1); i++) {
            if (pkt_dst->payload_len >= dst_max_len)
                break;
            buf[i] = (data[i] << left) | (data[i + 1] >> right);
            pkt_dst->payload_len++;
        }
        if (pkt_dst->payload_len < dst_max_len) {
            buf[datalen - 1] = data[datalen - 1] << left; /* set last octet */
            pkt_dst->payload_len++;
        }
    }
    buf[pkt_dst->payload_len - 1] &= left_aligned_bitmask[remaining_bits];

    return;
}

/*****************************************************************
*Function    :dpi_is_gbk
*Description :探测数据域是不是2字节的GBK数据
*Input       :数据的地址与长度
*Output      :none
*Return      :返回GBK数据的长度， -1代表不全是GBK数据
*Others      :none
*****************************************************************/
int dpi_is_gbk(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }


        return 0;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

/*****************************************************************
*Function    :dpi_is_utf8
*Description :检测数据是不是UTF8编码
*Input       :数据的长度与地址
*Output      :none
*Return      :返回UTF8数据的长度， 0代表不是UTF8编码数据
*Others      :none
*****************************************************************/

int dpi_is_utf8(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;

    if(NULL == pData || len <=0)
    {
        return 0;
    }

    while(loop > 0 )
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=2 &&  (0XC0 == (p[0] & 0XE0)) && (0X80 == (p[1] & 0XC0)))
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        else if(loop>=4 &&  (0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) )
        {
            p = p + 4;
            loop = loop - 4;
            continue;
        }
        return 0;/* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}


const uint8_t* memstr(const uint8_t* a,const char* b,int len){
    if( a == NULL || b == NULL || len < 1)
        return NULL;
    int i,j,n = strlen(b);
    if(len < n)
        return NULL;
    for(i=0; i <= len - n; i++){
        for(j=i; j < n+i; j++)
            if(a[j] != b[j-i])
                break;
        if(j == n+i)
            return a+i;
    }
    return NULL;
}

//similar to strstr
const char* mail_memstr(const char* a,const char* b,int len){
    if( a == NULL || b == NULL || len < 1)
        return NULL;

    int i,j,n = strlen(b);
    if(len < n)
        return NULL;

    for(i=0; i <= len - n; i++){
        for(j=i; j < n+i; j++)
            if(a[j] != b[j-i])
                break;
        if(j == n+i)
            return a+i;
     }
    return NULL;
}


const uint8_t* memstr_reverse(const uint8_t* a, const char* b, int len){
    const uint8_t* ret = memstr(a, b, len);
    if(ret)
        return ret + strlen(b) - 1;
    else
        return NULL;
}

unsigned long get_file_size(const char *path)
{
    if(path==NULL){return -1;}
    unsigned long filesize=-1;
    struct stat statbuff;

    if(stat(path,&statbuff)<0){
        return filesize;
    }else{
        filesize=statbuff.st_size;
    }

    return filesize;
}

int dpi_reported_length_left(int        payload_len,  int offset)
{
    int left_len=0;

    left_len=payload_len-offset;

    return left_len;
}

uint64_t  get_uint64_ntohll(uint64_t val)
{

    return   (((uint64_t)htonl((int)((val << 32) >>32 ))) <<32 ) | (uint32_t)htonl((int)(val>>32));
}

void mkdirs(const char *dir)
{
    if(dir==NULL && access(dir,0)==0)
    {
        return;
    }
    char tmp[2048];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp),"%s",dir);
    len = strlen(tmp);
    if(tmp[len - 1] == '/')
        tmp[len - 1] = 0;
    for(p = tmp + 1; *p; p++)
        if(*p == '/') {
            *p = 0;
            mkdir(tmp, 0777);
            *p = '/';
        }
    mkdir(tmp, 0777);
}

int is_file_exist(const char *filepath)
{
    if(filepath==NULL){
        return 0;
    }
    if((access(filepath,F_OK))!=-1){
        return 1;
    }

    return 0;
}

//在文件还原后，重新探测文件后缀
int discern_filetype(const uint8_t* payload,int payload_len,char* filename,int max_filename_len)
{
    const char* file_type = filetype((const char *)payload,payload_len);
    char sufix[16] = {0};
                            //"Microsoft_Office 2016"后续进一步细分docx等
    //if(file_type == NULL || memcmp(payload, "\x50\x4B\x03\x04\x14\x00\x06\x00", 8) == 0) {
    detect_file_type((const char*)payload,payload_len,sufix);
    if(strlen(sufix)>0)
        file_type = sufix;

    if(file_type == NULL) {
        if(memcmp(filename+strlen(filename)-4, ".bin", 4) == 0) { 
            if(dpi_is_utf8((char*)payload, payload_len)) {
                snprintf(sufix, sizeof(sufix), "text");
                file_type = sufix;
            } else {
                return 0;
            }
        } else
            return 0;
    }

    int ori_filename_len = strlen(filename);
    int i = ori_filename_len;
    while ( i > 0) {
        if(filename[i] == '.') {
            break;
        }
        i--;
    }

    if(i>0 &&i+ strlen(file_type)<(unsigned int)max_filename_len){
        strncpy(filename + i + 1 , file_type, max_filename_len-i-1);
    }

    return 0;
}


const char *filetype(const char*p, int l)
{
    struct
    {
        const char *prefix;
        int            len;
        const char *detail;
    } file_hdr[] = {
        {"\x19\xF1\x03\x00", 4, "Tencent_mmtls"},
        {"\xAB\xCD\x98\x76", 4, "Tencent_Encrypted"},
        {"\x30\x31\x30\x33\x25", 5, "Tencent_Encrypted"},
        {"voice_content=", 14, "搜狗语音输入法"},
        {"\x89\x50\x4E\x47\x0D\x0A\x1A\x0A", 8,   "PNG"},
        {"\x47\x49\x46\x38\x37\x61", 6,   "gif"},
        {"\x47\x49\x46\x38\x39\x61", 6,   "gif"},
        {"\xFF\xD8\xFF\xE0\x00\x10\x4A\x46\x49\x46\x00\x01", 12,   "jpeg"},
        {"\xFF\xD8\xFF\xEE", 4,   "JPG"},
        {"\xFF\xD8\xFF\xE1", 4,   "JPG"},
        {"\x00\x00\x01\xBA", 4,   "mpeg"},
        {"\x52\x49\x46\x46", 4,   "webp,avi,wav"},
        {"\x25\x50\x44\x46\x2d", 5,   "pdf"},
        {"\x44\x48\x41\x56", 4, "dahua_IPC"},
        {"\x47\x40", 2, "MPEG_TS"},
        {"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1", 8, "Microsoft_Office"},
        {"\x50\x4B\x03\x04\x14\x00\x06\x00", 8, "Microsoft_Office 2016"},
        {"\x52\x61\x72\x21\x1A\x07\x00", 7,   "rar"},
        {"\x52\x61\x72\x21\x1A\x07\x01\x00", 8,   "rar"},
        {"\x50\x4B\x03\x04", 4,   "zip"},
        {"\x50\x4B\x05\x06", 4,   "zip"},
        {"\x50\x4B\x07\x08", 4,   "zip"},
        {"\x75\x73\x74\x61\x72\x00\x30\x30", 8,   "tar"},
        {"\x75\x73\x74\x61\x72\x20\x20\x00", 8,   "tar"},
        {"\x37\x7A\xBC\xAF\x27\x1C", 6,   "7Z"},
        {"\x1F\x9D", 2,   "tar.z"},
        {"\x1F\xA0", 2,   "tar.z"},
        {"\x42\x5A\x68", 3,   "bz2"},
        {"\xed\xab\xee\xdb", 4,   "rpm"},
        {"\x66\x74\x79\x70\x33\x67", 6,   "3gp"},
        {"\x5A\x4D", 2,   "exe"},
        {"\x30\x26\xB2\x75\x8E\x66\xCF\x11", 8,   "wma"},
        {"\xA6\xD9\x00\xAA\x00\x62\xCE\x6C", 8,   "wmv"},
        {"\xFF\xFB\x50", 3,   "mp3"},
        {"\xFF\xF3", 2,   "mp3"},
        {"\xFF\xF2", 2,   "mp3"},
        {"\x49\x44\x33", 3,   "mp3"},
        {"\x43\x44\x30\x30\x31", 5,   "ios"},
        {"\x66\x4C\x61\x43", 4,   "flac"},
        {"\x1A\x45\xDF\xA3", 4,   "mkv"},
        {NULL, 0,   NULL},
    };

    for(int i = 0; 0 != file_hdr[i].len; i++)
    {
        if(l >= file_hdr[i].len && 0 == memcmp(p, file_hdr[i].prefix, file_hdr[i].len))
        {
            return file_hdr[i].detail;
        }
    }
    return NULL;
}

const char *time_to_datetime(time_t t)
{
    static char time_str[64];
    struct tm tm_tmp;
    //t += 28800;  //东八区
    //gmtime_r(&t, &tm_tmp);

    int count=0;
    struct timeval tv;

    localtime_r(&t, &tm_tmp);
    /*
    snprintf(time_str, sizeof(time_str), "%04d%02d%02d%02d%02d%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
                        */
    strftime(time_str, sizeof(time_str), "%Y%m%d%H%M%S", &tm_tmp);

    return time_str;
}
const char *time_to_date(time_t t)
{
    static char time_str[64];
    struct tm tm_tmp;
    int count=0;
    struct timeval tv;

    localtime_r(&t, &tm_tmp);
    strftime(time_str, sizeof(time_str), "%Y%m%d", &tm_tmp);
    return time_str;
}

void split(char *src, const char *separator, char **dest, int *num)
{
    char *pNext;
    int count=0;
    if(src == NULL || strlen(src) == 0)
        return;

    if(separator==NULL || strlen(separator)==0)
        return;

    pNext=strtok(src, separator);
    while(pNext != NULL){
    	*dest++=pNext;
    	++count;
        if(count>COMMON_FILTER_NUM){
            *num=count;
            return;
        }
    	pNext=strtok(NULL, separator);
    }
    *num=count;
}

int bintohex(const unsigned char *i, size_t l, char *o, size_t s)
{
    static char hex2char[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    size_t  pos = 0;
    size_t  walk = 0;
    for(pos = 0; pos < l && walk < s; pos++)
    {
        unsigned char H = i[pos] >> 4;
        unsigned char L = i[pos] & 0x0F;
        o[walk++] = hex2char[H];
        o[walk++] = hex2char[L];
    }
    return walk;
}

int bintoMAC(const unsigned char *i, size_t l, char *o, size_t s)
{
    static char hex2char[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    size_t  pos = 0;
    size_t  walk = 0;
    for(pos = 0; pos < l && walk < s; pos++)
    {
        unsigned char H = i[pos] >> 4;
        unsigned char L = i[pos] & 0x0F;
        if(pos)
        {
            o[walk++] = ':';
        }
        o[walk++] = hex2char[H];
        o[walk++] = hex2char[L];
    }
    return walk;
}

int get_data_key_value(const char *ptr, int len, const char *key, char *end,char **value_ptr, int *value_len)
{
    if(!ptr || !key){
        return 0;
    }

    const char *p=NULL;
    // key作为第一个字节开始
    if(strncmp(ptr, key, strlen(key))==0){
      if (end) {
        p=memmem(ptr,len,end,strlen(end));
        if(!p){
            *value_len=len-strlen(key);
        }else{
            *value_len=p-ptr;
        }
        *value_ptr=(char*)ptr;
        return 1;
      } else {
        for(int i = 0;i< len;i++)
        if(*(ptr + i) == 0){
          *value_len = i;
          return 1;
        }
      }
    }

    const char *q=NULL;
    p=memmem(ptr,len,key,strlen(key));
    if(!p){
        return 0;
    }
    int start_len=p-ptr+strlen(key);
    int left_len=len-start_len;
    if(left_len<=0){
        return 0;
    }
    if (end) {
      q=memmem(p,left_len,end,strlen(end));
      if(!q){
          return 0;
      }

      *value_ptr  = (char*)&ptr[start_len];
      *value_len = q-*value_ptr;
      }else {
        for(int i = 0;i< len;i++)
          if(*(p + i) == 0){
            *value_len = i - strlen(key);
            *value_ptr  =(char*) &ptr[start_len];
            return 1;
          }
      }
      return 1;
}

// 字符串转大写
char * yv_strupr(char *str)
{
    char * origin = str;
    for (; *str != '\0'; str++)
        *str = toupper(*str);
    return origin;
}


// 字符串转小写
char * yv_strlowr(char * str)
{
    char * origin = str;
    for (; *str != '\0'; str++)
        *str= tolower(*str);
    return origin;
}

int hex2bin(const unsigned char *i, size_t l, unsigned char *o, size_t s)
{
    size_t  pos = 0;
    size_t  walk = 0;
    unsigned char v = 0;
    for(pos = 0; pos < l && walk/2 < s; pos++)
    {
        switch(i[pos])
        {
            case '-':
            case ':':
                continue;

            case 'A' ... 'Z':
                v |= i[pos] - 'A' +10;
                break;

            case 'a' ... 'z':
                v |= i[pos] - 'a' +10;
                break;

            case '0' ... '9':
                v |= i[pos] - '0';
                break;

            default:
                continue;
        }
        o[walk/2] = v;
        walk++;
        v <<= 4;
    }
    return walk/2;
}

bool dpi_pthread_setaffinity(pthread_t th, uint8_t core_id)
{
    int ret = 0;
    cpu_set_t cpuset;

    CPU_ZERO(&cpuset);
    CPU_SET(core_id, &cpuset);
    ret = pthread_setaffinity_np(th, sizeof(cpu_set_t), &cpuset);
    if (ret != 0) {
        return false;
    }

    return true;
}

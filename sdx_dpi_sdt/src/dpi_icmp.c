/****************************************************************************************
 * 文 件 名 : dpi_dns.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 设计: wangy       2018/07/06
 * 编码: wangy       2018/07/06
 * 修改: wangch      2019/10/12
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_mbuf.h>
#include <time.h>
#include <sys/time.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_common.h"
#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_dissector.h"
#include "dpi_pschema.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

enum icmp_index_em{
    EM_ICMP_VERSION,
    EM_ICMP_TYPE,
    EM_ICMP_CODE,
    EM_ICMP_CHECKSUM,
    EM_ICMP_CHECKSUM_REPLAY,
    EM_ICMP_PAYLOADLEN,
    EM_ICMP_PAYLOAD,
    EM_ICMP_SEQ,
    EM_ICMP_ORIGIN_TIME,
    EM_ICMP_RECEIVE_TIME,
    EM_ICMP_TRANSMIT_TIME,
    EM_ICMP_RESTIME,
    EM_ICMP_UNR_SRC,
    EM_ICMP_UNR_DST,
    EM_ICMP_UNR_PROTO,
    EM_ICMP_UNR_TTL,
    EM_ICMP_RTR_ADDR,
    EM_ICMP_MASK,
    EM_ICMP_SUBNET_ID,
    EM_ICMP_RTR_TIMEOUT,
    EM_ICMP_QUR_DNS,
    EM_ICMP_QUR_TYPE,
    EM_ICMP_QUR_IP4,
    EM_ICMP_QUR_IP6,
    EM_ICMP_LINKADDR,
    EM_ICMP_T_LINKADDR,
    EM_ICMP_LIFETIME,
    EM_ICMP_VALIDTIME,
    EM_ICMP_CUR_MTU,
    EM_ICMP_PREFIX_LEN,
    EM_ICMP_PREFIX,

    EM_ICMP_GATEWAY_ADDR,
    EM_ICMP_TTL,

    EM_ICMP_EXC_SRCADDR,
    EM_ICMP_EXC_DSTADDR,
    EM_ICMP_EXC_PROTO,
    EM_ICMP_EXC_SRCPORT,
    EM_ICMP_EXC_DSTPORT,


    EM_ICMP_NEXT_HOP_MTU,
    EM_ICMP_EXC_POINTER,
    EM_ICMP_MUL_CAST_ADDR,

    EM_ICMP_MAX
};


/* ICMP definitions */
#define ICMP_ECHOREPLY     0
#define ICMP_UNREACH       3
#define ICMP_SOURCEQUENCH  4
#define ICMP_REDIRECT      5
#define ICMP_ALTHOST       6
#define ICMP_ECHO          8
#define ICMP_RTRADVERT     9
#define ICMP_RTRSOLICIT   10
#define ICMP_TIMXCEED     11
#define ICMP_PARAMPROB    12
#define ICMP_TSTAMP       13
#define ICMP_TSTAMPREPLY  14
#define ICMP_IREQ         15
#define ICMP_IREQREPLY    16
#define ICMP_MASKREQ      17
#define ICMP_MASKREPLY    18
#define ICMP_PHOTURIS     40


/* ICMP UNREACHABLE */
#define ICMP_NET_UNREACH         0    /* Network Unreachable */
#define ICMP_HOST_UNREACH        1    /* Host Unreachable */
#define ICMP_PROT_UNREACH        2    /* Protocol Unreachable */
#define ICMP_PORT_UNREACH        3    /* Port Unreachable */
#define ICMP_FRAG_NEEDED         4    /* Fragmentation Needed/DF set */
#define ICMP_SR_FAILED           5    /* Source Route failed */
#define ICMP_NET_UNKNOWN         6
#define ICMP_HOST_UNKNOWN        7
#define ICMP_HOST_ISOLATED       8
#define ICMP_NET_ANO             9
#define ICMP_HOST_ANO           10
#define ICMP_NET_UNR_TOS        11
#define ICMP_HOST_UNR_TOS       12
#define ICMP_PKT_FILTERED       13    /* Packet filtered */
#define ICMP_PREC_VIOLATION     14    /* Precedence violation */
#define ICMP_PREC_CUTOFF        15    /* Precedence cut off */


#define ICMP6_DST_UNREACH                 1
#define ICMP6_PACKET_TOO_BIG              2
#define ICMP6_TIME_EXCEEDED               3
#define ICMP6_PARAM_PROB                  4
#define ICMP6_ECHO_REQUEST              128
#define ICMP6_ECHO_REPLY                129
#define ICMP6_MEMBERSHIP_QUERY          130
#define ICMP6_MEMBERSHIP_REPORT         131
#define ICMP6_MEMBERSHIP_REDUCTION      132
#define ICMP6_ND_ROUTER_SOLICIT         133
#define ICMP6_ND_ROUTER_ADVERT          134
#define ICMP6_ND_NEIGHBOR_SOLICIT       135
#define ICMP6_ND_NEIGHBOR_ADVERT        136
#define ICMP6_ND_REDIRECT               137
#define ICMP6_ROUTER_RENUMBERING        138
#define ICMP6_NI_QUERY                  139
#define ICMP6_NI_REPLY                  140
#define ICMP6_IND_SOLICIT               141
#define ICMP6_IND_ADVERT                142
#define ICMP6_MLDV2_REPORT              143
#define ICMP6_MIP6_DHAAD_REQUEST        144
#define ICMP6_MIP6_DHAAD_REPLY          145
#define ICMP6_MIP6_MPS                  146
#define ICMP6_MIP6_MPA                  147
#define ICMP6_CERT_PATH_SOL             148
#define ICMP6_CERT_PATH_AD              149
#define ICMP6_EXPERIMENTAL_MOBILITY     150
#define ICMP6_MCAST_ROUTER_ADVERT       151
#define ICMP6_MCAST_ROUTER_SOLICIT      152
#define ICMP6_MCAST_ROUTER_TERM         153
#define ICMP6_FMIPV6_MESSAGES           154
#define ICMP6_RPL_CONTROL               155
#define ICMP6_ILNPV6                    156
#define ICMP6_6LOWPANND_DAR             157
#define ICMP6_6LOWPANND_DAC             158
#define ICMP6_MPL_CONTROL               159


static dpi_field_table icmp_field_array[] = {
    DPI_FIELD_D(EM_ICMP_VERSION,            YA_FT_UINT8,        "version"),
    DPI_FIELD_D(EM_ICMP_TYPE,               YA_FT_UINT8,        "type"),
    DPI_FIELD_D(EM_ICMP_CODE,               YA_FT_UINT8,        "code"),
    DPI_FIELD_D(EM_ICMP_CHECKSUM,           YA_FT_UINT16,       "checksum"),
    DPI_FIELD_D(EM_ICMP_CHECKSUM_REPLAY,    YA_FT_UINT16,       "checksum_replay"),
    DPI_FIELD_D(EM_ICMP_PAYLOADLEN,         YA_FT_UINT16,       "payload_len"),
    DPI_FIELD_D(EM_ICMP_PAYLOAD,            YA_FT_STRING,       "payload"),
    DPI_FIELD_D(EM_ICMP_SEQ,                YA_FT_UINT16,       "seq"),
    DPI_FIELD_D(EM_ICMP_ORIGIN_TIME,        YA_FT_STRING,       "origin_time"),
    DPI_FIELD_D(EM_ICMP_RECEIVE_TIME,       YA_FT_STRING,       "receive_time"),
    DPI_FIELD_D(EM_ICMP_TRANSMIT_TIME,      YA_FT_STRING,       "transmit_time"),
    DPI_FIELD_D(EM_ICMP_RESTIME,            YA_FT_UINT64,       "response_time"),
    DPI_FIELD_D(EM_ICMP_UNR_SRC,            YA_FT_STRING,       "unr_srcip"),
    DPI_FIELD_D(EM_ICMP_UNR_DST,            YA_FT_STRING,       "unr_dstip"),
    DPI_FIELD_D(EM_ICMP_UNR_PROTO,          YA_FT_UINT8,        "unr_proto"),
    DPI_FIELD_D(EM_ICMP_UNR_TTL,            YA_FT_UINT8,        "unr_ttl"),



    DPI_FIELD_D(EM_ICMP_RTR_ADDR,           YA_FT_STRING,       "rtraddr"),
    DPI_FIELD_D(EM_ICMP_MASK,               YA_FT_STRING,       "mask"),
    DPI_FIELD_D(EM_ICMP_SUBNET_ID,          YA_FT_UINT32,       "subnetId"),
    DPI_FIELD_D(EM_ICMP_RTR_TIMEOUT,        YA_FT_UINT32,       "rtrTimeOut"),
    DPI_FIELD_D(EM_ICMP_QUR_DNS,            YA_FT_STRING,       "qurDNS"),
    DPI_FIELD_D(EM_ICMP_QUR_TYPE,           YA_FT_STRING,       "qurType"),
    DPI_FIELD_D(EM_ICMP_QUR_IP4,            YA_FT_STRING,       "qurIP4"),
    DPI_FIELD_D(EM_ICMP_QUR_IP6,            YA_FT_STRING,       "qurIP6"),
    DPI_FIELD_D(EM_ICMP_LINKADDR,           YA_FT_STRING,       "linkaddr"),
    DPI_FIELD_D(EM_ICMP_T_LINKADDR,         YA_FT_STRING,       "targetlinkaddr"),
    DPI_FIELD_D(EM_ICMP_LIFETIME,           YA_FT_UINT64,       "lifetime"),
    DPI_FIELD_D(EM_ICMP_VALIDTIME,          YA_FT_UINT64,       "validtime"),
    DPI_FIELD_D(EM_ICMP_CUR_MTU,            YA_FT_UINT64,       "curMtu"),
    DPI_FIELD_D(EM_ICMP_PREFIX_LEN,         YA_FT_UINT64,       "prelen"),
    DPI_FIELD_D(EM_ICMP_PREFIX,             YA_FT_STRING,       "prefix"),
    DPI_FIELD_D(EM_ICMP_GATEWAY_ADDR,       YA_FT_STRING,       "gatewayAddr"),

    DPI_FIELD_D(EM_ICMP_TTL,                YA_FT_UINT8,        "ttl"),


    DPI_FIELD_D(EM_ICMP_EXC_SRCADDR,        YA_FT_STRING,       "exc_srcaddr"),
    DPI_FIELD_D(EM_ICMP_EXC_DSTADDR,        YA_FT_STRING,       "exc_dstaddr"),
    DPI_FIELD_D(EM_ICMP_EXC_PROTO,          YA_FT_UINT64,       "exc_proto"),
    DPI_FIELD_D(EM_ICMP_EXC_SRCPORT,        YA_FT_UINT64,       "exc_srcport"),
    DPI_FIELD_D(EM_ICMP_EXC_DSTPORT,        YA_FT_UINT64,       "exc_dstport"),

    DPI_FIELD_D(EM_ICMP_NEXT_HOP_MTU,       YA_FT_UINT32,       "next_hop_mtu"),
    DPI_FIELD_D(EM_ICMP_EXC_POINTER,        YA_FT_STRING,       "exc_pointer"),
    DPI_FIELD_D(EM_ICMP_MUL_CAST_ADDR,      YA_FT_STRING,       "mul_cast_addr"),



};



struct icmp_info{
    uint8_t type;
    uint8_t code;
    uint16_t seq;
    uint16_t checksum;
    uint16_t payload_len;
    uint32_t origin_time;
    uint32_t receive_time;
    uint32_t transmit_time;


    uint64_t res_time;
    uint32_t mtu;
    uint32_t lifetime;
    uint32_t validtime;
    uint8_t  prefix_len;
    const uint8_t *prefix;
    const uint8_t *linkaddr;
    const uint8_t *gateway_addr;
    const uint8_t *targetlinkaddr;
    const uint8_t *payload;

    int  ttl;

    uint8_t  query_flag;
    uint8_t  query_type;
    char     query_ip[COMMON_IP_MAX_SIZE];

    const struct dpi_iphdr   *inner;          //unreachable and timeout ipv4
    const struct dpi_iphdr   *inner_unreach;  //unreachable ipv4

    const struct dpi_ipv6hdr *inner6;         //unreachable and timeout ipv6
    const struct dpi_ipv6hdr *inner6_unreach; //unreachable ipv6

    uint32_t next_hop_mtu;
    uint8_t  exc_pointer_flag;
    uint32_t exc_pointer;
    const uint8_t *mul_cast_addr;
    uint16_t  mul_cast_addr_len;
};

static int write_icmp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int i;
    int idx = 0;
    char __str[64];
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "icmp");

    struct icmp_info *info=(struct icmp_info *)field_info;

    for(i=0; i != EM_ICMP_MAX; ++i)
    {
        switch(i){
        case EM_ICMP_VERSION:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->ip_version);
            break;
        case EM_ICMP_TYPE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->type);
            break;
        case EM_ICMP_CODE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->code);
            break;
        case EM_ICMP_CHECKSUM:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->checksum);
            break;
        //case EM_ICMP_CHECKSUM_REPLAY:
        //    break;

        case EM_ICMP_PAYLOADLEN:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->payload_len);
            break;
        case EM_ICMP_PAYLOAD:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->payload, info->payload_len);
            break;
        case EM_ICMP_SEQ:
            if(info->payload)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->seq);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_ORIGIN_TIME:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->origin_time);
            break;
        case EM_ICMP_RECEIVE_TIME:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->receive_time);
            break;
        case EM_ICMP_TRANSMIT_TIME:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->transmit_time);
            break;
        case EM_ICMP_RESTIME:
            if(info->res_time>0)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->res_time);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;




        case EM_ICMP_UNR_SRC:
            if(info->inner){
                get_iparray_to_string(__str, 64, info->inner->saddr);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else if(info->inner6_unreach){
                get_ip6string(__str, 64, info->inner6_unreach->ip6_src);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_UNR_DST:
            if(info->inner_unreach){
                get_iparray_to_string(__str, 64, info->inner_unreach->daddr);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else if(info->inner6_unreach){
                get_ip6string(__str, 64, info->inner6_unreach->ip6_dst);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_UNR_PROTO:
            if(info->inner_unreach)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->inner->protocol);
            else if(info->inner6_unreach)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->inner6_unreach->ip6_ctlun.ip6_un1.ip6_un1_nxt);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_UNR_TTL:
            if (info->inner_unreach)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->inner_unreach->ttl);
            else if(info->inner6_unreach)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->inner6_unreach->ip6_ctlun.ip6_un1.ip6_un1_hlim);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;

        //case EM_ICMP_RTR_ADDR:
        //    break;
        //case EM_ICMP_MASK:
        //    break;
        /*
        case EM_ICMP_SUBNET_ID:
        case EM_ICMP_RTR_TIMEOUT:
        case EM_ICMP_QUR_DNS:
        */

        case EM_ICMP_QUR_TYPE:
            if(1==info->query_flag)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->query_type);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_QUR_IP4:
            if(1==info->query_flag && 4==flow->ip_version)
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,info->query_ip, strlen(info->query_ip));
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_QUR_IP6:
            if(1==info->query_flag && 6==flow->ip_version)
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,info->query_ip, strlen(info->query_ip));
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;


        case EM_ICMP_LINKADDR:
            if (info->linkaddr)
                write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->linkaddr, info->linkaddr ? 6 : 0);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_T_LINKADDR:
            if (info->targetlinkaddr)
                write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->targetlinkaddr, info->targetlinkaddr ? 6 : 0);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_VALIDTIME:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->validtime);
            break;
        case EM_ICMP_LIFETIME:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->lifetime);
            break;
        case EM_ICMP_CUR_MTU:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->mtu);
            break;
        case EM_ICMP_PREFIX_LEN:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->prefix_len);
            break;
        case EM_ICMP_PREFIX:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->prefix, info->prefix ? 16 : 0);
            break;
        case EM_ICMP_GATEWAY_ADDR:
            if(info->gateway_addr){
                get_iparray_to_string(__str, 64, info->gateway_addr);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;

        case EM_ICMP_TTL:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ttl);

            break;


        case EM_ICMP_EXC_SRCADDR:
            if(info->inner){
                get_iparray_to_string(__str, 64, info->inner->saddr);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else if(info->inner6){
                get_iparray_to_string(__str, sizeof(__str), info->inner6->ip6_src);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_EXC_DSTADDR:
            if(info->inner){
                get_iparray_to_string(__str, 64, info->inner->daddr);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else if(info->inner6){
                get_iparray_to_string(__str, sizeof(__str), info->inner6->ip6_dst);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_EXC_PROTO:
            if(info->inner){
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->inner->protocol);
            }else if(info->inner6){
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->inner6->ip6_ctlun.ip6_un1.ip6_un1_nxt);
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_EXC_SRCPORT:
            if(info->inner){
                if(IPPROTO_UDP==info->inner->protocol || IPPROTO_TCP==info->inner->protocol){
                    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, get_uint16_ntohs((uint8_t*)(info->inner), info->inner->ihl*4));
                }else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
            }else if(info->inner6){
                if( IPPROTO_TCP==info->inner6->ip6_ctlun.ip6_un1.ip6_un1_nxt ||
                    IPPROTO_UDP==info->inner6->ip6_ctlun.ip6_un1.ip6_un1_nxt){
                    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, get_uint16_ntohs((uint8_t*)(info->inner6), sizeof(struct dpi_ipv6hdr)));
                }else{
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_EXC_DSTPORT:
            if(info->inner){
                if(IPPROTO_UDP==info->inner->protocol || IPPROTO_TCP==info->inner->protocol){
                    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, get_uint16_ntohs((uint8_t*)(info->inner), info->inner->ihl*4+2));
                }else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
            }else if(info->inner6){
                if( IPPROTO_TCP==info->inner6->ip6_ctlun.ip6_un1.ip6_un1_nxt ||
                    IPPROTO_UDP==info->inner6->ip6_ctlun.ip6_un1.ip6_un1_nxt){
                    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, get_uint16_ntohs((uint8_t*)(info->inner6),  sizeof(struct dpi_ipv6hdr)+2));
                }else{
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
            }else{
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_ICMP_NEXT_HOP_MTU:
            if(info->next_hop_mtu>0)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->next_hop_mtu);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_EXC_POINTER:
            if(1==info->exc_pointer_flag)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->exc_pointer);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_ICMP_MUL_CAST_ADDR:
            if(info->mul_cast_addr)
                write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->mul_cast_addr, info->mul_cast_addr_len);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        default:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_ICMP;
    log_ptr->log_len = idx;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;

}




static int dissect_icmpv6_option(const uint8_t *payload, const uint32_t payload_len, struct icmp_info *info)
{
    uint32_t offset = 0;

    while(offset + 8 <= payload_len)
    {
        uint8_t  type = payload[offset];
        uint32_t len  = payload[offset + 1] * 8;

        if(offset + len > payload_len)
            return 0;

        switch(type)
        {
          case 1: //Source Link-Layer Address
            if(len == 8)
                info->linkaddr = payload + offset + 2;
            break;

          case 2: //Target Link-Layer Address
            if(len == 8)
                info->targetlinkaddr = payload + offset + 2;
            break;

          case 3: //prefix info
            if(len == 32)
            {
                info->prefix_len = payload[offset + 2];
                info->validtime  = get_uint32_ntohl(payload, offset + 4);
                info->prefix     = payload + offset + 16;
            }
            break;

          case 4: //MTU
            if(len == 8)
                info->mtu = get_uint32_ntohl(payload, offset + 4);
            break;
        }

        offset += len;
    }

    return 0;
}


/*
#define ICMP6_DST_UNREACH                 1
#define ICMP6_PACKET_TOO_BIG              2
#define ICMP6_TIME_EXCEEDED               3
#define ICMP6_PARAM_PROB                  4
#define ICMP6_ECHO_REQUEST              128
#define ICMP6_ECHO_REPLY                129
#define ICMP6_MEMBERSHIP_QUERY          130
#define ICMP6_MEMBERSHIP_REPORT         131
#define ICMP6_MEMBERSHIP_REDUCTION      132
#define ICMP6_ND_ROUTER_SOLICIT         133
#define ICMP6_ND_ROUTER_ADVERT          134
#define ICMP6_ND_NEIGHBOR_SOLICIT       135
#define ICMP6_ND_NEIGHBOR_ADVERT        136
#define ICMP6_ND_REDIRECT               137
#define ICMP6_ROUTER_RENUMBERING        138
#define ICMP6_NI_QUERY                  139
#define ICMP6_NI_REPLY                  140
#define ICMP6_IND_SOLICIT               141
#define ICMP6_IND_ADVERT                142
#define ICMP6_MLDV2_REPORT              143
#define ICMP6_MIP6_DHAAD_REQUEST        144
#define ICMP6_MIP6_DHAAD_REPLY          145
#define ICMP6_MIP6_MPS                  146
#define ICMP6_MIP6_MPA                  147
#define ICMP6_CERT_PATH_SOL             148
#define ICMP6_CERT_PATH_AD              149
#define ICMP6_EXPERIMENTAL_MOBILITY     150
#define ICMP6_MCAST_ROUTER_ADVERT       151
#define ICMP6_MCAST_ROUTER_SOLICIT      152
#define ICMP6_MCAST_ROUTER_TERM         153
#define ICMP6_FMIPV6_MESSAGES           154
#define ICMP6_RPL_CONTROL               155
#define ICMP6_ILNPV6                    156
#define ICMP6_6LOWPANND_DAR             157
#define ICMP6_6LOWPANND_DAC             158
#define ICMP6_MPL_CONTROL               159
*/
static int dpi_dissect_icmpv6(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(payload_len < 8)
        return PKT_DROP;

    struct icmp_info info;
    bzero(&info, sizeof(struct icmp_info));

    info.type = payload[0];
    info.code = payload[1];
    info.checksum = get_uint16_ntohs(payload, 2);

    uint32_t offset = 8;

    const struct pkt_info *pkt=flow->pkt;
    info.ttl=-1;
    if(pkt && pkt->iph6){
        info.ttl=pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim;
    }else{
        return PKT_DROP;
    }

    switch(info.type)
    {
        case ICMP6_DST_UNREACH:    // 1
            if(offset + sizeof(struct dpi_ipv6hdr) <= payload_len)
            {
                info.inner6_unreach = (const struct dpi_ipv6hdr*)payload + offset;
            }
            break;
        case ICMP6_PACKET_TOO_BIG: // 2
            if(0==info.code){
                info.next_hop_mtu=get_uint32_ntohl(payload, 4);
            }
            break;
        case ICMP6_TIME_EXCEEDED:  // 3
            if(offset + sizeof(struct dpi_ipv6hdr) <= payload_len)
            {
                info.inner6_unreach = (const struct dpi_ipv6hdr*)payload + offset;
                info.inner6 = (const struct dpi_ipv6hdr*)payload + offset;
            }
            break;
        case ICMP6_PARAM_PROB:     // 4
            info.exc_pointer_flag=1;
            info.exc_pointer=get_uint32_ntohl(payload, 4);
            break;
        case ICMP6_ECHO_REQUEST:   // 128
        case ICMP6_ECHO_REPLY:     // 129
            info.query_flag = 1;
            info.query_type=info.type;
            get_ip6string(info.query_ip, COMMON_IP_MAX_SIZE, (const uint8_t *)pkt->iph6->ip6_dst);
            break;
        case ICMP6_MEMBERSHIP_QUERY:     // 130 group membership query
        case ICMP6_MEMBERSHIP_REPORT:    // 131 group membership report
        case ICMP6_MEMBERSHIP_REDUCTION: // 132 group membership reduction
            if(payload_len>8){
                info.mul_cast_addr=&payload[8];
                info.mul_cast_addr_len=payload_len-8;
            }
            break;
        case ICMP6_ND_ROUTER_ADVERT:     // 134
            if(offset + 8 <= payload_len)
            {
                info.lifetime = get_uint16_ntohs(payload, 6);
                dissect_icmpv6_option(payload + offset + 8, payload_len - offset - 8, &info);
            }
            break;

        case ICMP6_ND_NEIGHBOR_ADVERT:   // 136
            if(offset + 8 <= payload_len)
            {
                info.gateway_addr = payload + 8;
            }
            break;

        default:
            break;
    }

    write_icmp_log(flow, direction, &info, NULL);

    return PKT_OK;
}

int dpi_dissect_icmp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(g_config.protocol_switch[PROTOCOL_ICMP] == 0)
        return PKT_OK;

    if(flow->ip_version == 6)
    {
        return dpi_dissect_icmpv6(flow, direction, payload, payload_len, flag);
    }

    if(flow->ip_version != 4)
        return PKT_OK;

    if(payload_len < 8)
        return PKT_DROP;

    int offset=0;
    struct icmp_info info;
    bzero(&info, sizeof(struct icmp_info));

    info.type = payload[0];
    info.code = payload[1];
    info.payload_len = payload_len - 8;
    info.checksum    = get_uint16_ntohs(payload, 2);
    offset+=4;

    const struct pkt_info *pkt=flow->pkt;
    info.ttl=-1;
    if(pkt && pkt->iph4){
        info.ttl=pkt->iph4->ttl;
    }else{
        return PKT_DROP;
    }

    switch(info.type)
    {
    case ICMP_ECHOREPLY:  // 0
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        uint64_t now_time=(uint64_t)tv.tv_sec * 1000000 + tv.tv_usec;
        if(now_time>flow->time_tmp)
            info.res_time=now_time-flow->time_tmp;
    }
    case ICMP_ECHO:       // 8
        info.seq = get_uint16_t(payload, 6);
        info.payload = payload + 8;

        info.query_flag = 1;
        info.query_type = info.type;
        get_ip4string(info.query_ip, COMMON_IP_MAX_SIZE, get_uint32_t((const uint8_t *)pkt->iph4->daddr, 0));
        if(ICMP_ECHO==info.type)
            flow->time_tmp=g_config.g_now_time_usec;
        break;

    case ICMP_UNREACH:    // 3
        info.inner_unreach = (const struct dpi_iphdr*) &payload[8];
        info.inner = (const struct dpi_iphdr*) &payload[8];
        break;

    case ICMP_REDIRECT:   // 5
        info.gateway_addr=&payload[offset];

    case ICMP_TIMXCEED:   // 11
        info.inner = (const struct dpi_iphdr*) &payload[8];
        break;

    case ICMP_PARAMPROB:  // 12
        if(0==info.code){
            info.exc_pointer=get_uint8_t(payload, 4);
        }
        break;
    case ICMP_TSTAMP:
    case ICMP_TSTAMPREPLY:
        info.origin_time = get_uint32_ntohl(payload, 8);
        info.receive_time = get_uint32_ntohl(payload, 12);
        info.transmit_time = get_uint32_ntohl(payload, 16);
        break;

    default:
        break;
    }

    write_icmp_log(flow, direction, &info, NULL);

    return PKT_OK;
}

static void init_icmp_dissector(void)
{
    dpi_register_proto_schema((dpi_field_table*)icmp_field_array,   EM_ICMP_MAX,   "icmp");
    map_fields_info_register(icmp_field_array, PROTOCOL_ICMP, EM_ICMP_MAX, "icmp");
    pschema_t *schema = dpi_pschema_get_proto("icmp");
    pschema_register_field(schema, "mulCastAddr", YA_FT_STRING, "desc");
    pschema_register_field(schema, "pointer", YA_FT_STRING, "desc");
    pschema_register_field(schema, "mtu", YA_FT_STRING, "desc");
    return;
}

static __attribute((constructor)) void    before_init_icmp(void){
    register_tbl_array(TBL_LOG_ICMP, 0, "icmp", init_icmp_dissector);
}


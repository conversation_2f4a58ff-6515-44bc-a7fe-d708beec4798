#ifndef _DPI_SDT_IPP_H_
#define _DPI_SDT_IPP_H_



enum _ipp_index_e{
    ENUM_IPP_VLAN1,
    ENUM_IPP_VLAN2,
    ENUM_IPP_MPLS1,
    ENUM_IPP_MPLS2,
    ENUM_IPP_SRCMAX,
    ENUM_IPP_DSTMAX,


    ENUM_IPP_PROTNUM,
    ENUM_IPP_SRCADDR,
    ENUM_IPP_DSTADDR,
    ENUM_IPP_INNSRCADDR,
    ENUM_IPP_INNDSTADDR,

    ENUM_IPP_ADDRTYPE,
    ENUM_IPP_INNADDRTYPE,
    
    ENUM_IPP_SRCPORT,
    ENUM_IPP_DSTPORT,
    ENUM_IPP_VER,
    ENUM_IPP_IPFLAG,
    ENUM_IPP_TOTLEN,
    ENUM_IPP_CONTENT,
    ENUM_IPP_PAYLEN,
    ENUM_IPP_HEADER,
    
    ENUM_IPP_UDPPAY,
    ENUM_IPP_UDPPAYLEN,
    ENUM_IPP_UDPHEADER,
    
    ENUM_IPP_TCPFLAG,
    ENUM_IPP_TCPFLAGFIN,
    ENUM_IPP_TCPFLAGSYN,
    ENUM_IPP_TCPFLAGRST,
    ENUM_IPP_TCPFLAGPSH,
    ENUM_IPP_TCPFLAGACK,
    ENUM_IPP_TCPFLAGURG,
    ENUM_IPP_TCPFLAGECE,
    ENUM_IPP_TCPFLAGCWR,
    ENUM_IPP_TCPFLAGNS,

    
    ENUM_IPP_TCPWINS,
    ENUM_IPP_TCPPAY,
    ENUM_IPP_TCPPAYLEN,
    ENUM_IPP_TCPHEADER,


    ENUM_IPP_MAX
};





int 
dpi_sdt_packet_ipp_match(struct flow_info *flow, const struct pkt_info  *pkt, ProtoRecord *pRec);




void 
dpi_sdt_init_ipp_dissector(void);


#endif


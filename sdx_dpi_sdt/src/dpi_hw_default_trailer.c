#include "dpi_trailer.h"
#include <stdio.h>

static int transfer_tlv_str_type(uint8_t type)
{
	switch(type){
		case 0x02: return EM_HW_ACCOUNT;
		case 0x03: return EM_HW_MSISDN;
		case 0x04: return EM_HW_IMEI;
		case 0x05: return EM_HW_ESN;
		case 0x06: return EM_HW_MEID;
		case 0x12: return EM_HW_BSID;
	}
	return 0;
}

static int transfer_tlv_short_type(uint8_t type)
{
	switch(type){
		case 0x07: return EM_HW_LAC;
		case 0x08: return EM_HW_SAC;
		case 0x09: return EM_HW_CI;
		case 0x10: return EM_HW_TAC;
		case 0x14: return EM_HW_TAI;
		case 0x15: return EM_HW_MNC;
	}
	return 0;
}

static int transfer_tlv_long_type(uint8_t type)
{
	switch(type){
		case 0x11: return EM_HW_ECGI;
		case 0x13: return EM_HW_GRE_KEY;
		case 0x16: return EM_HW_TEID;
		case 0x17: return EM_HW_GTP_SIP;
		case 0x18: return EM_HW_GTP_DIP;
	}
	return 0;
}

static int get_tlv_data(struct hw_default_trailer *trailer, const uint8_t *payload, uint8_t offset, uint8_t len, int is_jsd)
{
	uint8_t type   = payload[offset];
	uint8_t length = payload[offset+1];
	int field;

	if(length + offset + 2 > len)
		return -1;

	switch(type){
		case 0x01:
			if(length != 8)
				return -1;
			trailer->imsi = be64toh(get_uint64_t(payload, offset+2));
			break;
		case 0x02:
		case 0x03:
		case 0x04:
		case 0x05:
		case 0x06:
		case 0x12:
			field = transfer_tlv_str_type(type);
			memcpy(trailer->tlv_str[field].str, payload+offset+2, (trailer->tlv_str[field].len = min(TLV_MAX_LENGTH, length)));
			break;
		case 0x07:
		case 0x08:
		case 0x09:
		case 0x10:
		case 0x14:
		case 0x15:
			if(length != 2)
				return -1;
			field = transfer_tlv_short_type(type);
			trailer->tlv_short[field] = get_uint16_ntohs(payload, offset+2);
			break;
		case 0x11:
		case 0x16:
		case 0x17:
		case 0x18:
			if(length != 4)
				return -1;
			field = transfer_tlv_long_type(type);
			trailer->tlv_long[field] = get_uint32_ntohl(payload, offset+2);
			break;
        case 0x20:
            if(length <= 32 && is_jsd){
                memcpy(trailer->RSClueid, payload + offset + 2, length);
                trailer->RSClueid[length] = 0;
            }

            break;
        case 0x21:
            if(length <= 32){
                char *tmp = is_jsd ? trailer->BidClueid : trailer->apn;
                memcpy(tmp, payload + offset + 2, length);
                tmp[length] = 0;
            }
            break;
		default:
			return 0;
	}
	return 0;
}

void parse_hw_default_trailer(struct hw_default_trailer *trailer, const uint8_t *payload, uint16_t payload_len, int is_jsd)
{
	if(payload_len < 2 || payload[0] != 0x10)
		return;

	uint8_t offset = 4;
	uint8_t len = payload[1];

	if(len < 2 || len + 2 > payload_len)
		return;

    len += 2; //type, len

	trailer->nsp   = payload[2];
	trailer->base  = payload[3];
    if(is_jsd && len > 3){
        trailer->data_type = payload[4];
        offset += 1;
    }

	while(offset+2  <  len){
		if(get_tlv_data(trailer, payload, offset, len, is_jsd) < 0)
			break;
		else
			offset += payload[offset+1] + 2;
	}

    //printf("RS: %s, Bid: %s\n", trailer->RSClueid, trailer->BidClueid); 
	return;
}

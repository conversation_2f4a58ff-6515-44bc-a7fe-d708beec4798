/****************************************************************************************
 * 文 件 名 : dpi_tds.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/09/14
编码: wangy            2018/09/14
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>

#include "dpi_pint.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_sdp.h"
#include "dpi_dissector.h"
#include "charsets.h"

extern struct rte_mempool *tbl_log_mempool;
extern dpi_field_table dbbasic_field_array[];

enum tids_index_em{
    EM_TDS_PACKETTYPE,
    EM_TDS_CHANNEL,
    EM_TDS_PACKETNUMBER,
    EM_TDS_CLIENTNAME,
    EM_TDS_USERNAME,
    EM_TDS_PASSWORD,
    EM_TDS_LOGIN_STATUS,
    EM_TDS_APPNAME,
    EM_TDS_SERVERNAME,
    EM_TDS_LIBRARYNAME,
    EM_TDS_LOCALE,
    EM_TDS_DATABASENAME,
    EM_TDS_WINDOW,
    EM_TDS_TDSVERSION,
    EM_TDS_CLIENTVERSION,
    EM_TDS_CLIENTPID,
    EM_TDS_CONNECTIONID,
    EM_TDS_OPTION_FLAGS1,
    EM_TDS_OPTION_FLAGS2,
    EM_TDS_RESERVED_FLAGS,
    EM_TDS_SQL_TYPE_FLAGS,
    EM_TDS_TIMEZONE,
    EM_TDS_COLLATION,
    EM_TDS_QUERY,
    EM_TDS_LANGUAGE_TEXT,
    EM_TDS_NTLMSSP,
    EM_TDS_GSSAPI,
    EM_TDS_TOKEN_00_VALUE,
    EM_TDS_TOKEN_00_NAME,
    EM_TDS_TOKEN_00_CONTENT,
    EM_TDS_TOKEN_01_VALUE,
    EM_TDS_TOKEN_01_NAME,
    EM_TDS_TOKEN_01_CONTENT,
    EM_TDS_TOKEN_02_VALUE,
    EM_TDS_TOKEN_02_NAME,
    EM_TDS_TOKEN_02_CONTENT,
    EM_TDS_TOKEN_03_VALUE,
    EM_TDS_TOKEN_03_NAME,
    EM_TDS_TOKEN_03_CONTENT,
    EM_TDS_TOKEN_04_VALUE,
    EM_TDS_TOKEN_04_NAME,
    EM_TDS_TOKEN_04_CONTENT,
    EM_TDS_TOKEN_05_VALUE,
    EM_TDS_TOKEN_05_NAME,
    EM_TDS_TOKEN_05_CONTENT,
    EM_TDS_TOKEN_06_VALUE,
    EM_TDS_TOKEN_06_NAME,
    EM_TDS_TOKEN_06_CONTENT,
    EM_TDS_TOKEN_07_VALUE,
    EM_TDS_TOKEN_07_NAME,
    EM_TDS_TOKEN_07_CONTENT,
    EM_TDS_TOKEN_08_VALUE,
    EM_TDS_TOKEN_08_NAME,
    EM_TDS_TOKEN_08_CONTENT,
    EM_TDS_TOKEN_09_VALUE,
    EM_TDS_TOKEN_09_NAME,
    EM_TDS_TOKEN_09_CONTENT,
    EM_TDS_TOKEN_10_VALUE,
    EM_TDS_TOKEN_10_NAME,
    EM_TDS_TOKEN_10_CONTENT,
    EM_TDS_TOKEN_11_VALUE,
    EM_TDS_TOKEN_11_NAME,
    EM_TDS_TOKEN_11_CONTENT,
    EM_TDS_TOKEN_12_VALUE,
    EM_TDS_TOKEN_12_NAME,
    EM_TDS_TOKEN_12_CONTENT,
    EM_TDS_TOKEN_13_VALUE,
    EM_TDS_TOKEN_13_NAME,
    EM_TDS_TOKEN_13_CONTENT,
    EM_TDS_TOKEN_14_VALUE,
    EM_TDS_TOKEN_14_NAME,
    EM_TDS_TOKEN_14_CONTENT,
    EM_TDS_TOKEN_15_VALUE,
    EM_TDS_TOKEN_15_NAME,
    EM_TDS_TOKEN_15_CONTENT,
    EM_TDS_PROCEDURE_00_NAME,
    EM_TDS_STOREDPROCEDUREID_00,
    EM_TDS_PROCEDURE_00_PARAMETERS,
    EM_TDS_PROCEDURE_01_NAME,
    EM_TDS_STOREDPROCEDUREID_01,
    EM_TDS_PROCEDURE_01_PARAMETERS,
    EM_TDS_PROCEDURE_02_NAME,
    EM_TDS_STOREDPROCEDUREID_02,
    EM_TDS_PROCEDURE_02_PARAMETERS,
    EM_TDS_PROCEDURE_03_NAME,
    EM_TDS_PROCEDURE_03_STORED_PROCEDURE_ID,
    EM_TDS_PROCEDURE_03_PARAMETERS,
    EM_TDS_PROCEDURE_04_NAME,
    EM_TDS_STOREDPROCEDUREID_04,
    EM_TDS_PROCEDURE_04_PARAMETERS,
    EM_TDS_PROCEDURE_05_NAME,
    EM_TDS_STOREDPROCEDUREID_05,
    EM_TDS_PROCEDURE_05_PARAMETERS,
    EM_TDS_PROCEDURE_06_NAME,
    EM_TDS_STOREDPROCEDUREID_06,
    EM_TDS_PROCEDURE_06_PARAMETERS,
    EM_TDS_PROCEDURE_07_NAME,
    EM_TDS_STOREDPROCEDUREID_07,
    EM_TDS_PROCEDURE_07_PARAMETERS,
    EM_TDS_PROCEDURE_08_NAME,
    EM_TDS_STOREDPROCEDUREID_08,
    EM_TDS_PROCEDURE_08_PARAMETERS,
    EM_TDS_PROCEDURE_09_NAME,
    EM_TDS_STOREDPROCEDUREID_09,
    EM_TDS_PROCEDURE_09_PARAMETERS,
    EM_TDS_PROCEDURE_10_NAME,
    EM_TDS_STOREDPROCEDUREID_10,
    EM_TDS_PROCEDURE_10_PARAMETERS,
    EM_TDS_PROCEDURE_11_NAME,
    EM_TDS_STOREDPROCEDUREID_11,
    EM_TDS_PROCEDURE_11_PARAMETERS,
    EM_TDS_PROCEDURE_12_NAME,
    EM_TDS_STOREDPROCEDUREID_12,
    EM_TDS_PROCEDURE_12_PARAMETERS,
    EM_TDS_PROCEDURE_13_NAME,
    EM_TDS_STOREDPROCEDUREID_13,
    EM_TDS_PROCEDURE_13_PARAMETERS,
    EM_TDS_PROCEDURE_14_NAME,
    EM_TDS_STOREDPROCEDUREID_14,
    EM_TDS_PROCEDURE_14_PARAMETERS,
    EM_TDS_PROCEDURE_15_NAME,
    EM_TDS_STOREDPROCEDUREID_15,
    EM_TDS_PROCEDURE_15_PARAMETERS,
    EM_TDS_MAX
};

static dpi_field_table  tds_field_array[] = {

    DPI_FIELD_D(EM_TDS_PACKETTYPE,                   EM_F_TYPE_STRING,                 "PacketType"),
    DPI_FIELD_D(EM_TDS_CHANNEL,                      EM_F_TYPE_UINT16,                 "Channel"),
    DPI_FIELD_D(EM_TDS_PACKETNUMBER,                 EM_F_TYPE_UINT8,                  "PacketNumber"),
    DPI_FIELD_D(EM_TDS_CLIENTNAME,                   EM_F_TYPE_STRING,                 "ClientName"),
    DPI_FIELD_D(EM_TDS_USERNAME,                     EM_F_TYPE_STRING,                 "Username"),
    DPI_FIELD_D(EM_TDS_PASSWORD,                     EM_F_TYPE_STRING,                 "Password"),
    DPI_FIELD_D(EM_TDS_LOGIN_STATUS,                 EM_F_TYPE_STRING,                 "LoginStatus"),
    DPI_FIELD_D(EM_TDS_APPNAME,                      EM_F_TYPE_STRING,                 "AppName"),
    DPI_FIELD_D(EM_TDS_SERVERNAME,                   EM_F_TYPE_STRING,                 "ServerName"),
    DPI_FIELD_D(EM_TDS_LIBRARYNAME,                  EM_F_TYPE_STRING,                 "LibraryName"),
    DPI_FIELD_D(EM_TDS_LOCALE,                       EM_F_TYPE_STRING,                 "Locale"),
    DPI_FIELD_D(EM_TDS_DATABASENAME,                 EM_F_TYPE_STRING,                 "DatabaseName"),

    DPI_FIELD_D(EM_TDS_WINDOW,                       EM_F_TYPE_UINT8,                  "Window"),
    DPI_FIELD_D(EM_TDS_TDSVERSION,                   EM_F_TYPE_UINT32,                 "TDSversion"),
    DPI_FIELD_D(EM_TDS_CLIENTVERSION,                EM_F_TYPE_UINT32,                 "Clientversion"),
    DPI_FIELD_D(EM_TDS_CLIENTPID,                    EM_F_TYPE_UINT32,                 "ClientPID"),
    DPI_FIELD_D(EM_TDS_CONNECTIONID,                 EM_F_TYPE_UINT32,                 "ConnectionID"),
    DPI_FIELD_D(EM_TDS_OPTION_FLAGS1,                EM_F_TYPE_UINT8,                  "Option_Flags1"),
    DPI_FIELD_D(EM_TDS_OPTION_FLAGS2,                EM_F_TYPE_UINT8,                  "Option_Flags2"),
    DPI_FIELD_D(EM_TDS_RESERVED_FLAGS,               EM_F_TYPE_UINT8,                  "Reserved_Flags"),
    DPI_FIELD_D(EM_TDS_SQL_TYPE_FLAGS,               EM_F_TYPE_UINT8,                  "SQL_Type_Flags"),
    DPI_FIELD_D(EM_TDS_TIMEZONE,                     EM_F_TYPE_UINT32,                 "TimeZone"),
    DPI_FIELD_D(EM_TDS_COLLATION,                    EM_F_TYPE_UINT32,                 "Collation"),
    
    DPI_FIELD_D(EM_TDS_QUERY,                        EM_F_TYPE_STRING,                 "Query"),
    DPI_FIELD_D(EM_TDS_LANGUAGE_TEXT,                EM_F_TYPE_STRING,                 "Language_text"),
    DPI_FIELD_D(EM_TDS_NTLMSSP,                      EM_F_TYPE_STRING,                 "NTLMSSP"),
    DPI_FIELD_D(EM_TDS_GSSAPI,                       EM_F_TYPE_STRING,                 "GSSAPI"),
    
    DPI_FIELD_D(EM_TDS_TOKEN_00_VALUE,               EM_F_TYPE_NULL,                 "Token_00_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_00_NAME,                EM_F_TYPE_NULL,                 "Token_00_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_00_CONTENT,             EM_F_TYPE_NULL,                 "Token_00_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_01_VALUE,               EM_F_TYPE_NULL,                 "Token_01_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_01_NAME,                EM_F_TYPE_NULL,                 "Token_01_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_01_CONTENT,             EM_F_TYPE_NULL,                 "Token_01_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_02_VALUE,               EM_F_TYPE_NULL,                 "Token_02_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_02_NAME,                EM_F_TYPE_NULL,                 "Token_02_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_02_CONTENT,             EM_F_TYPE_NULL,                 "Token_02_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_03_VALUE,               EM_F_TYPE_NULL,                 "Token_03_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_03_NAME,                EM_F_TYPE_NULL,                 "Token_03_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_03_CONTENT,             EM_F_TYPE_NULL,                 "Token_03_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_04_VALUE,               EM_F_TYPE_NULL,                 "Token_04_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_04_NAME,                EM_F_TYPE_NULL,                 "Token_04_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_04_CONTENT,             EM_F_TYPE_NULL,                 "Token_04_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_05_VALUE,               EM_F_TYPE_NULL,                 "Token_05_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_05_NAME,                EM_F_TYPE_NULL,                 "Token_05_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_05_CONTENT,             EM_F_TYPE_NULL,                 "Token_05_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_06_VALUE,               EM_F_TYPE_NULL,                 "Token_06_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_06_NAME,                EM_F_TYPE_NULL,                 "Token_06_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_06_CONTENT,             EM_F_TYPE_NULL,                 "Token_06_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_07_VALUE,               EM_F_TYPE_NULL,                 "Token_07_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_07_NAME,                EM_F_TYPE_NULL,                 "Token_07_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_07_CONTENT,             EM_F_TYPE_NULL,                 "Token_07_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_08_VALUE,               EM_F_TYPE_NULL,                 "Token_08_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_08_NAME,                EM_F_TYPE_NULL,                 "Token_08_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_08_CONTENT,             EM_F_TYPE_NULL,                 "Token_08_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_09_VALUE,               EM_F_TYPE_NULL,                 "Token_09_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_09_NAME,                EM_F_TYPE_NULL,                 "Token_09_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_09_CONTENT,             EM_F_TYPE_NULL,                 "Token_09_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_10_VALUE,               EM_F_TYPE_NULL,                 "Token_10_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_10_NAME,                EM_F_TYPE_NULL,                 "Token_10_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_10_CONTENT,             EM_F_TYPE_NULL,                 "Token_10_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_11_VALUE,               EM_F_TYPE_NULL,                 "Token_11_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_11_NAME,                EM_F_TYPE_NULL,                 "Token_11_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_11_CONTENT,             EM_F_TYPE_NULL,                 "Token_11_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_12_VALUE,               EM_F_TYPE_NULL,                 "Token_12_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_12_NAME,                EM_F_TYPE_NULL,                 "Token_12_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_12_CONTENT,             EM_F_TYPE_NULL,                 "Token_12_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_13_VALUE,               EM_F_TYPE_NULL,                 "Token_13_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_13_NAME,                EM_F_TYPE_NULL,                 "Token_13_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_13_CONTENT,             EM_F_TYPE_NULL,                 "Token_13_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_14_VALUE,               EM_F_TYPE_NULL,                 "Token_14_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_14_NAME,                EM_F_TYPE_NULL,                 "Token_14_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_14_CONTENT,             EM_F_TYPE_NULL,                 "Token_14_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_15_VALUE,               EM_F_TYPE_NULL,                 "Token_15_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_15_NAME,                EM_F_TYPE_NULL,                 "Token_15_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_15_CONTENT,             EM_F_TYPE_NULL,                 "Token_15_Content"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_00_NAME,            EM_F_TYPE_NULL,                 "Procedure_00_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_00,         EM_F_TYPE_NULL,                 "StoredProcedureID_00"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_00_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_00_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_01_NAME,            EM_F_TYPE_NULL,                 "Procedure_01_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_01,         EM_F_TYPE_NULL,                 "StoredProcedureID_01"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_01_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_01_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_02_NAME,            EM_F_TYPE_NULL,                 "Procedure_02_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_02,         EM_F_TYPE_NULL,                 "StoredProcedureID_02"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_02_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_02_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_03_NAME,            EM_F_TYPE_NULL,                 "Procedure_03_Name"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_03_STORED_PROCEDURE_ID,EM_F_TYPE_NULL,              "Procedure_03_Stored_procedure_ID"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_03_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_03_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_04_NAME,            EM_F_TYPE_NULL,                 "Procedure_04_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_04,         EM_F_TYPE_NULL,                 "StoredProcedureID_04"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_04_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_04_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_05_NAME,            EM_F_TYPE_NULL,                 "Procedure_05_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_05,         EM_F_TYPE_NULL,                 "StoredProcedureID_05"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_05_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_05_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_06_NAME,            EM_F_TYPE_NULL,                 "Procedure_06_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_06,         EM_F_TYPE_NULL,                 "StoredProcedureID_06"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_06_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_06_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_07_NAME,            EM_F_TYPE_NULL,                 "Procedure_07_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_07,         EM_F_TYPE_NULL,                 "StoredProcedureID_07"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_07_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_07_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_08_NAME,            EM_F_TYPE_NULL,                 "Procedure_08_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_08,         EM_F_TYPE_NULL,                 "StoredProcedureID_08"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_08_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_08_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_09_NAME,            EM_F_TYPE_NULL,                 "Procedure_09_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_09,         EM_F_TYPE_NULL,                 "StoredProcedureID_09"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_09_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_09_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_10_NAME,            EM_F_TYPE_NULL,                 "Procedure_10_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_10,         EM_F_TYPE_NULL,                 "StoredProcedureID_10"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_10_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_10_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_11_NAME,            EM_F_TYPE_NULL,                 "Procedure_11_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_11,         EM_F_TYPE_NULL,                 "StoredProcedureID_11"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_11_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_11_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_12_NAME,            EM_F_TYPE_NULL,                 "Procedure_12_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_12,         EM_F_TYPE_NULL,                 "StoredProcedureID_12"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_12_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_12_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_13_NAME,            EM_F_TYPE_NULL,                 "Procedure_13_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_13,         EM_F_TYPE_NULL,                 "StoredProcedureID_13"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_13_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_13_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_14_NAME,            EM_F_TYPE_NULL,                 "Procedure_14_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_14,         EM_F_TYPE_NULL,                 "StoredProcedureID_14"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_14_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_14_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_15_NAME,            EM_F_TYPE_NULL,                 "Procedure_15_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_15,         EM_F_TYPE_NULL,                 "StoredProcedureID_15"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_15_PARAMETERS,      EM_F_TYPE_NULL,                 "Procedure_15_Parameters"),
};

static dpi_field_table  tds_field_array_sdt[] = {

    DPI_FIELD_D(EM_TDS_PACKETTYPE,                   YA_FT_STRING,                 "PacketType"),
    DPI_FIELD_D(EM_TDS_CHANNEL,                      YA_FT_UINT16,                "Channel"),
    DPI_FIELD_D(EM_TDS_PACKETNUMBER,                 YA_FT_UINT8,                 "PacketNumber"),
    DPI_FIELD_D(EM_TDS_CLIENTNAME,                   YA_FT_STRING,                 "ClientName"),
    DPI_FIELD_D(EM_TDS_USERNAME,                     YA_FT_STRING,                 "Username"),
    DPI_FIELD_D(EM_TDS_PASSWORD,                     YA_FT_STRING,                 "Password"),
    DPI_FIELD_D(EM_TDS_LOGIN_STATUS,                 YA_FT_STRING,                 "LoginStatus"),
    DPI_FIELD_D(EM_TDS_APPNAME,                      YA_FT_STRING,                 "AppName"),
    DPI_FIELD_D(EM_TDS_SERVERNAME,                   YA_FT_STRING,                 "ServerName"),
    DPI_FIELD_D(EM_TDS_LIBRARYNAME,                  YA_FT_STRING,                 "LibraryName"),
    DPI_FIELD_D(EM_TDS_LOCALE,                       YA_FT_STRING,                 "Locale"),
    DPI_FIELD_D(EM_TDS_DATABASENAME,                 YA_FT_STRING,                 "DatabaseName"),

    DPI_FIELD_D(EM_TDS_WINDOW,                       YA_FT_UINT8,                 "Window"),
    DPI_FIELD_D(EM_TDS_TDSVERSION,                   YA_FT_UINT32,                "TDSversion"),
    DPI_FIELD_D(EM_TDS_CLIENTVERSION,                YA_FT_UINT32,                "Clientversion"),
    DPI_FIELD_D(EM_TDS_CLIENTPID,                    YA_FT_UINT32,                "ClientPID"),
    DPI_FIELD_D(EM_TDS_CONNECTIONID,                 YA_FT_UINT32,                "ConnectionID"),
    DPI_FIELD_D(EM_TDS_OPTION_FLAGS1,                YA_FT_UINT8,                 "Option_Flags1"),
    DPI_FIELD_D(EM_TDS_OPTION_FLAGS2,                YA_FT_UINT8,                 "Option_Flags2"),
    DPI_FIELD_D(EM_TDS_RESERVED_FLAGS,               YA_FT_UINT8,                 "Reserved_Flags"),
    DPI_FIELD_D(EM_TDS_SQL_TYPE_FLAGS,               YA_FT_UINT8,                 "SQL_Type_Flags"),
    DPI_FIELD_D(EM_TDS_TIMEZONE,                     YA_FT_UINT32,                "TimeZone"),
    DPI_FIELD_D(EM_TDS_COLLATION,                    YA_FT_UINT32,                "Collation"),

    DPI_FIELD_D(EM_TDS_QUERY,                        YA_FT_STRING,                 "Query"),
    DPI_FIELD_D(EM_TDS_LANGUAGE_TEXT,                YA_FT_STRING,                 "Language_text"),
    DPI_FIELD_D(EM_TDS_NTLMSSP,                      YA_FT_STRING,                 "NTLMSSP"),
    DPI_FIELD_D(EM_TDS_GSSAPI,                       YA_FT_STRING,                 "GSSAPI"),

    DPI_FIELD_D(EM_TDS_TOKEN_00_VALUE,               YA_FT_STRING,                 "Token_00_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_00_NAME,                YA_FT_STRING,                 "Token_00_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_00_CONTENT,             YA_FT_STRING,                 "Token_00_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_01_VALUE,               YA_FT_STRING,                 "Token_01_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_01_NAME,                YA_FT_STRING,                 "Token_01_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_01_CONTENT,             YA_FT_STRING,                 "Token_01_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_02_VALUE,               YA_FT_STRING,                 "Token_02_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_02_NAME,                YA_FT_STRING,                 "Token_02_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_02_CONTENT,             YA_FT_STRING,                 "Token_02_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_03_VALUE,               YA_FT_STRING,                 "Token_03_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_03_NAME,                YA_FT_STRING,                 "Token_03_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_03_CONTENT,             YA_FT_STRING,                 "Token_03_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_04_VALUE,               YA_FT_STRING,                 "Token_04_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_04_NAME,                YA_FT_STRING,                 "Token_04_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_04_CONTENT,             YA_FT_STRING,                 "Token_04_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_05_VALUE,               YA_FT_STRING,                 "Token_05_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_05_NAME,                YA_FT_STRING,                 "Token_05_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_05_CONTENT,             YA_FT_STRING,                 "Token_05_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_06_VALUE,               YA_FT_STRING,                 "Token_06_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_06_NAME,                YA_FT_STRING,                 "Token_06_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_06_CONTENT,             YA_FT_STRING,                 "Token_06_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_07_VALUE,               YA_FT_STRING,                 "Token_07_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_07_NAME,                YA_FT_STRING,                 "Token_07_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_07_CONTENT,             YA_FT_STRING,                 "Token_07_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_08_VALUE,               YA_FT_STRING,                 "Token_08_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_08_NAME,                YA_FT_STRING,                 "Token_08_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_08_CONTENT,             YA_FT_STRING,                 "Token_08_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_09_VALUE,               YA_FT_STRING,                 "Token_09_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_09_NAME,                YA_FT_STRING,                 "Token_09_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_09_CONTENT,             YA_FT_STRING,                 "Token_09_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_10_VALUE,               YA_FT_STRING,                 "Token_10_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_10_NAME,                YA_FT_STRING,                 "Token_10_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_10_CONTENT,             YA_FT_STRING,                 "Token_10_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_11_VALUE,               YA_FT_STRING,                 "Token_11_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_11_NAME,                YA_FT_STRING,                 "Token_11_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_11_CONTENT,             YA_FT_STRING,                 "Token_11_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_12_VALUE,               YA_FT_STRING,                 "Token_12_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_12_NAME,                YA_FT_STRING,                 "Token_12_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_12_CONTENT,             YA_FT_STRING,                 "Token_12_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_13_VALUE,               YA_FT_STRING,                 "Token_13_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_13_NAME,                YA_FT_STRING,                 "Token_13_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_13_CONTENT,             YA_FT_STRING,                 "Token_13_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_14_VALUE,               YA_FT_STRING,                 "Token_14_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_14_NAME,                YA_FT_STRING,                 "Token_14_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_14_CONTENT,             YA_FT_STRING,                 "Token_14_Content"),
    DPI_FIELD_D(EM_TDS_TOKEN_15_VALUE,               YA_FT_STRING,                 "Token_15_Value"),
    DPI_FIELD_D(EM_TDS_TOKEN_15_NAME,                YA_FT_STRING,                 "Token_15_Name"),
    DPI_FIELD_D(EM_TDS_TOKEN_15_CONTENT,             YA_FT_STRING,                 "Token_15_Content"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_00_NAME,            YA_FT_STRING,                 "Procedure_00_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_00,         YA_FT_STRING,                 "StoredProcedureID_00"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_00_PARAMETERS,      YA_FT_STRING,                 "Procedure_00_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_01_NAME,            YA_FT_STRING,                 "Procedure_01_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_01,         YA_FT_STRING,                 "StoredProcedureID_01"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_01_PARAMETERS,      YA_FT_STRING,                 "Procedure_01_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_02_NAME,            YA_FT_STRING,                 "Procedure_02_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_02,         YA_FT_STRING,                 "StoredProcedureID_02"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_02_PARAMETERS,      YA_FT_STRING,                 "Procedure_02_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_03_NAME,            YA_FT_STRING,                 "Procedure_03_Name"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_03_STORED_PROCEDURE_ID,YA_FT_STRING,              "Procedure_03_Stored_procedure_ID"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_03_PARAMETERS,      YA_FT_STRING,                 "Procedure_03_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_04_NAME,            YA_FT_STRING,                 "Procedure_04_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_04,         YA_FT_STRING,                 "StoredProcedureID_04"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_04_PARAMETERS,      YA_FT_STRING,                 "Procedure_04_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_05_NAME,            YA_FT_STRING,                 "Procedure_05_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_05,         YA_FT_STRING,                 "StoredProcedureID_05"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_05_PARAMETERS,      YA_FT_STRING,                 "Procedure_05_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_06_NAME,            YA_FT_STRING,                 "Procedure_06_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_06,         YA_FT_STRING,                 "StoredProcedureID_06"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_06_PARAMETERS,      YA_FT_STRING,                 "Procedure_06_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_07_NAME,            YA_FT_STRING,                 "Procedure_07_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_07,         YA_FT_STRING,                 "StoredProcedureID_07"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_07_PARAMETERS,      YA_FT_STRING,                 "Procedure_07_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_08_NAME,            YA_FT_STRING,                 "Procedure_08_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_08,         YA_FT_STRING,                 "StoredProcedureID_08"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_08_PARAMETERS,      YA_FT_STRING,                 "Procedure_08_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_09_NAME,            YA_FT_STRING,                 "Procedure_09_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_09,         YA_FT_STRING,                 "StoredProcedureID_09"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_09_PARAMETERS,      YA_FT_STRING,                 "Procedure_09_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_10_NAME,            YA_FT_STRING,                 "Procedure_10_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_10,         YA_FT_STRING,                 "StoredProcedureID_10"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_10_PARAMETERS,      YA_FT_STRING,                 "Procedure_10_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_11_NAME,            YA_FT_STRING,                 "Procedure_11_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_11,         YA_FT_STRING,                 "StoredProcedureID_11"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_11_PARAMETERS,      YA_FT_STRING,                 "Procedure_11_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_12_NAME,            YA_FT_STRING,                 "Procedure_12_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_12,         YA_FT_STRING,                 "StoredProcedureID_12"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_12_PARAMETERS,      YA_FT_STRING,                 "Procedure_12_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_13_NAME,            YA_FT_STRING,                 "Procedure_13_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_13,         YA_FT_STRING,                 "StoredProcedureID_13"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_13_PARAMETERS,      YA_FT_STRING,                 "Procedure_13_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_14_NAME,            YA_FT_STRING,                 "Procedure_14_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_14,         YA_FT_STRING,                 "StoredProcedureID_14"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_14_PARAMETERS,      YA_FT_STRING,                 "Procedure_14_Parameters"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_15_NAME,            YA_FT_STRING,                 "Procedure_15_Name"),
    DPI_FIELD_D(EM_TDS_STOREDPROCEDUREID_15,         YA_FT_STRING,                 "StoredProcedureID_15"),
    DPI_FIELD_D(EM_TDS_PROCEDURE_15_PARAMETERS,      YA_FT_STRING,                 "Procedure_15_Parameters"),
};



#define TDS_QUERY_PKT        1 /* SQLBatch in MS-TDS revision 18.0 */
#define TDS_LOGIN_PKT        2
#define TDS_RPC_PKT          3
#define TDS_RESP_PKT         4
#define TDS_RAW_PKT          5
#define TDS_ATTENTION_PKT    6
#define TDS_BULK_DATA_PKT    7 /* Bulk Load BCP in MS-TDS revision 18.0 */
#define TDS_OPEN_CHN_PKT     8
#define TDS_CLOSE_CHN_PKT    9
#define TDS_RES_ERROR_PKT   10
#define TDS_LOG_CHN_ACK_PKT 11
#define TDS_ECHO_PKT        12
#define TDS_LOGOUT_CHN_PKT  13
#define TDS_TRANS_MGR_PKT   14
#define TDS_QUERY5_PKT      15  /* or "Normal tokenized request or response */
#define TDS_LOGIN7_PKT      16  /* or "Urgent tokenized request or response */
#define TDS_SSPI_PKT        17
#define TDS_PRELOGIN_PKT    18
#define TDS_INVALID_PKT     19
#define TDS_TLS_PKT         23

#define is_valid_tds_type(x) (((x) >= TDS_QUERY_PKT && (x) < TDS_INVALID_PKT) || x == TDS_TLS_PKT)

/* The following constants are imported more or less directly from FreeTDS */
/*      Updated from FreeTDS v0.63 tds.h                                   */
/*         "$Id: tds.h,v 1.192 2004/10/28 12:42:12 freddy77]"              */
/* Note: [###] below means 'not defined in FreeTDS tds.h'                  */

#define TDS_TVPROW_TOKEN           1   /* 0x01 */
#define TDS5_PARAMFMT2_TOKEN       32  /* 0x20    TDS 5.0 only              */
#define TDS_LANG_TOKEN             33  /* 0x21    TDS 5.0 only              */
#define TDS5_ORDERBY2_TOKEN        34  /* 0x22    TDS 5.0 only              */
#define TDS5_CURDECLARE2_TOKEN     35  /* 0x23    TDS 5.0 only        [###] */
#define TDS5_ROWFMT2_TOKEN         97  /* 0x61    TDS 5.0 only              */
#define TDS5_MSG_TOKEN            101  /* 0x65    TDS 5.0 only        [###] */
#define TDS_LOGOUT_TOKEN          113  /* 0x71    TDS 5.0 only? ct_close()  */
#define TDS_OFFSET_TOKEN          120  /* 0x78    Removed in TDS 7.2        */
#define TDS_RET_STAT_TOKEN        121  /* 0x79                              */
#define TDS_PROCID_TOKEN          124  /* 0x7C    TDS 4.2 only - TDS_PROCID */
#define TDS_CURCLOSE_TOKEN        128  /* 0x80    TDS 5.0 only              */
#define TDS7_COL_METADATA_TOKEN   129  /* 0x81                              */
#define TDS_CURFETCH_TOKEN        130  /* 0x82    TDS 5.0 only              */
#define TDS_CURINFO_TOKEN         131  /* 0x83    TDS 5.0 only              */
#define TDS_CUROPEN_TOKEN         132  /* 0x84    TDS 5.0 only              */
#define TDS_CURDECLARE_TOKEN      134  /* 0x86    TDS 5.0 only              */
#define TDS7_ALTMETADATA_TOKEN    136  /* 0x88                              */
#define TDS_COL_NAME_TOKEN        160  /* 0xA0    TDS 4.2 only              */
#define TDS_COL_INFO_TOKEN        161  /* 0xA1    TDS 4.2 only - TDS_COLFMT */
#define TDS5_DYNAMIC2_TOKEN       163  /* 0xA3    TDS 5.0 only              */
#define TDS_TABNAME_TOKEN         164  /* 0xA4                              */
#define TDS7_COL_INFO_TOKEN       165  /* 0xA5                              */
#define TDS_OPTIONCMD_TOKEN       166  /* 0xA6 */
#define TDS_COMPUTE_NAMES_TOKEN   167  /* 0xA7 */
#define TDS_COMPUTE_RESULT_TOKEN  168  /* 0xA8 */
#define TDS_ORDER_TOKEN           169  /* 0xA9    TDS_ORDER                 */
#define TDS_ERR_TOKEN             170  /* 0xAA                              */
#define TDS_INFO_TOKEN            171  /* 0xAB                              */
#define TDS_RETURNVAL_TOKEN       172  /* 0xAC                              */
#define TDS_LOGIN_ACK_TOKEN       173  /* 0xAD                              */
#define TDS_FEATUREEXTACK_TOKEN   174  /* 0xAE    Introduced TDS 7.4        */
#define TDS_KEY_TOKEN             202  /* 0xCA                        [###] */
#define TDS_ROW_TOKEN             209  /* 0xD1                              */
#define TDS_NBCROW_TOKEN          210  /* 0xD2    Introduced TDS 7.3        */
#define TDS_ALTROW_TOKEN          211  /* 0xD3                              */
#define TDS5_PARAMS_TOKEN         215  /* 0xD7    TDS 5.0 only              */
#define TDS_CAP_TOKEN             226  /* 0xE2                              */
#define TDS_ENVCHG_TOKEN          227  /* 0xE3                              */
#define TDS_SESSIONSTATE_TOKEN    228  /* 0xE4    Introduced TDS 7.4        */
#define TDS_EED_TOKEN             229  /* 0xE5                              */
#define TDS_DBRPC_TOKEN           230  /* 0xE6                              */
#define TDS5_DYNAMIC_TOKEN        231  /* 0xE7    TDS 5.0 only              */
#define TDS5_PARAMFMT_TOKEN       236  /* 0xEC    TDS 5.0 only              */
#define TDS_AUTH_TOKEN            237  /* 0xED                              */  /* DUPLICATE! */
#define TDS_SSPI_TOKEN            237  /* 0xED                              */  /* DUPLICATE! */
#define TDS_RESULT_TOKEN          238  /* 0xEE                              */  /* DUPLICATE! */
#define TDS_FEDAUTHINFO_TOKEN     238  /* 0xEE    Introduced TDS 7.4        */  /* DUPLICATE! */
#define TDS_DONE_TOKEN            253  /* 0xFD                              */
#define TDS_DONEPROC_TOKEN        254  /* 0xFE                              */
#define TDS_DONEINPROC_TOKEN      255  /* 0xFF                              */

/* The status field */
#define STATUS_LAST_BUFFER              0x01
#define STATUS_IGNORE_EVENT             0x02
#define STATUS_EVENT_NOTIFICATION       0x04
#define STATUS_RESETCONNECTION          0x08
#define STATUS_RESETCONNECTIONSKIPTRAN  0x10

#define is_valid_tds_status(x) ((x) <= STATUS_EVENT_NOTIFICATION)

/* These correspond to the netlib packet type field */
static const struct int_to_string packet_type_names[] = {
    {TDS_QUERY_PKT,       "SQL batch"},
    {TDS_LOGIN_PKT,       "Pre-TDS7 login"},
    {TDS_RPC_PKT,         "Remote Procedure Call"},
    {TDS_RESP_PKT,        "Response"},
    {TDS_RAW_PKT,         "Unused"},
    {TDS_ATTENTION_PKT,   "Attention"},
    {TDS_BULK_DATA_PKT,   "Bulk load data"},
    {TDS_OPEN_CHN_PKT,    "Unused"},
    {TDS_CLOSE_CHN_PKT,   "Unused"},
    {TDS_RES_ERROR_PKT,   "Unused"},
    {TDS_LOG_CHN_ACK_PKT, "Unused"},
    {TDS_ECHO_PKT,        "Unused"},
    {TDS_LOGOUT_CHN_PKT,  "Unused"},
    {TDS_TRANS_MGR_PKT,   "Transaction Manager Request"},
    {TDS_QUERY5_PKT,      "TDS5 query"},
    {TDS_LOGIN7_PKT,      "TDS7 login"},
    {TDS_SSPI_PKT,        "SSPI message"},
    {TDS_PRELOGIN_PKT,    "TDS7 pre-login message"},
    {TDS_TLS_PKT,         "TLS exchange"},
    {0, NULL}
};
    
enum {
    TDS_HEADER_QUERY_NOTIF = 0x0001,
    TDS_HEADER_TRANS_DESCR = 0x0002
};


#define TDS_PROTOCOL_NOT_SPECIFIED   0xFFFF
#define TDS_PROTOCOL_4      0x4000
#define TDS_PROTOCOL_5      0x5000
#define TDS_PROTOCOL_7_0    0x7000
#define TDS_PROTOCOL_7_1    0x7100
#define TDS_PROTOCOL_7_2    0x7200
#define TDS_PROTOCOL_7_3    0x7300
#define TDS_PROTOCOL_7_3A   0x730a
#define TDS_PROTOCOL_7_3B   0x730b
#define TDS_PROTOCOL_7_4    0x7400
    
static uint32_t tds_protocol_type = TDS_PROTOCOL_NOT_SPECIFIED;

#if 0
static const enum_val_t tds_protocol_type_options[] = {
    {"not_specified", "Not Specified", TDS_PROTOCOL_NOT_SPECIFIED},
    {"tds4", "TDS 4", TDS_PROTOCOL_4},    /* TDS 4.2 and TDS 4.6 */
    {"tds5", "TDS 5", TDS_PROTOCOL_5},
    {"tds70", "TDS 7.0", TDS_PROTOCOL_7_0},
    {"tds71", "TDS 7.1", TDS_PROTOCOL_7_1},
    {"tds72", "TDS 7.2", TDS_PROTOCOL_7_2},
    {"tds73", "TDS 7.3", TDS_PROTOCOL_7_3},
    {"tds73a", "TDS 7.3A", TDS_PROTOCOL_7_3A},
    {"tds73b", "TDS 7.3B", TDS_PROTOCOL_7_3B},
    {"tds74", "TDS 7.4", TDS_PROTOCOL_7_4},
    {NULL, NULL, -1}
};
#endif
    
#define TDS_PROTO_PREF_NOT_SPECIFIED (tds_protocol_type == TDS_PROTOCOL_NOT_SPECIFIED)
#define TDS_PROTO_PREF_TDS4 (tds_protocol_type == TDS_PROTOCOL_4)
#define TDS_PROTO_PREF_TDS5 (tds_protocol_type == TDS_PROTOCOL_5)
#define TDS_PROTO_PREF_TDS7_0 (tds_protocol_type == TDS_PROTOCOL_7_0)
#define TDS_PROTO_PREF_TDS7_1 (tds_protocol_type == TDS_PROTOCOL_7_1)
#define TDS_PROTO_PREF_TDS7_2 (tds_protocol_type == TDS_PROTOCOL_7_2)
#define TDS_PROTO_PREF_TDS7_3 (tds_protocol_type == TDS_PROTOCOL_7_3)
#define TDS_PROTO_PREF_TDS7_3A (tds_protocol_type == TDS_PROTOCOL_7_3A)
#define TDS_PROTO_PREF_TDS7_3B (tds_protocol_type == TDS_PROTOCOL_7_3B)
#define TDS_PROTO_PREF_TDS7_4 (tds_protocol_type == TDS_PROTOCOL_7_4)
#define TDS_PROTO_PREF_TDS7 (tds_protocol_type >= TDS_PROTOCOL_7_0 && tds_protocol_type <= TDS_PROTOCOL_7_4)
    
#define TDS_PROTO_TDS4 TDS_PROTO_PREF_TDS4
#define TDS_PROTO_TDS7 (TDS_PROTO_PREF_TDS7 || \
                            (TDS_PROTO_PREF_NOT_SPECIFIED && (tds_info->tds7_version != TDS_PROTOCOL_NOT_SPECIFIED)))
#define TDS_PROTO_TDS7_1_OR_LESS ((tds_protocol_type <= TDS_PROTOCOL_7_1) || \
                                         (TDS_PROTO_PREF_NOT_SPECIFIED && (tds_info->tds7_version <= TDS_PROTOCOL_7_1)))
#define TDS_PROTO_TDS7_2_OR_GREATER ((tds_protocol_type >= TDS_PROTOCOL_7_2) || \
                                         (TDS_PROTO_PREF_NOT_SPECIFIED && (tds_info->tds7_version >= TDS_PROTOCOL_7_2)))
#define TDS_PROTO_TDS7_3A_OR_LESS ((tds_protocol_type <= TDS_PROTOCOL_7_3A) || \
                                         (TDS_PROTO_PREF_NOT_SPECIFIED && (tds_info->tds7_version <= TDS_PROTOCOL_7_3A)))
#define TDS_PROTO_TDS7_3B_OR_GREATER ((tds_protocol_type >= TDS_PROTOCOL_7_3B) || \
                                         (TDS_PROTO_PREF_NOT_SPECIFIED && (tds_info->tds7_version >= TDS_PROTOCOL_7_3B)))
#define TDS_PROTO_TDS7_4_OR_GREATER ((tds_protocol_type >= TDS_PROTOCOL_7_4) || \
                                         (TDS_PROTO_PREF_NOT_SPECIFIED && (tds_info->tds7_version >= TDS_PROTOCOL_7_4)))
                                         
/* Microsoft internal stored procedure id's */
#define TDS_SP_CURSOR           1
#define TDS_SP_CURSOROPEN       2
#define TDS_SP_CURSORPREPARE    3
#define TDS_SP_CURSOREXECUTE    4
#define TDS_SP_CURSORPREPEXEC   5
#define TDS_SP_CURSORUNPREPARE  6
#define TDS_SP_CURSORFETCH      7
#define TDS_SP_CURSOROPTION     8
#define TDS_SP_CURSORCLOSE      9
#define TDS_SP_EXECUTESQL      10
#define TDS_SP_PREPARE         11
#define TDS_SP_EXECUTE         12
#define TDS_SP_PREPEXEC        13
#define TDS_SP_PREPEXECRPC     14
#define TDS_SP_UNPREPARE       15

/* Sybase Data Types */
#define SYBCHAR        47  /* 0x2F */
#define SYBVARCHAR     39  /* 0x27 */
#define SYBINTN        38  /* 0x26 */
#define SYBINT1        48  /* 0x30 */
#define SYBINT2        52  /* 0x34 */
#define SYBINT4        56  /* 0x38 */
#define SYBINT8       127  /* 0x7F */
#define SYBFLT8        62  /* 0x3E */
#define SYBDATETIME    61  /* 0x3D */
#define SYBBIT         50  /* 0x32 */
#define SYBTEXT        35  /* 0x23 */
#define SYBNTEXT       99  /* 0x63 */
#define SYBIMAGE       34  /* 0x22 */
#define SYBMONEY4     122  /* 0x7A */
#define SYBMONEY       60  /* 0x3C */
#define SYBDATETIME4   58  /* 0x3A */
#define SYBREAL        59  /* 0x3B */
#define SYBBINARY      45  /* 0x2D */
#define SYBVOID        31  /* 0x1F */
#define SYBVARBINARY   37  /* 0x25 */
#define SYBNVARCHAR   103  /* 0x67 */
#define SYBBITN       104  /* 0x68 */
#define SYBNUMERIC    108  /* 0x6C */
#define SYBDECIMAL    106  /* 0x6A */
#define SYBFLTN       109  /* 0x6D */
#define SYBMONEYN     110  /* 0x6E */
#define SYBDATETIMN   111  /* 0x6F */
#define XSYBCHAR      175  /* 0xA7 */
#define XSYBVARCHAR   167  /* 0xAF */
#define XSYBNVARCHAR  231  /* 0xE7 */
#define XSYBNCHAR     239  /* 0xEF */
#define XSYBVARBINARY 165  /* 0xA5 */
#define XSYBBINARY    173  /* 0xAD */
#define SYBLONGBINARY 225  /* 0xE1 */
#define SYBSINT1       64  /* 0x40 */
#define SYBUINT2       65  /* 0x41 */
#define SYBUINT4       66  /* 0x42 */
#define SYBUINT8       67  /* 0x43 */
#define SYBUNIQUE      36  /* 0x24 */
#define SYBVARIANT     98  /* 0x62 */

/* FIXEDLENTYPE */
#define TDS_DATA_TYPE_NULL            0x1F  /* 31 = Null (no data associated with this type) */
#define TDS_DATA_TYPE_INT1            0x30  /* 48 = TinyInt (1 byte data representation) */
#define TDS_DATA_TYPE_BIT             0x32  /* 50 = Bit (1 byte data representation) */
#define TDS_DATA_TYPE_INT2            0x34  /* 52 = SmallInt (2 byte data representation) */
#define TDS_DATA_TYPE_INT4            0x38  /* 56 = Int (4 byte data representation) */
#define TDS_DATA_TYPE_DATETIME4       0x3A  /* 58 = SmallDateTime (4 byte data representation) */
#define TDS_DATA_TYPE_FLT4            0x3B  /* 59 = Real (4 byte data representation) */
#define TDS_DATA_TYPE_MONEY           0x3C  /* 60 = Money (8 byte data representation) */
#define TDS_DATA_TYPE_DATETIME        0x3D  /* 61 = DateTime (8 byte data representation) */
#define TDS_DATA_TYPE_FLT8            0x3E  /* 62 = Float (8 byte data representation) */
#define TDS_DATA_TYPE_MONEY4          0x7A  /* 122 = SmallMoney (4 byte data representation) */
#define TDS_DATA_TYPE_INT8            0x7F  /* 127 = BigInt (8 byte data representation) */
/* BYTELEN_TYPE */
#define TDS_DATA_TYPE_GUID            0x24  /* 36 = UniqueIdentifier */
#define TDS_DATA_TYPE_INTN            0x26  /* 38 */
#define TDS_DATA_TYPE_DECIMAL         0x37  /* 55 = Decimal (legacy support) */
#define TDS_DATA_TYPE_NUMERIC         0x3F  /* 63 = Numeric (legacy support) */
#define TDS_DATA_TYPE_BITN            0x68  /* 104 */
#define TDS_DATA_TYPE_DECIMALN        0x6A  /* 106 = Decimal */
#define TDS_DATA_TYPE_NUMERICN        0x6C  /* 108 = Numeric */
#define TDS_DATA_TYPE_FLTN            0x6D  /* 109 */
#define TDS_DATA_TYPE_MONEYN          0x6E  /* 110 */
#define TDS_DATA_TYPE_DATETIMN        0x6F  /* 111 */
#define TDS_DATA_TYPE_DATEN           0x28  /* 40 (introduced in TDS 7.3) */
#define TDS_DATA_TYPE_TIMEN           0x29  /* 41 (introduced in TDS 7.3) */
#define TDS_DATA_TYPE_DATETIME2N      0x2A  /* 42 (introduced in TDS 7.3) */
#define TDS_DATA_TYPE_DATETIMEOFFSETN 0x2B  /* 43 (introduced in TDS 7.3) */
#define TDS_DATA_TYPE_CHAR            0x2F  /* 47 = Char (legacy support) */
#define TDS_DATA_TYPE_VARCHAR         0x27  /* 39 = VarChar (legacy support) */
#define TDS_DATA_TYPE_BINARY          0x2D  /* 45 = Binary (legacy support) */
#define TDS_DATA_TYPE_VARBINARY       0x25  /* 37 = VarBinary (legacy support) */
/* USHORTLEN_TYPE */
#define TDS_DATA_TYPE_BIGVARBIN       0xA5  /* 165 = VarBinary */
#define TDS_DATA_TYPE_BIGVARCHR       0xA7  /* 167 = VarChar */
#define TDS_DATA_TYPE_BIGBINARY       0xAD  /* 173 = Binary */
#define TDS_DATA_TYPE_BIGCHAR         0xAF  /* 175 = Char */
#define TDS_DATA_TYPE_NVARCHAR        0xE7  /* 231 = NVarChar */
#define TDS_DATA_TYPE_NCHAR           0xEF  /* 239 = NChar */
/* LONGLEN_TYPE */
#define TDS_DATA_TYPE_XML             0xF1  /* 241 = XML (introduced in TDS 7.2) */
#define TDS_DATA_TYPE_UDT             0xF0  /* 240 = CLR-UDT (introduced in TDS 7.2) */
#define TDS_DATA_TYPE_TEXT            0x23  /* 35 = Text */
#define TDS_DATA_TYPE_IMAGE           0x22  /* 34 = Image */
#define TDS_DATA_TYPE_NTEXT           0x63  /* 99 = NText */
#define TDS_DATA_TYPE_SSVARIANT       0x62  /* 98 = Sql_Variant (introduced in TDS 7.2) */
#define TDS_DATA_TYPE_INVALID         0xff

#define is_fixedlen_type_sybase(x) (x==SYBINT1      ||            \
                                    x==SYBINT2      ||            \
                                    x==SYBINT4      ||            \
                                    x==SYBINT8      ||            \
                                    x==SYBREAL      ||            \
                                    x==SYBFLT8      ||            \
                                    x==SYBDATETIME  ||            \
                                    x==SYBDATETIME4 ||            \
                                    x==SYBBIT       ||            \
                                    x==SYBMONEY     ||            \
                                    x==SYBMONEY4    ||            \
                                    x==SYBUNIQUE                  \
                                   )

#define is_fixedlen_type_tds(x)    (x==TDS_DATA_TYPE_NULL ||      \
                                    x==TDS_DATA_TYPE_INT1 ||      \
                                    x==TDS_DATA_TYPE_BIT  ||      \
                                    x==TDS_DATA_TYPE_INT2 ||      \
                                    x==TDS_DATA_TYPE_INT4 ||      \
                                    x==TDS_DATA_TYPE_DATETIME4 || \
                                    x==TDS_DATA_TYPE_FLT4 ||      \
                                    x==TDS_DATA_TYPE_MONEY ||     \
                                    x==TDS_DATA_TYPE_DATETIME ||  \
                                    x==TDS_DATA_TYPE_FLT8 ||      \
                                    x==TDS_DATA_TYPE_MONEY4 ||    \
                                    x==TDS_DATA_TYPE_INT8         \
                                   )

#define is_varlen_type_tds(x)     (x==TDS_DATA_TYPE_GUID            ||  \
                                   x==TDS_DATA_TYPE_INTN            ||  \
                                   x==TDS_DATA_TYPE_DECIMAL         ||  \
                                   x==TDS_DATA_TYPE_NUMERIC         ||  \
                                   x==TDS_DATA_TYPE_BITN            ||  \
                                   x==TDS_DATA_TYPE_DECIMALN        ||  \
                                   x==TDS_DATA_TYPE_NUMERICN        ||  \
                                   x==TDS_DATA_TYPE_FLTN            ||  \
                                   x==TDS_DATA_TYPE_MONEYN          ||  \
                                   x==TDS_DATA_TYPE_DATETIMN        ||  \
                                   x==TDS_DATA_TYPE_DATEN           ||  \
                                   x==TDS_DATA_TYPE_TIMEN           ||  \
                                   x==TDS_DATA_TYPE_DATETIME2N      ||  \
                                   x==TDS_DATA_TYPE_DATETIMEOFFSETN ||  \
                                   x==TDS_DATA_TYPE_CHAR            ||  \
                                   x==TDS_DATA_TYPE_VARCHAR         ||  \
                                   x==TDS_DATA_TYPE_BINARY          ||  \
                                   x==TDS_DATA_TYPE_VARBINARY       ||  \
                                   x==TDS_DATA_TYPE_BIGVARBIN       ||  \
                                   x==TDS_DATA_TYPE_BIGVARCHR       ||  \
                                   x==TDS_DATA_TYPE_BIGBINARY       ||  \
                                   x==TDS_DATA_TYPE_BIGCHAR         ||  \
                                   x==TDS_DATA_TYPE_NVARCHAR        ||  \
                                   x==TDS_DATA_TYPE_NCHAR           ||  \
                                   x==TDS_DATA_TYPE_XML             ||  \
                                   x==TDS_DATA_TYPE_UDT             ||  \
                                   x==TDS_DATA_TYPE_TEXT            ||  \
                                   x==TDS_DATA_TYPE_IMAGE           ||  \
                                   x==TDS_DATA_TYPE_NTEXT           ||  \
                                   x==TDS_DATA_TYPE_SSVARIANT           \
                                  )

#define TDS_GEN_NULL        0x00U
#define TDS_CHARBIN_NULL    0xFFFFU
#define TDS_CHARBIN_NULL32  0xFFFFFFFFU

#define TDS_PLP_TERMINATOR  0x0000000000000000ULL
#define TDS_UNKNOWN_PLP_LEN 0xFFFFFFFFFFFFFFFEULL
#define TDS_PLP_NULL        0xFFFFFFFFFFFFFFFFULL

static const struct int_to_string internal_stored_proc_id_names[] = {
    {TDS_SP_CURSOR,          "sp_cursor"         },
    {TDS_SP_CURSOROPEN,      "sp_cursoropen"     },
    {TDS_SP_CURSORPREPARE,     "sp_cursorprepare"  },
    {TDS_SP_CURSOREXECUTE,     "sp_cursorexecute"  },
    {TDS_SP_CURSORPREPEXEC,  "sp_cursorprepexec" },
    {TDS_SP_CURSORUNPREPARE, "sp_cursorunprepare"},
    {TDS_SP_CURSORFETCH,     "sp_cursorfetch"     },
    {TDS_SP_CURSOROPTION,     "sp_cursoroption"     },
    {TDS_SP_CURSORCLOSE,     "sp_cursorclose"     },
    {TDS_SP_EXECUTESQL,      "sp_executesql"     },
    {TDS_SP_PREPARE,         "sp_prepare"         },
    {TDS_SP_EXECUTE,         "sp_execute"         },
    {TDS_SP_PREPEXEC,         "sp_prepexec"         },
    {TDS_SP_PREPEXECRPC,     "sp_prepexecrpc"     },
    {TDS_SP_UNPREPARE,         "sp_unprepare"      },
    {0,                      NULL                 }
};

/* The one byte token at the start of each TDS PDU */
static const struct int_to_string token_names[] = {
    {TDS5_DYNAMIC_TOKEN,        "TDS5 Dynamic SQL"},
    {TDS5_PARAMFMT_TOKEN,        "TDS5 Parameter Format"},
    {TDS5_PARAMFMT2_TOKEN,        "TDS5 Parameter2 Format"},
    {TDS5_PARAMS_TOKEN,         "TDS5 Parameters"},
    {TDS_LANG_TOKEN,            "Language"},
    {TDS_LOGOUT_TOKEN,            "Logout"},
    {TDS_RET_STAT_TOKEN,        "Return Status"},
    {TDS_PROCID_TOKEN,            "Proc ID"},
    {TDS_COL_NAME_TOKEN,        "Column Names"},
    {TDS_COL_INFO_TOKEN,        "Column Info"},
    {TDS_COMPUTE_NAMES_TOKEN,    "Compute Names"},
    {TDS_COMPUTE_RESULT_TOKEN,    "Compute Results"},
    {TDS_ORDER_TOKEN,            "Order"},
    {TDS_ERR_TOKEN,             "Error Message"},
    {TDS_INFO_TOKEN,            "Info Message"},
    {TDS_LOGIN_ACK_TOKEN,        "Login Acknowledgement"},
    {TDS_KEY_TOKEN,             "TDS Key"},
    {TDS_ROW_TOKEN,             "Row"},
    {TDS_CAP_TOKEN,             "Capabilities"},
    {TDS_ENVCHG_TOKEN,           "Environment Change"},
    {TDS_EED_TOKEN,             "Extended Error"},
    {TDS_AUTH_TOKEN,            "Authentication"},
    {TDS_RESULT_TOKEN,            "Results"},
    {TDS_DONE_TOKEN,            "Done"},
    {TDS_DONEPROC_TOKEN,        "Done Proc"},
    {TDS_DONEINPROC_TOKEN,        "Done In Proc"},
    {TDS5_DYNAMIC2_TOKEN,        "TDS5 Dynamic2"},
    {TDS5_ORDERBY2_TOKEN,        "TDS5 OrderBy2"},
    {TDS5_CURDECLARE2_TOKEN,    "TDS5 CurDeclare2"},
    {TDS5_ROWFMT2_TOKEN,        "TDS5 RowFmt2"},
    {TDS5_MSG_TOKEN,            "TDS5 Msg"},
    {TDS_OFFSET_TOKEN,            "Offset"},
    {TDS_CURCLOSE_TOKEN,        "CurClose"},
    {TDS7_COL_METADATA_TOKEN,    "Column Metadata"},
    {TDS_CURFETCH_TOKEN,        "CurFetch"},
    {TDS_CURINFO_TOKEN,         "CurInfo"},
    {TDS_CUROPEN_TOKEN,         "CurOpen"},
    {TDS_CURDECLARE_TOKEN,        "CurDeclare"},
    {TDS7_ALTMETADATA_TOKEN,    "AltMetaData"},
    {TDS_TABNAME_TOKEN,         "Table Name"},
    {TDS7_COL_INFO_TOKEN,        "Column Info"},
    {TDS_OPTIONCMD_TOKEN,        "OptionCmd"},
    {TDS_RETURNVAL_TOKEN,        "Return Value"},
    {TDS_FEATUREEXTACK_TOKEN,    "FeatureExt Acknowledgement"},
    {TDS_NBCROW_TOKEN,            "Row (with Null Bitmap Compression)"},
    {TDS_ALTROW_TOKEN,            "ALTROW"},
    {TDS_SESSIONSTATE_TOKEN,    "Session State"},
    {TDS_DBRPC_TOKEN,            "DBRPC"},
    {TDS_SSPI_TOKEN,            "SSPI"},
    {TDS_FEDAUTHINFO_TOKEN,     "FEDAUTHINFO"},
    {0, NULL}
};

#define tds_little_endian 1

#define TDS_MAX_COLUMNS 256

/*
 * This is where we store the column information to be used in decoding the
 * TDS_ROW_TOKEN tokens.
 */
struct _tds_col {
    char name[256];
    uint32_t utype;
    uint8_t ctype;
    uint8_t precision;
    uint8_t scale;
    uint32_t csize;
};

struct _netlib_data {
    uint32_t num_cols;
    struct _tds_col columns[TDS_MAX_COLUMNS];
};

struct _row{
    char col[64];
};

struct _row_data {
    uint32_t num_cols;
    struct _row row[TDS_MAX_COLUMNS];
};

struct tds7_login_packet_hdr {
    uint32_t total_packet_size;
    uint32_t tds_version;
    uint32_t packet_size;
    uint32_t client_version;
    uint32_t client_pid;
    uint32_t connection_id;
    uint8_t  option_flags1;
    uint8_t  option_flags2;
    uint8_t  sql_type_flags;
    uint8_t  reserved_flags;
    uint32_t time_zone;
    uint32_t collation;
};

struct dts_session {    
    int tds7_version;
    uint32_t src2dst_rsm_len;
    uint32_t dst2src_rsm_len;
};

#define RPC_PARAM_NUM 16
#define TOKEN_NUM 16

struct rpc_param
{
    char rpc_name[64];
    const char *rpc_id;
    char param_value[4096];
};

struct token_data
{
    char token_value[64];
    const char *token_name;
    char token_content[4096];
};

struct tds_info {
    const char * PacketType;
    uint16_t Channel;
    uint8_t PacketNumber;
    char ClientName[64];
    char Username[64];
    char Password[64];
    char AppName[64];
    char ServerName[64];
    char LibraryName[64];
    char Locale[64];
    char DatabaseName[64];
    uint8_t Window;
    uint32_t TDSversion;
    uint32_t Clientversion;
    uint32_t ClientPID;
    uint32_t ConnectionID;
    uint8_t Option_Flags1;
    uint8_t Option_Flags2;
    uint8_t Reserved_Flags;
    uint8_t SQL_Type_Flags;
    uint32_t TimeZone;
    uint32_t Collation;
    char Query[4096];
    char Language_text[64];
    char NTLMSSP[64];
    char GSSAPI[64];
    
    struct token_data token[TOKEN_NUM];
    struct rpc_param param[RPC_PARAM_NUM];
};

static void dissect_tds_type_varbyte(struct dpi_pkt_st *pkt, uint32_t *offset, 
        uint8_t data_type, uint8_t scale, uint8_t plp, int fieldnum,
        char *param_val, uint32_t max_len);
static void dissect_tds_type_info_minimal(uint8_t data_type, uint32_t size, uint8_t *plp);


/*
* If the packet type from the netlib header is a login packet, then dig into
* the packet to see if this is a supported TDS version and verify the otherwise
* weak heuristics of the netlib check.
*/
static uint8_t netlib_check_login_pkt(struct flow_info *flow, const uint8_t *payload, uint32_t offset, uint32_t payload_len, uint8_t type)
{
    uint32_t tds_major, bytes_avail;

    bytes_avail = payload_len - offset;
    /*
    * we have two login packet styles, one for TDS 4.2 and 5.0
    */
    if (type == TDS_LOGIN_PKT) {
        /* Use major version number to validate TDS 4/5 login
        * packet */

        /* Login packet is first in stream and should not be fragmented...
        * if it is we are screwed */
        if (bytes_avail < 467) 
            return 0;
        tds_major = get_uint8_t(payload, 466);
        if (tds_major != 4 && tds_major != 5) {
            return 0;
        }
        /*
        * and one added by Microsoft in SQL Server 7
        */
    } else if (type==TDS_LOGIN7_PKT) {
        if (bytes_avail < 16)
            return 0;
        tds_major = get_uint8_t(payload, 15);
        if (tds_major < 0x70 ||  tds_major > 0x80) {
            return 0;
        }
    } else if (type==TDS_QUERY5_PKT) {
        if (bytes_avail < 9) 
            return 0;
    /* if this is a TDS 5.0 query check the token */
        if (get_uint8_t(payload, 8) != TDS_LANG_TOKEN) {
            return 0;
        }
    }
    /*
    * See if either tcp.destport or tcp.srcport is specified
    * in the preferences as being a TDS port.
    */
    else if (ntohs(flow->tuple.inner.port_src) != 1433
            && ntohs(flow->tuple.inner.port_src) != 2433
            && ntohs(flow->tuple.inner.port_dst) != 1433
            && ntohs(flow->tuple.inner.port_dst) != 2433) {
        return 0;
    }

    return 1;
}

static int detect_tls(const uint8_t *payload, uint32_t payload_len)
{
    uint8_t tls_type, tls_maj_ver, tls_min_ver;
    uint32_t offset = 0, tls_len;

    tls_type = get_uint8_t(payload, offset);
    tls_maj_ver = get_uint8_t(payload, offset + 1);
    tls_min_ver = get_uint8_t(payload, offset + 2);
    tls_len = get_uint16_ntohs(payload, offset + 3);

    if (tls_type >= 0x14
            && tls_type <= 0x18
            && tls_maj_ver == 3
            && tls_min_ver <= 3
            && tls_len + 5 <= payload_len)
    {
        return 1;
    }

    return 0;
}

static void identify_tds(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{

    uint16_t offset = 0;
    uint8_t type;
    uint8_t status;
    uint16_t plen;

    /*
    * If we don't have even enough data for a Netlib header,
    * just say it's not TDS.
    */
    if (payload_len < 8) {            
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TDS);
        return;
    }

    /*
    * Quickly scan all the data we have in order to see if
    * everything in it looks like Netlib traffic.
    */

    /*
    * Check the type field.
    */
    type = get_uint8_t(payload, offset);
    if (!is_valid_tds_type(type)) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TDS);
        return;
    }

    /*
    * Check the status field
    */
    status = get_uint8_t(payload, offset + 1);
    if (!is_valid_tds_status(status) 
            && ntohs(flow->tuple.inner.port_src) != 1433 
            && ntohs(flow->tuple.inner.port_src) != 2433
            && ntohs(flow->tuple.inner.port_dst) != 1433
            && ntohs(flow->tuple.inner.port_dst) != 2433) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TDS);
        return;
    }

    /*
    * Get the length of the PDU.
    */
    plen = get_uint16_ntohs(payload, offset + 2);
    if (plen < 8) {
        /*
        * The length is less than the header length.
        * That's bogus.
        */             
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TDS);
        return;
    }

    if (!netlib_check_login_pkt(flow, payload, offset, payload_len, type)) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TDS);
        return;
    }
    
    flow->real_protocol_id = PROTOCOL_TDS;    
    return;
}

#if 1
static int tds_token_is_fixed_size(uint8_t token)
{
    switch (token) {
        case TDS_DONE_TOKEN:
        case TDS_DONEPROC_TOKEN:
        case TDS_DONEINPROC_TOKEN:
        case TDS_RET_STAT_TOKEN:
        case TDS_PROCID_TOKEN:
        case TDS_LOGOUT_TOKEN:
        case TDS_OFFSET_TOKEN:
            return 1;
        default:
            return 0;
    }
}

static int tds_get_fixed_token_size(uint8_t token, struct dts_session *tds_info)
{
    switch(token) {
        case TDS_DONE_TOKEN:
        case TDS_DONEPROC_TOKEN:
        case TDS_DONEINPROC_TOKEN:
            if (TDS_PROTO_TDS7_1_OR_LESS) {
                return 8;
            } else {
                return 12;
            }
        case TDS_PROCID_TOKEN:
            return 8;
        case TDS_RET_STAT_TOKEN:
            return 4;
        case TDS_LOGOUT_TOKEN:
            return 1;
        case TDS_OFFSET_TOKEN:
            return 4;
        default:
        return 0;
    }
}

static uint32_t tds_get_variable_token_size(const uint8_t *payload, uint32_t payload_len, uint32_t offset, uint8_t token,
        uint32_t *len_field_size_p, uint32_t *len_field_val_p)
{
    UNUSED(payload_len);
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    switch (token) {
        /* some tokens have a 4 byte length field */
        case TDS5_PARAMFMT2_TOKEN:
        case TDS_LANG_TOKEN:
        case TDS5_ORDERBY2_TOKEN:
        case TDS5_CURDECLARE2_TOKEN:
        case TDS5_ROWFMT2_TOKEN:
        case TDS5_DYNAMIC2_TOKEN:
        case TDS_SESSIONSTATE_TOKEN:
            if (offset + 8 <= payload_len) {
                *len_field_size_p = 4;
                *len_field_val_p = pletoh32(payload + offset);
            }
            break;
        /* some have a 1 byte length field */
        case TDS5_MSG_TOKEN:            
            if (offset + 2 <= payload_len) {
                *len_field_size_p = 1;
                *len_field_val_p = get_uint8_t(payload, offset);
            }
            break;
        /* and most have a 2 byte length field */
        default:
            if (offset + 4 <= payload_len) {
                *len_field_size_p = 2;
                *len_field_val_p = pletoh16(payload + offset);
            }
            break;
    }
    return *len_field_val_p + *len_field_size_p + 1;
}
#endif

static int get_size_by_coltype(int servertype)
{
    switch(servertype)
    {
        case SYBINT1:         return 1;
        case SYBINT2:         return 2;
        case SYBINT4:         return 4;
        case SYBINT8:         return 8;
        case SYBREAL:         return 4;
        case SYBFLT8:         return 8;
        case SYBDATETIME:     return 8;
        case SYBDATETIME4:     return 4;
        case SYBBIT:         return 1;
        case SYBBITN:         return 1;
        case SYBMONEY:         return 8;
        case SYBMONEY4:      return 4;
        case SYBUNIQUE:      return 16;

        default:             return -1;
    }
}

/*
 * Process TDS 4 "COL_INFO" token and store relevant information in the
 * _netlib_data structure for later use (see tds_get_row_size)
 *
 * XXX Can TDS 4 be "big-endian" ? we'll assume yes.
 *
 */
static uint8_t dissect_tds_col_info_token(struct dpi_pkt_st *pkt, struct _netlib_data *nl_data, uint32_t offset)
{
    uint8_t var8;
    uint16_t var16;
    uint32_t next, cur;
    uint32_t col;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    if (-1 == dpi_get_le16(pkt, offset + 1, &var16))
        return 0;

    next = offset + var16 + 3;
    cur = offset + 3;

    col = 0;
    while (cur < next) {
        if (col >= TDS_MAX_COLUMNS) {
            nl_data->num_cols = 0;
            return 0;
        }

        nl_data->columns[col].name[0] ='\0';
        
        if (-1 == dpi_get_le16(pkt, cur, &var16))
            break;
        nl_data->columns[col].utype = var16;
        cur += 2;
        cur += 2; /* unknown */
        if (-1 == dpi_get_le16(pkt, cur, &var16))
            break;
        nl_data->columns[col].ctype = var16;
        cur++;

        if (!is_fixedlen_type_tds(nl_data->columns[col].ctype)) {
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;
            nl_data->columns[col].csize = var8;
            cur++;
        } else {
            nl_data->columns[col].csize = get_size_by_coltype(nl_data->columns[col].ctype);
        }
        col += 1;
    } /* while */

    nl_data->num_cols = col;
    return 1;
}

/*
 * Read the results token and store the relevant information in the
 * _netlib_data structure for later use (see tds_get_row_size).
 *
 * TODO: check we don't go past end of the token
 */
static uint8_t read_results_tds5_token(struct dpi_pkt_st *pkt, struct _netlib_data *nl_data, uint32_t offset)
{
    uint8_t var8;
    uint16_t var16;
    uint32_t name_len;
    uint32_t cur;
    uint32_t i;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    cur = offset;

    /*
    * This would be the logical place to check for little/big endianess
    * if we didn't see the login packet.
    * XXX: We'll take a hint
    */
    if (-1 == dpi_get_le16(pkt, cur, &var16))
        return 0;
    nl_data->num_cols = var16;
    if (nl_data->num_cols > TDS_MAX_COLUMNS) {
        nl_data->num_cols = 0;
        return 0;
    }

    cur += 2;

    for (i = 0; i < nl_data->num_cols; i++) {
        if (-1 == dpi_get_uint8(pkt, cur, &var8))
            break;        
        name_len = var8;
        cur++;
        cur += name_len;

        cur++; /* unknown */
        
        if (-1 == dpi_get_le16(pkt, cur, &var16))
            break;
        nl_data->columns[i].utype = var16;
        cur += 2;

        cur += 2; /* unknown */
        
        if (-1 == dpi_get_uint8(pkt, cur, &var8))
            break;    
        nl_data->columns[i].ctype = var8;
        cur++;

        if (!is_fixedlen_type_tds(nl_data->columns[i].ctype)) {
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;    
            nl_data->columns[i].csize = var8;
            cur++;
        } else {
            nl_data->columns[i].csize = get_size_by_coltype(nl_data->columns[i].ctype);
        }
        cur++; /* unknown */
    }
    return 1;
}

static int dissect_tds7_colmetadata_token(struct dpi_pkt_st *pkt, struct _netlib_data *nl_data, uint32_t offset, struct dts_session *tds_info)
{
    uint8_t var8;
    uint16_t var16;
    uint32_t cur = offset;
    uint16_t num_columns; //flags, 
    uint16_t numparts, parti, partlen, msg_len;
    uint8_t type;
    int i; //col_offset;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    if (-1 == dpi_get_le16(pkt, cur, &num_columns))
        return 0;
    nl_data->num_cols = num_columns;
    if (nl_data->num_cols > TDS_MAX_COLUMNS) {
        nl_data->num_cols = 0;
        return 0;
    }
    cur += 2;

    for (i = 0; i != num_columns; i++) {
        //col_offset = cur;
        nl_data->columns[i].name[0] ='\0';
        if (TDS_PROTO_TDS7_1_OR_LESS) {
            if (-1 == dpi_get_le16(pkt, cur, &var16))
                break;
            nl_data->columns[i].utype = var16;
            cur += 2;
        } else {
            if (-1 == dpi_get_le32(pkt, cur, &nl_data->columns[i].utype))
                break;
            cur += 4;
        }

        //flags = pletoh16(payload + cur);
        cur += 2;

        /* TYPE_INFO */
        if (-1 == dpi_get_uint8(pkt, cur, &type))
            break;    
        nl_data->columns[i].ctype = type;
        cur++;

        if (is_fixedlen_type_tds(type)) {
            nl_data->columns[i].csize = get_size_by_coltype(type);
        } else if(is_varlen_type_tds(type)) {
            switch(type)
            {
                case TDS_DATA_TYPE_GUID:
                case TDS_DATA_TYPE_INTN:
                case TDS_DATA_TYPE_BITN:
                case TDS_DATA_TYPE_FLTN:
                case TDS_DATA_TYPE_MONEYN:
                case TDS_DATA_TYPE_DATETIMN:
                case TDS_DATA_TYPE_CHAR:
                case TDS_DATA_TYPE_VARCHAR:
                case TDS_DATA_TYPE_BINARY:
                case TDS_DATA_TYPE_VARBINARY:
                {
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;    
                    nl_data->columns[i].csize = var8;
                    cur++;
                    break;
                }
                case TDS_DATA_TYPE_DATEN:
                {
                    break;
                }
                case TDS_DATA_TYPE_DECIMAL:
                case TDS_DATA_TYPE_NUMERIC:
                case TDS_DATA_TYPE_DECIMALN:
                case TDS_DATA_TYPE_NUMERICN:
                {
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;
                    nl_data->columns[i].csize = var8;
                    cur++;
                    
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;
                    nl_data->columns[i].precision = var8;
                    cur++;
                    
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;
                    nl_data->columns[i].scale = var8;
                    cur++;
                    break;
                }
                case TDS_DATA_TYPE_TIMEN:
                case TDS_DATA_TYPE_DATETIME2N:
                case TDS_DATA_TYPE_DATETIMEOFFSETN:
                {
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;
                    nl_data->columns[i].scale = var8;
                    cur++;
                    break;
                }
                case TDS_DATA_TYPE_BIGVARBIN:
                {
                    if (-1 == dpi_get_le16(pkt, cur, &var16))
                        break;
                    nl_data->columns[i].csize = var16;
                    cur += 2;
                    break;
                }
                case TDS_DATA_TYPE_BIGVARCHR:
                {
                    if (-1 == dpi_get_le16(pkt, cur, &var16))
                        break;
                    nl_data->columns[i].csize = var16;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_large_type_size, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_codepage, tvb, cur, 2, ENC_LITTLE_ENDIAN );
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_flags, tvb, cur, 2, ENC_LITTLE_ENDIAN );
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_charset_id, tvb, cur, 1, ENC_LITTLE_ENDIAN );
                    cur +=1;
                    break;
                }
                case TDS_DATA_TYPE_BIGBINARY:
                {
                    if (-1 == dpi_get_le16(pkt, cur, &var16))
                        break;
                    nl_data->columns[i].csize = var16;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_large_type_size, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                    cur += 2;
                    break;
                }
                case TDS_DATA_TYPE_BIGCHAR:
                case TDS_DATA_TYPE_NVARCHAR:
                case TDS_DATA_TYPE_NCHAR:
                {
                    if (-1 == dpi_get_le16(pkt, cur, &var16))
                        break;
                    nl_data->columns[i].csize = var16;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_large_type_size, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_codepage, tvb, cur, 2, ENC_LITTLE_ENDIAN );
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_flags, tvb, cur, 2, ENC_LITTLE_ENDIAN );
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_charset_id, tvb, cur, 1, ENC_LITTLE_ENDIAN );
                    cur +=1;
                    break;
                }
                case TDS_DATA_TYPE_XML:
                {
                    uint8_t schema_present;
                    if (-1 == dpi_get_uint8(pkt, cur, &schema_present))
                        break;    
                    cur += 1;

                    if (schema_present)
                    {
                        if (-1 == dpi_get_uint8(pkt, cur, &var8))
                            break;                            
                        msg_len = var8;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_dbname_length, tvb, cur, 1, ENC_NA);
                        cur += 1;
                        if (msg_len != 0) {
                            msg_len *= 2;
                            //proto_tree_add_item(col_tree, hf_tds_colmetadata_dbname, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                            cur += msg_len;
                        }
                        
                        if (-1 == dpi_get_uint8(pkt, cur, &var8))
                            break;    
                        msg_len = var8;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_owningschema_length, tvb, cur, 1, ENC_NA);
                        cur += 1;
                        if (msg_len != 0) {
                            msg_len *= 2;
                            //proto_tree_add_item(col_tree, hf_tds_colmetadata_owningschema, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                            cur += msg_len;
                        }
                        if (-1 == dpi_get_uint8(pkt, cur, &var8))
                            break;    
                        msg_len = var8;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_typename_length, tvb, cur, 1, ENC_NA);
                        cur += 1;
                        if (msg_len != 0) {
                            msg_len *= 2;
                            //proto_tree_add_item(col_tree, hf_tds_colmetadata_typename, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                            cur += msg_len;
                        }
                        if (-1 == dpi_get_uint8(pkt, cur, &var8))
                            break;    
                        msg_len = var8;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_xmlschemacollection_length, tvb, cur, 1, ENC_NA);
                        cur += 1;
                        if (msg_len != 0) {
                            msg_len *= 2;
                            //proto_tree_add_item(col_tree, hf_tds_colmetadata_xmlschemacollection, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                            cur += msg_len;
                        }
                    }

                    break;
                }
                case TDS_DATA_TYPE_UDT:
                {
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_maxbytesize, tvb, cur, 2, ENC_NA|ENC_LITTLE_ENDIAN);
                    cur += 2;
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;    
                    msg_len = var8;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_dbname_length, tvb, cur, 1, ENC_NA);
                    cur += 1;
                    if(msg_len != 0) {
                        msg_len *= 2;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_dbname, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                        cur += msg_len;
                    }
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;    
                    msg_len = var8;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_schemaname_length, tvb, cur, 1, ENC_NA);
                    cur += 1;
                    if(msg_len != 0) {
                        msg_len *= 2;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_schemaname, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                        cur += msg_len;
                    }
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;    
                    msg_len = var8;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_typename_length, tvb, cur, 1, ENC_NA);
                    cur += 1;
                    if(msg_len != 0) {
                        msg_len *= 2;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_typename, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                        cur += msg_len;
                    }
                    
                    if (-1 == dpi_get_le16(pkt, cur, &var16))
                        break;    
                    msg_len = var16;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_assemblyqualifiedname_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                    cur += 2;
                    if(msg_len != 0) {
                        msg_len *= 2;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_assemblyqualifiedname, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                        cur += msg_len;
                    }

                    break;
                }
                case TDS_DATA_TYPE_IMAGE:
                {
                    cur += 4;

                    /* Table name */
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;    
                    numparts = var8;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_table_name_parts, tvb, cur, 1, ENC_LITTLE_ENDIAN);
                    cur += 1;

                    for (parti = 0; parti < numparts; parti++) {
                        if (-1 == dpi_get_le16(pkt, cur, &var16))
                            break;    
                        partlen = var16;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_table_name_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_table_name, tvb, cur + 2, partlen * 2, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                        cur += 2 + (partlen * 2);
                    }
                    break;
                }
                case TDS_DATA_TYPE_TEXT:
                case TDS_DATA_TYPE_NTEXT:
                {
                    /* Not sure what we are stepping over here */
                    cur += 2;
                    if (-1 == dpi_get_le16(pkt, cur, &var16))
                        break;    
                    nl_data->columns[i].csize = var16;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_large_type_size, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                    cur += 2;

                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_codepage, tvb, cur, 2, ENC_LITTLE_ENDIAN );
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_flags, tvb, cur, 2, ENC_LITTLE_ENDIAN );
                    cur += 2;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_collate_charset_id, tvb, cur, 1, ENC_LITTLE_ENDIAN );
                    cur +=1;

                    /* Table name */
                    if (-1 == dpi_get_uint8(pkt, cur, &var8))
                        break;    
                    numparts = var8;
                    //proto_tree_add_item(col_tree, hf_tds_colmetadata_table_name_parts, tvb, cur, 1, ENC_LITTLE_ENDIAN);
                    cur += 1;

                    for(parti = 0; parti < numparts; parti++)
                    {
                        if (-1 == dpi_get_le16(pkt, cur, &var16))
                            break;    
                        partlen = var16;
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_table_name_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
                        //proto_tree_add_item(col_tree, hf_tds_colmetadata_table_name, tvb, cur + 2, partlen * 2, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                        cur += 2 + (partlen * 2);
                    }

                    break;
                }
                case TDS_DATA_TYPE_SSVARIANT:
                {
                    cur += 4;
                    break;
                }
            }
        }

        /* ColName */
        if (-1 == dpi_get_uint8(pkt, cur, &var8))
            break;    
        msg_len = var8;
        //proto_tree_add_item(col_tree, hf_tds_colmetadata_colname_length, tvb, cur, 1, ENC_NA);
        cur += 1;
        if(msg_len != 0) {
            msg_len *= 2;
            if (msg_len + cur > pkt->payload_len)
                return pkt->payload_len;
            get_utf_16_string(nl_data->columns[i].name, sizeof(nl_data->columns[i].name), pkt->payload + cur, msg_len, ENC_LITTLE_ENDIAN);
            cur += msg_len;
        }

    }

    return cur - offset;
}

static int dissect_tds_done_token(struct dpi_pkt_st *pkt, uint32_t offset, struct dts_session *tds_info, struct token_data *token)
{
    uint32_t cur = offset;

    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    //proto_tree_add_item(tree, hf_tds_done_status, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;
    //proto_tree_add_item(tree, hf_tds_done_curcmd, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;
    if (TDS_PROTO_TDS7_1_OR_LESS) {
        if (cur + 4 > pkt->payload_len)
            return pkt->payload_len - offset;
        snprintf(token->token_content, sizeof(token->token_content), "%u", pletoh32(pkt->payload + cur));
        //proto_tree_add_item(tree, hf_tds_done_donerowcount_32, tvb, cur, 4, encoding);
        cur += 4;
    } else {
        if (cur + 8 > pkt->payload_len)
            return pkt->payload_len - offset;
        snprintf(token->token_content, sizeof(token->token_content), "%llu", (unsigned long long)pletoh64(pkt->payload + cur));
        //proto_tree_add_item(tree, hf_tds_done_donerowcount_64, tvb, cur, 8, encoding);
        cur += 8;
    }

    return cur - offset;
}

static int dissect_tds_doneproc_token(struct dpi_pkt_st *pkt, uint32_t offset, struct dts_session *tds_info, struct token_data *token)
{
    uint32_t cur = offset;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    //proto_tree_add_item(tree, hf_tds_doneproc_status, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;
    //proto_tree_add_item(tree, hf_tds_doneproc_curcmd, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;
    if (TDS_PROTO_TDS7_1_OR_LESS) {
        if (cur + 4 > pkt->payload_len)
            return pkt->payload_len - offset;
        //proto_tree_add_item(tree, hf_tds_doneproc_donerowcount_32, tvb, cur, 4, encoding);        
        snprintf(token->token_content, sizeof(token->token_content), "%u", pletoh32(pkt->payload + cur));
        cur += 4;
    } else {
        if (cur + 8 > pkt->payload_len)
            return pkt->payload_len - offset;
        snprintf(token->token_content, sizeof(token->token_content), "%llu", (unsigned long long)pletoh64(pkt->payload + cur));
        //proto_tree_add_item(tree, hf_tds_doneproc_donerowcount_64, tvb, cur, 8, encoding);
        cur += 8;
    }

    return cur - offset;
}

static int dissect_tds_doneinproc_token(struct dpi_pkt_st *pkt, uint32_t offset, struct dts_session *tds_info, struct token_data *token)
{
    uint32_t cur = offset;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    //proto_tree_add_item(tree, hf_tds_doneinproc_status, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;
    //proto_tree_add_item(tree, hf_tds_doneinproc_curcmd, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;
    if (TDS_PROTO_TDS7_1_OR_LESS) {
        if (cur + 4 > pkt->payload_len)
            return pkt->payload_len - offset;
        //proto_tree_add_item(tree, hf_tds_doneinproc_donerowcount_32, tvb, cur, 4, encoding);
        snprintf(token->token_content, sizeof(token->token_content), "%u", pletoh32(pkt->payload + cur));
        cur += 4;
    } else {
        if (cur + 8 > pkt->payload_len)
            return pkt->payload_len - offset;
        snprintf(token->token_content, sizeof(token->token_content), "%llu", (unsigned long long)pletoh64(pkt->payload + cur));
        //proto_tree_add_item(tree, hf_tds_doneinproc_donerowcount_64, tvb, cur, 8, encoding);
        cur += 8;
    }

    return cur - offset;
}

static int dissect_tds_envchg_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint8_t var8;
    uint8_t env_type;
    uint16_t total_len = 0xff;
    uint32_t cur = offset;
    uint32_t new_len, old_len;

    //proto_tree_add_item(tree, hf_tds_envchg_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    if (-1 == dpi_get_le16(pkt, cur, &total_len))
        return total_len + 2;
    cur += 2;

    if (-1 == dpi_get_uint8(pkt, cur, &env_type))
        return total_len + 2;
    //proto_tree_add_item(tree, hf_tds_envchg_type, tvb, cur, 1, ENC_NA);
    cur += 1;

    /* Read new value */
    switch (env_type)
    {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 13:
        case 19:
            /* B_VARCHAR, Unicode strings */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;
            new_len = var8 * 2;
            //proto_tree_add_item(tree, hf_tds_envchg_newvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;
            if (new_len > 0) {
                //proto_tree_add_item(tree, hf_tds_envchg_newvalue_string, tvb, cur, new_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                cur += new_len;
            }

            break;

        case 7:
            /* parse collation info structure. From http://www.freetds.org/tds.html#collate */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;
            new_len = var8;
            //proto_tree_add_item(tree, hf_tds_envchg_newvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;

            //proto_tree_add_item(tree, hf_tds_envchg_collate_codepage, tvb, cur, 2, ENC_LITTLE_ENDIAN );
            //proto_tree_add_item(tree, hf_tds_envchg_collate_flags, tvb, cur + 2, 2, ENC_LITTLE_ENDIAN );
            //proto_tree_add_item(tree, hf_tds_envchg_collate_charset_id, tvb, cur + 4, 1, ENC_LITTLE_ENDIAN );
            cur += new_len;

            break;

        case 8:
        case 12:
        case 16:
            /* B_VARBYTE */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;
            new_len = var8;
            //proto_tree_add_item(tree, hf_tds_envchg_newvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;
            if(new_len > 0)
            {
                //proto_tree_add_item(tree, hf_tds_envchg_newvalue_bytes, tvb, cur, new_len, ENC_NA);
                cur += new_len;
            }
            break;

        case 9:
        case 10:
        case 11:
        case 17:
        case 18:
            /* %x00 */
            //proto_tree_add_item(tree, hf_tds_envchg_newvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;
            break;

        case 15:
            /* L_VARBYTE */
            break;

        case 20:
            break;

    }

    /* Read old value */
    switch(env_type)
    {
        case 1:
        case 2:
        case 3:
        case 4:
            /* B_VARCHAR, Unicode strings */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;
            old_len = var8 * 2;
            //proto_tree_add_item(tree, hf_tds_envchg_oldvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;
            if(old_len > 0)
            {
                //proto_tree_add_item(tree, hf_tds_envchg_oldvalue_string, tvb, cur, old_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                cur += old_len;
            }
            break;

        case 5:
        case 6:
        case 8:
        case 12:
        case 13:
        case 15:
        case 16:
        case 18:
        case 19:
            /* %x00 */
            //proto_tree_add_item(tree, hf_tds_envchg_oldvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;
            break;

        case 7:
        case 9:
        case 10:
        case 11:
        case 17:
            /* B_VARBYTE */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;
            old_len = var8;
            //proto_tree_add_item(tree, hf_tds_envchg_oldvalue_length, tvb, cur, 1, ENC_NA);
            cur += 1;
            if(old_len > 0)
            {
                //proto_tree_add_item(tree, hf_tds_envchg_oldvalue_bytes, tvb, cur, old_len, ENC_NA);
                cur += old_len;
            }
            break;

        case 20:
            break;
    }

    //return cur - offset;
    return total_len + 2;
}

static int dissect_tds_error_token(struct dpi_pkt_st *pkt, uint32_t offset, struct dts_session *tds_info)
{
    uint8_t var8;
    uint16_t var16, total_len = 0xff;
    uint32_t cur = offset;
    uint32_t msg_len;
    uint32_t srvr_len, proc_len;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;

    //proto_tree_add_item(tree, hf_tds_error_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    if (-1 == dpi_get_le16(pkt, cur, &total_len))
        goto out;
    cur += 2;

    //proto_tree_add_item(tree, hf_tds_error_number, tvb, cur, 4, encoding);
    cur += 4;
    //proto_tree_add_item(tree, hf_tds_error_state, tvb, cur, 1, ENC_NA);
    cur += 1;
    //proto_tree_add_item(tree, hf_tds_error_class, tvb, cur, 1, ENC_NA);
    cur += 1;
    
    if (-1 == dpi_get_le16(pkt, cur, &var16))
        goto out;
    msg_len = var16;
    //proto_tree_add_item_ret_uint(tree, hf_tds_error_msgtext_length, tvb, cur, 2, encoding, &msg_len);
    cur +=2;

    msg_len *= 2;
    //proto_tree_add_item(tree, hf_tds_error_msgtext, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
    cur += msg_len;
    
    if (-1 == dpi_get_uint8(pkt, cur, &var8))
        goto out;
    srvr_len = var8;
    //proto_tree_add_item_ret_uint(tree, hf_tds_error_servername_length, tvb, cur, 1, ENC_NA, &srvr_len);
    cur +=1;
    if(srvr_len) {
        srvr_len *=2;
        //proto_tree_add_item(tree, hf_tds_error_servername, tvb, cur, srvr_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
        cur += srvr_len;
    }

    if (-1 == dpi_get_uint8(pkt, cur, &var8))
        goto out;
    proc_len = var8;
    //proto_tree_add_item_ret_uint(tree, hf_tds_error_procname_length, tvb, cur, 1, ENC_NA, &proc_len);
    cur +=1;
    if(proc_len) {
        proc_len *=2;
        //proto_tree_add_item(tree, hf_tds_error_procname, tvb, cur, proc_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
        cur += proc_len;
    }

    if (TDS_PROTO_TDS7_1_OR_LESS) {
        //proto_tree_add_item(tree, hf_tds_error_linenumber_16, tvb, cur, 2, encoding);
        cur += 2;
    } else {
        //proto_tree_add_item(tree, hf_tds_error_linenumber_32, tvb, cur, 4, encoding);
        cur += 4;
    }

out:
    //return cur - offset;
    return total_len + 2;
}

static int dissect_tds_info_token(struct dpi_pkt_st *pkt, uint32_t offset, struct dts_session *tds_info)
{
    uint8_t var8;
    uint16_t var16, total_len = 0xff;
    uint32_t cur = offset;
    uint32_t msg_len;
    uint32_t srvr_len, proc_len;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;


    if (-1 == dpi_get_le16(pkt, cur, &total_len))
        goto out;
    cur += 2;

    //proto_tree_add_item(tree, hf_tds_info_number, tvb, cur, 4, encoding);
    cur += 4;
    //proto_tree_add_item(tree, hf_tds_info_state, tvb, cur, 1, ENC_NA);
    cur +=1;
    //proto_tree_add_item(tree, hf_tds_info_class, tvb, cur, 1, ENC_NA);
    cur +=1;
    if (-1 == dpi_get_le16(pkt, cur, &var16))
        goto out;
    msg_len = var16;
    //proto_tree_add_item_ret_uint(tree, hf_tds_info_msgtext_length, tvb, cur, 2, encoding, &msg_len);
    cur +=2;

    msg_len *= 2;
    //proto_tree_add_item(tree, hf_tds_info_msgtext, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
    cur += msg_len;
    
    if (-1 == dpi_get_uint8(pkt, cur, &var8))
        goto out;
    srvr_len = var8;
    //proto_tree_add_item_ret_uint(tree, hf_tds_info_servername_length, tvb, cur, 1, ENC_NA, &srvr_len);
    cur += 1;
    if(srvr_len) {
        srvr_len *=2;
        //proto_tree_add_item(tree, hf_tds_info_servername, tvb, cur, srvr_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
        cur += srvr_len;
    }

    if (-1 == dpi_get_uint8(pkt, cur, &var8))
        goto out;
    proc_len = var8;
    //proto_tree_add_item_ret_uint(tree, hf_tds_info_procname_length, tvb, cur, 1, ENC_NA, &proc_len);
    cur +=1;
    if(proc_len) {
        proc_len *=2;
        //proto_tree_add_item(tree, hf_tds_info_procname, tvb, cur, proc_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
        cur += proc_len;
    }

    if (TDS_PROTO_TDS7_1_OR_LESS) {
        //proto_tree_add_item(tree, hf_tds_info_linenumber_16, tvb, cur, 2, encoding);
        cur += 2;
    } else {
        //proto_tree_add_item(tree, hf_tds_info_linenumber_32, tvb, cur, 4, encoding);
        cur += 4;
    }

out:
    return total_len + 2;
    //return cur - offset;
}

static int dissect_tds_featureextack_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint8_t featureid;
    uint32_t featureackdatalen;
    uint32_t cur = offset;

    while (cur + 5 <= pkt->payload_len)
    {
        featureid = get_uint8_t(pkt->payload, cur);
        featureackdatalen = pletoh32(pkt->payload + cur + 1);

        //feature_item = proto_tree_add_item(tree, hf_tds_featureextack_feature, tvb, cur, featureid == 0xff ? 1 : 5 + featureackdatalen, ENC_NA);
        //feature_tree = proto_item_add_subtree(feature_item, ett_tds_col);

        //proto_tree_add_item(feature_tree, hf_tds_featureextack_featureid, tvb, cur, 1, ENC_LITTLE_ENDIAN);
        cur += 1;

        if(featureid == 0xff)
            break;

        //proto_tree_add_item(feature_tree, hf_tds_featureextack_featureackdatalen, tvb, cur, 4, ENC_LITTLE_ENDIAN);
        cur += 4;

        //proto_tree_add_item(feature_tree, hf_tds_featureextack_featureackdata, tvb, cur, featureackdatalen, ENC_NA);
        cur += featureackdatalen;
    }

    return cur - offset;
}

static int dissect_tds_login_ack_token(struct dpi_pkt_st *pkt, uint32_t offset, struct dts_session *tds_info)
{
    uint8_t msg_len;
    uint32_t tds_version;
    uint32_t cur = offset;

    //proto_tree_add_item(tree, hf_tds_loginack_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;

    //proto_tree_add_item(tree, hf_tds_loginack_interface, tvb, cur, 1, ENC_NA);
    cur +=1;
    
    if (-1 == dpi_get_be32(pkt, cur, &tds_version))
        goto out;    
    //proto_tree_add_item_ret_uint(tree, hf_tds_loginack_tdsversion, tvb, cur, 4, ENC_BIG_ENDIAN, &tds_version);
    switch (tds_version) {
        case 0x07000000:
            tds_info->tds7_version = TDS_PROTOCOL_7_0;
            break;
        case 0x07010000:
        case 0x71000001:
            tds_info->tds7_version = TDS_PROTOCOL_7_1;
            break;
        case 0x72090002:
            tds_info->tds7_version = TDS_PROTOCOL_7_2;
            break;
        case 0x730A0003:
            tds_info->tds7_version = TDS_PROTOCOL_7_3A;
            break;
        case 0x730B0003:
            tds_info->tds7_version = TDS_PROTOCOL_7_3B;
            break;
        case 0x74000004:
            tds_info->tds7_version = TDS_PROTOCOL_7_4;
            break;
        default:
            tds_info->tds7_version = TDS_PROTOCOL_7_4;
            break;
    }
    cur += 4;

    if (-1 == dpi_get_uint8(pkt, cur, &msg_len))
        goto out;
    cur +=1;

    msg_len *= 2;
    //proto_tree_add_item(tree, hf_tds_loginack_progname, tvb, cur, msg_len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
    cur += msg_len;
    //proto_tree_add_item(tree, hf_tds_loginack_progversion, tvb, cur, 4, ENC_NA);
    cur += 4;

out:
    return cur - offset;
}

static int dissect_tds_nbc_row_token(struct dpi_pkt_st *pkt, struct _netlib_data *nl_data, uint32_t offset,
        struct dts_session *tds_info, struct _row_data *info)
{
    UNUSED(tds_info);
    uint8_t var8;
    uint32_t relbyte, relbit, i, cur;
    uint8_t plp = 0;
    
    info->num_cols = nl_data->num_cols;

    cur = offset + nl_data->num_cols / 8;
    if ((nl_data->num_cols % 8) != 0) 
        cur++;

    for (i = 0; i < nl_data->num_cols; i++) {
        
        if (-1 == dpi_get_uint8(pkt, offset + i / 8, &var8))
            break;    
        relbyte = var8;
        relbit = relbyte & (1 << (i % 8));
        if(relbit == 0)
        {
            dissect_tds_type_info_minimal(nl_data->columns[i].ctype, nl_data->columns[i].csize, &plp);

            if(nl_data->columns[i].ctype == TDS_DATA_TYPE_NTEXT 
                    || nl_data->columns[i].ctype == TDS_DATA_TYPE_TEXT 
                    || nl_data->columns[i].ctype == TDS_DATA_TYPE_IMAGE)
            {
            /* TextPointer */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;                
            cur += 1 + var8;

            /* Timestamp */
            cur += 8;
            }
            dissect_tds_type_varbyte(pkt, &cur, nl_data->columns[i].ctype, nl_data->columns[i].scale, plp, i + 1, 
                    info->row[i].col, sizeof(info->row[i].col));

            //dissect_tds_type_varbyte(tvb, &cur, pinfo, tree, hf_tds_row_field, nl_data->columns[i]->ctype, nl_data->columns[i]->scale, plp, i+1);
        }
    }

    return cur - offset;
}

static int dissect_tds_row_token(struct dpi_pkt_st *pkt, struct _netlib_data *nl_data, uint32_t offset,
        struct dts_session *tds_info, struct _row_data *info)
{
    UNUSED(tds_info);
    uint8_t var8;
    uint32_t cur = offset, i, type;
    uint8_t plp = 0;

    info->num_cols = nl_data->num_cols;

    for (i = 0; i < nl_data->num_cols; i++) {
        type = nl_data->columns[i].ctype;
        dissect_tds_type_info_minimal(type, nl_data->columns[i].csize, &plp);

        if(nl_data->columns[i].ctype == TDS_DATA_TYPE_NTEXT ||
            nl_data->columns[i].ctype == TDS_DATA_TYPE_TEXT ||
            nl_data->columns[i].ctype == TDS_DATA_TYPE_IMAGE)
        {
            /* TextPointer */
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                break;    

            cur += 1 + var8;

            /* Timestamp */
            cur += 8;
        }

        dissect_tds_type_varbyte(pkt, &cur, type, nl_data->columns[i].scale, plp, i + 1, 
                info->row[i].col, sizeof(info->row[i].col));

    }

    return cur - offset;
}

static int dissect_tds_offset_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    UNUSED(pkt);
    uint32_t cur = offset;

    //proto_tree_add_item(tree, hf_tds_offset_id, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    //proto_tree_add_item(tree, hf_tds_offset_len, tvb, cur + 2, 2, ENC_LITTLE_ENDIAN);
    cur += 4;

    return cur - offset;
}

static int dissect_tds_order_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint32_t cur = offset;
    uint16_t i, length;
    
    if (-1 == dpi_get_be16(pkt, cur, &length))
        goto out;    

    //proto_tree_add_item(tree, hf_tds_order_length, tvb, cur, 2, ENC_LITTLE_ENDIAN);
    cur += 2;

    for (i = 0; i < length / 2; i++) {
        //proto_tree_add_item(tree, hf_tds_order_colnum, tvb, cur, 2, ENC_LITTLE_ENDIAN);
        cur += 2;
    }

out:
    return cur - offset;
}

static int dissect_tds_returnstatus_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    UNUSED(pkt);
    uint32_t cur = offset;

    //proto_tree_add_item(tree, hf_tds_returnstatus_value, tvb, cur, 4, ENC_LITTLE_ENDIAN);
    cur += 4;

    return cur - offset;
}

static int dissect_tds_sessionstate_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint8_t var8;
    uint16_t statelen;
    uint32_t cur = offset, len;
    
    if (-1 == dpi_get_be32(pkt, cur, &len))
        goto out;    

    //proto_tree_add_item_ret_uint(tree, hf_tds_sessionstate_length, tvb, cur, 4, ENC_LITTLE_ENDIAN, &len);
    cur += 4;

    //proto_tree_add_item(tree, hf_tds_sessionstate_seqno, tvb, cur, 4, ENC_LITTLE_ENDIAN);
    cur += 4;

    //proto_tree_add_item(tree, hf_tds_sessionstate_status, tvb, cur, 1, ENC_LITTLE_ENDIAN);
    cur += 1;

    while ((cur - offset - 3) < len) {
        //proto_tree_add_item(tree, hf_tds_sessionstate_stateid, tvb, cur, 1, ENC_LITTLE_ENDIAN);
        cur += 1;
        
        if (-1 == dpi_get_uint8(pkt, cur, &var8))
            goto out;    

        if (var8 == 0xFF) {
            cur += 1;
            
            if (-1 == dpi_get_be16(pkt, cur, &statelen))
                goto out;    
            //proto_tree_add_item(tree, hf_tds_sessionstate_statelen, tvb, cur, 2, ENC_LITTLE_ENDIAN);
            cur += 2;
        } else {
            if (-1 == dpi_get_uint8(pkt, cur, &var8))
                goto out;
            statelen = var8;
            //proto_tree_add_item(tree, hf_tds_sessionstate_statelen, tvb, cur, 1, ENC_LITTLE_ENDIAN);
            cur += 1;
        }

        //proto_tree_add_item(tree, hf_tds_sessionstate_statevalue, tvb, cur, statelen, ENC_NA);
        cur += statelen;
    }

out:
    return cur - offset;
}

static int dissect_tds_sspi_token(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint16_t var16;
    uint32_t cur = offset, len_field_val;
    //int encoding = tds_little_endian ? ENC_LITTLE_ENDIAN : ENC_BIG_ENDIAN;
    if (-1 == dpi_get_le16(pkt, cur, &var16))
        goto out;    

    len_field_val = var16 * 2;
    cur += 2;

    if (len_field_val) {
        //proto_tree_add_item(tree, hf_tds_sspi_buffer, tvb, cur, len_field_val, ENC_NA);
        cur += len_field_val;
    }

out:
    return cur - offset;
}

static uint8_t variant_propbytes(uint8_t type)
{
    switch (type)
    {
        /* FIXEDLENTYPE */
        case TDS_DATA_TYPE_BIT: return 0;
        case TDS_DATA_TYPE_INT1: return 0;
        case TDS_DATA_TYPE_INT2: return 0;
        case TDS_DATA_TYPE_INT4: return 0;
        case TDS_DATA_TYPE_INT8: return 0;
        case TDS_DATA_TYPE_DATETIME: return 0;
        case TDS_DATA_TYPE_DATETIME4: return 0;
        case TDS_DATA_TYPE_FLT4: return 0;
        case TDS_DATA_TYPE_FLT8: return 0;
        case TDS_DATA_TYPE_MONEY: return 0;
        case TDS_DATA_TYPE_MONEY4: return 0;

        /* BYTELEN_TYPE */
        case TDS_DATA_TYPE_DATEN: return 0;
        case TDS_DATA_TYPE_GUID: return 0;
        case TDS_DATA_TYPE_TIMEN: return 1;
        case TDS_DATA_TYPE_DATETIME2N: return 1;
        case TDS_DATA_TYPE_DATETIMEOFFSETN: return 1;
        case TDS_DATA_TYPE_DECIMALN: return 2;
        case TDS_DATA_TYPE_NUMERICN: return 2;

        /* USHORTLEN_TYPE */
        case TDS_DATA_TYPE_BIGVARBIN: return 2;
        case TDS_DATA_TYPE_BIGVARCHR: return 7;
        case TDS_DATA_TYPE_BIGBINARY: return 2;
        case TDS_DATA_TYPE_BIGCHAR: return 7;
        case TDS_DATA_TYPE_NVARCHAR: return 7;
        case TDS_DATA_TYPE_NCHAR: return 7;

        default: return 0;
    }
}

static void dissect_tds_type_info_minimal(uint8_t data_type, uint32_t size, uint8_t *plp)
{
    *plp = 0; /* most types are not Partially Length-Prefixed */
    /* optional TYPE_VARLEN for variable length types */
    switch(data_type) {
        /* USHORTLEN_TYPE */
        case TDS_DATA_TYPE_BIGVARCHR:       /* VarChar */
        case TDS_DATA_TYPE_BIGVARBIN:       /* VarBinary */
        case TDS_DATA_TYPE_NVARCHAR:        /* NVarChar */
            /* A type with unlimited max size, known as varchar(max), varbinary(max) and nvarchar(max),
            which has a max size of 0xFFFF, defined by PARTLENTYPE. This class of types was introduced in TDS 7.2. */
            if(size == 0xFFFF)
            *plp = 1;
            break;
        /* LONGLEN_TYPE */
        case TDS_DATA_TYPE_XML:             /* XML (introduced in TDS 7.2) */
        case TDS_DATA_TYPE_UDT:             /* CLR-UDT (introduced in TDS 7.2) */
            *plp = 1;
            break;
    }
}

static void dissect_tds_type_varbyte(struct dpi_pkt_st *pkt, uint32_t *offset, 
        uint8_t data_type, uint8_t scale, uint8_t plp, int fieldnum,
        char *param_val, uint32_t max_len)
{
    UNUSED(fieldnum);

    size_t copy_len;
    uint32_t length;
    uint64_t plp_length;

    uint8_t tmp;
    uint8_t var8;
    uint16_t var16;
    uint32_t var32;    
    uint64_t var64;
    //int encoding = tds_little_endian;

    if (plp) {
        if (-1 == dpi_get_le64(pkt, *offset, &plp_length))
            return;
        *offset += 8;
        if(plp_length == TDS_PLP_NULL) {
            //proto_item_append_text(length_item, " (PLP_NULL)");
        } else {
            if(plp_length == TDS_UNKNOWN_PLP_LEN) {
                //proto_item_append_text(length_item, " (UNKNOWN_PLP_LEN)");
            }
            while (1) {
                if (-1 == dpi_get_le32(pkt, *offset, &length))
                    return;
                *offset += 4;
                if (length == TDS_PLP_TERMINATOR) {
                    break;
                }
                if (*offset + length > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }
                switch(data_type) {
                    case TDS_DATA_TYPE_BIGVARBIN: /* VarBinary */
                        copy_len = length >= max_len ? max_len : length + 1;
                        snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));
                        break;
                    case TDS_DATA_TYPE_BIGVARCHR: /* VarChar */
                        copy_len = length >= max_len ? max_len : length + 1;
                        snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));
                        break;
                    case TDS_DATA_TYPE_NVARCHAR:  /* NVarChar */
                        get_utf_16_string(param_val, max_len, pkt->payload + *offset, length, ENC_LITTLE_ENDIAN);
                        break;
                    case TDS_DATA_TYPE_XML:       /* XML (introduced in TDS 7.2) */
                    case TDS_DATA_TYPE_UDT:       /* CLR-UDT (introduced in TDS 7.2) */
                        copy_len = length >= max_len ? max_len : length + 1;
                        snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));                        
                        break;
                    default:
                        break;
                    /* no other data type sets plp = TRUE */
                    //expert_add_info_format(pinfo, length_item, &ei_tds_invalid_plp_type, "This type should not use PLP");
                }
                *offset += length;
            }
        }
    } else {
        switch (data_type) {
            /* FIXEDLENTYPE */
            case TDS_DATA_TYPE_NULL:            /* Null (no data associated with this type) */
                break;
            case TDS_DATA_TYPE_BIT:             /* Bit (1 byte data representation) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;                
                snprintf(param_val, max_len, "%u", var8);
                *offset += 1;
                break;
            case TDS_DATA_TYPE_INT1:            /* TinyInt (1 byte data representation) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;                
                snprintf(param_val, max_len, "%u", var8);
                *offset += 1;
                break;
            case TDS_DATA_TYPE_INT2:            /* SmallInt (2 byte data representation) */
                if (-1 == dpi_get_le16(pkt, *offset, &var16))
                    return;                
                snprintf(param_val, max_len, "%u", var16);
                *offset += 2;
                break;
            case TDS_DATA_TYPE_INT4:            /* Int (4 byte data representation) */
                if (-1 == dpi_get_le32(pkt, *offset, &var32))
                    return;                
                snprintf(param_val, max_len, "%u", var32);
                *offset += 4;
                break;
            case TDS_DATA_TYPE_INT8:            /* BigInt (8 byte data representation) */
                if (-1 == dpi_get_le64(pkt, *offset, &var64))
                    return;                
                snprintf(param_val, max_len, "%llu", (unsigned long long)var64);
                *offset += 8;
                break;
            case TDS_DATA_TYPE_FLT4:            /* Real (4 byte data representation) */
                if (*offset + 4 <= pkt->payload_len) {
                    snprintf(param_val, max_len, "%f", *(const float *)(pkt->payload + *offset));
                    *offset += 4;
                }
                break;
            case TDS_DATA_TYPE_FLT8:            /* Float (8 byte data representation) */
                if (*offset + 8 <= pkt->payload_len) {
                    snprintf(param_val, max_len, "%f", *(const double *)(pkt->payload + *offset));
                    *offset += 8;
                }
                break;
            case TDS_DATA_TYPE_MONEY4:          /* SmallMoney (4 byte data representation) */
            case TDS_DATA_TYPE_DATETIME4:       /* SmallDateTime (4 byte data representation) */
                /*TODO*/
                if (*offset + 4 <= pkt->payload_len) {
                    offset += 4;
                }
                break;
            case TDS_DATA_TYPE_MONEY:           /* Money (8 byte data representation) */
            case TDS_DATA_TYPE_DATETIME:        /* DateTime (8 byte data representation) */
                /*TODO*/
                if (*offset + 8 <= pkt->payload_len) {
                    *offset += 8;
                }
                break;

            /* BYTELEN_TYPE - types prefixed with 1-byte length */
            case TDS_DATA_TYPE_GUID:            /* UniqueIdentifier */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;        
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }
                switch(var8) {
                    case TDS_GEN_NULL:
                        //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_null, tvb, *offset, 0, ENC_NA);
                        break;
                    case 16:
                        copy_len = var8 >= max_len ? max_len : var8;
                        snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset + 1));
                        //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_guid, tvb, *offset + 1, length, ENC_LITTLE_ENDIAN);
                        break;
                    default:
                        break;
                        //expert_add_info(pinfo, length_item, &ei_tds_invalid_length);
                }
                *offset += 1 + var8;
                break;

            case TDS_DATA_TYPE_BITN:
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                switch(var8) {
                    case TDS_GEN_NULL:
                        //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_null, tvb, *offset, 0, ENC_NA);
                        break;
                    case 1:
                        copy_len = var8 >= max_len ? max_len : var8;
                        snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset + 1));
                        break;
                    default:
                        break;
                        //expert_add_info(pinfo, length_item, &ei_tds_invalid_length);
                }
                *offset += 1 + var8;
                break;

            case TDS_DATA_TYPE_INTN:
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }
                switch (var8) {
                    case TDS_GEN_NULL:
                        //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_null, tvb, *offset, 0, ENC_NA);
                        break;
                    case 1:
                        if (-1 == dpi_get_uint8(pkt, *offset + 1, &tmp))
                            break;    
                        snprintf(param_val, max_len, "%u", tmp);
                        break;
                    case 2:
                        if (-1 == dpi_get_le16(pkt, *offset + 1, &var16))
                            break;    
                        snprintf(param_val, max_len, "%u", var16);
                        break;
                    case 4:
                        if (-1 == dpi_get_le32(pkt, *offset + 1, &var32))
                            break;    
                        snprintf(param_val, max_len, "%u", var32);
                        break;
                    case 8:
                        if (-1 == dpi_get_le64(pkt, *offset + 1, &var64))
                            break;    
                        snprintf(param_val, max_len, "%llu", (unsigned long long)var64);
                        break;
                    default:
                        break;
                        //expert_add_info(pinfo, length_item, &ei_tds_invalid_length);
                }
                *offset += 1 + var8;
                break;

            case TDS_DATA_TYPE_FLTN:
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }
                switch (var8) {
                    case TDS_GEN_NULL:
                        //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_null, tvb, *offset, 0, ENC_NA);
                        break;
                    case 4:
                        snprintf(param_val, max_len, "%f", *(const float *)(pkt->payload + *offset + 1));
                        break;
                    case 8:
                        snprintf(param_val, max_len, "%f", *(const double *)(pkt->payload + *offset + 1));
                        break;
                    default:
                        break;
                        //expert_add_info(pinfo, length_item, &ei_tds_invalid_length);
                }
                *offset += 1 + var8;
                break;

            case TDS_DATA_TYPE_MONEYN:
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if (var8 > 0) {
                    if (var8 == 4) {
                        float dblvalue = (float)pletoh32(pkt->payload + *offset);
                        snprintf(param_val, max_len, "%f", dblvalue / 10000);
                    }
                    if (var8 == 8) {
                        double dblvalue;
                        dblvalue = (double)pletoh64(pkt->payload + *offset);
                        snprintf(param_val, max_len, "%f", dblvalue / 10000);
                    }
                    *offset += var8;
                }
                break;

            case TDS_DATA_TYPE_DATEN:           /* (introduced in TDS 7.3) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if (var8 == 3) {
                    uint32_t days = 0;
                    time_t tv;

                    days += get_uint8_t(pkt->payload, *offset + 2) << 16;
                    days += get_uint8_t(pkt->payload, *offset + 1) << 8;
                    days += get_uint8_t(pkt->payload, *offset);

                    tv = (time_t)((days * (86400)) - (62135596800)); /* 62135596800 - seconds between Jan 1, 1 and Jan 1, 1970 */
                    timet_to_datetime(tv, param_val, max_len);
                }
                *offset += var8;
                break;

            case TDS_DATA_TYPE_TIMEN:           /* (introduced in TDS 7.3) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if(var8 > 0) {
                    int i;
                    uint64_t value = 0;
                    double dblvalue;
                    time_t tv;

                    for (i = var8 - 1; i > 0; i--) {
                        value = value + get_uint8_t(pkt->payload, *offset + i);
                        value = value << 8;
                    }
                    value = value + get_uint8_t(pkt->payload, *offset);

                    dblvalue = (double)value;
                    for (i = 0; i < scale; i++) {
                        dblvalue = dblvalue / 10;
                    }

                    tv = (time_t)dblvalue;
                    //tv.nsecs = (guint)(dblvalue - tv.secs) * 1000000000;
                    timet_to_datetime(tv, param_val, max_len);

                    *offset += var8;
                }
                break;

            case TDS_DATA_TYPE_DATETIMN:
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if (var8 > 0) {
                    if (var8 == 4) {
                        /* SQL smalldatetime */
                        time_t tv;
                        uint32_t days = pletoh16(pkt->payload +*offset);
                        uint32_t minutes = pletoh32(pkt->payload + *offset + 2);

                        tv = (time_t)((days * (86400)) + (minutes * 60) - (2208988800)); /* 2208988800 - seconds between Jan 1, 1900 and Jan 1, 1970 */
                        timet_to_datetime(tv, param_val, max_len);
                    }
                    if (var8 == 8) {
                        /* SQL datetime */
                        time_t tv;
                        uint32_t days = pletoh32(pkt->payload + *offset);
                        uint32_t threehndths = pletoh32(pkt->payload + *offset + 4);

                        tv = (time_t)((days * (86400)) + (threehndths/300) - (2208988800)); /* 2208988800 - seconds between Jan 1, 1900 and Jan 1, 1970 */
                        //tv.nsecs = (threehndths%300) * 10000000 / 3;
                        timet_to_datetime(tv, param_val, max_len);
                    }
                    *offset += var8;
                }
                break;

            case TDS_DATA_TYPE_DATETIME2N:      /* (introduced in TDS 7.3) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if(var8 > 0) {
                    int i, bytestoread = 0;
                    uint64_t value = 0;
                    double dblvalue;
                    uint days = 0;
                    uint64_t secs;
                    time_t tv;

                    if(scale <= 2) bytestoread = 3;
                    if((scale >= 3) && (scale <= 4)) bytestoread = 4;
                    if((scale >= 5) && (scale <= 7)) bytestoread = 5;
                    
                    if (*offset + bytestoread + 3 > pkt->payload_len)
                        return;

                    for (i = bytestoread - 1; i > 0; i--) {
                        value = value + get_uint8_t(pkt->payload, *offset + i);
                        value = value << 8;
                    }
                    value = value + get_uint8_t(pkt->payload, *offset);

                    dblvalue = (double)value;
                    for (i = 0; i < scale; i++) {
                        dblvalue = dblvalue / 10;
                    }

                    days += get_uint8_t(pkt->payload, *offset + bytestoread + 2) << 16;
                    days += get_uint8_t(pkt->payload, *offset + bytestoread + 1) << 8;
                    days += get_uint8_t(pkt->payload, *offset + bytestoread);

                    secs = (days * (86400)) - (62135596800); /* 62135596800 - seconds between Jan 1, 1 and Jan 1, 1970 */

                    value = (uint64_t)dblvalue;
                    tv = (time_t)(secs + value);
                    //dblvalue = dblvalue - value;
                    //tv.nsecs = (guint)dblvalue * 1000000000;
                    timet_to_datetime(tv, param_val, max_len);

                    *offset += bytestoread + 3;
                }
                break;

            case TDS_DATA_TYPE_DATETIMEOFFSETN: /* (introduced in TDS 7.3) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if(var8 > 0) {
                    int i, bytestoread = 0;
                    uint64_t value = 0;
                    double dblvalue;
                    uint32_t days = 0;
                    //short timeoffset = 0;
                    uint64_t secs;
                    time_t tv;

                    if(scale <= 2) bytestoread = 3;
                    if((scale >= 3) && (scale <= 4)) bytestoread = 4;
                    if((scale >= 5) && (scale <= 7)) bytestoread = 5;
                    if (*offset + bytestoread + 5 > pkt->payload_len)
                        return;

                    for (i = bytestoread - 1; i > 0; i--) {
                        value = value + get_uint8_t(pkt->payload, *offset + i);
                        value = value << 8;
                    }
                    value = value + get_uint8_t(pkt->payload, *offset);

                    dblvalue = (double)value;
                    for (i = 0; i < scale; i++) {
                        dblvalue = dblvalue / 10;
                    }

                    days += get_uint8_t(pkt->payload, *offset + bytestoread + 2) << 16;
                    days += get_uint8_t(pkt->payload, *offset + bytestoread + 1) << 8;
                    days += get_uint8_t(pkt->payload, *offset + bytestoread);

                    secs = (days * (86400)) - (62135596800); /* 62135596800 - seconds between Jan 1, 1 and Jan 1, 1970 */

                    value = (uint64_t)dblvalue;
                    tv = (time_t)(secs + value);
                    //dblvalue = dblvalue - value;
                    //tv.nsecs = (guint)dblvalue * 1000000000;
                    //timeitem = proto_tree_add_time(sub_tree, hf_tds_type_varbyte_data_absdatetime, tvb, *offset, length, &tv);
                    timet_to_datetime(tv, param_val, max_len);

                    //timeoffset = pletoh16(payload + *offset + bytestoread + 3);

                    /* TODO: Need to find a way to convey the time and the offset in a single item, rather than appending text */
                    //proto_item_append_text(timeitem, " %c%02i:%02i", timeoffset > 0 ? '+':'-', timeoffset / 60, timeoffset % 60);
                    *offset += bytestoread + 5;
                }
                break;

            case TDS_DATA_TYPE_DECIMAL:         /* Decimal (legacy support) */
            case TDS_DATA_TYPE_NUMERIC:         /* Numeric (legacy support) */
            case TDS_DATA_TYPE_DECIMALN:        /* Decimal */
            case TDS_DATA_TYPE_NUMERICN:        /* Numeric */
            {
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if (var8 > 0) {
                    //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_sign, tvb, *offset, 1, ENC_NA);
                    /*    
                    switch(length - 1)
                    {
                        case 4:
                            {
                            numericitem = proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_int4, tvb, *offset + 1, 4, ENC_LITTLE_ENDIAN);
                            if(scale != 0)
                                proto_item_append_text(numericitem, " x 10^%u", scale);
                                break;
                            }
                        case 8:
                            {
                            numericitem = proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_int8, tvb, *offset + 1, 8, ENC_LITTLE_ENDIAN);

                            if(scale != 0)
                            proto_item_append_text(numericitem, " x 10^%u", scale);
                            break;
                            }
                        case 12:
                        case 16:
                            {
                            proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_bytes, tvb, *offset + 1, length, ENC_NA);
                            break;
                            }
                    }*/
                    *offset += var8;
                }
                break;
            }
            case TDS_DATA_TYPE_CHAR:            /* Char (legacy support) */
            case TDS_DATA_TYPE_VARCHAR:         /* VarChar (legacy support) */
            case TDS_DATA_TYPE_BINARY:          /* Binary (legacy support) */
            case TDS_DATA_TYPE_VARBINARY:       /* VarBinary (legacy support) */
                if (-1 == dpi_get_uint8(pkt, *offset, &var8))
                    return;    
                if (*offset + var8 + 1 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 1;
                if (*offset + var8 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }
                if(var8 > 0) {
                    copy_len = var8 >= max_len ? max_len : var8;
                    snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));
                    *offset += var8;
                }
                break;

            /* USHORTLEN_TYPE - types prefixed with 2-byte length */
            case TDS_DATA_TYPE_BIGVARBIN:       /* VarBinary */
            case TDS_DATA_TYPE_BIGBINARY:       /* Binary */
            case TDS_DATA_TYPE_BIGVARCHR:       /* VarChar */
            case TDS_DATA_TYPE_BIGCHAR:         /* Char */
            case TDS_DATA_TYPE_NVARCHAR:        /* NVarChar */
            case TDS_DATA_TYPE_NCHAR:           /* NChar */
                if (-1 == dpi_get_le16(pkt, *offset, &var16))
                    return;    
                if (*offset + var16 + 2 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 2;
                if(var16 == TDS_CHARBIN_NULL) {
                    //proto_item_append_text(length_item, " (CHARBIN_NULL)");
                    //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_null, tvb, *offset, 0, ENC_NA);
                } else {
                    switch(data_type) {
                        case TDS_DATA_TYPE_BIGVARBIN: /* VarBinary */
                        case TDS_DATA_TYPE_BIGBINARY: /* Binary */
                            copy_len = var16 >= max_len ? max_len : var16;
                            snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));
                            break;
                        case TDS_DATA_TYPE_BIGVARCHR: /* VarChar */
                        case TDS_DATA_TYPE_BIGCHAR:   /* Char */
                            copy_len = var16 >= max_len ? max_len : var16;
                            snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));
                            break;
                        case TDS_DATA_TYPE_NVARCHAR:  /* NVarChar */
                        case TDS_DATA_TYPE_NCHAR:     /* NChar */
                            get_utf_16_string(param_val, max_len, pkt->payload + *offset, var16, ENC_LITTLE_ENDIAN);
                            //snprintf(param_val, max_len, "%s", (const char *)(payload + *offset));
                            break;
                    }
                    *offset += var16;
                }
                break;

            /* LONGLEN_TYPE - types prefixed with 4-byte length */
            case TDS_DATA_TYPE_NTEXT:           /* NText */
            case TDS_DATA_TYPE_TEXT:            /* Text */
            case TDS_DATA_TYPE_IMAGE:           /* Image */
            case TDS_DATA_TYPE_XML:             /* XML (introduced in TDS 7.2) */
            case TDS_DATA_TYPE_UDT:             /* CLR-UDT (introduced in TDS 7.2) */
            case TDS_DATA_TYPE_SSVARIANT:       /* Sql_Variant (introduced in TDS 7.2) */
                if (-1 == dpi_get_le32(pkt, *offset, &var32))
                    return;    
                if (*offset + var32 + 4 > pkt->payload_len) {
                    *offset = pkt->payload_len;
                    return;
                }

                *offset += 4;
                if(var32 == TDS_CHARBIN_NULL32) {
                    //proto_item_append_text(length_item, " (CHARBIN_NULL)");
                    //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_null, tvb, *offset, 0, ENC_NA);
                } else {
                    switch(data_type) {
                        case TDS_DATA_TYPE_NTEXT: /* NText */
                            copy_len = var32 >= max_len ? max_len : var32;
                            snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset));
                            break;
                        case TDS_DATA_TYPE_TEXT:
                            copy_len = var32 >= max_len ? max_len : var32;
                            snprintf(param_val, copy_len, "%s", (const char *)(pkt->payload + *offset)); 
                            break;
                        default: /*TODO*/
                            break;
                            //proto_tree_add_item(sub_tree, hf_tds_type_varbyte_data_bytes, tvb, *offset, length, ENC_NA);
                    }
                    *offset += var32;
                }
                break;
        }
        //proto_item_set_end(item, tvb, *offset);
    }
}

static uint8_t dissect_tds_type_info(struct dpi_pkt_st *pkt, uint32_t *offset, uint8_t *plp, uint8_t variantprop)
{
    uint8_t varlen8;
    uint16_t varlen16;
    uint32_t varlen, varlen_len = 0;
    uint8_t data_type = TDS_DATA_TYPE_INVALID;

    *plp = 0; /* most types are not Partially Length-Prefixed */
    if (-1 == dpi_get_uint8(pkt, *offset, &data_type))
        return data_type;
    *offset += 1;

    if (variantprop) {
        uint8_t prop_bytes = variant_propbytes(data_type);
        if (*offset + prop_bytes < pkt->payload_len)
            *offset += prop_bytes;
    }

    /* optional TYPE_VARLEN for variable length types */
    switch (data_type) {
        /* FIXEDLENTYPE */
        case TDS_DATA_TYPE_NULL:            /* Null (no data associated with this type) */
        case TDS_DATA_TYPE_INT1:            /* TinyInt (1 byte data representation) */
        case TDS_DATA_TYPE_BIT:             /* Bit (1 byte data representation) */
        case TDS_DATA_TYPE_INT2:            /* SmallInt (2 byte data representation) */
        case TDS_DATA_TYPE_INT4:            /* Int (4 byte data representation) */
        case TDS_DATA_TYPE_FLT4:            /* Real (4 byte data representation) */
        case TDS_DATA_TYPE_DATETIME4:       /* SmallDateTime (4 byte data representation) */
        case TDS_DATA_TYPE_MONEY4:          /* SmallMoney (4 byte data representation) */
        case TDS_DATA_TYPE_INT8:            /* BigInt (8 byte data representation) */
        case TDS_DATA_TYPE_FLT8:            /* Float (8 byte data representation) */
        case TDS_DATA_TYPE_MONEY:           /* Money (8 byte data representation) */
        case TDS_DATA_TYPE_DATETIME:        /* DateTime (8 byte data representation) */
        /* BYTELEN_TYPE with length determined by SCALE */
        case TDS_DATA_TYPE_TIMEN:           /* (introduced in TDS 7.3) */
        case TDS_DATA_TYPE_DATETIME2N:      /* (introduced in TDS 7.3) */
        case TDS_DATA_TYPE_DATETIMEOFFSETN: /* (introduced in TDS 7.3) */
            varlen_len = 0;
            break;
        /* BYTELEN_TYPE */
        case TDS_DATA_TYPE_GUID:            /* UniqueIdentifier */
        case TDS_DATA_TYPE_INTN:
        case TDS_DATA_TYPE_DECIMAL:         /* Decimal (legacy support) */
        case TDS_DATA_TYPE_NUMERIC:         /* Numeric (legacy support) */
        case TDS_DATA_TYPE_BITN:
        case TDS_DATA_TYPE_DECIMALN:        /* Decimal */
        case TDS_DATA_TYPE_NUMERICN:        /* Numeric */
        case TDS_DATA_TYPE_FLTN:
        case TDS_DATA_TYPE_MONEYN:
        case TDS_DATA_TYPE_DATETIMN:
        case TDS_DATA_TYPE_DATEN:           /* (introduced in TDS 7.3) */
        case TDS_DATA_TYPE_CHAR:            /* Char (legacy support) */
        case TDS_DATA_TYPE_VARCHAR:         /* VarChar (legacy support) */
        case TDS_DATA_TYPE_BINARY:          /* Binary (legacy support) */
        case TDS_DATA_TYPE_VARBINARY:       /* VarBinary (legacy support) */
            varlen_len = 1;
            if (-1 == dpi_get_uint8(pkt, *offset, &varlen8))
                break;
            break;
        /* USHORTLEN_TYPE */
        case TDS_DATA_TYPE_BIGVARCHR:       /* VarChar */
        case TDS_DATA_TYPE_BIGVARBIN:       /* VarBinary */
        case TDS_DATA_TYPE_NVARCHAR:        /* NVarChar */
            varlen_len = 2;
            if (-1 == dpi_get_le16(pkt, *offset, &varlen16))
                break;        
            /* A type with unlimited max size, known as varchar(max), varbinary(max) and nvarchar(max),
            which has a max size of 0xFFFF, defined by PARTLENTYPE. This class of types was introduced in TDS 7.2. */
            if(varlen16 == 0xFFFF)
                *plp = 1;
            break;
        case TDS_DATA_TYPE_BIGBINARY:       /* Binary */
        case TDS_DATA_TYPE_BIGCHAR:         /* Char */
        case TDS_DATA_TYPE_NCHAR:           /* NChar */
            varlen_len = 2;
            if (-1 == dpi_get_le16(pkt, *offset, &varlen16))
                break;    
            break;
        /* LONGLEN_TYPE */
        case TDS_DATA_TYPE_XML:             /* XML (introduced in TDS 7.2) */
        case TDS_DATA_TYPE_UDT:             /* CLR-UDT (introduced in TDS 7.2) */
            *plp = 1;
        /* Fall through */
        case TDS_DATA_TYPE_TEXT:            /* Text */
        case TDS_DATA_TYPE_IMAGE:           /* Image */
        case TDS_DATA_TYPE_NTEXT:           /* NText */
        case TDS_DATA_TYPE_SSVARIANT:       /* Sql_Variant (introduced in TDS 7.2) */
            varlen_len = 4;
            if (-1 == dpi_get_le32(pkt, *offset, &varlen))
                break;    
            break;
        default:
            //expert_add_info(pinfo, data_type_item, &ei_tds_type_info_type);
            varlen_len = 0;
            data_type = TDS_DATA_TYPE_INVALID;
    }

    //if(varlen_len)
    //    item1 = proto_tree_add_uint(sub_tree, hf_tds_type_info_varlen, tvb, *offset, varlen_len, varlen);
    //if(*plp)
    //   proto_item_append_text(item1, " (PLP - Partially Length-Prefixed data type)");

    *offset += varlen_len;

    /* Optional data dependent on type */
    switch(data_type) {
        /* PRECISION and SCALE */
        case TDS_DATA_TYPE_DECIMAL:         /* Decimal (legacy support) */
        case TDS_DATA_TYPE_NUMERIC:         /* Numeric (legacy support) */
        case TDS_DATA_TYPE_DECIMALN:        /* Decimal */
        case TDS_DATA_TYPE_NUMERICN:        /* Numeric */
            //proto_tree_add_item(sub_tree, hf_tds_type_info_precision, tvb, *offset, 1, ENC_LITTLE_ENDIAN);
            *offset += 1;
            /* fallthrough */

        /* SCALE */
        case TDS_DATA_TYPE_TIMEN:           /* (introduced in TDS 7.3) */
        case TDS_DATA_TYPE_DATETIME2N:      /* (introduced in TDS 7.3) */
        case TDS_DATA_TYPE_DATETIMEOFFSETN: /* (introduced in TDS 7.3) */
            //proto_tree_add_item(sub_tree, hf_tds_type_info_scale, tvb, *offset, 1, ENC_LITTLE_ENDIAN);
            *offset += 1;
            break;
        /* COLLATION */
        case TDS_DATA_TYPE_BIGCHAR:         /* Char */
        case TDS_DATA_TYPE_BIGVARCHR:       /* VarChar */
        case TDS_DATA_TYPE_TEXT:            /* Text */
        case TDS_DATA_TYPE_NTEXT:           /* NText */
        case TDS_DATA_TYPE_NCHAR:           /* NChar */
        case TDS_DATA_TYPE_NVARCHAR:        /* NVarChar */
            /*
            item1 = proto_tree_add_item(sub_tree, hf_tds_type_info_collation, tvb, *offset, 5, ENC_NA);
            collation_tree = proto_item_add_subtree(item1, ett_tds_type_info_collation);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_lcid, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_ign_case, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_ign_accent, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_ign_kana, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_ign_width, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_binary, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_version, tvb, *offset, 4, ENC_LITTLE_ENDIAN);
            proto_tree_add_item(collation_tree, hf_tds_type_info_collation_sortid, tvb, *offset + 4, 1, ENC_LITTLE_ENDIAN);
            */
            *offset += 5;
        break;
    }

    //proto_item_set_end(item, tvb, *offset);
    return data_type;
}

static void dissect_tds_all_headers(struct dpi_pkt_st *pkt, uint32_t *offset)
{
    uint32_t total_length;
    uint32_t final_offset;

    if (-1 == dpi_get_le32(pkt, *offset, &total_length))
        return;
    if (total_length >= 0x100)
        return;
    final_offset = *offset + total_length;
    if (final_offset > pkt->payload_len)
        final_offset = pkt->payload_len;
    *offset += 4;
    do {
        /* dissect a stream header */
        uint32_t header_length = 0;
        uint16_t header_type;
        if (-1 == dpi_get_le32(pkt, *offset, &header_length))
            break;
        if(header_length == 0 ) {
            break;
        }
        if (-1 == dpi_get_le16(pkt, *offset + 4, &header_type))
            break;

        switch(header_type) {
            case TDS_HEADER_QUERY_NOTIF:
                break;
            case TDS_HEADER_TRANS_DESCR:
                if(header_length != 18)
                    DPI_LOG(DPI_LOG_WARNING, "tds header Length should equal 18");
                break;
            default:
                break;
        }

        *offset += header_length;
    } while(*offset < final_offset);
    
    if (*offset != final_offset) {
        *offset = final_offset;
        DPI_LOG(DPI_LOG_WARNING, "tds header error");
        return;
    }
}

static void dissect_tds7_prelogin_packet(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint8_t token;
    //uint32_t offset = 0;
    //uint16_t tokenoffset;
    uint16_t tokenlen;

    //item = proto_tree_add_item(tree, hf_tds_prelogin, tvb, 0, -1, ENC_NA);

    if(detect_tls(pkt->payload, pkt->payload_len))
    {
        //proto_item_append_text(item, " - TLS exchange");
        return;
    }

    //prelogin_tree = proto_item_add_subtree(item, ett_tds_message);
    while (offset < pkt->payload_len) {
        token = get_uint8_t(pkt->payload, offset);
        //option_tree = proto_tree_add_subtree(prelogin_tree, tvb, offset, token == 0xff ? 1 : 5, ett_tds_prelogin_option, NULL, "Option");
        //proto_tree_add_item(option_tree, hf_tds_prelogin_option_token, tvb, offset, 1, ENC_LITTLE_ENDIAN);
        offset += 1;

        if(token == 0xff)
            break;

        if (offset + 2 > pkt->payload_len)
            break;
        //tokenoffset = get_uint16_ntohs(payload, offset);
        //proto_tree_add_item(option_tree, hf_tds_prelogin_option_offset, tvb, offset, 2, ENC_BIG_ENDIAN);
        offset += 2;
        
        if (offset + 2 > pkt->payload_len)
            break;
        tokenlen = get_uint16_ntohs(pkt->payload, offset);
        //proto_tree_add_item(option_tree, hf_tds_prelogin_option_length, tvb, offset, 2, ENC_BIG_ENDIAN);
        offset += 2;

        if (tokenlen != 0) {
            switch(token)
            {
                case 0: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_version, tvb, tokenoffset, 4, ENC_LITTLE_ENDIAN);
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_subbuild, tvb, tokenoffset + 4, 2, ENC_LITTLE_ENDIAN);
                    break;
                }
                case 1: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_encryption, tvb, tokenoffset, 1, ENC_LITTLE_ENDIAN);
                    break;
                }
                case 2: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_instopt, tvb, tokenoffset, tokenlen, ENC_ASCII | ENC_NA);
                    break;
                }
                case 3: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_threadid, tvb, tokenoffset, 4, ENC_BIG_ENDIAN);
                    break;
                }
                case 4: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_mars, tvb, tokenoffset, 1, ENC_LITTLE_ENDIAN);
                    break;
                }
                case 5: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_traceid, tvb, tokenoffset, tokenlen, ENC_NA);
                    break;
                }
                case 6: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_fedauthrequired, tvb, tokenoffset, 1, ENC_LITTLE_ENDIAN);
                    break;
                }
                case 7: {
                    //proto_tree_add_item(option_tree, hf_tds_prelogin_option_nonceopt, tvb, tokenoffset, tokenlen, ENC_NA);
                    break;
                }
            }
        }
    }
}

static int dissect_tds_prelogin_response(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint8_t token = 0;
    uint16_t var16;
    uint32_t tokenoffset, tokenlen, cur = offset, valid = 0;

    /* Test for prelogin format compliance */
    while (cur < pkt->payload_len) {
        if (-1 == dpi_get_uint8(pkt, cur, &token))
            break;
        cur += 1;

        if ((token <= 8) || (token == 0xff)) {
            valid = 1;
        } else {
            valid = 0;
            break;
        }

        if(token == 0xff)
            break;
        
        if (-1 == dpi_get_be16(pkt, cur, &var16))
            break;
        tokenoffset = var16;

        if (cur + tokenoffset > pkt->payload_len) {
            valid = 0;
            break;
        }
        cur += 2;
        
        if (-1 == dpi_get_be16(pkt, cur, &var16))
            break;
        tokenlen = var16;
        
        if (cur + tokenlen > pkt->payload_len) {
            valid = 0;
            break;
        }
        cur += 2;
    }

    if (token != 0xff) {
        valid = 0;
    }

    if (valid) {
        dissect_tds7_prelogin_packet(pkt, offset);
    }

    return valid;
}

static void dissect_tds_rpc(struct dpi_pkt_st *pkt, uint32_t rpc_offset, struct tds_info *info)
{
    int i = 0;
    uint32_t copy_len;
    uint32_t offset = rpc_offset;
    uint8_t len8;
    uint16_t len;
    uint16_t proc_id;
    uint8_t data_type;

    dissect_tds_all_headers(pkt, &offset);
    
    while (offset < pkt->payload_len) {
        const char *p_rpc_id = NULL;
        char rpc_name[64] = {0};
        i = 0;
        /*
        * RPC name.
        */
        switch (tds_protocol_type) {
            case TDS_PROTOCOL_4:
                if (-1 == dpi_get_uint8(pkt, offset, &len8))
                    return;
                if (len8 + offset + 1 > pkt->payload_len)
                    return;
                copy_len = len8;
                if (copy_len >= sizeof(rpc_name))
                    copy_len = sizeof(rpc_name) - 1;
                strncpy(rpc_name, (const char *)pkt->payload + offset + 1, copy_len);
                rpc_name[copy_len] = 0;
                offset += 1 + len8;
                break;

            case TDS_PROTOCOL_7_0:
            case TDS_PROTOCOL_7_1:
            case TDS_PROTOCOL_7_2:
            case TDS_PROTOCOL_7_3:
            case TDS_PROTOCOL_7_4:
            default: /* unspecified: try as if TDS7 */
                if (-1 == dpi_get_le16(pkt, offset, &len))
                    return;

                offset += 2;
                if (len == 0xFFFF) {
                    if (-1 == dpi_get_le16(pkt, offset, &proc_id))
                        return;
                    p_rpc_id = val_to_string(proc_id, internal_stored_proc_id_names);
                    offset += 2;
                } else if (len != 0) {
                    len *= 2;
                    if (len + offset > pkt->payload_len)
                        return;
                    get_utf_16_string(rpc_name, sizeof(rpc_name), pkt->payload + offset, len, ENC_LITTLE_ENDIAN);
                    offset += len;
                }
                break;
        }
        offset += 2;

        /* dissect parameters */
        while (offset < pkt->payload_len) {
            uint8_t plp;
            if (-1 == dpi_get_uint8(pkt, offset, &len8))
                return;

            /* check for BatchFlag or NoExecFlag */
            if (len8 > 0x7f) {
                //proto_tree_add_item(tree, hf_tds_rpc_separator, tvb, offset, 1, ENC_LITTLE_ENDIAN);
                offset++;
                break;
            }
            offset++;
            if (len8) {
                len = len8 * 2;
                //proto_tree_add_item(sub_tree, hf_tds_rpc_parameter_name, tvb, offset, len, ENC_UTF_16|ENC_LITTLE_ENDIAN);
                if (offset + len > pkt->payload_len)
                    return;
                offset += len;
            }
            //item = proto_tree_add_item(sub_tree, hf_tds_rpc_parameter_status, tvb, offset, 1, ENC_LITTLE_ENDIAN);
            //status_sub_tree = proto_item_add_subtree(item, ett_tds_rpc_parameter_status);
            //proto_tree_add_item(status_sub_tree, hf_tds_rpc_parameter_status_by_ref, tvb, offset, 1, ENC_LITTLE_ENDIAN);
            //proto_tree_add_item(status_sub_tree, hf_tds_rpc_parameter_status_default, tvb, offset, 1, ENC_LITTLE_ENDIAN);
            ++offset;
            data_type = dissect_tds_type_info(pkt, &offset, &plp, 0);
            if (data_type == TDS_DATA_TYPE_INVALID || offset >= pkt->payload_len)
                break;
            if (i < RPC_PARAM_NUM) {
                info->param[i].rpc_id = p_rpc_id;
                strcpy(info->param[i].rpc_name, rpc_name);
                dissect_tds_type_varbyte(pkt, &offset, data_type, 0, plp, -1,
                        info->param[i].param_value, sizeof(info->param[i].param_value)); /* TODO: Precision needs setting? */
            } else {
                info->param[RPC_PARAM_NUM - 1].rpc_id = p_rpc_id;
                strcpy(info->param[RPC_PARAM_NUM - 1].rpc_name, rpc_name);
                dissect_tds_type_varbyte(pkt, &offset, data_type, 0, plp, -1,
                        info->param[RPC_PARAM_NUM - 1].param_value, sizeof(info->param[RPC_PARAM_NUM - 1].param_value)); /* TODO: Precision needs setting? */
            }
            i++;
        //proto_item_set_end(param_item, tvb, offset);
        }
    }
}

static void dissect_tds7_login(struct dpi_pkt_st *pkt, uint32_t rpc_offset, struct tds_info *info)
{
    uint32_t offset, i, j, k, offset2, len;
    const char *val;
    int copy_len;

    struct tds7_login_packet_hdr td7hdr;

    /* create display subtree for the protocol */
    offset = rpc_offset;

    if (offset + 4 > pkt->payload_len)
        return;
    td7hdr.total_packet_size = pletoh32(pkt->payload + offset);
    offset += 4;

    if (offset + 4 > pkt->payload_len)
        return;
    info->TDSversion = td7hdr.tds_version = pletoh32(pkt->payload + offset);
    offset += 4;

    if (offset + 4 > pkt->payload_len)
        return;
    td7hdr.packet_size = pletoh32(pkt->payload + offset);
    offset += 4;

    if (offset + 4 > pkt->payload_len)
        return;
    info->Clientversion = td7hdr.client_version = pletoh32(pkt->payload + offset);
    offset += 4;

    if (offset + 4 > pkt->payload_len)
        return;
    info->ClientPID = td7hdr.client_pid = pletoh32(pkt->payload + offset);
    offset += 4;

    if (offset + 4 > pkt->payload_len)
        return;
    info->ConnectionID = td7hdr.connection_id = pletoh32(pkt->payload + offset);
    offset += 4;

    if (offset + 1 > pkt->payload_len)
        return;
    info->Option_Flags1 = td7hdr.option_flags1 = get_uint8_t(pkt->payload, offset);
    offset += 1;
    
    if (offset + 1 > pkt->payload_len)
        return;
    info->Option_Flags2 = td7hdr.option_flags2 = get_uint8_t(pkt->payload, offset);
    offset += 1;
    
    if (offset + 1 > pkt->payload_len)
        return;
    info->SQL_Type_Flags = td7hdr.sql_type_flags = get_uint8_t(pkt->payload, offset);
    offset += 1;
    
    if (offset + 1 > pkt->payload_len)
        return;
    info->Reserved_Flags = td7hdr.reserved_flags = get_uint8_t(pkt->payload, offset);
    offset += 1;
    
    if (offset + 4 > pkt->payload_len)
        return;
    info->TimeZone = td7hdr.time_zone = pletoh32(pkt->payload + offset);
    offset += 4;
    
    if (offset + 4 > pkt->payload_len)
        return;
    info->Collation = td7hdr.collation = get_uint8_t(pkt->payload, offset);
    offset += 4;

    for (i = 0; i < 9; i++) {
        if (offset + i * 4 + 4 > pkt->payload_len)
            break;
        offset2 = pletoh16(pkt->payload + offset + i * 4) + 8;
        len = pletoh16(pkt->payload + offset + i * 4 + 2);

        switch (i) {
            case 0:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->ClientName) - 1; j++)
                        if(!val[j*2+1])
                            info->ClientName[k++] = val[j*2];
                    info->ClientName[k] = '\0'; 
                }
                break;
            case 1:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->Username) - 1; j++)
                        if(!val[j*2+1])
                            info->Username[k++] = val[j*2];
                    info->Username[k] = '\0'; 
                }
                break;
            case 2:
                len *= 2;
                if (len > 0 && offset2 + len <= pkt->payload_len) {
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->Password) - 1; j += 2, k++) {
                        info->Password[k] = val[j];
                        info->Password[k] ^= 0xA5;
                        /* Swap the most and least significant bits */
                        info->Password[k] = ((info->Password[k] & 0x0F) << 4) | ((info->Password[k] & 0xF0) >> 4);
                    }
                    info->Password[k] = '\0'; /* Null terminate our new string */
                }
                break;
            case 3:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->AppName) - 1; j++)
                        if(!val[j*2+1])
                            info->AppName[k++] = val[j*2];
                    info->AppName[k] = '\0'; 
                }
                break;
            case 4:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->ServerName) - 1; j++, k++)
                        info->ServerName[k] = val[j*2];
                    info->ServerName[k] = '\0'; 
                }
                break;
            case 6:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->LibraryName) - 1; j++)
                        if(!val[j*2+1])
                            info->LibraryName[k++] = val[j*2];
                    info->LibraryName[k] = '\0'; 
                }
                break;
            case 7:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->Locale) - 1; j++)
                        if(!val[j*2+1])
                        info->Locale[k++] = val[j*2];
                    info->Locale[k] = '\0'; 
                }
                break;
            case 8:
                if (len > 0 && offset2 + (len * 2) <= pkt->payload_len) { //UTF-16
                    val = (const char *)pkt->payload + offset2;
                    for (j = 0, k = 0; j < len && k < sizeof(info->DatabaseName) - 1; j++)
                        if(!val[j*2+1])
                            info->DatabaseName[k++] = val[j*2];
                    info->DatabaseName[k] = '\0'; 
                }
                break;
        }
    }

    /*
    * XXX - what about the client MAC address, etc.?
    */
    if (offset2 + len < pkt->payload_len) {
        //dissect_tds_nt(tvb, pinfo, login_tree, offset2 + len,
        //               length_remaining);
    }
}

static void dissect_tds_query_packet(struct dpi_pkt_st *pkt, uint32_t rpc_offset, struct dts_session *tds_info, struct tds_info *info)
{
    uint32_t offset, len;
    //uint32_t string_encoding = ENC_UTF_16|ENC_LITTLE_ENDIAN;
    int copy_len;

    offset = rpc_offset;
    dissect_tds_all_headers(pkt, &offset);
    
    len = pkt->payload_len - offset;
    if (len <= 0)
        return;
    if (TDS_PROTO_TDS4 
            || (!TDS_PROTO_TDS7 && ((len < 2) || get_uint8_t(pkt->payload, offset + 1) != 0))) {
        copy_len = len >= sizeof(info->Query) ? sizeof(info->Query) - 1 : len;
        strncpy(info->Query, (const char *)pkt->payload + offset, copy_len);
        info->Query[copy_len] = 0;
        //string_encoding = ENC_ASCII|ENC_NA;
    } else {
        get_utf_16_string(info->Query, sizeof(info->Query), pkt->payload + offset, len, ENC_LITTLE_ENDIAN);
    }

    //proto_tree_add_item(query_tree, hf_tds_query, tvb, offset, len, string_encoding);
    /* offset += len; */
}

static void dissect_tds_query5_packet(struct dpi_pkt_st *pkt, uint32_t rpc_offset, struct dts_session *tds_info, struct tds_info *info)
{
    //UNUSED(payload);
    //UNUSED(payload_len);
    //UNUSED(rpc_offset);
    //UNUSED(info);
    uint32_t pos;
    uint32_t token_len_field_size = 2;
    uint32_t token_len_field_val = 0;
    uint8_t token;
    uint32_t token_sz;
    int copy_len;

    pos = rpc_offset;
    while (pos < pkt->payload_len) {

        /* our token */
        if (-1 == dpi_get_uint8(pkt, pos, &token))
            break;
        if (tds_token_is_fixed_size(token))
            token_sz = tds_get_fixed_token_size(token, tds_info) + 1;
        else
            token_sz = tds_get_variable_token_size(pkt->payload, pkt->payload_len, pos + 1, token, &token_len_field_size,
                                                   &token_len_field_val);

        //token_tree = proto_tree_add_subtree_format(query_tree, tvb, pos, token_sz,
        //                                 ett_tds_token, &token_item, "Token 0x%02x %s", token,
        //                                 val_to_str_const(token, token_names, "Unknown Token Type"));

        if ((int) token_sz < 0) {
            break;
        }

        /*
         * If it's a variable token, put the length field in here
         * instead of replicating this for each token subdissector.
         */
        //if (!tds_token_is_fixed_size(token))
        //{
        //    token_item = proto_tree_add_uint(token_tree, hf_tds_token_len, tvb, pos + 1, 1, token_len_field_val);
        //    proto_item_set_len(token_item, token_len_field_size);
        //}

        switch (token) {
            case TDS_LANG_TOKEN:
                //dissect_tds5_lang_token(tvb, pos + 5, token_sz -5, token_tree);
                //proto_tree_add_item(tree, hf_tds_lang_token_status, tvb, offset, 1, ENC_NA);
                if (pos + token_sz > pkt->payload_len)
                    break;
                pos += 6;
                copy_len = token_sz - 6 >= sizeof(info->Query) ? sizeof(info->Query) - 1 : token_sz - 6;
                strncpy(info->Query, (const char *)pkt->payload + pos, copy_len);
                info->Query[copy_len] = 0;
                break;
            default:
                break;
        }

        pos += token_sz;

    }  /* while */
}

static void _netlib_data_to_str(struct _netlib_data *data, char *result, int max_len)
{
    uint32_t i;
    int idx = 0;
    int ret;
    for (i = 0; i < data->num_cols; i++) {
        if (idx >= max_len)
            break;
        ret = snprintf(result + idx, max_len - idx, "%s,", data->columns[i].name);
        idx += ret;
    }
}

static void _row_data_to_str(struct _row_data *data, char *result, int max_len)
{
    uint32_t i;
    int idx = 0;
    int ret;
    for (i = 0; i < data->num_cols; i++) {
        if (idx >= max_len)
            break;
        ret = snprintf(result + idx, max_len - idx, "%s,", data->row[i].col);
        idx += ret;
    }
}

static void dissect_tds_resp(struct dpi_pkt_st *pkt, uint32_t rpc_offset, struct dts_session *session, struct tds_info *info)
{
    int i=0;
    uint8_t var8;
    uint32_t pos = rpc_offset;
    uint32_t token_sz = 0;
    uint32_t token;
    struct _netlib_data nl_data;
    struct token_data *data = NULL;
    struct _row_data row_data;


    /* Test for pre-login response in case this response is not a token stream */
    if (dissect_tds_prelogin_response(pkt, pos) == 1) {
        return;
    }
    memset(&nl_data, 0, sizeof(nl_data));

    /*
     * Until we reach the end of the packet, read tokens.
     */
    while (pos < pkt->payload_len) {
        memset(&row_data, 0, sizeof(row_data));

        if(i<0){
            return;
        }
        if (i < TOKEN_NUM)
            data = &info->token[i];
        else
            data = &info->token[TOKEN_NUM - 1];

        i++;
        /* our token */
        if (-1 == dpi_get_uint8(pkt, pos, &var8))
            break;
        token = var8;

        if (TDS_PROTO_TDS4) {
            uint8_t nomatch = 0;

            switch (token) {
                case TDS_COL_NAME_TOKEN:
                    /*
                    * TDS 4.2
                    * TODO dissect token to get "column names" to fill in _netlib_data
                    */
                    break;

                case TDS_COL_INFO_TOKEN:
                    /*
                    * TDS 4.2: get the column info
                    */
                    dissect_tds_col_info_token(pkt, &nl_data, pos);
                    break;

                case TDS_RESULT_TOKEN:
                    /*
                    * If it's a result token, we need to stash the
                    * column info.
                    */
                    read_results_tds5_token(pkt, &nl_data, pos + 3);
                    break;

                case TDS_AUTH_TOKEN:
                    //dissect_tds_nt(tvb, pinfo, token_tree, pos + 3, token_sz - 3);
                    break;

                default:
                    nomatch = 1;
                    break;
            }

            if (nomatch) {
                break;
            }

        } else {

            /* Tokens from MS-TDS specification, revision 18.0 (up to TDS 7.4) */
            switch (token) {
                case TDS7_COL_METADATA_TOKEN:
                    data->token_name = "metadata token";
                    token_sz = dissect_tds7_colmetadata_token(pkt, &nl_data, pos + 1, session) + 1;
                    _netlib_data_to_str(&nl_data, data->token_content, sizeof(data->token_content));
                    break;
                case TDS_DONE_TOKEN:
                    data->token_name = "done token";
                    token_sz = dissect_tds_done_token(pkt, pos + 1, session, data) + 1;
                    break;
                case TDS_DONEPROC_TOKEN:
                    data->token_name = "doneproc token";
                    token_sz = dissect_tds_doneproc_token(pkt, pos + 1, session, data) + 1;
                    break;
                case TDS_DONEINPROC_TOKEN:
                    data->token_name = "inproc token";
                    token_sz = dissect_tds_doneinproc_token(pkt, pos + 1, session, data) + 1;
                    break;
                case TDS_ENVCHG_TOKEN:
                    data->token_name = "envchg token";
                    token_sz = dissect_tds_envchg_token(pkt, pos + 1) + 1;
                    break;
                case TDS_ERR_TOKEN:
                    data->token_name = "err token";
                    token_sz = dissect_tds_error_token(pkt, pos + 1, session) + 1;
                    break;
                case TDS_INFO_TOKEN:
                    data->token_name = "info token";
                    token_sz = dissect_tds_info_token(pkt, pos + 1, session) + 1;
                    break;
                case TDS_FEATUREEXTACK_TOKEN:
                    data->token_name = "featureextack token";
                    token_sz = dissect_tds_featureextack_token(pkt, pos + 1) + 1;
                    break;
                case TDS_LOGIN_ACK_TOKEN:
                    data->token_name = "login ack token";
                    token_sz = dissect_tds_login_ack_token(pkt, pos + 1, session) + 1;
                    break;
                case TDS_NBCROW_TOKEN:
                    data->token_name = "nbcrow token";
                    token_sz = dissect_tds_nbc_row_token(pkt, &nl_data, pos + 1, session, &row_data) + 1;
                    _row_data_to_str(&row_data, data->token_content, sizeof(data->token_content));
                    break;
                case TDS_OFFSET_TOKEN:
                    data->token_name = "offset token";
                    token_sz = dissect_tds_offset_token(pkt, pos + 1) + 1;
                    break;
                case TDS_ORDER_TOKEN:
                    data->token_name = "order token";
                    token_sz = dissect_tds_order_token(pkt, pos + 1) + 1;
                    break;
                case TDS_RET_STAT_TOKEN:
                    data->token_name = "ret stat token";
                    token_sz = dissect_tds_returnstatus_token(pkt, pos + 1) + 1;
                    break;
                case TDS_ROW_TOKEN:
                    data->token_name = "row token";
                    token_sz = dissect_tds_row_token(pkt, &nl_data, pos + 1, session, &row_data) + 1;                
                    _row_data_to_str(&row_data, data->token_content, sizeof(data->token_content));
                    break;
                case TDS_SESSIONSTATE_TOKEN:
                    data->token_name = "session state token";
                    token_sz = dissect_tds_sessionstate_token(pkt, pos + 1) + 1;
                    break;
                case TDS_SSPI_TOKEN:
                    data->token_name = "sspi token";
                    token_sz = dissect_tds_sspi_token(pkt, pos + 1) + 1;
                    break;
                default:
                    token_sz = 0;
                    break;
            }

            /* Move on if nothing identifiable found */
            if(token_sz == 0)
                break;

            /* and step to the end of the token, rinse, lather, repeat */
            pos += token_sz;
        }
    }
}

static void write_tds_log(struct flow_info *flow, int direction, struct tds_info *info)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "tds");

    for(i=0; i<EM_TDS_TOKEN_00_VALUE; i++){
        switch(tds_field_array[i].index){
        case EM_TDS_PACKETTYPE:
            if (info->PacketType)
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->PacketType, strlen(info->PacketType));
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_TDS_CHANNEL:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Channel);
            break;
        case EM_TDS_PACKETNUMBER:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->PacketNumber);
            break;
        case EM_TDS_CLIENTNAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->ClientName, strlen(info->ClientName));
            break;
        case EM_TDS_USERNAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type,  (const uint8_t *)info->Username, strlen(info->Username));
            break;
        case EM_TDS_PASSWORD:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->Password, strlen(info->Password));
            break;
        case EM_TDS_LOGIN_STATUS:
            if(flow->userdata[0])
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "YES", 3);
            else
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "NO", 2);
            break;
        case EM_TDS_APPNAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->AppName, strlen(info->AppName));
            break;
        case EM_TDS_SERVERNAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->ServerName, strlen(info->ServerName));
            break;
        case EM_TDS_LIBRARYNAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->LibraryName, strlen(info->LibraryName));
            break;
        case EM_TDS_LOCALE:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->Locale, strlen(info->Locale));
            break;
        case EM_TDS_DATABASENAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->DatabaseName, strlen(info->DatabaseName));
            break;

        case EM_TDS_WINDOW:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Window);
            break;
        case EM_TDS_TDSVERSION:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->TDSversion);
            break;
        case EM_TDS_CLIENTVERSION:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Clientversion);
            break;
        case EM_TDS_CLIENTPID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->ClientPID);
            break;
        case EM_TDS_CONNECTIONID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->ConnectionID);
            break;
        case EM_TDS_OPTION_FLAGS1:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Option_Flags1);
            break;
        case EM_TDS_OPTION_FLAGS2:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Option_Flags2);
            break;
        case EM_TDS_RESERVED_FLAGS:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Reserved_Flags);
            break;
        case EM_TDS_SQL_TYPE_FLAGS:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->SQL_Type_Flags);
            break;
        case EM_TDS_TIMEZONE:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->TimeZone);
            break;
        case EM_TDS_COLLATION:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, NULL, info->Collation);
            break;

        case EM_TDS_QUERY:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->Query, strlen(info->Query));
            break;
        case EM_TDS_LANGUAGE_TEXT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->Language_text, strlen(info->Language_text));
            break;
        case EM_TDS_NTLMSSP:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->NTLMSSP, strlen(info->NTLMSSP));
            break;
        case EM_TDS_GSSAPI:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tds_field_array[i].type, (const uint8_t *)info->GSSAPI, strlen(info->GSSAPI));
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }

    }

    for (i = 0; i < TOKEN_NUM; i++) {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->token[i].token_value, strlen(info->token[i].token_value));
        if (info->token[i].token_name)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->token[i].token_name, strlen(info->token[i].token_name));
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->token[i].token_content, strlen(info->token[i].token_content));
    }

    for (i = 0; i < RPC_PARAM_NUM; i++) {
        if (info->param[i].rpc_id)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->param[i].rpc_id, strlen(info->param[i].rpc_id));
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->param[i].rpc_name, strlen(info->param[i].rpc_name));

        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->param[i].param_value, strlen(info->param[i].param_value));
    }

    #if 0
    if (info->PacketType)
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->PacketType, strlen(info->PacketType));
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Channel);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->PacketNumber);    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ClientName, strlen(info->ClientName));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Username, strlen(info->Username));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Password, strlen(info->Password));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->AppName, strlen(info->AppName));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ServerName, strlen(info->ServerName));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->LibraryName, strlen(info->LibraryName));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Locale, strlen(info->Locale));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->DatabaseName, strlen(info->DatabaseName));    

    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Window);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->TDSversion);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Clientversion);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ClientPID);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ConnectionID);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Option_Flags1);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Option_Flags2);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Reserved_Flags);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->SQL_Type_Flags);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->TimeZone);    
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Collation);
    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Query, strlen(info->Query));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Language_text, strlen(info->Language_text));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->NTLMSSP, strlen(info->NTLMSSP));    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->GSSAPI, strlen(info->GSSAPI));    

    for (i = 0; i < TOKEN_NUM; i++) {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->token[i].token_value, strlen(info->token[i].token_value)); 
        if (info->token[i].token_name)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->token[i].token_name, strlen(info->token[i].token_name));
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->token[i].token_content, strlen(info->token[i].token_content));    
    }
    
    for (i = 0; i < RPC_PARAM_NUM; i++) {
        if (info->param[i].rpc_id)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->param[i].rpc_id, strlen(info->param[i].rpc_id));
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->param[i].rpc_name, strlen(info->param[i].rpc_name)); 

        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->param[i].param_value, strlen(info->param[i].param_value));    
    }
    #endif

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_TDS;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
        
    if (write_tbl_log(log_ptr) != 1)
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    
    return;
}

static void write_dbbasic_log(struct flow_info *flow, int direction, struct tds_info *info)
{
    char __str[64] = {0};
    int idx = 0,i;
    struct tbl_log *log_ptr;
    
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }

    init_log_ptr_data(log_ptr, flow,PROTOCOL_DBBASIC);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "dbbasic");

    for(i=0;i<EM_DBBASIC_MAX;i++){
        switch(dbbasic_field_array[i].index){
        case EM_DBBASIC_DBTYPE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "sqlserver", 9);
            break;
        case EM_DBBASIC_USERNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Username, strlen(info->Username));    
            break;
        case EM_DBBASIC_PASSWORD:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Password, strlen(info->Password));
            break;
        case EM_DBBASIC_DBNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->DatabaseName, strlen(info->DatabaseName));    
            break;
        case EM_DBBASIC_DBSQL:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->Query, strlen(info->Query));
            break;
        case EM_DBBASIC_DBIP:
            if (flow->ip_version == 4)
                get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
            else
                get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_DBBASIC_DBPORT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ntohs(flow->tuple.inner.port_dst));
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    log_ptr->proto_id = PROTOCOL_DBBASIC;
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_DBBASIC;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);

    return;
}


static int dissect_tds_payload(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len)
{
    UNUSED(direction);
    uint8_t type;
    //uint8_t status;
    uint16_t channel;
    uint8_t packet_number;
    //uint32_t len = 0;
    uint32_t offset = 0;
    struct dpi_pkt_st pkt;
    struct tds_info info;
    memset(&info, 0, sizeof(info));
    pkt.payload = payload;
    pkt.payload_len = payload_len;
    
    if (payload_len < 8)
        return 0;

    flow->match_data_len = payload_len;

    type = get_uint8_t(payload, offset);
    //status = get_uint8_t(payload, offset + 1);
    //len = get_uint16_ntohs(payload, offset + 2);
    channel = get_uint16_ntohs(payload, offset + 4);
    packet_number = get_uint8_t(payload, offset + 6);
    offset += 8;        /* skip Netlib header */
    
    info.Channel = channel;
    info.PacketNumber = packet_number;
    info.PacketType = val_to_string(type, packet_type_names);
    
    switch (type) {

        case TDS_RPC_PKT:
            flow->userdata[0] = 1;
            dissect_tds_rpc(&pkt, offset, &info);
            break;
        case TDS_RESP_PKT:            
            dissect_tds_resp(&pkt, offset, (struct dts_session *)flow->app_session, &info);
            break;
        case TDS_LOGIN7_PKT:        
            dissect_tds7_login(&pkt, offset, &info);
            break;
        case TDS_QUERY_PKT:
            flow->userdata[0] = 1;
            dissect_tds_query_packet(&pkt, offset, (struct dts_session *)flow->app_session, &info);
            break;
        case TDS_QUERY5_PKT:
            flow->userdata[0] = 1;
            dissect_tds_query5_packet(&pkt, offset, (struct dts_session *)flow->app_session, &info);
            break;
        case TDS_SSPI_PKT:
            //dissect_tds_nt(next_tvb, pinfo, tds_tree, offset - 8, -1);
            break;
        case TDS_TRANS_MGR_PKT:
            //dissect_tds_transmgr_packet(next_tvb, pinfo, tds_tree);
            break;
        case TDS_ATTENTION_PKT:
            break;
        case TDS_PRELOGIN_PKT:
            dissect_tds7_prelogin_packet(&pkt, offset);
            break;

        default:
            //proto_tree_add_item(tds_tree, hf_tds_unknown_tds_packet, next_tvb, 0, -1, ENC_NA);
            break;
    }
    //write_tds_log(flow, direction, &info);
    write_dbbasic_log(flow, direction, &info);
    
    return 0;
}

static int dissect_tds(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    //uint8_t type;
    uint8_t status;
    //uint16_t channel;
    uint8_t packet_number;
    uint32_t len = 0;
    uint32_t offset = 0;
    struct dts_session *session;

    uint8_t *real_payload;
    uint32_t real_payload_len;
    
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct dts_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct dts_session));
        session = (struct dts_session *)flow->app_session;
        session->tds7_version = TDS_PROTOCOL_NOT_SPECIFIED;
    }
    session = (struct dts_session *)flow->app_session;
    
    if (detect_tls(payload, payload_len)) {
        //tls do not dissect
        return 0;
    }

    if (flag == DISSECT_PKT_FIANL) {
        return dissect_tds_payload(flow, direction, payload, payload_len);
    }

    if (direction == FLOW_DIR_SRC2DST && flow->reassemble_pkt_src2dst_num < 32 && session->src2dst_rsm_len > 0) {
        tcp_reassemble_add_item(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
        flow->reassemble_pkt_src2dst_num++;
        if (flow->rsm_src2dst_len >= session->src2dst_rsm_len) {
            session->src2dst_rsm_len = 0;
            real_payload_len = flow->rsm_src2dst_len;
            real_payload = (uint8_t *)dpi_malloc(flow->rsm_src2dst_len);
            if (real_payload) {
                tcp_reassemble_do(&flow->reassemble_src2dst_head, real_payload, &real_payload_len);
                dissect_tds_payload(flow, direction, real_payload, real_payload_len);
                dpi_free(real_payload);
            }
            tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
        }
        return 0;
    } else if (direction == FLOW_DIR_DST2SRC && flow->reassemble_pkt_dst2src_num < 32 && session->dst2src_rsm_len > 0) {
        tcp_reassemble_add_item(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len, seq, payload, payload_len);
        flow->reassemble_pkt_dst2src_num++;
        if (flow->rsm_dst2src_len >= session->dst2src_rsm_len) {
            session->dst2src_rsm_len = 0;
            real_payload_len = flow->rsm_dst2src_len;
            real_payload = (uint8_t *)dpi_malloc(flow->rsm_dst2src_len);
            if (real_payload) {
                tcp_reassemble_do(&flow->reassemble_dst2src_head, real_payload, &real_payload_len);
                dissect_tds_payload(flow, direction, real_payload, real_payload_len);
                dpi_free(real_payload);
            }
            tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);
        }
        return 0;
    }
    
    if (payload_len < 8)
        return 0;

    //type = get_uint8_t(payload, offset);
    status = get_uint8_t(payload, offset + 1);
    len = get_uint16_ntohs(payload, offset + 2);
    //channel = get_uint16_ntohs(payload, offset + 4);
    packet_number = get_uint8_t(payload, offset + 6);
    offset += 8;        /* skip Netlib header */
    
    if (packet_number > 1 || (status & STATUS_LAST_BUFFER) == 0) {
        if (direction == FLOW_DIR_SRC2DST && flow->reassemble_pkt_src2dst_num < 32) {
            tcp_reassemble_add_item(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
            flow->reassemble_pkt_src2dst_num++;
            session->src2dst_rsm_len = len;
        } else if (direction == FLOW_DIR_DST2SRC && flow->reassemble_pkt_dst2src_num < 32) {
            tcp_reassemble_add_item(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len, seq, payload, payload_len);
            flow->reassemble_pkt_dst2src_num++;
            session->dst2src_rsm_len = len;
        } else
            return PKT_DROP;
    } else {
        dissect_tds_payload(flow, direction, payload, payload_len);
    }

    return 0;
}

static void init_tds_dissector(void)
{
    dpi_register_proto_schema(tds_field_array,EM_TDS_MAX,"tds");
    port_add_proto_head(IPPROTO_TCP, 1433, PROTOCOL_TDS);
    port_add_proto_head(IPPROTO_TCP, 2433, PROTOCOL_TDS);

    tcp_detection_array[PROTOCOL_TDS].proto = PROTOCOL_TDS;    
    tcp_detection_array[PROTOCOL_TDS].identify_func = identify_tds;
    tcp_detection_array[PROTOCOL_TDS].dissect_func = dissect_tds;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TDS].excluded_protocol_bitmask, PROTOCOL_TDS);

    map_fields_info_register(tds_field_array,PROTOCOL_TDS, EM_TDS_MAX,"tds");

    return;
}


static __attribute((constructor)) void    before_init_tds(void){
    register_tbl_array(TBL_LOG_TDS, 0, "tds", init_tds_dissector);
}


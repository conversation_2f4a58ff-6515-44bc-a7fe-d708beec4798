#ifndef _DPI_SSL_H_
#define _DPI_SSL_H_

#include "dpi_common.h"
#include <openssl/x509v3.h>

#define CERT_MAX_NUM 4
typedef void (*linux_free)(void *);
#define PROTOCOL_HEAD_DEF_LINUX(head_name) char *head_name##_ptr; uint32_t head_name##_len; linux_free head_name##_free;
#define PROTOCOL_HEAD_STRDEF(head_name) char *head_name##_val_ptr; uint32_t head_name##_val_len;

/* SSL Cipher Suite modes */
typedef enum {
	MODE_STREAM,    /* GenericStreamCipher */
	MODE_CBC,       /* GenericBlockCipher */
	MODE_GCM,       /* GenericAEADCipher */
	MODE_CCM,       /* AEAD_AES_{128,256}_CCM with 16 byte auth tag */
	MODE_CCM_8,     /* AEAD_AES_{128,256}_CCM with 8 byte auth tag */
	MODE_POLY1305,  /* AEAD_CHACHA20_POLY1305 with 16 byte auth tag (RFC 7905) */
} ssl_cipher_mode_t;

typedef enum {
    DPI_X509_NAME_COUNTRY,      //国家名，
    DPI_X509_NAME_PROVINCE,     //省州名
    DPI_X509_NAME_CITY,         //地区名
    DPI_X509_NAME_ORG_UNIT,     //单位名
    DPI_X509_NAME_ORG,          //组织名
    DPI_X509_NAME_STREET,       //街道名
    DPI_X509_NAME_COMMON,       //通用名
    DPI_X509_NAME_EMAIL,        //邮箱
    DPI_X509_NAME_MAX
} x509_name_level_t;

typedef enum {
    SSL_HND_HELLO_REQUEST          = 0,
    SSL_HND_CLIENT_HELLO           = 1,
    SSL_HND_SERVER_HELLO           = 2,
    SSL_HND_HELLO_VERIFY_REQUEST   = 3,
    SSL_HND_NEWSESSION_TICKET      = 4,
    SSL_HND_END_OF_EARLY_DATA      = 5,
    SSL_HND_HELLO_RETRY_REQUEST    = 6,
    SSL_HND_ENCRYPTED_EXTENSIONS   = 8,
    SSL_HND_CERTIFICATE            = 11,
    SSL_HND_SERVER_KEY_EXCHG       = 12,
    SSL_HND_CERT_REQUEST           = 13,
    SSL_HND_SVR_HELLO_DONE         = 14,
    SSL_HND_CERT_VERIFY            = 15,
    SSL_HND_CLIENT_KEY_EXCHG       = 16,
    SSL_HND_FINISHED               = 20,
    SSL_HND_CERT_URL               = 21,
    SSL_HND_CERT_STATUS            = 22,
    SSL_HND_SUPPLEMENTAL_DATA      = 23,
    SSL_HND_KEY_UPDATE             = 24,
    SSL_HND_ENCRYPTED_EXTS         = 67
} HandshakeType;

typedef struct DpiX509Extension_ {
    int         extCount;
    char        extIDSet[128];

    uint32_t    key_usage;
    char        ext_key_usage[32];
    char        cert_policies[64];
    char        sub_alt_name[128];
    char        sub_key_id[64];
    char        iss_alt_name[128];
    char        auth_info_access[256];
    char        auth_key_id[64];
    char        crl_dist_points[256];

    struct {
        int CA;
        long pathLen;
    }           basic_cons;

} DpiX509Extension;

struct dpi_x509_chain_st{
struct dpi_x509_chain_st* subject;
char desc[1024];
struct dpi_x509_chain_st *iss;
};
struct dpi_x509_st
{
    uint8_t  polluted;                     //标志此结构是否写过
    uint8_t  direction;

    uint32_t length;                       //本证书长度　
    long version;                          //版本
    X509 *x509;                            //open x509
    int sign_valid;                        //签名是否有效
    const char* signature_alg;             //签名算法,用于上级证书对本证书签名
    char signature[1024];
    int   signature_len;
    const char* pubKey_alg;                //公钥算法,用于本证书对下级证书签名或认证
    int issuer_num;                        //颁发者字段数
    int subject_num;                       //拥有者字段数
    struct tm begintime;                   //生效时间
    struct tm endtime;                     //失效时间
    PROTOCOL_HEAD_STRDEF(serialNum);       //序列号
    PROTOCOL_HEAD_STRDEF(issuer);          //颁发者总字段
    PROTOCOL_HEAD_STRDEF(subject);         //拥有者总字段
    PROTOCOL_HEAD_STRDEF(pubKey);          //公钥
    PROTOCOL_HEAD_STRDEF(subjectPubKey);          //公钥
    PROTOCOL_HEAD_STRDEF(pubKey_exp);      //公钥指数
    uint8_t inDatabase;                    //是否写为实体文件
    uint8_t is_wildcard;                   //是否为通配符证书
    uint8_t is_self_signed;                //是否为自签名证书
    uint8_t subject_alt_DNSNum;            //拥有者可用的DNS数量

    DpiX509Extension   extension;          // 扩展项

    char fingerprint[48];                  //指纹
    char md5sum[48];                       //指纹md5值

    // const char *issuer_cn, *issuer_o, *issuer_ou, *issuer_c, *issuer_l, *issuer_st;  //通用名，组织名，单位名，国家名，地区名　省州名
    // const char *subject_cn, *subject_o, *subject_ou, *subject_c, *subject_l, *subject_st;

    const char *issuer_name[DPI_X509_NAME_MAX];
    const char *subject_name[DPI_X509_NAME_MAX];
};


typedef struct _new_session_ticket {
    uint32_t        lifetime_hint;
    uint16_t        ticket_len;
    uint8_t         ticket_data[256];

} new_session_ticket;

struct SslCertInfo
{
	  uint8_t         CertificatesDissectedFlag;    //本次握手证书是否已经解过标志,以防重复解析
    uint8_t         CertificatesNums;           //证书数量
    uint32_t        CertificatesLength;         //证书总长度
    struct dpi_x509_st  cert[CERT_MAX_NUM];      //证书机构数组
};

typedef struct _SslCipherSuite {
	int number;
	int kex;
	int enc;
	int dig;
	ssl_cipher_mode_t mode;
} SslCipherSuite;


#define MAX_CERT_REQUEST    10
typedef struct SSLCertRequest_ {
    int     cert_types_count;
    uint8_t cert_types_array[MAX_CERT_REQUEST];

} SSLCertRequest;


#define CLIENT_CERT    FLOW_DIR_SRC2DST
#define SERVER_CERT    FLOW_DIR_DST2SRC

#define EXT_TYPE_MAX_SIZE 20

typedef struct SSL_info_t
{
    uint32_t ContentType;
    uint32_t Version;
    uint32_t RecordLayerLength;

    uint32_t ChangeCipherSpec;
    uint32_t Application_Data;

    uint32_t AlertLen;
    uint32_t AlertLevel;
    uint32_t AlertDescription;

    uint32_t HandshakeType;

    uint8_t  unlegal_flag;

    uint32_t ClientHelloLength;
    uint32_t ClientProtocolVersion;
    char     ClientGMTUnixTime[32];
    uint8_t  ClientRandomBytes[28];
    uint8_t  ClientSessionID_val_ptr[32];
    uint16_t ClientSessionID_val_len;
    char     ClientCipherSuites_val_ptr[128];
    uint16_t ClientCipherSuites_val_len;
    uint8_t  ClientCompressionMethods_val_ptr[8];
    uint16_t ClientCompressionMethods_val_len;
    PROTOCOL_HEAD_DEF(ClientExtensions);         // -> ClientExtensionsLength, ClientExtensions
    uint8_t  ClientECFormats[4];
    uint8_t  ClientECGroups[32];

    uint32_t ServerHelloLength;
    uint32_t ServerProtocolVersion;
    char     ServerGMTUnixTime[32];
    uint8_t  ServerRandomBytes[28];
    uint8_t  ServerSessionID_val_ptr[32];
    uint16_t ServerSessionID_val_len;
    uint32_t ServerCipherSuite;
    uint32_t ServerCompressionMethod;
    PROTOCOL_HEAD_DEF(ServerExtensions);         // -> ServerExtensionsLength, ServerExtensions
    uint8_t  ServerECFormats[4];
    uint8_t  ServerECGroups[32];
    char        ec_point_format[32];

    uint8_t ClientHelloDissectedFlag;
    uint8_t ServerHelloDissectedFlag;
    uint8_t ClientKexDissectedFlag;
    uint8_t ServerKexDissectedFlag;
    uint8_t NewTicketDissectedFlag;

    uint8_t CertificatesDissectedFlag[2];    //本次握手证书是否已经解过标志,以防重复解析

    struct SslCertInfo  cert_infos[2];      // 区分是客户端证书 还是 服务端证书
    struct dpi_x509_chain_st *cert_chain[CERT_MAX_NUM * 2]; //理论上最多 CERT_MAX_NUM * 2 数量的证书会有 CERT_MAX_NUM * 2 的证书链(每个证书都没有交集)
    int cert_chain_num;
    /* Server Key Exchange _S_ */
    uint32_t ServerKexLength;
    const char* ECDHCurveType;
    const char* ECDHNamedCurve;
    PROTOCOL_HEAD_DEF_LINUX(ECDHPubkey);
    const char*ECDHSignatureHashAlgorithm;
    const char*ECDHSignatureSigAlgorithm;
    PROTOCOL_HEAD_DEF_LINUX(ECDHSignature);
    PROTOCOL_HEAD_DEF_LINUX(EncrypedPubkey);
    struct { int type; const uint8_t* ptr; uint32_t len; } ClientKeyExchangeSuites; //客户端密钥交换套件

    SSLCertRequest      cert_request;
    /* Hello ExtenSion 相关 */
    uint8_t     server_name_type;
    char        ServerName[32];
    uint16_t    ext_type[EXT_TYPE_MAX_SIZE];
    uint8_t     ext_type_num;
    uint16_t    cli_exts_id[EXT_TYPE_MAX_SIZE];
    int         cli_exts_num;
    uint16_t    srv_exts_id[EXT_TYPE_MAX_SIZE];
    int         srv_exts_num;
    uint8_t   ext_session_ticket;
    uint8_t   session_ticket[32];
    uint8_t   client_grease;
    uint8_t   ext_heartbeat;
    uint8_t   ext_renegotiate;
    uint8_t   support_group[128];



    uint8_t JA3C[16];
    uint8_t JA3C_flag;
    uint8_t JA3S[16];
    uint8_t JA3S_flag;
    const uint8_t *svr_cert;
    uint32_t svr_cert_len;
    char svr_cert_chain[1024];

    char        ext_ticket_data[256];
    int         ext_ticket_length;
    new_session_ticket new_sess_ticket;   // add by hongll
    char        ServerKeyExDHGen_g[1024];   // 底数
    uint16_t    ServerKeyExDHGen_g_length;
    char        ServerKeyExDHMod_p[1024];
    uint16_t    ServerKeyExDHMod_p_length;
    char        ServerKeyExDHYs[1024];
    uint16_t    ServerKeyExDHYs_length;

    uint8_t     ext_grease;     //按位存储clien(FLOW_DIR_SRC2DIR) 和 server(FLOW_DIR_DST2SRC) grease扩展标志

}__attribute__((packed)) ST_SSLInfo;


typedef struct _SSL_Sesion {
	int         cipher;  // 选中的加密套件
	int         compression;
	int         version;
	uint8_t     client_cert_type;
	uint8_t     server_cert_type;
	int         state;
	const       SslCipherSuite *cipher_suite;
    /********* http_cache_t ************/
    struct ssl_cache_t
    {
        char     *cache;
        int       cache_size;
        int       cache_hold;
    } cache[2];
    /********* http_cache_t ************/
    struct flow_info *flow;

	ST_SSLInfo  ssl_info;

} SSL_Session;


int  dissect_x509(struct flow_info *flow, int direction, const uint8_t* payload, uint32_t payload_len, struct SSL_info_t* pst_sslinfo);   //解析证书
void write_x509_fields_storage(struct flow_info *flow, int direction, struct SSL_info_t* sslinfo);                                    //写证书tbl
int  write_x509_log(struct flow_info *flow, int direction, struct dpi_x509_st *info, SdtMatchResult *match_result);                //写证书tbl
void free_x509(struct dpi_x509_st *cert, uint32_t count);                                                               //释放证书结构

#endif


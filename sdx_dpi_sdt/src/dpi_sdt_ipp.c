#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <glib.h>

#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"

#include "sdt_ip_protocols.h"
#include "dpi_sdt_ipp.h"


extern struct rte_mempool *tbl_log_mempool;


static dpi_field_table  ipp_field_array[] = {
    DPI_FIELD_D(ENUM_IPP_VLAN1,                    YA_FT_UINT16,       "vlan1"),
    DPI_FIELD_D(ENUM_IPP_VLAN2,                    YA_FT_UINT16,       "vlan2"),
    DPI_FIELD_D(ENUM_IPP_MPLS1,                    YA_FT_UINT32,       "mpls1"),
    DPI_FIELD_D(ENUM_IPP_MPLS2,                    YA_FT_UINT32,       "mpls2"),
    DPI_FIELD_D(ENUM_IPP_SRCMAX,                   YA_FT_STRING,       "src_mac"),
    DPI_FIELD_D(ENUM_IPP_DSTMAX,                   YA_FT_STRING,       "dst_mac"),

    DPI_FIELD_D(ENUM_IPP_PROTNUM,                  YA_FT_UINT8,        "ip_proto"),
    DPI_FIELD_D(ENUM_IPP_SRCADDR,                  YA_FT_STRING,       "ip_srcip"),
    DPI_FIELD_D(ENUM_IPP_DSTADDR,                  YA_FT_STRING,       "ip_dstip"),
    DPI_FIELD_D(ENUM_IPP_INNSRCADDR,               YA_FT_STRING,       "ip_inner_srcip"),
    DPI_FIELD_D(ENUM_IPP_INNDSTADDR,               YA_FT_STRING,       "ip_inner_dstip"),

    DPI_FIELD_D(ENUM_IPP_ADDRTYPE,                 YA_FT_UINT8,        "ip_addr_type"),
    DPI_FIELD_D(ENUM_IPP_INNADDRTYPE,              YA_FT_UINT8,        "ip_inner_addr_type"),

    DPI_FIELD_D(ENUM_IPP_SRCPORT,                  YA_FT_UINT16,       "ip_src_port"),
    DPI_FIELD_D(ENUM_IPP_DSTPORT,                  YA_FT_UINT16,       "ip_dst_port"),
    DPI_FIELD_D(ENUM_IPP_VER,                      YA_FT_UINT8,        "ip_version"),
    DPI_FIELD_D(ENUM_IPP_IPFLAG,                   YA_FT_UINT16,       "ip_flag"),
    DPI_FIELD_D(ENUM_IPP_TOTLEN,                   YA_FT_UINT16,       "ip_length"),
    DPI_FIELD_D(ENUM_IPP_CONTENT,                  YA_FT_STRING,       "ip_payload"),
    DPI_FIELD_D(ENUM_IPP_PAYLEN,                   YA_FT_UINT16,       "ip_payload_len"),
    DPI_FIELD_D(ENUM_IPP_HEADER,                   YA_FT_STRING,       "ip_header"),

    DPI_FIELD_D(ENUM_IPP_UDPPAY,                   YA_FT_STRING,       "udp_payload"),
    DPI_FIELD_D(ENUM_IPP_UDPPAYLEN,                YA_FT_UINT16,       "udp_payload_len"),
    DPI_FIELD_D(ENUM_IPP_UDPHEADER,                YA_FT_STRING,       "udp_header"),

    DPI_FIELD_D(ENUM_IPP_TCPFLAG,                  YA_FT_UINT16,       "tcp_flags"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGFIN,               YA_FT_UINT8,        "tcp_flags_fin"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGSYN,               YA_FT_UINT8,        "tcp_flags_syn"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGRST,               YA_FT_UINT8,        "tcp_flags_rst"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGPSH,               YA_FT_UINT8,        "tcp_flags_psh"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGACK,               YA_FT_UINT8,        "tcp_flags_ack"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGURG,               YA_FT_UINT8,        "tcp_flags_urg"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGECE,               YA_FT_UINT8,        "tcp_flags_ece"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGCWR,               YA_FT_UINT8,        "tcp_flags_cwr"),
    DPI_FIELD_D(ENUM_IPP_TCPFLAGNS,                YA_FT_UINT8,        "tcp_flags_ns"),
    DPI_FIELD_D(ENUM_IPP_TCPWINS,                  YA_FT_UINT16,       "tcp_windows"),
    DPI_FIELD_D(ENUM_IPP_TCPPAY,                   YA_FT_STRING,       "tcp_payload"),
    DPI_FIELD_D(ENUM_IPP_TCPPAYLEN,                YA_FT_UINT16,       "tcp_payload_len"),
    DPI_FIELD_D(ENUM_IPP_TCPHEADER,                YA_FT_STRING,       "tcp_header"),
};

static int
ipp_field_element(struct tbl_log *log_ptr,struct flow_info *flow,int direction, int *idx, int i, ProtoRecord * pRec)
{
    const struct pkt_info  *pkt=flow->pkt;
    if(!pkt){
        return 0;
    }

    char dot_sip[32]={0};
    char dot_dip[32]={0};
    char smac[32]   = {0};
    char dmac[32]   = {0};

    switch(i){
    case ENUM_IPP_VLAN1:
        if(flow->vlan_flag>0)
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, flow->data_link_layer.vlan_id[0]);
        else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;


    case ENUM_IPP_VLAN2:
        if(flow->vlan_flag>1)
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, flow->data_link_layer.vlan_id[1]);
        else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_MPLS1:
        if(flow->is_mpls>0)
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, flow->data_link_layer.mpls_label[0]);
        else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_MPLS2:
        if(flow->is_mpls>1)
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, flow->data_link_layer.mpls_label[1]);
        else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_SRCMAX:
        if(flow->pkt && flow->pkt->ethhdr){
            snprintf(smac, 32, "%x:%x:%x:%x:%x:%x",
                                                    flow->pkt->ethhdr->h_source[0], flow->pkt->ethhdr->h_source[1],
                                                    flow->pkt->ethhdr->h_source[2], flow->pkt->ethhdr->h_source[3],
                                                    flow->pkt->ethhdr->h_source[4], flow->pkt->ethhdr->h_source[5]);
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, (const uint8_t *)smac, (uint64_t)strlen(smac));
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        break;
    case ENUM_IPP_DSTMAX:
        if(flow->pkt && flow->pkt->ethhdr){
            snprintf(dmac, 32, "%x:%x:%x:%x:%x:%x",
                                                    flow->pkt->ethhdr->h_dest[0], flow->pkt->ethhdr->h_dest[1],
                                                    flow->pkt->ethhdr->h_dest[2], flow->pkt->ethhdr->h_dest[3],
                                                    flow->pkt->ethhdr->h_dest[4], flow->pkt->ethhdr->h_dest[5]);
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, (const uint8_t *)dmac, (uint64_t)strlen(dmac));
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        break;
    case ENUM_IPP_PROTNUM:
        if(4==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,  (uint64_t)pkt->iph4->protocol);
        }else if(6==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,  (uint64_t)pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt);
        }

        break;
    case ENUM_IPP_SRCADDR:
        if(4==pRec->ip_version){
            get_ip4string(dot_sip, sizeof(dot_sip),( uint32_t)pRec->tuple.IpSrc.ipv4);
        }else if(6==pRec->ip_version){
            get_ip6string(dot_sip, sizeof(dot_sip),(const uint8_t *)pRec->tuple.IpSrc.ipv6);
        }
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, (const uint8_t *)dot_sip, (uint64_t)strlen(dot_sip));
        break;
    case ENUM_IPP_DSTADDR:
        if(4==pRec->ip_version){
            get_ip4string(dot_dip, sizeof(dot_dip),( uint32_t)pRec->tuple.IpDst.ipv4);
        }else if(6==pRec->ip_version){
            get_ip6string(dot_dip, sizeof(dot_dip),(const uint8_t *)pRec->tuple.IpDst.ipv6);
        }
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, (const uint8_t *)dot_dip, (uint64_t)strlen(dot_dip));
        break;
    //case ENUM_IPP_INNSRCADDR:
    //    break;
    //case ENUM_IPP_INNDSTADDR:
    //    break;
    case ENUM_IPP_ADDRTYPE:
        if(4==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, (uint64_t)1);
        }else if(6==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, (uint64_t)0);
        }

        break;
    //case ENUM_IPP_INNADDRTYPE:
    //    break;
    case ENUM_IPP_SRCPORT:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, (uint64_t)ntohs(pRec->tuple.PortSrc));
        break;
    case ENUM_IPP_DSTPORT:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL, (uint64_t)ntohs(pRec->tuple.PortDst));
        break;
    case ENUM_IPP_VER:
        if(pRec->ip_version==4 ||pRec->ip_version==6){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,(uint64_t)pRec->ip_version);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,(uint64_t)1);
        }
        break;
    case ENUM_IPP_IPFLAG:
        if(pRec->ip_version==4){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,(uint64_t)ntohs(pkt->iph4->frag_off));
        }else if(6==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,(uint64_t)0);
        }
        break;
    case ENUM_IPP_TOTLEN:
        if(pRec->ip_version==4){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,(uint64_t)ntohs(pkt->iph4->tot_len));
        }else if(6==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type, NULL,(uint64_t)ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen));
        }
        break;
    //case ENUM_IPP_CONTENT:

        break;
    case ENUM_IPP_PAYLEN:
        if(pRec->ip_version==4){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                NULL,
                                (uint64_t)pRec->ip_len+pRec->l3h_start-pRec->l4h_start);
        }else if(6==pRec->ip_version){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                NULL,
                                (uint64_t)pRec->ip_len);
        }
        break;
    case ENUM_IPP_HEADER:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;

    case ENUM_IPP_UDPPAY:
        #if 0
        if((pRec->ip_proto==PROTOCOL_UDP) && (pRec->pPayload.pBuff!=NULL || pRec->pPayload.len>0)){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX,
                                    (const uint8_t*)&pRec->pPayload.pBuff[pRec->l4payload_start],
                                    (uint64_t)pRec->ip_len+pRec->l3h_start-pRec->l4payload_start);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        #endif
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_UDPPAYLEN:
        if(pRec->ip_proto==PROTOCOL_IPP)
        {
            if(pRec->ip_version==4 && pRec->ip_len+pRec->l3h_start>=pRec->l4payload_start){
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                                NULL,
                                                (uint64_t)pRec->ip_len+pRec->l3h_start-pRec->l4payload_start);
            }else if(6==pRec->ip_version  && pRec->ip_len+pRec->l4h_start>=pRec->l4payload_start){
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                                NULL,
                                                (uint64_t)pRec->ip_len+pRec->l4h_start-pRec->l4payload_start);
            }
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_UDPHEADER:
    case ENUM_IPP_TCPHEADER:
        #if 0
        if(pRec->ip_proto==PROTOCOL_UDP || pRec->ip_proto==IPPROTO_TCP){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX,
                                    (const uint8_t*)&pRec->pPayload.pBuff[pRec->l4h_start],
                                    (uint64_t)(pRec->l4h_start-pRec->l4payload_start));
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        #endif
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;

    case ENUM_IPP_TCPFLAG:
        if(pRec->ip_version==4 || pRec->ip_version==6)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pRec->tcp_flags);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);

        break;
    case ENUM_IPP_TCPFLAGFIN:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->fin);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGSYN:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->fin);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGRST:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->rst);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGPSH:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->psh);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGACK:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->ack);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGURG:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->urg);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGECE:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->ece);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGCWR:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->cwr);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPFLAGNS:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pkt->tcph->res1&0x1);
        }else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;


    case ENUM_IPP_TCPWINS:
        if((pRec->ip_proto==IPPROTO_TCP) &&
           (pRec->ip_version==4 || pRec->ip_version==6))
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                    NULL,
                                    (uint64_t)pRec->tcp_windowsize);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        break;
    case ENUM_IPP_TCPPAY:
        #if 0
        if((pRec->ip_proto==IPPROTO_TCP) &&
            (pRec->pPayload.pBuff!=NULL || pRec->pPayload.len>0))
        {
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX,
                                    (const uint8_t*)&pRec->pPayload.pBuff[pRec->l4payload_start],
                                    (uint64_t)pRec->ip_len+pRec->l3h_start-pRec->l4payload_start);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        #endif
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case ENUM_IPP_TCPPAYLEN:
        if(pRec->ip_proto==IPPROTO_TCP)
        {
            if(4==pRec->ip_version && pRec->ip_len+pRec->l3h_start>=pRec->l4payload_start){
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                                NULL,
                                                (uint64_t)pRec->ip_len+pRec->l3h_start-pRec->l4payload_start);
            }else if(6==pRec->ip_version  && pRec->ip_len+pRec->l4h_start>=pRec->l4payload_start){
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ipp_field_array[i].type,
                                                NULL,
                                                (uint64_t)pRec->ip_len+pRec->l4h_start-pRec->l4payload_start);
            }
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        break;

    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }


    return PKT_OK;
}



static int write_ipp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    if (g_config.protocol_switch[PROTOCOL_IPP] == 0)
        return 0;


    ProtoRecord * pRec=(ProtoRecord * )field_info;
    if(!pRec){
        return 0;
    }

    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ipp");

    for(i=0; i<ENUM_IPP_MAX;i++){
        ipp_field_element(log_ptr,flow, direction, &idx, i, pRec);
    }
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_IPP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}

void dpi_sdt_init_ipp_dissector(void)
{
    map_fields_info_register(ipp_field_array,PROTOCOL_IPP, ENUM_IPP_MAX,"ipp");
    return;
}



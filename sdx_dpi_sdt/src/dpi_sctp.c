/****************************************************************************************
 * 文 件 名 : dpi_sctp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
编码: wangch   2021/01/18
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <glib.h>
#include <rte_mempool.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "dpi_sctp.h"
#include "dpi_detect.h"
#include "dpi_pschema.h"
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern __thread char    g_protoinfo[MAX_CONTENT_SIZE];
extern __thread uint16_t g_proto_layer[32];
extern __thread uint8_t  g_proto_layer_cnt;

/* mask for FCF */
#define WIFI_DATA                          0x2    /* 0000 0010 */
#define FCF_TYPE(fc)       (((fc) >> 2) & 0x3)    /* 0000 0011 = 0x3 */
#define FCF_SUBTYPE(fc)    (((fc) >> 4) & 0xF)    /* 0000 1111 = 0xF */
#define FCF_TO_DS(fc)          ((fc) & 0x0100)
#define FCF_FROM_DS(fc)        ((fc) & 0x0200)
#define ADD_PADDING(x) ((((x) + 3) >> 2) << 2)    /* 保证值为4的倍数，余大于0倍数进一 */
#define SCTP_CHUNCK_HEADER_LEN 16

#define SCTP_DATA_CHUNK_ID               0
#define SCTP_INIT_CHUNK_ID               1
#define SCTP_INIT_ACK_CHUNK_ID           2
#define SCTP_SACK_CHUNK_ID               3
#define SCTP_HEARTBEAT_CHUNK_ID          4
#define SCTP_HEARTBEAT_ACK_CHUNK_ID      5
#define SCTP_ABORT_CHUNK_ID              6
#define SCTP_SHUTDOWN_CHUNK_ID           7
#define SCTP_SHUTDOWN_ACK_CHUNK_ID       8
#define SCTP_ERROR_CHUNK_ID              9
#define SCTP_COOKIE_ECHO_CHUNK_ID       10
#define SCTP_COOKIE_ACK_CHUNK_ID        11
#define SCTP_ECNE_CHUNK_ID              12
#define SCTP_CWR_CHUNK_ID               13
#define SCTP_SHUTDOWN_COMPLETE_CHUNK_ID 14
#define SCTP_AUTH_CHUNK_ID              15
#define SCTP_NR_SACK_CHUNK_ID           16
#define SCTP_I_DATA_CHUNK_ID          0x40
#define SCTP_ASCONF_ACK_CHUNK_ID      0x80
#define SCTP_PKTDROP_CHUNK_ID         0x81
#define SCTP_RE_CONFIG_CHUNK_ID       0x82
#define SCTP_PAD_CHUNK_ID             0x84
#define SCTP_FORWARD_TSN_CHUNK_ID     0xC0
#define SCTP_ASCONF_CHUNK_ID          0xC1
#define SCTP_I_FORWARD_TSN_CHUNK_ID   0xC2
#define SCTP_IETF_EXT                 0xFF

struct int_to_string chunk_type_values[] = {
  { SCTP_DATA_CHUNK_ID,              "DATA" },
  { SCTP_INIT_CHUNK_ID,              "INIT" },
  { SCTP_INIT_ACK_CHUNK_ID,          "INIT_ACK" },
  { SCTP_SACK_CHUNK_ID,              "SACK" },
  { SCTP_HEARTBEAT_CHUNK_ID,         "HEARTBEAT" },
  { SCTP_HEARTBEAT_ACK_CHUNK_ID,     "HEARTBEAT_ACK" },
  { SCTP_ABORT_CHUNK_ID,             "ABORT" },
  { SCTP_SHUTDOWN_CHUNK_ID,          "SHUTDOWN" },
  { SCTP_SHUTDOWN_ACK_CHUNK_ID,      "SHUTDOWN_ACK" },
  { SCTP_ERROR_CHUNK_ID,             "ERROR" },
  { SCTP_COOKIE_ECHO_CHUNK_ID,       "COOKIE_ECHO" },
  { SCTP_COOKIE_ACK_CHUNK_ID,        "COOKIE_ACK" },
  { SCTP_ECNE_CHUNK_ID,              "ECNE" },
  { SCTP_CWR_CHUNK_ID,               "CWR" },
  { SCTP_SHUTDOWN_COMPLETE_CHUNK_ID, "SHUTDOWN_COMPLETE" },
  { SCTP_AUTH_CHUNK_ID,              "AUTH" },
  { SCTP_NR_SACK_CHUNK_ID,           "NR_SACK" },
  { SCTP_I_DATA_CHUNK_ID,            "I_DATA" },
  { SCTP_ASCONF_ACK_CHUNK_ID,        "ASCONF_ACK" },
  { SCTP_PKTDROP_CHUNK_ID,           "PKTDROP" },
  { SCTP_RE_CONFIG_CHUNK_ID,         "RE_CONFIG" },
  { SCTP_PAD_CHUNK_ID,               "PAD" },
  { SCTP_FORWARD_TSN_CHUNK_ID,       "FORWARD_TSN" },
  { SCTP_ASCONF_CHUNK_ID,            "ASCONF" },
  { SCTP_I_FORWARD_TSN_CHUNK_ID,     "I_FORWARD_TSN" },
  { SCTP_IETF_EXT,                   "IETF_EXTENSION" },
  { 0,                               NULL }
};

#define SCTP_MAX_HEADER  256
enum
{
    EN_SCTP_HEADER,
    EN_SCTP_CHUNK_TYPE,
    EN_SCTP_PAYLOAD,
    EN_SCTP_PAYLOAD_LEN,
    EN_SCTP_STREAM_ID,
    EN_SCTP_PROTO_ID,
    EN_SCTP_MAX
};

dpi_field_table  sctp_field_array[] = {
    DPI_FIELD_D(EN_SCTP_HEADER,         EM_F_TYPE_STRING,    "header"),
    DPI_FIELD_D(EN_SCTP_CHUNK_TYPE,     EM_F_TYPE_UINT32,    "chunk_type"),
    DPI_FIELD_D(EN_SCTP_PAYLOAD,        EM_F_TYPE_STRING,    "payload"),
    DPI_FIELD_D(EN_SCTP_PAYLOAD_LEN,    EM_F_TYPE_UINT32,    "payload_len"),
    DPI_FIELD_D(EN_SCTP_STREAM_ID,      EM_F_TYPE_UINT32,    "stream_id"),
    DPI_FIELD_D(EN_SCTP_PROTO_ID,       EM_F_TYPE_UINT32,    "proto_id"),
};

struct value_type*cb_sctp_header(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    const struct dpi_sctphdr*sctph = (const struct dpi_sctphdr*)pRec->pkt->sctph;
    if(pRec->pkt && sctph)
    {
        p->type = VALUE_TYPE_BYTES;
        p->len  = sizeof(struct dpi_sctphdr);
        p->val  = (void*)(size_t)sctph;
        return p;
    }
    return NULL;
}
struct value_type*cb_sctp_header_len(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    const struct dpi_sctphdr*sctph = (const struct dpi_sctphdr*)pRec->pkt->sctph;
    if(pRec->pkt && sctph)
    {
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)sizeof(struct dpi_sctphdr);
        return p;
    }
    return NULL;
}
struct value_type*cb_sctp_chunck_type(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    const struct dpi_sctphdr*sctph = (const struct dpi_sctphdr*)pRec->pkt->sctph;
    if(pRec->pkt && sctph)
    {
        const unsigned char *chunck = (const unsigned char *)(pRec->pkt->sctph) + sizeof(struct dpi_sctphdr);
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(chunck[0]);
        return p;
    }
    return NULL;
}
struct value_type*cb_sctp_payload(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    const struct dpi_sctphdr*sctph = (const struct dpi_sctphdr*)pRec->pkt->sctph;
    if(pRec->pkt && sctph)
    {
        const unsigned char *chunck = (const unsigned char *)(pRec->pkt->sctph) + sizeof(struct dpi_sctphdr);
        unsigned short len = *(unsigned short*)(chunck+2);
        len = ntohs(len);
        if(0 == *chunck)
        {
            p->type = VALUE_TYPE_BYTES;
            p->len  = len;
            p->val  = (void*)(size_t)(chunck+16);
        }
        return p;
    }
    return NULL;
}
struct value_type*cb_sctp_stream_id(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    const struct dpi_sctphdr*sctph = (const struct dpi_sctphdr*)pRec->pkt->sctph;
    if(pRec->pkt && sctph)
    {
        const unsigned char *chunck = (const unsigned char *)(pRec->pkt->sctph) + sizeof(struct dpi_sctphdr);
        unsigned int  id = *(unsigned short*)(chunck+6);
        if(0 == *chunck)
        {
            p->type = VALUE_TYPE_UINT;
            p->len  = 0;
            p->val  = (void*)(size_t)(id);
        }
        return p;
    }
    return NULL;
}
struct value_type*cb_sctp_proto_id(ProtoRecord *pRec)
{
    struct value_type *p = &pRec->val_temp;
    const struct dpi_sctphdr*sctph = (const struct dpi_sctphdr*)pRec->pkt->sctph;
    if(pRec->pkt && sctph)
    {
        const unsigned char *chunck = (const unsigned char *)(pRec->pkt->sctph) + sizeof(struct dpi_sctphdr);
        unsigned int  id = *(unsigned short*)(chunck+8);
        if(0 == *chunck)
        {
            p->type = VALUE_TYPE_UINT;
            p->len  = 0;
            p->val  = (void*)(size_t)(id);
        }
        return p;
    }
    return NULL;
}
void write_sctp_log(struct flow_info *flow, const struct sctp_info *info)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    char header[SCTP_MAX_HEADER] = {0};
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, info->dir, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "sctp");

    for(i=0; i < EN_SCTP_MAX; i++){
        switch(sctp_field_array[i].index){
        case EN_SCTP_HEADER:
            write_hex_to_string(header, SCTP_MAX_HEADER, (const uint8_t *)info->sctph, sizeof(struct dpi_sctphdr));
            write_string_reconds(log_ptr->record,  &idx, TBL_LOG_MAX_LEN,  header);
            break;
        case EN_SCTP_CHUNK_TYPE:
            write_one_num_reconds(log_ptr->record,  &idx, TBL_LOG_MAX_LEN,  info->chunk_type);
            break;
        case EN_SCTP_PAYLOAD:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  info->payload, info->payload_len);
            break;
        case EN_SCTP_PAYLOAD_LEN:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  info->payload_len);
            break;
        case EN_SCTP_STREAM_ID:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  info->s_id);
            break;
        case EN_SCTP_PROTO_ID:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  info->proto_id);
            break;
        default:
             write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  1);
             break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_SCTP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
}

void dpi_dissect_sctp(struct flow_info *flow, int src_to_dst_direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(g_config.protocol_switch[PROTOCOL_SCTP] == 0 || payload_len < sizeof(struct dpi_sctphdr))
        return;

    uint8_t  type, tag;
    uint16_t s_id = 0;
    uint16_t offset = 0;
    uint16_t s_seq_num = 0;
    uint16_t len, total_length;
    uint32_t t_seq_num  = 0;
    uint32_t p_protocol = 0;

    const struct  dpi_sctphdr *sctph = (const struct dpi_sctphdr *)payload;
    const uint8_t *chunck_data = payload + sizeof(const struct dpi_sctphdr);
    uint16_t chunck_total_len  = payload_len - sizeof(struct dpi_sctphdr);

    char chunk_type_ptr[1024] = {0};
    uint32_t pos = 0;

    while (offset + 4 <= chunck_total_len) {
        type = chunck_data[offset];
        if(pos != 0)
            pos += snprintf(chunk_type_ptr + pos, sizeof(chunk_type_ptr)-pos-1, ",%u(%s)", type, val_to_string(type, chunk_type_values));
        else
            pos += snprintf(chunk_type_ptr + pos, sizeof(chunk_type_ptr)-pos-1, "%u(%s)", type, val_to_string(type, chunk_type_values));

        tag = get_uint8_t(chunck_data, offset + 1);
        len = get_uint16_ntohs(chunck_data, offset + 2);
        total_length = ADD_PADDING(len);
        if (total_length < SCTP_CHUNCK_HEADER_LEN || offset + total_length > chunck_total_len) {
            break;
        }

        if (type){
            s_id = 0;
        }
        else{
            t_seq_num  = get_uint32_ntohl(chunck_data, offset + 4);
            s_id       = get_uint16_ntohs(chunck_data, offset + 8);
            s_seq_num  = get_uint16_ntohs(chunck_data, offset + 10);
            p_protocol = get_uint32_ntohl(chunck_data, offset + 12);
            len -= 16;
        }

        if(type){
            offset += total_length;
            continue;
        }

        if(p_protocol == 0){
            if (flow->real_protocol_id == PROTOCOL_UNKNOWN && len > 0
                && flow->src2dst_packets + flow->dst2src_packets <= g_config.sctp_identify_pkt_num)
            {
                check_dpi_flow_func(flow, chunck_data + offset + SCTP_CHUNCK_HEADER_LEN, len);
            }
            if ((flow->real_protocol_id > PROTOCOL_UNKNOWN) && (flow->real_protocol_id < PROTOCOL_MAX) && len > 0) {
                if (sctp_detection_array[flow->real_protocol_id].dissect_func) {
                    append_proto_info(flow);
                    sctp_detection_array[flow->real_protocol_id].dissect_func(flow, src_to_dst_direction, t_seq_num, chunck_data + offset + SCTP_CHUNCK_HEADER_LEN, len, DISSECT_PKT_ORIGINAL);
                    g_proto_layer_cnt = 0;
                }
            }
        }
        offset += total_length;
    }

    struct sctp_info info;
    memset(&info, 0, sizeof(info));

    info.dir                = src_to_dst_direction;
    info.sctph              = sctph;
    info.chunk_type         = type;
    info.payload            = chunck_data;
    info.payload_len        = chunck_total_len,
    info.s_id               = s_id;
    info.proto_id           = p_protocol;

    //直接创建sctp的tbl
    write_sctp_log(flow, &info);
}

static void init_sctp_dissector(void){
    sctp_field_array[EN_SCTP_HEADER].callback       = cb_sctp_header;
    sctp_field_array[EN_SCTP_CHUNK_TYPE].callback   = cb_sctp_chunck_type;
    sctp_field_array[EN_SCTP_PAYLOAD].callback      = cb_sctp_payload;
    sctp_field_array[EN_SCTP_PAYLOAD_LEN].callback  = cb_sctp_header_len;
    sctp_field_array[EN_SCTP_STREAM_ID].callback    = cb_sctp_stream_id;
    sctp_field_array[EN_SCTP_PROTO_ID].callback     = cb_sctp_proto_id;

    dpi_register_proto_schema(sctp_field_array, EN_SCTP_MAX, "sctp");
    map_fields_info_register(sctp_field_array, PROTOCOL_SCTP, EN_SCTP_MAX, "sctp");
    pschema_t *schema = dpi_pschema_get_proto("sctp");
    pschema_register_field(schema, "trans_seq", YA_FT_STRING, "desc");
    pschema_register_field(schema, "stream_seq", YA_FT_STRING, "desc");
    pschema_register_field(schema, "cookie", YA_FT_STRING, "desc");
    pschema_register_field(schema, "num_of_outbound", YA_FT_STRING, "desc");
    pschema_register_field(schema, "num_of_inbound", YA_FT_STRING, "desc");
    pschema_register_field(schema, "flag", YA_FT_STRING, "desc");

}

static __attribute((constructor)) void before_init_sctp(void){
    register_tbl_array(TBL_LOG_SCTP, 0, "sctp", init_sctp_dissector);
}

/****************************************************************************************
 * 文 件 名 : dpi_vrrp.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *修改: hongll  2022/07/14
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <stdint.h>
#include <rte_mbuf.h>

#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_common.h"
#include "dpi_proto_ids.h"
#include "dpi_dissector.h"


extern struct rte_mempool *tbl_log_mempool;

#define VRRP_PROTO			(112)

#define VRRP_VERSION_MASK  0xf0
#define VRRP_TYPE_MASK     0x0f
#define VRRP_AUTH_DATA_LEN    8

#define VRRP_TYPE_ADVERTISEMENT 1
static const char* vrrp_type_vals(uint8_t type) {
    switch (type) {
        case VRRP_TYPE_ADVERTISEMENT:   return "Advertisement";
        default:                        return "Unknown";
    }
};

#define VRRP_AUTH_TYPE_NONE        0
#define VRRP_AUTH_TYPE_SIMPLE_TEXT 1
#define VRRP_AUTH_TYPE_IP_AUTH_HDR 2
#define VRRP_AUTH_TYPE_IP_MD5      254
static const char* vrrp_auth_vals(uint8_t type) {
    switch (type) {
        case VRRP_AUTH_TYPE_NONE:           return "No Authentication";
        case VRRP_AUTH_TYPE_SIMPLE_TEXT:    return "Simple Text Authentication [RFC 2338] / Reserved [RFC 3768]";
        case VRRP_AUTH_TYPE_IP_AUTH_HDR:    return "IP Authentication Header [RFC 2338] / Reserved [RFC 3768]";
        case VRRP_AUTH_TYPE_IP_MD5:         return "Cisco VRRP MD5 authentication";
        default:                            return "Unknown";
    }
};

#define VRRP_PRIORITY_MASTER_STOPPING 0
/* Values between 1 and 254 inclusive are for backup VRRP routers */
#define VRRP_PRIORITY_DEFAULT 100
#define VRRP_PRIORITY_OWNER   255
static const char* vrrp_prio_vals(uint8_t type) {
    switch (type) {
        case VRRP_PRIORITY_MASTER_STOPPING: return "Current Master has stopped participating in VRRP";
        case VRRP_PRIORITY_DEFAULT:         return "Default priority for a backup VRRP router";
        case VRRP_PRIORITY_OWNER:           return "This VRRP router owns the virtual router's IP address(es)";
        default:                            return "Unknown";
    }
};

typedef enum _vrrp_index_enum {
    EM_VRRP_VERSION,
    EM_VRRP_TYPE,
    EM_VRRP_VIRT_RTR_ID,
    EM_VRRP_PRIORITY,
    EM_VRRP_ADDR_COUNT,
    EM_VRRP_AUTH_TYPE,
    EM_VRRP_ADVER_INT,
    EM_VRRP_RESERVED_MBZ,
    EM_VRRP_SHORT_ADVER_INT,
    EM_VRRP_CHECKSUM,
    EM_VRRP_IP1,
    EM_VRRP_IP2,
    EM_VRRP_IP3,
    EM_VRRP_IP4,
    EM_VRRP_IP5,
    EM_VRRP_IP6,
    EM_VRRP_IP7,
    EM_VRRP_IP8,
    EM_VRRP_IP9,
    EM_VRRP_IP10,
    EM_VRRP_AUTH_STRING,
    EM_VRRP_MD5_AUTH_DATA,
    EM_VRRP_MAX
} vrrp_index_enum;

static dpi_field_table vrrp_field_array[] = {

	DPI_FIELD_D(EM_VRRP_VERSION,                    EM_F_TYPE_NULL,             "version"),
	DPI_FIELD_D(EM_VRRP_TYPE,                       EM_F_TYPE_NULL,             "type"),
	DPI_FIELD_D(EM_VRRP_VIRT_RTR_ID,                EM_F_TYPE_NULL,             "virt_rtr_id"),
	DPI_FIELD_D(EM_VRRP_PRIORITY,                   EM_F_TYPE_NULL,             "priority"),
	DPI_FIELD_D(EM_VRRP_ADDR_COUNT,                 EM_F_TYPE_NULL,             "addr_count"),
	DPI_FIELD_D(EM_VRRP_AUTH_TYPE,                  EM_F_TYPE_NULL,             "auth_type"),
	DPI_FIELD_D(EM_VRRP_ADVER_INT,                  EM_F_TYPE_NULL,             "adver_int"),
	DPI_FIELD_D(EM_VRRP_RESERVED_MBZ,               EM_F_TYPE_NULL,             "reserved_mbz"),
	DPI_FIELD_D(EM_VRRP_SHORT_ADVER_INT,            EM_F_TYPE_NULL,             "short_adver_int"),
	DPI_FIELD_D(EM_VRRP_CHECKSUM,                   EM_F_TYPE_NULL,             "checksum"),
	DPI_FIELD_D(EM_VRRP_IP1,                        EM_F_TYPE_NULL,             "ip1"),
	DPI_FIELD_D(EM_VRRP_IP2,                        EM_F_TYPE_NULL,             "ip2"),
	DPI_FIELD_D(EM_VRRP_IP3,                        EM_F_TYPE_NULL,             "ip3"),
	DPI_FIELD_D(EM_VRRP_IP4,                        EM_F_TYPE_NULL,             "ip4"),
	DPI_FIELD_D(EM_VRRP_IP5,                        EM_F_TYPE_NULL,             "ip5"),
	DPI_FIELD_D(EM_VRRP_IP6,                        EM_F_TYPE_NULL,             "ip6"),
	DPI_FIELD_D(EM_VRRP_IP7,                        EM_F_TYPE_NULL,             "ip7"),
	DPI_FIELD_D(EM_VRRP_IP8,                        EM_F_TYPE_NULL,             "ip8"),
	DPI_FIELD_D(EM_VRRP_IP9,                        EM_F_TYPE_NULL,             "ip9"),
	DPI_FIELD_D(EM_VRRP_IP10,                       EM_F_TYPE_NULL,             "ip10"),
	DPI_FIELD_D(EM_VRRP_AUTH_STRING,                EM_F_TYPE_NULL,             "auth_string"),
	DPI_FIELD_D(EM_VRRP_MD5_AUTH_DATA,              EM_F_TYPE_NULL,             "md5_auth_data"),
};

struct vrrp_info {
    uint8_t         version;
    uint8_t         type;
    uint8_t         virt_rtr_id;
    uint8_t         priority;
    uint8_t         addr_count;   // 最多显示10个 ip
    uint8_t         show_count;   //  实际显 示的数量
    uint8_t         auth_type;
    uint8_t         adver_int;
    uint8_t         reserved_mbz;
    uint8_t         short_adver_int;
    uint32_t        checksum;
    uint8_t         is_ip6;
    uint8_t         ip6[10][16];
    uint8_t         ip4[10][4];
    char            auth_str[VRRP_AUTH_DATA_LEN+1];
    char            md5_auth_data[16];
};






static int vrrp_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct vrrp_info* info, int *idx, int i)
{

    int local_idx=*idx;
    switch(i){
    case EM_VRRP_VERSION:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->version);
        break;
    case EM_VRRP_TYPE:
    {
        char str[2048];
        memset(str, 0, sizeof(str));
        snprintf(str, sizeof(str), "%d (%s)", info->type, vrrp_type_vals(info->type));
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, strlen(str));
        break;
    }
    case EM_VRRP_VIRT_RTR_ID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->virt_rtr_id);
        break;
    case EM_VRRP_PRIORITY:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->priority);
        break;
    case EM_VRRP_ADDR_COUNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->addr_count);
        break;
    case EM_VRRP_AUTH_TYPE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->auth_type);
        break;
    case EM_VRRP_ADVER_INT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->adver_int);
        break;
    case EM_VRRP_RESERVED_MBZ:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->reserved_mbz);
        break;
    case EM_VRRP_SHORT_ADVER_INT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->short_adver_int);
        break;
    case EM_VRRP_CHECKSUM:
        write_one_hexnum_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (uint32_t)info->checksum);
        break;
    case EM_VRRP_IP1:
    case EM_VRRP_IP2:
    case EM_VRRP_IP3:
    case EM_VRRP_IP4:
    case EM_VRRP_IP5:
    case EM_VRRP_IP6:
    case EM_VRRP_IP7:
    case EM_VRRP_IP8:
    case EM_VRRP_IP9:
    case EM_VRRP_IP10:
    {
        if(info->show_count+EM_VRRP_IP1-i>0){
            char str[2048];
            memset(str, 0, sizeof(str));
            if (info->is_ip6) {
                snprintf(str, sizeof(str), "%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x"
                                        , info->ip6[i][0], info->ip6[i][1], info->ip6[i][2], info->ip6[i][3]
                                        , info->ip6[i][4], info->ip6[i][5], info->ip6[i][6], info->ip6[i][7]
                                        , info->ip6[i][8], info->ip6[i][9], info->ip6[i][10], info->ip6[i][11]
                                        , info->ip6[i][12], info->ip6[i][13], info->ip6[i][14], info->ip6[i][15]);
            }
            else {
                snprintf(str, sizeof(str), "%d.%d.%d.%d"
                                        , info->ip4[i][0], info->ip4[i][1], info->ip4[i][2], info->ip4[i][3]);
            }
        
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, strlen(str));
        }else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        
    }
        break;
    case EM_VRRP_AUTH_STRING:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->auth_str, strlen(info->auth_str));
        break;
    case EM_VRRP_MD5_AUTH_DATA:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->md5_auth_data, strlen(info->md5_auth_data));
        break;

    default:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = vrrp_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif

    return 0;
}


static int write_vrrp_log(struct flow_info* flow, int direction, struct vrrp_info* info) 
{

    int i;
    int idx = 0;
    struct tbl_log* log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "vrrp");

    for(i=0;i<EM_VRRP_MAX;i++){
        vrrp_field_element(log_ptr,flow, direction, info, &idx, i);
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_VRRP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }

    return 0;
}




//static 
int dpi_dissect_vrrp(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint8_t flag)
{
    struct vrrp_info  info;
    int offset = 0;
    int vrrp_len;
    uint8_t ver_type;
    uint32_t phdr[2];
    uint8_t is_ipv6;
    int i, j;

    info.show_count = 0;

    uint8_t priority, addr_count = 0, auth_type = VRRP_AUTH_TYPE_NONE;
    uint16_t computed_cksum = 0;

    is_ipv6 = flow->ip_version == 6;

    info.is_ip6 = is_ipv6;

    ver_type = payload[offset];
    info.version = (ver_type & 0xf0) >> 4;
    info.type = ver_type & 0x0f;
    offset += 1;

	info.virt_rtr_id = payload[offset];
    offset += 1;

    priority = payload[offset];
    info.priority = priority;
    offset += 1;

    addr_count = payload[offset];
    info.addr_count = addr_count;
    offset += 1;

    switch (info.version) {
        case 3:
            info.reserved_mbz = payload[offset];
            offset += 1;
            info.short_adver_int = payload[offset];
            offset += 1;
            break;
        case 2:
        default:
            info.auth_type = payload[offset];
            offset += 1;
            info.adver_int = payload[offset];
            offset += 1;
            break;
    }
    info.checksum = get_uint16_ntohs(payload, offset);
    offset += 2;

    for (i = 0; i < addr_count; i++) {
        if (is_ipv6) {
            if (i < 10) {
                info.show_count++;
                for (j = 0; j < 16; j++){
                    info.ip6[i][j] = payload[offset];
                    offset += 1;
                }
            }
            else {
                offset += 16;
            }
        }
        else {
            if (i < 10) {
                info.show_count++;
                for (j = 0; j < 4; j++){
                    info.ip4[i][j] = payload[offset];
                    offset += 1;
                }
            }
            else {
                offset += 4;
            }
        }
    }

	memset(info.auth_str, 0, sizeof(info.auth_str));
	memset(info.md5_auth_data, 0, sizeof(info.md5_auth_data));

    if (VRRP_AUTH_TYPE_SIMPLE_TEXT == info.auth_type) {
        strncat(info.auth_str, (const char *)(payload + offset), VRRP_AUTH_DATA_LEN);
        offset += VRRP_AUTH_DATA_LEN;
    }
	else if (VRRP_AUTH_TYPE_IP_MD5 == info.auth_type) {
		if (payload_len - offset >= 16) {
			strncat(info.md5_auth_data, (const char *)(payload + offset), 16);
			offset += 16;
		}
	}

	write_vrrp_log(flow, direction, &info);

	return 0;
}

static void
identify_vrrp(struct flow_info* flow, const uint8_t* payload, const uint16_t payload_len)
{
	if (g_config.protocol_switch[PROTOCOL_VRRP] == 0) return;

	uint16_t s_port = 0, d_port = 0;

	s_port = ntohs(flow->tuple.inner.port_src);
	d_port = ntohs(flow->tuple.inner.port_dst);

/*	if (d_port == VRRP_PROTO || s_port == VRRP_PROTO) {
		flow->real_protocol_id = PROTOCOL_VRRP;
	}
*/
	return;
}

static void init_vrrp_dissector(void)
{
    dpi_register_proto_schema(vrrp_field_array, EM_VRRP_MAX, "vrrp");
    map_fields_info_register(vrrp_field_array, PROTOCOL_VRRP, EM_VRRP_MAX, "vrrp");
	return;
}

static __attribute((constructor)) void before_init_vrrp(void) {
	register_tbl_array(TBL_LOG_VRRP, 0, "vrrp", init_vrrp_dissector);
}

#ifndef _DPI_UTILS_H_
#define _DPI_UTILS_H_

#include <stdint.h>
#include <stddef.h>

#include "dpi_typedefs.h"
#include <glib.h>

#define ARRAY_LEN(array) ((sizeof (array)) / (sizeof (array)[0]))

const char *dpi_utils_show_bytes(uint64_t bytes, char out[], int len);

const char *dpi_utils_show_pps(uint64_t pps, char out[], int len);

char *dpi_utils_lstrip(char *str, int max_len, const char *chars);

char *dpi_utils_rstrip(char *str, int max_len, const char *chars);

/**
 * 同时执行 dpi_utils_lstrip 和 dpi_utils_rstrip
*/
char *dpi_utils_strip(char *str, int max_len, const char *chars);

char * dpi_strstr(const char * haystack, int len_haystack,
                   const char * needle, int len_needle);
char * dpi_strstr_kmp(const char * haystack, const char * neddle);
int dpi_str_join(char *str1, const char * str2, const char * spearator);

/** IPv4, TCP, UDP 基于 checksum 的流完整性校验 **/
uint16_t IPv4Checksum(const uint16_t *pkt, uint16_t hlen, uint16_t init);

uint16_t TCPv4Checksum(const struct dpi_iphdr   *iph4, const struct dpi_tcphdr *tcph, uint16_t tlen, uint16_t init);
uint16_t TCPv6Checksum(const struct dpi_ipv6hdr *iph6, const struct dpi_tcphdr *tcph, uint16_t tlen, uint16_t init);

uint16_t UDPv4Checksum(const struct dpi_iphdr   *iph4, const struct dpi_udphdr *udph, uint16_t tlen, uint16_t init);
uint16_t UDPv6Checksum(const struct dpi_ipv6hdr *iph6, const struct dpi_udphdr *udph, uint16_t tlen, uint16_t init);
char* dpi_utils_get_ipv4_addr_of_if(const char *interface_name,char* ip_addr);
char* dpi_utils_get_tbl_out_dir(const char *mount_path,char* operation);
gpointer white_filter(const char* servername, uint16_t namelen, GHashTable *whitelist_table);
char * dpi_strnstr_kmp(const char * haystack, int haystack_len, const char * needle);
typedef void (*dir_callback)(const char * file_path);
void dpi_utils_traverse_dir(const char * dir, dir_callback cb);
#endif /* _DPI_UTILS_H_*/

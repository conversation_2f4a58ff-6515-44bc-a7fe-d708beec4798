#include "dpi_metrics.h"

#include <stdio.h>

#include <glib.h>

#include "dpi_log.h"
#include "dpi_flow.h"

DpiMetrics g_metrics;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];

void dpi_metrics_init()
{
  g_metrics.tick = 5;
  rte_atomic64_init(&g_metrics_output.total);
  rte_atomic64_init(&g_metrics_output.enqueue_failed);
  rte_atomic64_init(&g_metrics_output.enqueue_total);
  // rte_atomic64_init(&g_metrics_output.);
  rte_atomic64_init(&g_metrics.record.total);
  rte_atomic64_init(&g_metrics.record.del_total);

}

void dpi_metrics_output_statics(int period)
{
    if (period <= 0) return;
    uint64_t total = rte_atomic64_read(&g_metrics_output.total);

    g_metrics_output.qps = (total - g_metrics_output.last_total) / period;
    g_metrics_output.last_total = total;

    uint64_t e_total = rte_atomic64_read(&g_metrics_output.enqueue_total);
    g_metrics_output.enqueue_qps = (e_total - g_metrics_output.last_enqueue_total) / period;
    g_metrics_output.last_enqueue_total = e_total;
}


void dpi_metrics_output_statics_print()
{
  DpiMetricsOutput *stat = &g_metrics_output;
  uint64_t          total          = rte_atomic64_read(&stat->total);
  uint64_t          enqueue_failed = rte_atomic64_read(&stat->enqueue_failed);
  uint64_t          e_total        = rte_atomic64_read(&stat->enqueue_total);
  printf("dpi enqueue total %lu, enqueue QPS %u, enqueue failed %lu, output total %lu, QPS %u\n", e_total,
         stat->enqueue_qps, enqueue_failed, total, stat->qps);
}

void dpi_metrics_flow()
{
  DpiMetricsFlow *m_flow = NULL;
  uint32_t all_curr_cnt = 0;
  uint32_t curr_cnt;
  uint64_t all_cnt = 0;
  uint64_t all_del_cnt = 0;
  uint64_t all_qps = 0;
  uint64_t all_del_qps = 0;
  for (int i = 0; i < DPI_METRICS_ELEM_MAX; ++i) {
    m_flow = &g_metrics.flow[i];

    if (m_flow->total == 0) continue;
    // if (rte_atomic64_read(&g_metrics.flow[i].total) == 0) continue;

    // uint64_t total = rte_atomic64_read(&g_metrics.flow[i].total);
    uint64_t total = m_flow->total;
    all_cnt += total;
    g_metrics.flow[i].qps = (total - g_metrics.flow[i].last_total) / g_metrics.tick;
    all_qps += g_metrics.flow[i].qps;
    g_metrics.flow[i].last_total = total;

    // uint64_t del = rte_atomic64_read(&g_metrics.flow[i].del);
    uint64_t del = m_flow->del;
    all_del_cnt += del;
    g_metrics.flow[i].del_qps = (del - g_metrics.flow[i].last_del) / g_metrics.tick;
    all_del_qps += g_metrics.flow[i].del_qps;
    g_metrics.flow[i].last_del = del;

    m_flow->curr = dpi_flow_hash_size(flow_thread_info[i].hash);
    all_curr_cnt += m_flow->curr;

    log_info("flow %d total %lu, curr %u, gen qps %lu, del total %lu, del qps %lu",
               i, m_flow->total, m_flow->curr, m_flow->qps, m_flow->del, m_flow->del_qps);
  }

  log_info("flow all %lu, curr all %lu, del all %lu, qps all %lu, del qps all %lu", all_cnt, all_curr_cnt, all_del_cnt, all_qps, all_del_qps);
}

void dpi_metrics_mbuf()
{
  DpiMetricsMbuf *m_mbuf = NULL;
  uint64_t all_mbuf = 0;
  uint64_t all_qps = 0;
  for (int i = 0; i < DPI_METRICS_ELEM_MAX; ++i) {
    m_mbuf = &g_metrics.mbuf[i];
    if (m_mbuf->total == 0) continue;

    m_mbuf->qps = (m_mbuf->total - m_mbuf->last_total) / g_metrics.tick;
    all_qps += m_mbuf->qps;
    all_mbuf += m_mbuf->total;
    m_mbuf->last_total = m_mbuf->total;

    log_info("mbuf id %d, total %lu, qps %lu", i, m_mbuf->total, m_mbuf->qps);
  }

  log_info("mbuf all %lu, qps all %lu", all_mbuf, all_qps);
}
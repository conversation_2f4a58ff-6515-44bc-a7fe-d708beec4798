/****************************************************************************************
 * 文 件 名 : dpi_rdp.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/11/22
编码: wangy                2018/11/22
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_RDP_H_
#define _DPI_RDP_H_

#include "dpi_tpkt.h"

typedef enum _rdp_enum_index {
    //EM_RDP_HEADERTYPE,
    //EM_RDP_HEADERLENGTH,
    EM_RDP_VERSIONMAJOR,
    EM_RDP_VERSIONMINOR,
    EM_RDP_DESKTOPWIDTH,
    EM_RDP_DESKTOPHEIGHT,
    EM_RDP_COLORDEPTH,
    EM_RDP_SASSEQUENCE,
    EM_RDP_KEYBOARDLAYOUT,
    EM_RDP_CLIENTBUILD,
    EM_RDP_CLIENTNAME,
    EM_RDP_KEYBOARDTYPE,
    EM_RDP_KEYBOARDSUBTYPE,
    EM_RDP_KEYBOARDFUNCTIONKEY,
    EM_RDP_IMEFILENAME,
    EM_RDP_ENCRYPTIONMETHODS,
    EM_RDP_EXTENCRYPTIONMETHODS,
    EM_RDP_CLUSTER_FLAGS,
    EM_RDP_REDIRECTEDSESSIONID,
    EM_RDP_MSGCHANNELFLAGS,
    EM_RDP_MONITORCOUNT,
    EM_RDP_MONITOREXFLAGS,
    EM_RDP_MONITORATTRIBUTESIZE,
    EM_RDP_MULTITRANSPORTFLAGS,
    EM_RDP_ENCRYPTIONLEVEL,
    EM_RDP_SERVERRANDOMLEN,
    EM_RDP_SERVERCERTLEN,
    EM_RDP_SERVERCERTIFICATE,
    EM_RDP_MCSCHANNELID,
    EM_RDP_CHANNELCOUNT,
    EM_RDP_PAD,
    EM_RDP_MSGCHANNELID,

    EM_RDP_MAX
}rdp_enum_index;

// rdp connect request
int dissect_rdp_cr(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len, rdp_info* info, int only_check, int *rdp_packet_type);

// rdp connect confirm
int dissect_rdp_cc(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len, rdp_info* info, int only_check, int *rdp_packet_type);

#endif // !_DPI_RDP_H_
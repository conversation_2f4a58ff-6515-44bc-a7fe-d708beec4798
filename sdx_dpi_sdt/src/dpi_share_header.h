#ifndef DPI_SHARE_HEADER_H
#define DPI_SHARE_HEADER_H

#include "dpi_memory.h"
#include "dpi_tbl_log.h"

#include <yaProtoRecord/precord.h>

typedef enum _dpi_field_common_em{
#ifndef DPI_SDT_P327
    EM_COMMON_TASKINFO,
#endif
    EM_COMMON_TAGTYPE,
    EM_COMMON_OPERATOR_TYPE,
    EM_COMMON_HW_NCODE,
    EM_COMMON_HW_ACCOUNT,
    EM_COMMON_HW_ESN,
    EM_COMMON_HW_MEID,
    EM_COMMON_HW_LAC,
    EM_COMMON_HW_SAC,
    EM_COMMON_HW_RAC,
    EM_COMMON_HW_CALL_ID,
    EM_COMMON_HW_ECGI,
    EM_COMMON_HW_BSID,
    EM_COMMON_HW_GRE_KEY,
    EM_COMMON_HW_TAI,
    EM_COMMON_HW_EGI_MNC,
    EM_COMMON_HW_APN,
    EM_COMMON_RTL_TEID,
    EM_COMMON_RTL_OUTTER_SRC,
    EM_COMMON_RTL_OUTTER_DST,
    EM_COMMON_RTL_MSISDN,
    EM_COMMON_RTL_IMEI,
    EM_COMMON_RTL_IMSI,
    EM_COMMON_RTL_TAC,
    EM_COMMON_RTL_PLMN_ID,
    EM_COMMON_RTL_ULI,
    EM_COMMON_RTL_ENODE_ID,
    EM_COMMON_RTL_CELL_ID,
    EM_COMMON_RTL_BS,
    EM_COMMON_DEVNO,
    EM_COMMON_LINENO,
    EM_COMMON_LINKLAYERTYPE,
    EM_COMMON_ISIPV6,
    EM_COMMON_ISMPLS,
    EM_COMMON_NLABEL,
    EM_COMMON_INNERLABEL,
    EM_COMMON_OTHERLABEL,
    EM_COMMON_RESV1,
    EM_COMMON_RESV2,
    EM_COMMON_RESV3,
    EM_COMMON_RESV4,
    EM_COMMON_RESV5,
    EM_COMMON_RESV6,
    EM_COMMON_RESV7,
    EM_COMMON_RESV8,
    EM_COMMON_CAPDATE,
    EM_COMMON_SRCIP,
    EM_COMMON_SRCCOUNTRY,
    EM_COMMON_SRCAREA,
    EM_COMMON_SRCCITY,
    EM_COMMON_SRCCARRIER,
    EM_COMMON_DSTIP,
    EM_COMMON_DSTCOUNTRY,
    EM_COMMON_DSTAREA,
    EM_COMMON_DSTCITY,
    EM_COMMON_DSTCARRIER,
    EM_COMMON_SRCPORT,
    EM_COMMON_DSTPORT,
    EM_COMMON_C2S,
    EM_COMMON_PROTO,
    EM_COMMON_TTL,
    EM_COMMON_MAX
}dpi_field_common_em;

enum _share_header_index_e{
   
    ENUM_SHARE_HEADER_LINENO1,
    ENUM_SHARE_HEADER_LINENO2,
    ENUM_SHARE_HEADER_LINENO3,
    ENUM_SHARE_HEADER_LINENO4,
    ENUM_SHARE_HEADER_LINENAME1,
    ENUM_SHARE_HEADER_LINENAME2,
    ENUM_SHARE_HEADER_SYSFROM,
    ENUM_SHARE_HEADER_DATAFROM,
    ENUM_SHARE_HEADER_SIGTYPE,
    ENUM_SHARE_HEADER_INFOTYPE, 
    ENUM_SHARE_HEADER_BEGTIME,
    ENUM_SHARE_HEADER_ENDTIME,
    ENUM_SHARE_HEADER_COMDUR,
    ENUM_SHARE_HEADER_MEANID,
    ENUM_SHARE_HEADER_SITEID,
    ENUM_SHARE_HEADER_UNITID,
    ENUM_SHARE_HEADER_TASKID,
    ENUM_SHARE_HEADER_GUID,
    ENUM_SHARE_HEADER_STORTIME,
    ENUM_SHARE_HEADER_MDSECDEG,
    ENUM_SHARE_HEADER_FILESECDEG,
    ENUM_SHARE_HEADER_SECDEGPRO,
    ENUM_SHARE_HEADER_IPVER,
    ENUM_SHARE_HEADER_TTL,
    ENUM_SHARE_HEADER_SRCADDR,
    ENUM_SHARE_HEADER_DSTADDR,
    ENUM_SHARE_HEADER_SRCPORT,
    ENUM_SHARE_HEADER_DSTPORT,
    ENUM_SHARE_HEADER_PROTNUM,
    ENUM_SHARE_HEADER_SRCADDRV6,
    ENUM_SHARE_HEADER_DSTADDRV6,
    ENUM_SHARE_HEADER_PROTINFO,
    ENUM_SHARE_HEADER_PROTTYPE,
    ENUM_SHARE_HEADER_PROTNAME,
    ENUM_SHARE_HEADER_MULROUFLAG,
    ENUM_SHARE_HEADER_INTFLAG,
    ENUM_SHARE_HEADER_STRDIREC,
    ENUM_SHARE_HEADER_PKTNUM,
    ENUM_SHARE_HEADER_PAYLEN,
    ENUM_SHARE_HEADER_STREAMID,
    ENUM_SHARE_HEADER_ETAGS,
    ENUM_SHARE_HEADER_TTAGS,
    ENUM_SHARE_HEADER_ATAGS,
    ENUM_SHARE_HEADER_UTAGS,
    ENUM_SHARE_HEADER_LABLE1,
    ENUM_SHARE_HEADER_LABLE2,
    ENUM_SHARE_HEADER_LABLE3,
    ENUM_SHARE_HEADER_LABLE4,
    ENUM_SHARE_HEADER_VLANID1,
    ENUM_SHARE_HEADER_VLANID2,
    ENUM_SHARE_HEADER_SRCMAC,
    ENUM_SHARE_HEADER_DSTMAC,
    ENUM_SHARE_HEADER_TUNNELID,

    ENUM_SHARE_HEADER_SRCCOUNTRY,
    ENUM_SHARE_HEADER_SRCSTATE,
    ENUM_SHARE_HEADER_SRCCITY,
    ENUM_SHARE_HEADER_SRCLONGITUDE,
    ENUM_SHARE_HEADER_SRCLATITUDE,
    ENUM_SHARE_HEADER_SRCISP,
    ENUM_SHARE_HEADER_SRCASN,

    ENUM_SHARE_HEADER_DSTCOUNTRY,
    ENUM_SHARE_HEADER_DSTSTATE,
    ENUM_SHARE_HEADER_DSTCITY,
    ENUM_SHARE_HEADER_DSTLONGITUDE,
    ENUM_SHARE_HEADER_DSTLATITUDE,
    ENUM_SHARE_HEADER_DSTISP,
    ENUM_SHARE_HEADER_DSTASN,

    ENUM_SHARE_HEADER_OUTADDRTYPE,
    ENUM_SHARE_HEADER_OUTSRCADDR,
    ENUM_SHARE_HEADER_OUTDSTADDR,
    ENUM_SHARE_HEADER_OUTER_IPV6_SRC,
    ENUM_SHARE_HEADER_OUTER_IPV6_DST,
    ENUM_SHARE_HEADER_OUTSRCPORT,
    ENUM_SHARE_HEADER_OUTDSTPORT,
    ENUM_SHARE_HEADER_OUTTRANSPROTO,
    ENUM_SHARE_HEADER_CAPTURETIME,
    ENUM_SHARE_HEADER_SRCMACOUI,
    ENUM_SHARE_HEADER_DSTMACOUI,

#ifdef DPI_SDT_ZDY
    ENUM_SHARE_HEADER_MAX = EM_COMMON_MAX,
#else
    ENUM_SHARE_HEADER_MAX
#endif
};

int dpi_pschema_get_share_header_field(dpi_field_table *field_table_array[]);
void init_share_header_map_fields(void);

#endif



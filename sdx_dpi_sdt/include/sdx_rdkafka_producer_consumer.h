
#ifndef _SDX_RDKAFKA_PRODUCER_CONSUMER_H_
#define _SDX_RDKAFKA_PRODUCER_CONSUMER_H_

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <librdkafka/rdkafka.h>

typedef struct
{
    rd_kafka_t *rk;
    char *topic;
} __attribute__((packed))YV_kafka_handle_t;
/*数据包管理头-32字节*/
typedef struct
{
    /*
     * * 同步标志
     * * 0：0XFE
     * * 1：0XEF
     * * 2：0X9F
     * * 3：0XF9
     * */
    unsigned int sync_flag;
    #define _DPMHEAD_SYN_FLAG_          0xf99feffe
    /*
     * * 版本号
     * * 高4位为主版本号，低4位为副版本号，默认3.0
     * */
    unsigned char version;
    /*
     * * 报文是否结束标志
     * * 0：完整报文包
     * * 1：大报文拆包时的开始包
     * * 2：大报文拆包时的中间包
     * * 3：大报文拆包时的结束包
     * */
    unsigned char end_flag;
    /*
     * * 包长度
     * * 本包的总长度（byte），
     * * 包括数据包管理头和数据区长度
     * *
     * */
    unsigned int pkt_len;
    
    /*
     * * 包序号
     * * 发送方顺序编码的流水号，0X0000-0XFFFF
     * * 循环编码，由收发双方约定
     * */
    unsigned short pkt_serial;
    /*
     * * 数据包类型
     * */
    unsigned short pkt_type;
    /*
     * * 设备标识
     * * 包含单位编码、设备类型编码、设备序号等
     * */
    unsigned char device_id[14];
    /*
     * * 保留
     * */
    unsigned int keep;
} __attribute__((packed))DPMHeader;

//负载头
typedef struct _Payload_Header
{
    uint16_t       idx;//序号
    uint8_t        helu;
    uint16_t       payload_len;//下层负载的长度
    uint8_t        stat;//状态
    uint8_t        send_type;//发送的类型
    uint8_t        xml_format;//数据格式
    uint8_t        res[4];//保留
} __attribute__((packed))PAYLOAD_HEADER;


/**
 * @brief create  kafka producer handle
 * 
 * brokers:        kafka服务器地址(ip:port)
 * topic:          kafka主题分类
 *
 * 返回值;  成功返回句柄  失败返回NULL
 */
YV_kafka_handle_t *kafka_create_producer_handle(const char *brokers, const char *topic);

/**
 * @brief send message to kafka
 * 
 * producer_handle: 发送消息到kafka句柄        
 * dpm_header:      载荷头
 * payload_header:  负载头
 * payload:         原始数据
 *
 * 返回值;  0成功 -1失败
 */
int kafka_producer_send_message(YV_kafka_handle_t *producer_handle, DPMHeader *dpm_header, PAYLOAD_HEADER *payload_header, const uint8_t *payload, uint32_t payload_len);


void kafka_free_producer_handle(YV_kafka_handle_t *producer_handle);

/**
 * @brief create  kafka consumer handle
 * 
 * brokers:        kafka服务器地址(ip:port)
 * topic:          kafka主题分类
 * groupid:        消费者组id(每个消费组之间的消费位移互不影响)
 *
 * 返回值;  成功返回句柄  失败返回NULL
 */
YV_kafka_handle_t *kafka_create_consumer_handle(const char *brokers, const char *topic, const char *groupid);

/**
 * @brief get message from kafka
 * 
 * consumer_handle: 从kafka获取消息句柄        
 * message:         获取消息指针(消息格式：DPMHeader+PAYLOAD_HEADER+payload),使用后需调用free释放
 * message_len:     获取的消息长度
 *
 * 返回值;  0成功 -1失败
 */
int kafka_consumer_get_message(YV_kafka_handle_t *consumer_handle, uint8_t **message, uint32_t *message_len);

void kafka_free_consumer_handle(YV_kafka_handle_t *consumer_handle);

#endif


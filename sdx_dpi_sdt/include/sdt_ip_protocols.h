#ifndef _SDT_IP_PROTOCOLS_H_
#define _SDT_IP_PROTOCOLS_H_

/* # $Id: protocols,v 1.11 2011/05/03 14:45:40 ovasik Exp $ */
/* # */
/* # Internet (IP) protocols */
/* # */
/* #    from: @(#)protocols 5.1 (Berkeley) 4/17/89 */
/* # */
/* # Updated for NetBSD based on RFC 1340, Assigned Numbers (July 1992). */
/* # Last IANA update included dated 2011-05-03 */
/* # */
/* # See also http://www.iana.org/assignments/protocol-numbers */

#define IP_PROTO_IP               0   /*  IP               # internet protocol, pseudo protocol number */
#define IP_PROTO_HOPOPT           0   /*  HOPOPT           # hop_by_hop options for ipv6 */
#define IP_PROTO_ICMP             1   /*  ICMP             # internet control message protocol */
#define IP_PROTO_IGMP             2   /*  IGMP             # internet group management protocol */
#define IP_PROTO_GGP              3   /*  GGP              # gateway_gateway protocol */
#define IP_PROTO_IPV4             4   /*  IPv4             # IPv4 encapsulation */
#define IP_PROTO_ST               5   /*  ST               # ST datagram mode */
#define IP_PROTO_TCP              6   /*  TCP              # transmission control protocol */
#define IP_PROTO_CBT              7   /*  CBT              # CBT, <PERSON>ie <<EMAIL>> */
#define IP_PROTO_EGP              8   /*  EGP              # exterior gateway protocol */
#define IP_PROTO_IGP              9   /*  IGP              # any private interior gateway (Cisco: for IGRP) */
#define IP_PROTO_BBN_RCC          10  /*  BBN_RCC_MON      # BBN RCC Monitoring */
#define IP_PROTO_NVP              11  /*  NVP_II           # Network Voice Protocol */
#define IP_PROTO_PUP              12  /*  PUP              # PARC universal packet protocol */
#define IP_PROTO_ARGUS            13  /*  ARGUS            # ARGUS */
#define IP_PROTO_EMCON            14  /*  EMCON            # EMCON */
#define IP_PROTO_XNET             15  /*  XNET             # Cross Net Debugger */
#define IP_PROTO_CHAOS            16  /*  CHAOS            # Chaos */
#define IP_PROTO_UDP              17  /*  UDP              # user datagram protocol */
#define IP_PROTO_MUX              18  /*  MUX              # Multiplexing protocol */
#define IP_PROTO_DCN              19  /*  DCN_MEAS         # DCN Measurement Subsystems */
#define IP_PROTO_HMP              20  /*  HMP              # host monitoring protocol */
#define IP_PROTO_PRM              21  /*  PRM              # packet radio measurement protocol */
#define IP_PROTO_XNS_IDP          22  /*  XNS_IDP          # Xerox NS IDP */
#define IP_PROTO_TRUNK_1          23  /*  TRUNK_1          # Trunk_1 */
#define IP_PROTO_TRUNK_2          24  /*  TRUNK_2          # Trunk_2 */
#define IP_PROTO_LEAF_1           25  /*  LEAF_1           # Leaf_1 */
#define IP_PROTO_LEAF_2           26  /*  LEAF_2           # Leaf_2 */
#define IP_PROTO_RDP              27  /*  RDP              # "reliable datagram" protocol */
#define IP_PROTO_IRTP             28  /*  IRTP             # Internet Reliable Transaction Protocol */
#define IP_PROTO_ISO_TP4          29  /*  ISO_TP4          # ISO Transport Protocol Class 4 */
#define IP_PROTO_NETBLT           30  /*  NETBLT           # Bulk Data Transfer Protocol */
#define IP_PROTO_MFE_NSP          31  /*  MFE_NSP          # MFE Network Services Protocol */
#define IP_PROTO_MERIT_INP        32  /*  MERIT_INP        # MERIT Internodal Protocol */
#define IP_PROTO_DCCP             33  /*  DCCP             # Datagram Congestion Control Protocol */
#define IP_PROTO_3PC              34  /*  3PC              # Third Party Connect Protocol */
#define IP_PROTO_IDPR             35  /*  IDPR             # Inter_Domain Policy Routing Protocol */
#define IP_PROTO_XTP              36  /*  XTP              # Xpress Tranfer Protocol */
#define IP_PROTO_DDP              37  /*  DDP              # Datagram Delivery Protocol */
#define IP_PROTO_IDPR_CMTP        38  /*  IDPR_CMTP        # IDPR Control Message Transport Proto */
#define IP_PROTO_TP_PLUS_PLUS     39  /*  TP++             # TP++ Transport Protocol */
#define IP_PROTO_IL               40  /*  IL               # IL Transport Protocol */
#define IP_PROTO_IPV6             41  /*  IPv6             # IPv6 encapsulation */
#define IP_PROTO_SDRP             42  /*  SDRP             # Source Demand Routing Protocol */
#define IP_PROTO_IPV6_ROUTE       43  /*  IPv6_Route       # Routing Header for IPv6 */
#define IP_PROTO_IPV6_FRAG        44  /*  IPv6_Frag        # Fragment Header for IPv6 */
#define IP_PROTO_IDRP             45  /*  IDRP             # Inter_Domain Routing Protocol */
#define IP_PROTO_RSVP             46  /*  RSVP             # Resource ReSerVation Protocol */
#define IP_PROTO_GRE              47  /*  GRE              # Generic Routing Encapsulation */
#define IP_PROTO_DSR              48  /*  DSR              # Dynamic Source Routing Protocol */
#define IP_PROTO_BNA              49  /*  BNA              # BNA */
#define IP_PROTO_ESP              50  /*  ESP              # Encap Security Payload */
#define IP_PROTO_IPV6_CRYPT       50  /*  IPv6_Crypt       # Encryption Header for IPv6 (not in official list) */
#define IP_PROTO_AH               51  /*  AH               # Authentication Header */
#define IP_PROTO_IPV6_AUTH        51  /*  IPv6_Auth        # Authentication Header for IPv6 (not in official list) */
#define IP_PROTO_I_NLSP           52  /*  I_NLSP           # Integrated Net Layer Security TUBA */
#define IP_PROTO_SWIPE            53  /*  SWIPE            # IP with Encryption */
#define IP_PROTO_NARP             54  /*  NARP             # NBMA Address Resolution Protocol */
#define IP_PROTO_MOBILE           55  /*  MOBILE           # IP Mobility */
#define IP_PROTO_TLSP             56  /*  TLSP             # Transport Layer Security Protocol */
#define IP_PROTO_SKIP             57  /*  SKIP             # SKIP */
#define IP_PROTO_IPV6_ICMP        58  /*  IPv6_ICMP        # ICMP for IPv6 */
#define IP_PROTO_IPV6_NONXT       59  /*  IPv6_NoNxt       # No Next Header for IPv6 */
#define IP_PROTO_IPV6_OPTS        60  /*  IPv6_Opts        # Destination Options for IPv6 */
#define IP_PROTO_NONE_61          61  /*                   # any host internal protocol */
#define IP_PROTO_CFTP             62  /*  CFTP             # CFTP */
#define IP_PROTO_NONE_63          63  /*                   # any local network */
#define IP_PROTO_SAT_EXPAK        64  /*  SAT_EXPAK        # SATNET and Backroom EXPAK */
#define IP_PROTO_KRYPTOLAN        65  /*  KRYPTOLAN        # Kryptolan */
#define IP_PROTO_RVD              66  /*  RVD              # MIT Remote Virtual Disk Protocol */
#define IP_PROTO_IPPC             67  /*  IPPC             # Internet Pluribus Packet Core */
#define IP_PROTO_NONE_68          68  /*                   # any distributed file system */
#define IP_PROTO_SAT_MON          69  /*  SAT_MON          # SATNET Monitoring */
#define IP_PROTO_VISA             70  /*  VISA             # VISA Protocol */
#define IP_PROTO_IPCV             71  /*  IPCV             # Internet Packet Core Utility */
#define IP_PROTO_CPNX             72  /*  CPNX             # Computer Protocol Network Executive */
#define IP_PROTO_CPHB             73  /*  CPHB             # Computer Protocol Heart Beat */
#define IP_PROTO_WSN              74  /*  WSN              # Wang Span Network */
#define IP_PROTO_PVP              75  /*  PVP              # Packet Video Protocol */
#define IP_PROTO_BR_SAT_MON       76  /*  BR_SAT_MON       # Backroom SATNET Monitoring */
#define IP_PROTO_SUN_ND           77  /*  SUN_ND           # SUN ND PROTOCOL_Temporary */
#define IP_PROTO_WB_MON           78  /*  WB_MON           # WIDEBAND Monitoring */
#define IP_PROTO_WB_EXPAK         79  /*  WB_EXPAK         # WIDEBAND EXPAK */
#define IP_PROTO_ISO_IP           80  /*  ISO_IP           # ISO Internet Protocol */
#define IP_PROTO_VMTP             81  /*  VMTP             # Versatile Message Transport */
#define IP_PROTO_SECURE_VMTP      82  /*  SECURE_VMTP      # SECURE_VMTP */
#define IP_PROTO_VINES            83  /*  VINES            # VINES */
#define IP_PROTO_TTP              84  /*  TTP              # TTP */
#define IP_PROTO_NSFNET_IGP       85  /*  NSFNET_IGP       # NSFNET_IGP */
#define IP_PROTO_DGP              86  /*  DGP              # Dissimilar Gateway Protocol */
#define IP_PROTO_TCF              87  /*  TCF              # TCF */
#define IP_PROTO_EIGRP            88  /*  EIGRP            # Enhanced Interior Routing Protocol (Cisco) */
#define IP_PROTO_OSPF             89  /*  OSPFIGP          # Open Shortest Path First IGP */
#define IP_PROTO_SPRITE_RPC       90  /*  Sprite_RPC       # Sprite RPC Protocol */
#define IP_PROTO_LARP             91  /*  LARP             # Locus Address Resolution Protocol */
#define IP_PROTO_MTP              92  /*  MTP              # Multicast Transport Protocol */
#define IP_PROTO_AX_25            93  /*  AX.25            # AX.25 Frames */
#define IP_PROTO_IPIP             94  /*  IPIP             # Yet Another IP encapsulation */
#define IP_PROTO_MICP             95  /*  MICP             # Mobile Internetworking Control Pro. */
#define IP_PROTO_SCC_SP           96  /*  SCC_SP           # Semaphore Communications Sec. Pro. */
#define IP_PROTO_ETHERIP          97  /*  ETHERIP          # Ethernet_within_IP Encapsulation */
#define IP_PROTO_ENCAP            98  /*  ENCAP            # Yet Another IP encapsulation */
#define IP_PROTO_NONE_99          99  /*                   # any private encryption scheme */
#define IP_PROTO_GMTP             100 /*  GMTP             # GMTP */
#define IP_PROTO_IFMP             101 /*  IFMP             # Ipsilon Flow Management Protocol */
#define IP_PROTO_PNNI             102 /*  PNNI             # PNNI over IP */
#define IP_PROTO_PIM              103 /*  PIM              # Protocol Independent Multicast */
#define IP_PROTO_ARIS             104 /*  ARIS             # ARIS */
#define IP_PROTO_SCPS             105 /*  SCPS             # SCPS */
#define IP_PROTO_QNX              106 /*  QNX              # QNX */
#define IP_PROTO_A_N              107 /*  A/N              # Active Networks */
#define IP_PROTO_IPCOMP           108 /*  IPComp           # IP Payload Compression Protocol */
#define IP_PROTO_SNP              109 /*  SNP              # Sitara Networks Protocol */
#define IP_PROTO_COMPAQ_PEER      110 /*  Compaq_Peer      # Compaq Peer Protocol */
#define IP_PROTO_IPX_IN_IP        111 /*  IPX_in_IP        # IPX in IP */
#define IP_PROTO_VRRP             112 /*  VRRP             # Virtual Router Redundancy Protocol */
#define IP_PROTO_PGM              113 /*  PGM              # PGM Reliable Transport Protocol */
#define IP_PROTO_NONE_114         114 /*                   # any 0_hop protocol */
#define IP_PROTO_L2TP             115 /*  L2TP             # Layer Two Tunneling Protocol */
#define IP_PROTO_DDX              116 /*  DDX              # D_II Data Exchange */
#define IP_PROTO_IATP             117 /*  IATP             # Interactive Agent Transfer Protocol */
#define IP_PROTO_STP              118 /*  STP              # Schedule Transfer */
#define IP_PROTO_SRP              119 /*  SRP              # SpectraLink Radio Protocol */
#define IP_PROTO_UTI              120 /*  UTI              # UTI */
#define IP_PROTO_SMP              121 /*  SMP              # Simple Message Protocol */
#define IP_PROTO_SM               122 /*  SM               # SM */
#define IP_PROTO_PTP              123 /*  PTP              # Performance Transparency Protocol */
#define IP_PROTO_ISIS             124 /*  ISIS             # ISIS over IPv4 */
#define IP_PROTO_FIRE             125 /*  FIRE */
#define IP_PROTO_CRTP             126 /*  CRTP             # Combat Radio Transport Protocol */
#define IP_PROTO_CRDUP            127 /*  CRUDP            # Combat Radio User Datagram */
#define IP_PROTO_SSCOPMCE         128 /*  SSCOPMCE */
#define IP_PROTO_IPLT             129 /*  IPLT */
#define IP_PROTO_SPS              130 /*  SPS              # Secure Packet Shield */
#define IP_PROTO_PIPE             131 /*  PIPE             # Private IP Encapsulation within IP */
#define IP_PROTO_SCTP             132 /*  SCTP             # Stream Control Transmission Protocol */
#define IP_PROTO_FC               133 /*  FC               # Fibre Channel */
#define IP_PROTO_RSVP_E2E_IGNORE  134 /*  RSVP_E2E_IGNORE */
#define IP_PROTO_MOBILITY_HEADER  135 /*  Mobility_Header  # Mobility Header */
#define IP_PROTO_UDPLITE          136 /*  UDPLite */
#define IP_PROTO_MPLS_IN_IP       137 /*  MPLS_in_IP */
#define IP_PROTO_MANET            138 /*  manet            # MANET Protocols */
#define IP_PROTO_HIP              139 /*  HIP              # Host Identity Protocol */
#define IP_PROTO_SHIM6            140 /*  Shim6            # Shim6 Protocol */
#define IP_PROTO_WESP             141 /*  WESP             # Wrapped Encapsulating Security Payload */
#define IP_PROTO_ROHC             142 /*  ROHC             # Robust Header Compression */
/* #                              143-252 Unassigned                                    [IANA] */
/* #                              253     Use for experimentation and testing           [RFC3692] */
/* #                              254     Use for experimentation and testing           [RFC3692] */
/* #                              255     Reserved                                      [IANA] */

#endif /* _SDT_IP_PROTOCOLS_H_ */

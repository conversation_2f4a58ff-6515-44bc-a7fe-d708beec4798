#ifndef _SDT_TYPES_H_
#define _SDT_TYPES_H_

#include <stdint.h>
#include <arpa/inet.h>

#include <yaFtypes/ftypes.h>

/*
 * 基本类型定义
 */
typedef unsigned char byte;
typedef unsigned char u8;
typedef signed   char i8;

typedef signed   short i16;
typedef unsigned short u16;

typedef signed   int i32;
typedef unsigned int u32;

typedef int64_t      i64;
typedef uint64_t     u64;

typedef double f64;
typedef float f32;

typedef struct
{
    uint8_t     protoID;
    uint8_t     ipversion;          /* ipv4: AF_INET, ipv6: AF_INET6 */
    union       {int ipv4;char ipv6[16];}IpSrc;
    union       {int ipv4;char ipv6[16];}IpDst;
    uint16_t    PortSrc;
    uint16_t    PortDst;
}ip_tuples;

//SDT 参数传输到 linsdt引擎
enum EN_SDT_SYSCONFIG
{
    SDT_SURVIVE_BYTES  = 0,         //规则存活时长: 流量触发
    SDT_SURVIVE_ALERT,              //规则告警时长: 告警时长
    SDT_SURVIVE_DYNAMIC,            //动态规则老化: 有效时长
    SDT_TBL_OUT_DIR,                //tbl落盘目录
    SDT_PAUSE_SCAN_PCAP_FLAG,       //scan模式暂停扫描新pcap标识
};

/* 协议字段类型枚举 */
typedef enum FieldType_enum
{
    YV_FT_NONE = 0,
    YV_FT_BOOLEAN       = YA_FT_BOOLEAN,
    YV_FT_CHAR          = YA_FT_CHAR,
    YV_FT_UINT8         = YA_FT_UINT8,
    YV_FT_UINT16        = YA_FT_UINT16,
    YV_FT_UINT32        = YA_FT_UINT32,
    YV_FT_UINT56        = YA_FT_UINT56,
    YV_FT_UINT64        = YA_FT_UINT64,
    YV_FT_INT8          = YA_FT_INT8,
    YV_FT_INT16         = YA_FT_INT16,
    YV_FT_INT32         = YA_FT_INT32,
    YV_FT_INT56         = YA_FT_INT56,
    YV_FT_INT64         = YA_FT_INT64,
    YV_FT_TIME          = YA_FT_ABSOLUTE_TIME,
    YV_FT_IPV4          = YA_FT_IPv4,
    YV_FT_IPV6          = YA_FT_IPv6,
    YV_FT_INLINE_IPV6   = YA_FT_RESERVE_01,
    YV_FT_STRING        = YA_FT_STRING,                  /* in FieldValue_t value is v_pString */
    YV_FT_INLINE_STRING = YA_FT_RESERVE_02,           /* in FieldValue_t value is v_inline_string */
    YV_FT_BYTES         = YA_FT_BYTES,                   /* in FieldValue_t value is v_pString */
    YV_FT_INLINE_BYTES  = YA_FT_RESERVE_03,            /* in FieldValue_t value is v_inline_bytes */
    YV_FT_ETHER         = YA_FT_ETHER,
    YV_FT_NIL           = YA_FT_RESERVE_04,
    YV_FT_STR           = YA_FT_RESERVE_05,
    YV_FT_MAC           = YA_FT_RESERVE_06
} FieldType_t;

/* 字段值类型定义 */
/* 可存放变长数据string, bytes 类型 */
typedef struct FieldValue
{
    FieldType_t     fType;
    u64             fLen;

    union
    {
        u8       v_u8;
        i8       v_i8;
        u16      v_u16;
        i16      v_i16;
        u32      v_u32;
        i32      v_i32;
        u64      v_u64;
        i64      v_i64;
        f64      v_float64;
        time_t   v_time;
        u32      v_ipv4;
        u32      v_ipv6;
        u8       v_inline_ipv6[0];
        char    *v_pString;                 /* ftype is FT_STRING */
        char     v_inline_string[0];        /* ftype is FT_INLINE_STRING */
        byte    *v_pBytes;                  /* ftype is FT_BYTES */
        byte     v_inline_bytes[0];         /* ftype is FT_INLINE_BYTES */
    } u;
} FieldValue_t;

/* 协议字段头 */
typedef struct FieldHdrDef
{
    int                 protoLayer;     /* 协议层次，SdtProtoLayer_enum */
    int                 protoID;        /* 协议ID */
    int                 index;          /* 字段ID */
    FieldType_t         type;
    char                field_name[64];
} FieldHdrDef;

typedef struct ProtoDef
{
    int protoId;                    /* 协议编号 */
    int protoLayer;                 /* 协议层次，SdtProtoLayer_enum */
    char protoName[20];             /* 协议名 */
    int fieldCnt;                   /* 协议字段数 */
} ProtoDef;

/* payloadBuff 用于 tcp, udp, stream 等 */
typedef struct PayloadBuff
{
    int  len;
    u8  *pBuff;
} PayloadBuff;

/* 规则状态枚举值 */
typedef enum SdtRuleStatus_enum
{
    SRS_load_error      = 0,
    SRS_compile_succeed = 1,
    SRS_load_succeed    = 2,
} SdtRuleStatus_enum;

/* 规则状态，含有分配到的 ruleId 等 */
typedef struct SdtRuleStatus
{
    SdtRuleStatus_enum      status;             /* 规则状态 */
    int                     ruleId;             /* 命中的规则号, 未命中时为 -1 */
    unsigned int            ruleHashCode;       /* 命中的规则 hash code, 未命中时为 -1  */
} SdtRuleStatus_t;

/* sdt 接口调用的报错信息 */
typedef struct SdtErrorMsg
{
    int  lErrorCode;
    char pszErrorBuff[1024];
} SdtErrorMsg_t;

/* packet 匹配模式，决定执行规则匹配的时机
 * 每条规则仅其中一个 bit 位置 1，
 * 作为 sdtapp 检测 命中 acl 规则的 body ，
 * 由于可能多条规则的 acl 部分相同，sdt body 要求的匹配模式不同，匹配时可能出现多个 bit 置 1
 */
typedef enum SdtPacketMatchMode_enum
{
    SPMM_match_per_packet  = 1,
    SPMM_match_per_session = 1 << 1,
} SdtPacketMatchMode_enum;


/* 规则匹配模式，用于描述命中某 acl 规则后，后续 sdt 规则可能的匹配模式概括 */
typedef enum SdtRuleMatchMode_enum
{
    SRMM_sdt_match_only_one_rule = 1,
    SRMM_sdt_match_multi_rules   = 2,
} SdtRuleMatchMode_enum;

/* 协议层次，用于描述命中某 acl 规则后，后续 sdt 规则用到的协议字段的协议层次 */
typedef enum SdtProtoLayer_enum
{
    SPL_layer_none        = 0,
    SPL_layer_data_link   = 1 << 2,
    SPL_layer_network     = 1 << 3,
    SPL_layer_transport   = 1 << 4,
    SPL_layer_application = 1 << 5,
} SdtProtoLayer_enum;

/* 规则动作，用于描述命中某 acl 规则后，后续 sdt 规则可能的 action 行为概括 */
typedef enum SdtRuleAction_enum
{
    SRA_action_none         = 0,
    SRA_action_event        = 1,
    SRA_action_dump_packet  = 1 << 2,
    SRA_action_dump_session = 1 << 3,
} SdtRuleAction_enum;

/* 规则匹配提示，用于优化(加速) acl 匹配命中后的执行流程 */
typedef enum SdtMatchHint_enum
{
    SMH_hint_none               = 0,
    SMH_hint_rule_has_body      = 1 << 0,
    SMH_hint_rule_has_no_body   = 1 << 1,
} SdtMatchHint_enum;

#define SDT_ACL_MAX_CATEGORIES 16

/*
 * acl 规则命中信息，用于 sdtEngine_matchAclRules 匹配时的结果返回
 */
typedef struct SdtAclMatchedRuleInfo
{
    unsigned int            aclHashCode[SDT_ACL_MAX_CATEGORIES];
    unsigned int            aclHashCnt;

    //记下Rule 的全部 proto_ID. 应用场景: 如果没有IP/TCP/UDP的规则, 那就可以 避免构造REC 节省时间
    uint64_t                bitmap_proto_id[4];

    /*
     * 规则概括信息 - 匹配模式
     * 命中了该 acl 规则的后续 sdt 规则整体匹配模式概览，某些可能需要做 "单包匹配", 某些可能需要做 "会话匹配"
     * 有效值： 单包匹配:       SPMM_match_per_packet
     *          会话匹配:       SPMM_match_per_session
     *          单包、会话匹配: (SPMM_match_per_packet | SPMM_match_per_session)
     */
    SdtPacketMatchMode_enum         sumInfo_matchModeFlag;

    /*
     * 规则概括信息 - 规则用到的字段的协议层次
     * 命中了该 acl 规则的后续 sdt 规则整体用到的字段协议层次概览，某些可能用到了传输层字段, 某些可能用到了应用层字段
     * 有效值： 传输层:         SPL_layer_transport
     *          应用层:         SPL_layer_application
     *          传输层、应用层: (SPMM_match_per_packet | SPL_layer_application)
     */
    SdtProtoLayer_enum              sumInfo_protoLayerFlag;

    /*
     * 规则概括信息 - 规则可能执行的动作
     * 命中了该 acl 规则的后续 sdt 规则整体将执行的动作的概括，某些可能进行 event, 某些可能进行 packet dump
     * 有效值：  event:         SRA_action_event
     *     dump packet:         SRA_action_dump_packet
     *    dump session:         SRA_action_dump_session
     *            混合: (SRA_action_event | SRA_action_dump_packet)
     *        其它混合: ......
     */
    SdtRuleAction_enum              sumInfo_actionFlag;

    /*
     * 规则概括信息 - 匹配提示
     * 提示信息用来优化后续匹配流程，如"acl 规则关联的所有 sdt 部分均没有 body 体等"
     * 有效值：none   :      SMH_hint_none
     *  rule hash body:      SMH_hint_rule_has_body
     */
    SdtMatchHint_enum               sumInfo_matchHintFlag;
} SdtAclMatchedRuleInfo;

//规则中的 匹配标记[ipff/link]
typedef enum SdtRuleMatchMode_Enum
{
    MATCH_MODE_PKT,
    MATCH_MODE_FLOW
} SdtRuleMatchMode_Enum;

/* SDT 规则 action */
typedef enum SdtAction_Enum
{
    SAE_none            = 0,               /* record 未命中任何规则 */
    SAE_drop            = 1 << 0,
    SAE_event           = 1 << 1,
    SAE_report          = 1 << 2,
    SAE_packetDump      = 1 << 3,
    SAE_alert           = 1 << 4,       // 丑陋的 特殊标记调整; 待调整
} SdtAction_Enum;


typedef enum SdtEventOption_Enum
{
    SEO_default = 0,
    SEO_emax_M,                 /* M 参数存放在 SdtMatchResult i32_arg1 */
    SEO_emax_M_N,               /* M 参数存放在 SdtMatchResult i32_arg1, N 存放在 i32_arg2 中 */
    SEO_elimit_M,               /* M 参数存放在 SdtMatchResult i32_arg1 */
    SEO_elimit_M_N,             /* M 参数存放在 SdtMatchResult i32_arg1, N 存放在 i32_arg2 中 */
    SEO_efreq,                  /* 参数存放在 f_arg  */
} SdtEventOption_Enum;

typedef enum SdtPktDumpOption_Enum
{
    SPO_default = 0,
    SPO_out_pkt,
    SPO_out_pkt_N,
    SPO_out_this,
    SPO_out_front,
} SdtPktDumpOption_Enum;

typedef struct SDTRuleAction
{
    uint32_t                action;
    int                     ruleId;             /* 命中的规则号 */
    unsigned int            ruleHashCode;       /* 命中的规则 hash code  */
    int                     dynamic_mask;
    int                     lineNumber;
    int                     match_mode;       // 模式
    int                     matchHintFlag;      /* 匹配提示，为 SdtMatchHint_enum 枚举的组合; 提示信息用来优化后续匹配流程，如"规则没有 body 体等 */
    SdtEventOption_Enum     eventOpt;
    SdtPktDumpOption_Enum   dumpOpt;

    //记下Rule 的全部 proto_ID. 应用场景: 如果没有IP/TCP/UDP的规则, 那就可以 避免构造REC 节省时间
    uint64_t                bitmap_proto_id[4];
    union{
        struct{
            int M;
            int N;
        } int_args;
        double freq;
    } event_args;

    struct{
        int minute;
        int size;
        int count;
    } packetDump_args;

    uint32_t uuid;
    uint32_t score;
    char *msg;
    char *classtype;
    char *reference;

    char* out_string[4];
    void* spare_hook0;  //外部钩子, 这种丑陋的语法, 受LEX/YACC限制, 暂时先这样.
    char is_ipff;       // ipff  | link ? 用于标记规则上的记录模式

    size_t   hitOnCnt ; // 命中的次数   累计
    size_t   hitOnByte; // 命中的字节数 累计
    size_t   hitOnTimeStamp;//首次命中时刻

    char  unitID[64];   //来自XML
    char  taskID[64];   //来自XML
    char  groupName[64];//来自XML
    char  groupID[64];  //来自XML
    char  method[64];   //来自XML
    char  topicName[64];//来自XML

    int  rule_mode;         //来自XML 20241120 牡丹江现场 
    int  task_mode;         //来自XML 20241120 牡丹江现场
    int  task_sub_type;     //来自XML 20241120 牡丹江现场
    int  mode_param_num;    //来自XML 20241120 牡丹江现场
    char mode_param[10][6]; //来自XML 20241120 牡丹江现场
    
} SdtMatchResult;


/* 规则种类数量统计 */
typedef enum SdtRuleStatistics_Enum
{
    RULE_TYPE_ip_p = 0,         //精确IP,precise
    RULE_TYPE_ip_b,             //B网段IP
    RULE_TYPE_ip_c,             //C网段IP
    RULE_TYPE_fivetupe,         //五元组
    RULE_TYPE_linename,         //线路名称
    RULE_TYPE_linklayer,        //链路层要素,mpls,vlan,mac
    RULE_TYPE_payloadlen,       //包长匹配
    RULE_TYPE_ttl,              //ttl匹配
    RULE_TYPE_tcp_flag,         //tcp.flag匹配
    RULE_TYPE_fixed_match,      //固定位置
    RULE_TYPE_float_match,      //浮动位置
    RULE_TYPE_regex_match,      //正则表达式
    RULE_TYPE_metafield_match,  //元数据字段
    RULE_TYPE_link_features,    //流特征link
    RULE_TYPE_effective,        //生效规则数
    RULE_TYPE_last_update,      //规则最后更新时间
    RULE_TYPE_MAX
} SdtRuleStatistics_Enum;

typedef struct SdtRuleStatistics
{
    int stats_array[RULE_TYPE_MAX];
}SdtRuleStatistics;

/*
 * Sdt rule 更新事件处理函数操作接口
 * 当发生规则"clearAndFlush" 类型规则事务操作时，事件按照 SdtRuleEventOps 中
 * 事件定义次序被调用.
 * 事件类型: transact: 一个事务中可以加载多个 task, totalTask 事件一个事务中仅触发一次;
 * 事件类型: perTask:   一个事务中的每个 task 触发一次;
 */
typedef struct SdtRuleEventOps
{
    int (*rule_event_ops_on_transact_execute_start)(int transactId);                     /* transact 事件: 事务执行开始 */
    int (*rule_event_ops_on_rules_cleared)(void);                                            /* perTask 事件：规则被清空; 返回 0 表示事件正常处理 */
    int (*rule_event_ops_on_editing_start)(void);                                            /* perTask 事件：规则编辑开始; 返回 0 表示事件正常处理 */
    int (*rule_event_ops_on_rule_added)(SdtMatchResult *ruleAction);                     /* perTask 事件：添加了一条规则; 返回 0 表示事件正常处理 */
    int (*rule_event_ops_on_rules_sdt_rules_load_done)(void);                                /* perTask 事件：sdt 规则加载完成 */
    int (*rule_event_ops_on_rules_sdt_rules_load_post_process_done)(void);                   /* perTask 事件: sdt 规则加载后处理完成，通常为正则规则编译 */
    int (*rule_event_ops_on_rules_acl_rules_load_done)(void);                                /* perTask 事件: acl 规则加载完成 */
    int (*rule_event_ops_on_editing_finish)(void);                                           /* perTask 事件：规则编辑结束; 返回 0 表示事件正常处理 */
    int (*rule_event_ops_on_transact_execute_finish)(int transactId, SdtErrorMsg_t *pErr); /* transact 事件: 事务执行结束, pErr->lErrorCode < 0 代表事务执行失败 */
} SdtRuleEventOps;

/*
 * 协议字典类型声明，由 sdt-app 定义
 */
//typedef struct ProtoDict ProtoDict;





/*
 * 插件信息
 */
typedef struct SdtPluginInfo
{
    int workingProtoLayer;      /* 取值自 Plugin_Proto_Layer_enum */
    int protoVariableCnt;
    char **pszVariableArray;
    const char *pluginName;
} SdtPluginInfo;

/*
 * 规则命中统计记录
 */
typedef struct rule_web_stat
{
    uint32_t  uid;
    uint32_t  gid;
    uint32_t  rule_id;

    time_t    first_time;
    time_t    last_time;
    uint64_t  ics_hits;
    uint64_t  ics_pkts;
    uint64_t  ics_bytes;
    uint64_t  ics_flows;
}rule_web_stat;



typedef struct{
    char   dir;                   //报文方向，0：请求报文 1：响应报文
    char   RES[3];                //保留字段
    struct iphdr *ip_hdr;         //IP头部指针
    struct tcphdr *tcp_hdr;       //tcp头部指针(非tcp则为NULL)
    char   *payload;              //负载头部指针
    int    payload_len;           //IP包长度
    struct pcap_pkthdr *pcap_hdr; //时间戳、报文原始长度
} ys_packets;

typedef struct {
    //客户端
    int   c_num;
    int   c_pkt_len[12];        //负载长度
    char *c_pkt_data[12];       // 负载内容
    //服务端
    int   s_num;
    int   s_pkt_len[12];
    char *s_pkt_data[12];
} ys_stream;


typedef struct ys_ipseq{
    uint8_t   nums;
    uint32_t  seqs[8];
}ys_ipseq;



/* http server(restful) 接口参数 */
typedef struct SdtHttpServerArgs
{
    uint16_t    listen_port;        // 监听端口
    char        case_name[32];      // 此sdt实例的名称(用于区分多sdt app)
    char        web_addr[32];       // web后台地址: ip:port
    char        app_version[32];    // 程序版本号
    uint8_t     is_reload_rule : 1; // 是否从web后台请求加载历史规则，0：不加载; 1：加载。
    uint8_t     is_reflec_rule : 1; // 是否执行规则反射。
} SdtHttpServerArgs_t;


#endif /* _SDT_TYPES_H_ */

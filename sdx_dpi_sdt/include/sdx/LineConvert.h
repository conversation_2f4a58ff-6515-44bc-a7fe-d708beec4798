#ifdef __cplusplus
extern "C" {
#endif
	/**
     * @brief 初始化-仅初始化一次。
     *
     * @param[in] name     初始化。
     * @retval  0  成功。
     * @retval -1  失败。
     */
	int LineConvertInit();

	/**
     * @brief 转换函数。
     *
     * @param[in] hw     16字节整数（主机字节序）。

     * @param[out] globename   返回转换后的名称。
     *
     * @note  hw为16字节整数值，传入的是主机字节序。
     *
     * @retval true  处理正常。
     * @retval false  处理出现错误。
     */
	bool LineConvertHWToname(const unsigned int *hw, char* globename);
#ifdef __cplusplus
}
#endif

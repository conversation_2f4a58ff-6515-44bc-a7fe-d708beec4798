#ifndef _DPI_VTYSH_MSG_H_
#define _DPI_VTYSH_MSG_H_

#define MSG_MAX_LEN 65535
#define SOCKET_PORT 4567

enum msg_type
{
    MSG_SHOW_THREAD_INFO,    
    MSG_SHOW_FLOW_DETAIL_INFO,
    MSG_SHOW_FLOW_INC_INFO,
    MSG_SHOW_FLOW_TOTAL_INFO,
    MSG_CLEAN_FLOW_TOTAL_INFO,
    MSG_SHOW_FLOW_HASH_INFO,
    MSG_SHOW_MEMPOOL_DETAIL_INFO,
    MSG_SHOW_RING_DETAIL_INFO,
    MSG_SHOW_TRAFFIC_SPEED_INFO,
    MSG_SHOW_CURRENT_TRAFFIC_SPEED,
    MSG_SHOW_DEV_TRAFFIC_SPEED_INFO,

    MSG_SHOW_FLOW_TIMEOUT,
    MSG_SHOW_FLOW_IDENTIFY_PKT_NUM,
    MSG_SHOW_FAIL_INFO,
    
    MSG_SET_TCP_TIMEOUT,
    MSG_SET_UDP_TIMEOUT,
    MSG_SET_SCTP_TIMEOUT,
    MSG_SET_TCP_IDENTIFY_PKT_NUM,
    MSG_SET_UDP_IDENTIFY_PKT_NUM,
    MSG_SET_SCTP_IDENTIFY_PKT_NUM,
    
    MSG_DISABLE_PROTOCOL_IDENTIFY,
    MSG_ENABLE_PROTOCOL_IDENTIFY,
    MSG_SHOW_PROTOCOL_IDENTIFY,

    MSG_DISABLE_CONVERSATION_IDENTIFY,
    MSG_ENABLE_CONVERSATION_IDENTIFY,
    
    MSG_SHOW_LOG_LEVEL,
    MSG_SET_LOG_LEVEL,
    
    MSG_STOP_RCV_PKTS,
    MSG_START_RCV_PKTS,
    
    MSG_SHOW_TBL_LOG_INFO,
    MSG_CLEAN_TBL_LOG_INFO,
    MSG_SHOW_HTTP_POST_COUNT,
    
    MSG_SHOW_ETH_DEV_INFO,
    MSG_SHOW_PORT_RSS_INFO,

    MSG_SHOW_RULES_INFO,
    MSG_SHOW_RULES_MATCH,
    MSG_SHOW_RULES_ID,

    MSG_NORMAL_EXIT,

    MSG_DPI_TEST_MODULE,

    
    MSG_MAX
};

#endif

#!/usr/bin/env python3

import logging
import os
from logging import handlers

__all__ = ['logger']


# log 记录器
logger = logging.getLogger('yview')
logger.setLevel(logging.WARN)

# formatter 格式
formatter = logging.Formatter('%(asctime)s | %(levelname)-7s| %(filename)s:%(lineno)-5s| %(message)s',
                            datefmt="%Y-%m-%d %H:%M:%S")

# 日志级别从左到右逐渐提高， DEBUG INFO WARNING ERROR CRITICAL
# Streamhandler: 标准流处理器，将消息发送到标准输出流、错误流
consoleHandler = logging.StreamHandler()
consoleHandler.setLevel(logging.DEBUG)
consoleHandler.setFormatter(formatter)

logger.addHandler(consoleHandler)

# RotatingFileHandler文件处理器:文件达到指定大小后，启用新文件存储日志
log_dir = os.path.join(os.getcwd(), 'log')
os.makedirs(log_dir, exist_ok=True)
fileHandler = handlers.RotatingFileHandler(
                        filename=os.path.join(log_dir, 'log.txt'),
                        maxBytes=1024*1024*100,
                        backupCount=10)
fileHandler.setLevel(logging.DEBUG)
fileHandler.setFormatter(formatter)

logger.addHandler(fileHandler)

#!/usr/bin/env python3
import os,sys, time
import subprocess,signal
from requests import RequestException
import glob

from sdt_rule_file import SdtTaskSet, get_SdtTaskSet_from_xml
from sdt_http_operator import SdtHttpOperator
from sdt_utils import *
from sdt_exception import *
from sdt_logger import logger

__VERSION__ ="0.0.1"


############## 配置变量区 ##################
dpi_http_port = 8888  #sdt程序本地监听http端口

os.environ['SDT_RULEFILE_MONITOR'] = '0'
############## 配置变量区 END ##############

sdt_pid = 0

class TestProcess:

    def __init__(self, case_name) -> None:
        self.case_name = case_name
        self.dpi_pid = 0
        self.task_set = None

    def __del__(self):
        # print('call "%s" del' % self.case_name)
        #尽可能在退出前杀掉进程, 不能影响下一次测试
        # 若是直接向start.sh进程发送信号, dpi会变成孤儿进程
        if self.dpi_pid > 0:
            os.kill(self.dpi_pid, signal.SIGTERM)

    def run(self, test_dir):
        print("[RUN       ]", "测试用例 ({})".format(self.case_name))

        self.test_dir = test_dir
        self.__prepare_work()
        self.__start_dpi()
        self.__push_rules()
        self.__wait_dpi()

        if self.__check_result():
            print("\033[32m[        OK]", "测试用例 ({})\033[0m".format(self.case_name))
        else:
            print("\033[31m[    FAILED]", "测试用例 ({})\033[0m".format(self.case_name))


    def __prepare_work(self):
        result_log_file = glob.glob(os.path.join(self.test_dir, 'test_*.txt'))
        for path in result_log_file:
            os.remove(path)

    def __start_dpi(self):
        # 以离线模式运行dpi程序
        # 源码仓库和打包发布的程序的start.sh脚本不在同一目录
        if os.path.exists('../run'):
            start_path = "../run/start.sh"
        else:
            start_path = "../start.sh"

        if sys.version_info >= (3, 7):
            popen_kwargs = {'text': True}
        else:
            popen_kwargs = {'encoding': 'utf-8'}

        self.dpi_process = subprocess.Popen([start_path, "--", "--test", "--pcap_dir", self.test_dir],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            bufsize=1,
            **popen_kwargs
        )

        # 等待dpi初始化完成, http服务启动
        for line in self.dpi_process.stdout:
            line = line[:-1]
            if line[0:10] == "INIT_DONE:":
                self.dpi_pid = int(line[10:])
                break
        if self.dpi_pid == 0:
            raise TestDpiRunningError('dpi 运行失败')


    def __wait_dpi(self):
        # 等待结束
        logger.info("等待 dpi 结束...")
        for line in self.dpi_process.stdout:
            # 持续读取, 防止写端阻塞. 程序停止,自动结束循环.
            # ps: 和wait()效果一致了
            # if line.startswith('Processing pcap file'):
            #     # 打印pcap处理进度
            #     print(line, end='')
            continue
        self.dpi_process.wait()
        self.dpi_pid = 0
        logger.info('dpi exit code: %d' % (self.dpi_process.returncode))
        if self.dpi_process.returncode != 0:
            raise TestDpiExitFailed('exit (%d)' % (self.dpi_process.returncode))


    def __push_rules(self):
        # 读取规则文件
        rule_paths = glob.glob(os.path.join(self.test_dir, '*.xml'))
        if not rule_paths:
            return
        logger.info("找到规则文件: %s" % (rule_paths))

        for path in rule_paths:
            self.task_set = get_SdtTaskSet_from_xml(path, self.task_set)

        logger.info("RESTful下发规则...")
        # RESTful 下发任务
        with SdtHttpOperator('127.0.0.1', dpi_http_port) as http_opt:
            http_opt.push_task_rule(self.task_set.format_for_sdx())


    def __check_result(self):
        #程序跑完, 分析结果
        logger.info("结果分析中...")
        result_log_file = glob.glob(os.path.join(self.test_dir, 'test_*.txt'))

        for file in result_log_file:
            with open(file, "r") as fp:
                while True:
                    line = fp.readline().strip()
                    if not line:
                        break
                    unit, task, rule, *_ = line.split('|', 3)
                    self.task_set.mark_hit(unit, task, rule)

        return self.task_set.dispaly_result()


def main():
    """ 可输入的三种形式
    1) 输入单个测试用例(单个目录 test/a)
    2) 输入一个目录,运行目录下所有的测试用例(单个目录 test)
    3) 输入的路径包含通配符(非目录 test/*)
    实测, 在情况3)中, python解释器会自动展开,不需要glob解析
    """
    input_path = [os.path.abspath(arg) for arg in sys.argv[1:] if os.path.isdir(arg)]
    if len(input_path) == 0:
        return

    for case in input_path:
        # 仅有一个测试用例
        if glob.glob(os.path.join(case, '*.xml')):
            case_name = os.path.basename(case)
            TestProcess(case_name).run(case)
            continue

        #同时测试多个测试用例
        for root, dirs, files in os.walk(case):
            for dir in dirs:
                TestProcess(dir).run(os.path.join(root, dir))


if __name__ == '__main__':
    try:
        main()
    except RequestException:
        logger.error('RESTful 连接失败, 请检查http端口配置')
    except TestRuleLoadError:
        logger.error('规则加载失败, 请检查规则语法是否有误')
    except SdtTestException as e:
        logger.error(str(e))
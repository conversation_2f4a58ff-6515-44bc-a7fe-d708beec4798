#!/usr/bin/env python3

__all__ = [
    "SdtRule", "SdtTask", "SdtTaskSet", "get_SdtTaskSet_from_xml"
]

from typing import List, Dict, Tuple
from xml.etree import ElementTree as ET
from xml.dom.minidom import Document, Element
from xml.dom import minidom as dom
import sys, json
from sdt_exception import *

class SdtRule:
    attrib: Dict[str, str]
    ruleid: str
    has_hit: int
    should_hit: bool

    def __init__(self, ruleid, should_hit) -> None:
        self.ruleid = ruleid
        self.has_hit = 0
        self.should_hit = True if should_hit=="1" else False
        self.attrib = {'rule_id': ruleid}

    def __eq__(self, other: "SdtRule") -> bool:
        return self.ruleid == other.ruleid


class SdtTask:
    rules: Dict[str, SdtRule]
    attrib: Dict[str, str]

    def __init__(self, unitid, taskid) -> None:
        self.unitid = unitid
        self.taskid = taskid
        self.rules  = {}
        self.attrib = {'unit': unitid, 'task_id': taskid}

    def __eq__(self, other: "SdtTask") -> bool:
        return self.unitid == other.unitid and  \
               self.taskid == other.taskid

    def insert(self, rule: SdtRule):
        self.rules[rule.ruleid] = rule


class SdtTaskSet:
    tasks: Dict[Tuple[str,str], SdtTask]

    def __init__(self) -> None:
        self.tasks = {}

    def insert(self, task: SdtTask):
        self.tasks[(task.unitid, task.taskid)] = task

    def format_for_sdx(self):
        '''
        格式化为dpi协商的格式
        '''
        d = []
        for task in self.tasks.values():
            for rule in task.rules.values():
                d.append({**task.attrib, **rule.attrib})
        return d

    def mark_hit(self, unitid, taskid, ruleid):
        '''
        标记指定的规则被命中了
        '''
        if (unitid, taskid) not in self.tasks:
            raise TestRuleNotFound(f"{unitid}_{taskid}")
        task = self.tasks[(unitid, taskid)]
        if ruleid not in task.rules:
            raise TestRuleNotFound(f"{unitid}_{taskid}_{taskid}")
        rule = task.rules[ruleid]
        rule.has_hit += 1

    def dispaly_result(self):
        test_ok = True
        for task in self.tasks.values():
            success_num = 0
            failed_num = 0
            for rule in task.rules.values():
                if rule.should_hit and rule.has_hit > 0:
                    success_num +=1
                elif not rule.should_hit and rule.has_hit == 0:
                    success_num +=1
                else:
                    failed_num +=1
                    print("\033[31m[  FAILED  ]\033[0m", "id({}) {}".format(rule.ruleid, rule.attrib['rule_text']))

            if failed_num > 0:
                test_ok = False
                print("\033[31m[  FAILED  ]", "任务: task({}): 通过({}) 失败({})\033[0m"
                        .format(task.taskid, success_num, failed_num))
            else:
                print("\033[32m[    OK    ]", "任务: task({}): 通过({})\033[0m"
                        .format(task.taskid, success_num))
        return test_ok

def get_SdtTaskSet_from_xml(xmlfile, sdt_task_set) -> SdtTaskSet:
    """从XML文件中读取任务集合 """

    xml         = ET.parse(xmlfile)
    taskparams  = xml.getroot()

    if sdt_task_set is None:
        sdt_task_set = SdtTaskSet()

    for task in taskparams:
        sdt_task = SdtTask(task.attrib['unit'], task.attrib['task_id'])
        sdt_task.attrib['method']        = task.attrib['method']
        sdt_task.attrib['topic_name']    = ''
        sdt_task.attrib['task_sub_type'] = task.attrib['task_sub_type']
        sdt_task.attrib['task_mode']     = task.attrib.get('task_mode')
        sdt_task.attrib['mode_param']    = task.attrib.get('mode_param')

        for rule in task:
            sdt_rule = SdtRule(rule.attrib['rule_id'], rule.attrib.get("hit", "1"))
            sdt_rule.attrib['rule_groupid'] = rule.attrib['rule_groupid']
            sdt_rule.attrib['rule_group']   = rule.attrib['rule_group']
            sdt_rule.attrib['rule_mode']    = rule.attrib['rule_mode']
            sdt_rule.attrib['rule_text']    = rule.text
            sdt_task.insert(sdt_rule)
        sdt_task_set.insert(sdt_task)

    del xml
    return sdt_task_set


def main():
    xml_file = sys.argv[1]
    task_set = get_SdtTaskSet_from_xml(xml_file, None)
    for task in task_set.tasks.values():
        print(task.attrib)
        for rule in task.rules.values():
            print('  ', rule.attrib)

if __name__ == "__main__":
    main()
# yaDpiSdt

SDT(软件定义威胁 )

# 使用信号 [暂停/恢复] 收包
kill -SIGUSR2 $(pidof yaDpiSdt)


## 依赖

**yumi**
```shell
yumi install libicu
yumi install gcc-c++ librdkafka-devel -y
yumi update cmake3 -y
yumi install libyaFtypes
yumi install libyaProtoRecord
yumi install libxml2-devel libxml2-static


#Gtest使用最新的 rpm 仓库
eval $(curl -s http://192.168.101.22/exec/get_c7_repo.sh | sh) 
#必须删除旧的 gtest
yumi autoremove gtest gtest-devel
yumi install yaGtest
```


**pkg-config**
 - [tcp_rsm](http://192.168.20.98/dev_dpi/tcp_rsm)
 - [dpdk](http://192.168.20.98/opensrc/dataplane/dpdk)
 - [yaSdxWatch](http://192.168.20.98/sdx/yasdxwatch)
 - [libyv_sub](http://192.168.20.98/dev_dpi/yaconfigweb)
 - [libsdt](http://192.168.20.98/sdx/libsdt)



## 构建

### cmake 构建方法

直接构建
```shell
./cmake_build.sh clean && ./cmake_build.sh
# 默认构建release

# 一键法
rm -rf build && cmake3 -DCMAKE_BUILD_TYPE=RelWithDebInfo -B build &&  make -C build && make -C build/ install
```

支持的 target
 - **clean**
 - **debug**
 - **release**

```shell
./cmake_build.sh debug
./cmake_build.sh release
```

# 327 ZDY
   sdt 和 327 ZDY 共用此项目，通过编译参数控制编译为哪个项目
## 编译
    编译时候添加 -DENABLE_SDT_ZDY=ON 参数
```
    cmake -B build -DENABLE_SDT_ZDY=ON
```
## 其他注意事项
   配置文件需要修改的地方
```shell
  TBL_OUT_DIR = /tmp/tbls
  YV_RECORD_TYPE = 0
```

# 依赖的libmicroxml
```
tar xf mxml-3.3.1.tar.gz
cd mxml-3.3.1/

# --enable-sanitizer 添加了ASAN
# --enable-debug 添加了调试符号
./configure --prefix=/opt/mxml --disable-shared  --enable-debug --enable-sanitizer
make
```

## 查看预编译
```
gcc -E dpi_email.c -I /usr/local/include/dpdk/ -I ../include/ `pkg-config --cflags glib-2.0` -I utils/
```

# 构建支持智能网卡收包模式的版本

## 1.安装额外依赖项
```
yumi install -y yadpdk-19 libsmart_nic
```

## 2.cmake 添加命令参数 ENABLE_SMART_NIC=ON
```
cmake . -B build -DENABLE_SMART_NIC=ON
```

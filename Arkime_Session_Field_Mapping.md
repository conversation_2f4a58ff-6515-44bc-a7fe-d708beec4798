# Arkime会话字段与字段定义对应关系文档

本文档详细说明了`arkime_sessions3-*`索引中的会话数据字段与`arkime_fields_v30`索引中字段定义的对应关系。

## 概述

- **会话索引**: `arkime_sessions3-YYYYMMDD` (如: arkime_sessions3-20021231)
- **字段定义索引**: `arkime_fields_v30`
- **总字段定义数**: 216
- **映射关系**: 会话数据字段 ↔ 字段定义ID ↔ 数据库字段名

## 会话数据结构

### 基础字段

| 会话字段 | 描述 | 对应字段定义ID | 字段定义状态 |
|----------|------|----------------|--------------|
| `@timestamp` | 处理时间戳(毫秒) | `N/A` | 🔧 内置字段 |
| `firstPacket` | 会话第一包时间戳(毫秒) | `N/A` | 🔧 内置字段 |
| `lastPacket` | 会话最后一包时间戳(毫秒) | `N/A` | 🔧 内置字段 |
| `length` | 会话持续时间(毫秒) | `session.length` | ✅ 有定义 |
| `ipProtocol` | IP协议号 (6=TCP, 17=UDP) | `N/A` | 🔧 内置字段 |
| `node` | 捕获节点名称 | `N/A` | 🔧 内置字段 |
| `packetPos` | 数据包在PCAP文件中的位置数组 | `N/A` | 🔧 内置字段 |
| `fileId` | 对应arkime_files_v30索引中的文件ID数组 | `N/A` | 🔧 内置字段 |
| `protocols` | 协议数组 | `protocols` | ✅ 有定义 |

### 嵌套字段结构

#### 源端信息 (source.*)

| 会话字段 | 描述 | 对应字段定义ID | 数据库字段 | 字段类型 |
|----------|------|----------------|------------|----------|
| `source.packets` | 源端包数 | `packets.src` | `srcPackets` | integer |
| `source.mac` | 源MAC地址 | `mac.src` | `source.mac` | termfield |

#### 目标端信息 (destination.*)

| 会话字段 | 描述 | 对应字段定义ID | 数据库字段 | 字段类型 |
|----------|------|----------------|------------|----------|
| `destination.packets` | 目标端包数 | `packets.dst` | `dstPackets` | integer |
| `destination.mac` | 目标MAC地址 | `mac.dst` | `destination.mac` | termfield |

#### 网络统计 (network.*)

| 会话字段 | 描述 | 对应字段定义ID | 数据库字段 | 字段类型 |
|----------|------|----------------|------------|----------|
| `network.vlan.id` | VLAN ID | `vlan` | `network.vlan.id` | integer |

## 协议特定字段映射

### HTTP协议字段

HTTP协议字段在会话数据中以`http`对象形式存储，对应字段定义中的`http.*`字段。

| 字段定义ID | 友好名称 | 数据库字段 | 类型 | 会话数据路径 |
|------------|----------|------------|------|--------------|
| `host.http` | Hostname | `http.host` | termfield | `http.host` |
| `host.http` | Hostname | `http.host` | termfield | `http.host` |
| `host.http.tokens` | Hostname Tokens | `http.hostTokens` | textfield | `http.hostTokens` |
| `http.authtype` | Auth Type | `http.authType` | termfield | `http.authType` |
| `http.bodymagic` | Body Magic | `http.bodyMagic` | termfield | `http.bodyMagic` |
| `http.bodymagic` | Body Magic | `http.bodyMagic` | termfield | `http.bodyMagic` |
| `http.cookie.key` | Cookie Keys | `http.cookieKey` | termfield | `http.cookieKey` |
| `http.cookie.value` | Cookie Values | `http.cookieValue` | termfield | `http.cookieValue` |
| `http.hasheader` | Has Src or Dst Header | `hhall` | termfield | `http.hhall` |
| `http.hasheader.dst` | Has Dst Header | `http.responseHeader` | termfield | `http.responseHeader` |
| ... | ... | ... | ... | ... |
| *共37个HTTP字段* | | | | |

### DNS协议字段

DNS协议字段在会话数据中以`dns`对象形式存储。

| 字段定义ID | 友好名称 | 数据库字段 | 类型 | 会话数据路径 |
|------------|----------|------------|------|--------------|
| `dns.answer.caa` | DNS Answer CAA | `dns.answers.caa` | termfield | `dns.answers.caa` |
| `dns.answer.class` | DNS Answer Class | `dns.answers.class` | termfield | `dns.answers.class` |
| `dns.answer.cname` | DNS Answer CNAME | `dns.answers.cname` | termfield | `dns.answers.cname` |
| `dns.answer.cnt` | DNS Answers Cnt | `dns.answersCnt` | integer | `dns.answersCnt` |
| `dns.answer.https` | DNS Answer HTTPS | `dns.answers.https` | termfield | `dns.answers.https` |
| `dns.answer.ip` | DNS Answer IP | `dns.answers.ip` | ip | `dns.answers.ip` |
| `dns.answer.mx` | DNS Answer MX | `dns.answers.mx` | termfield | `dns.answers.mx` |
| `dns.answer.name` | DNS Name | `dns.answers.name` | termfield | `dns.answers.name` |
| `dns.answer.ns` | DNS Answer NS | `dns.answers.nameserver` | termfield | `dns.answers.nameserver` |
| `dns.answer.priority` | DNS Answer Priority | `dns.answers.priority` | integer | `dns.answers.priority` |
| ... | ... | ... | ... | ... |
| *共29个DNS字段* | | | | |

### TLS/SSL协议字段

TLS协议字段在会话数据中以`tls`对象形式存储。

| 字段定义ID | 友好名称 | 数据库字段 | 类型 | 会话数据路径 |
|------------|----------|------------|------|--------------|
| `tls.cipher` | Cipher | `tls.cipher` | termfield | `tls.cipher` |
| `tls.ja3` | JA3 | `tls.ja3` | termfield | `tls.ja3` |
| `tls.ja3s` | JA3S | `tls.ja3s` | termfield | `tls.ja3s` |
| `tls.ja3sstring` | JA3SSTR | `tls.ja3sstring` | termfield | `tls.ja3sstring` |
| `tls.ja3string` | JA3STR | `tls.ja3string` | termfield | `tls.ja3string` |
| `tls.ja4` | JA4 | `tls.ja4` | termfield | `tls.ja4` |
| `tls.ja4` | JA4 | `tls.ja4` | termfield | `tls.ja4` |
| `tls.ja4_r` | JA4_r | `tls.ja4_r` | termfield | `tls.ja4_r` |
| `tls.ja4_r` | JA4_r | `tls.ja4_r` | termfield | `tls.ja4_r` |
| `tls.sessionid.dst` | Dst Session Id | `tls.dstSessionId` | termfield | `tls.dstSessionId` |
| `tls.sessionid.src` | Src Session Id | `tls.srcSessionId` | termfield | `tls.srcSessionId` |
| `tls.version` | Version | `tls.version` | termfield | `tls.version` |

## 字段映射规律总结

### 1. 直接映射字段
- 基础会话信息字段直接存储在会话文档根级别
- 如: `length`, `protocols`, `node` 等

### 2. 嵌套结构映射
- 源端信息: `source.*` → 字段定义 `*.src`
- 目标端信息: `destination.*` → 字段定义 `*.dst`
- 网络信息: `network.*` → 对应字段定义

### 3. 协议特定字段
- HTTP: `http.*` → 字段定义 `http.*`
- DNS: `dns.*` → 字段定义 `dns.*`
- TLS: `tls.*` → 字段定义 `tls.*`
- 其他协议类似

### 4. 内置字段
以下字段为Arkime内置字段，无对应字段定义：
- `@timestamp`: Elasticsearch时间戳
- `firstPacket`: 会话首包时间
- `lastPacket`: 会话末包时间
- `ipProtocol`: IP协议号
- `node`: 捕获节点
- `packetPos`: 包位置信息
- `fileId`: 文件ID引用

## 完整字段统计

### 按组分类的字段数量
- **general**: 42 个字段
- **http**: 37 个字段
- **dns**: 29 个字段
- **email**: 19 个字段
- **cert**: 18 个字段
- **tls**: 12 个字段
- **smb**: 9 个字段
- **suricata**: 7 个字段
- **dhcp**: 6 个字段
- **socks**: 5 个字段
- **modbus**: 5 个字段
- **snmp**: 5 个字段
- **quic**: 4 个字段
- **ssh**: 4 个字段
- **radius**: 4 个字段
- **oracle**: 4 个字段
- **postgresql**: 3 个字段
- **krb5**: 3 个字段
- **mysql**: 2 个字段
- **ldap**: 2 个字段
- **irc**: 2 个字段
- **bgp**: 1 个字段
- **isis**: 1 个字段

### 字段类型分布
- **termfield**: 156 个字段
- **integer**: 44 个字段
- **ip**: 12 个字段
- **textfield**: 10 个字段
- **date**: 2 个字段

## 使用示例

### 1. 查询特定字段
```json
// 查询HTTP方法为GET的会话
{
  "query": {
    "term": {
      "http.method": "GET"
    }
  }
}
```

### 2. 聚合统计
```json
// 按协议类型聚合
{
  "aggs": {
    "protocols": {
      "terms": {
        "field": "protocols"
      }
    }
  }
}
```

### 3. 字段映射查找
```bash
# 根据字段定义ID查找会话数据路径
# 字段定义: http.method → 会话数据: http.method
# 字段定义: mac.src → 会话数据: source.mac
```

---

*本文档详细说明了Arkime会话数据与字段定义的完整对应关系，便于理解数据结构和进行查询开发。*

# Arkime字段注册提取结果

本文档说明了从Arkime代码库中提取所有`arkime_field_define`调用的完整过程和结果。

## 提取结果概览

✅ **成功提取**: 224个字段定义  
✅ **覆盖范围**: 23个字段组  
✅ **格式标准**: Elasticsearch v30文档格式  
✅ **数据完整性**: 包含所有必要字段信息  

## 生成的文件列表

### 1. 核心数据文件

#### `arkime_fields_clean.json` 
- **格式**: 标准Elasticsearch文档格式JSON
- **内容**: 224个字段的完整定义
- **用途**: 直接用于Elasticsearch导入或程序处理
- **示例格式**:
```json
{
    "_index": "arkime_fields_v30",
    "_type": "_doc",
    "_id": "tls.ja4",
    "_score": 1,
    "_source": {
        "friendlyName": "JA4",
        "group": "tls",
        "help": "SSL/TLS JA4 field",
        "dbField2": "tls.ja4",
        "type": "termfield"
    }
}
```

### 2. 文档文件

#### `Arkime_Field_Registration_Complete_Documentation.md`
- **格式**: Markdown
- **内容**: 完整的字段注册统计文档
- **包含**: 统计信息、示例、使用说明

#### `Arkime_Fields_Complete_Documentation.md`
- **格式**: Markdown  
- **内容**: 按组分类的详细字段文档
- **特点**: 每个字段都有完整的JSON格式和属性表格

#### `Arkime_Fields_Summary_Table.md`
- **格式**: Markdown表格
- **内容**: 所有字段的汇总表格
- **用途**: 快速查找和参考

### 3. 工具脚本

#### `extract_arkime_fields.py`
- **功能**: 从源码中提取arkime_field_define调用
- **输出**: 原始字段定义JSON

#### `generate_field_documentation.py`
- **功能**: 生成详细的Markdown文档
- **输出**: 完整文档和汇总表

#### `create_es_format_doc.py`
- **功能**: 创建符合要求的ES格式文档
- **输出**: 清理后的JSON和最终文档

## 字段统计详情

### 按组分类统计
```
general: 42 个字段    (通用字段)
http: 37 个字段       (HTTP协议)
dns: 29 个字段        (DNS协议)
email: 19 个字段      (邮件协议)
cert: 18 个字段       (证书相关)
tls: 12 个字段        (TLS/SSL)
smb: 9 个字段         (SMB协议)
suricata: 7 个字段    (Suricata集成)
dhcp: 6 个字段        (DHCP协议)
socks: 5 个字段       (SOCKS代理)
modbus: 5 个字段      (Modbus协议)
snmp: 5 个字段        (SNMP协议)
quic: 4 个字段        (QUIC协议)
ssh: 4 个字段         (SSH协议)
radius: 4 个字段      (RADIUS协议)
oracle: 4 个字段      (Oracle数据库)
postgresql: 3 个字段  (PostgreSQL)
krb5: 3 个字段        (Kerberos 5)
mysql: 2 个字段       (MySQL数据库)
ldap: 2 个字段        (LDAP协议)
irc: 2 个字段         (IRC协议)
bgp: 1 个字段         (BGP协议)
isis: 1 个字段        (ISIS协议)
```

### 字段类型分布
- **termfield**: 可搜索字符串字段 (最常用)
- **integer**: 整数字段
- **ip**: IP地址字段  
- **date**: 日期时间字段
- **textfield**: 文本字段

## 源码分布

字段定义分布在以下源码文件中：

### 核心模块
- `capture/parsers.c`: 基础解析器字段
- `capture/packet.c`: 网络包字段
- `capture/session.c`: 会话字段
- `capture/field.c`: 字段管理函数

### 协议解析器 (capture/parsers/)
- `http.c`: HTTP协议字段 (37个)
- `dns.c`: DNS协议字段 (29个)  
- `smtp.c`: 邮件协议字段 (19个)
- `certs.c`: 证书字段 (18个)
- `tls.c`: TLS/SSL字段 (12个)
- 其他协议解析器...

### 插件模块 (capture/plugins/)
- `suricata.c`: Suricata集成
- `wise.c`: WISE插件
- `tagger.c`: 标签插件

## 使用建议

1. **查看完整数据**: 使用 `arkime_fields_clean.json`
2. **快速查找**: 参考 `Arkime_Fields_Summary_Table.md`
3. **详细了解**: 阅读 `Arkime_Fields_Complete_Documentation.md`
4. **程序处理**: 直接解析 `arkime_fields_clean.json`

## 数据质量保证

✅ **完整性**: 覆盖所有arkime_field_define调用  
✅ **准确性**: 直接从源码提取，无人工修改  
✅ **格式标准**: 严格按照Elasticsearch文档格式  
✅ **可追溯**: 保留源文件信息便于验证  

---

## 🆕 新增内容 - 会话字段映射

### 会话数据映射文档

#### `Arkime_Session_Field_Mapping.md`
- **内容**: `arkime_sessions3-*`与`arkime_fields_v30`的对应关系
- **特点**: 详细说明会话数据结构与字段定义的映射
- **包含**: 基础字段、嵌套结构、协议特定字段的完整映射

#### `Arkime_Complete_Field_Mapping_Table.md`
- **内容**: 224个字段的完整映射对应表
- **格式**: 表格形式，包含字段ID、会话数据路径、说明
- **用途**: 开发查询时的完整参考手册

#### `Arkime_Fields_Final_Documentation.md`
- **内容**: 完整的字段系统参考手册
- **特点**: 整合所有文档，提供使用指南和实际示例

### 映射关系核心发现

#### 1. 会话数据结构
```json
{
  "@timestamp": "处理时间戳",
  "firstPacket": "首包时间",
  "lastPacket": "末包时间",
  "length": "会话持续时间",
  "source": {
    "ip": "源IP",
    "port": "源端口",
    "mac": ["源MAC地址"]
  },
  "destination": {
    "ip": "目标IP",
    "port": "目标端口",
    "mac": ["目标MAC地址"]
  },
  "protocols": ["协议数组"],
  "http": { "协议特定字段" },
  "dns": { "协议特定字段" }
}
```

#### 2. 字段映射规律
- **直接映射**: `length` ↔ `session.length`
- **嵌套映射**: `source.ip` ↔ `ip.src`
- **协议映射**: `http.method` ↔ `http.method`
- **内置字段**: `@timestamp`, `node`, `fileId` 等无字段定义

#### 3. 查询应用示例
```json
// 查询HTTP GET请求
{"query": {"term": {"http.method": "GET"}}}

// 查询源IP
{"query": {"term": {"source.ip": "***********"}}}

// 聚合协议分布
{"aggs": {"protocols": {"terms": {"field": "protocols"}}}}
```

---

*提取时间*: 2025年7月16日
*Arkime版本*: 基于当前代码库
*提取方法*: 自动化脚本扫描源码
*新增功能*: 会话字段映射关系分析
